<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.archiveroom.ArchiveAdviceDao">
    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_archive_advice set is_valid = 0 where id = #{id}
    </update>
    <!--添加-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity">
        insert into psycloud_archive_advice(
            advice,
            is_valid
        )
        values(
            #{adviceContent,jdbcType=VARCHAR},
            1
        )
    </insert>
    <!--修改-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity">
        update
            psycloud_archive_advice
        set
            advice = #{adviceContent,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--根据条件查询评语集合-->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.archiveroom.ArchiveAdviceDto" resultType="cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity">
        select
            id,
            advice as adviceContent
        from psycloud_archive_advice
        where is_valid = 1
        <if test="adviceContent != null and adviceContent != ''">and advice like  CONCAT('%',#{adviceContent},'%')</if>
    </select>

    <!--根据id查询评语-->
    <select id="getById" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity">
        select
            id,
            advice as adviceContent
        from psycloud_archive_advice
        where is_valid = 1 and id = #{id}
    </select>
</mapper>
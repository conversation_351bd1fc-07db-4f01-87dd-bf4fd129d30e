<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.activityroom.ActivityPicDao">
    <!-- 上传活动图片 -->
    <insert id="uploadPic" parameterType="cn.psycloud.psyplatform.entity.activityroom.ActivityPicEntity">
        insert into psycloud_activity_pic (
            file_name,
            activity_id,
            upload_time,
            operator,
            is_valid
        )
        values (
            #{fileName,jdbcType=VARCHAR},
            #{activityId,jdbcType=INTEGER},
            #{uploadTime,jdbcType=TIMESTAMP},
            #{operator,jdbcType=INTEGER},
            1
        )
    </insert>
    <!-- 获取活动图片列表 -->
    <select id="getList" resultType="cn.psycloud.psyplatform.entity.activityroom.ActivityPicEntity" parameterType="Integer">
        select id,file_name
        from psycloud_activity_pic
        where is_valid = 1 and activity_id = #{activityId}
    </select>
    <!-- 删除图片 -->
    <update id="delPic" parameterType="Integer">
        update psycloud_activity_pic
        set is_valid = 0
        where id = #{id}
    </update>
</mapper>
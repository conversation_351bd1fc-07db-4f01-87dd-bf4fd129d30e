<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.activityroom.ActivityClockingPicDao">
    <!-- 上传签到图片 -->
    <insert id="uploadPic" parameterType="cn.psycloud.psyplatform.entity.activityroom.ActivityClockingPicEntity">
        insert into psycloud_activity_clocking_pic (
            struct_id,
            file_name,
            upload_time,
            operator,
            is_valid
        ) values (
            #{structId,jdbcType=INTEGER},
            #{fileName,jdbcType=VARCHAR},
            #{uploadTime,jdbcType=TIMESTAMP},
            #{operator,jdbcType=INTEGER},
            1
        )
    </insert>

    <!-- 获取签到图片列表 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.activityroom.ActivityClockingPicDto" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityClockingPicDto">
        select id,file_name
        from psycloud_activity_clocking_pic
        where is_valid = 1
        <if test="structId != null and structId != 0">
            and struct_id = #{structId}
        </if>
        <if test="childStructs!=null and childStructs.size()>0">
            and struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 删除图片 -->
    <update id="delPic" parameterType="Integer">
        update psycloud_activity_clocking_pic
        set is_valid = 0
        where id = #{id}
    </update>

    <!-- 获取随机图片 -->
    <select id="getRandomPic" resultType="String" parameterType="Integer">
        select file_name
        from psycloud_activity_clocking_pic
        where struct_id = #{structId}
        order by rand()
        limit 1
    </select>
</mapper>
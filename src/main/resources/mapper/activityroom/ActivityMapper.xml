<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.activityroom.ActivityDao">
    <!-- 添加心理活动 -->
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.activityroom.ActivityEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_activity (
            activity_name, activity_type, activity_intro, activity_cover, start_time, end_time, counselor, add_date, is_valid, survey_id, operator, struct_id
        ) values (
            #{activityName,jdbcType=VARCHAR}, 
            #{activityType,jdbcType=INTEGER}, 
            #{activityIntro,jdbcType=LONGVARCHAR},
            #{activityCover,jdbcType=VARCHAR}, 
            #{startTime,jdbcType=TIMESTAMP}, 
            #{endTime,jdbcType=TIMESTAMP}, 
            #{counselor,jdbcType=INTEGER}, 
            #{addDate,jdbcType=TIMESTAMP}, 
            1,
            #{surveyId,jdbcType=INTEGER},
            #{operator,jdbcType=INTEGER},
            #{structId,jdbcType=INTEGER}
        )
    </insert>
    
    <!-- 修改心理活动 -->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.activityroom.ActivityEntity">
        update psycloud_activity
        set
            activity_name = #{activityName,jdbcType=VARCHAR},
            activity_type = #{activityType,jdbcType=INTEGER},
            activity_intro = #{activityIntro,jdbcType=VARCHAR},
            activity_cover = #{activityCover,jdbcType=VARCHAR},
            start_time = #{startTime,jdbcType=TIMESTAMP},
            end_time = #{endTime,jdbcType=TIMESTAMP},
            counselor = #{counselor,jdbcType=INTEGER},
            survey_id = #{surveyId,jdbcType=INTEGER},
            struct_id = #{structId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <!-- 删除心理活动 -->
    <update id="delete" parameterType="java.lang.Integer">
        update psycloud_activity
        set is_valid = 0
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <!-- 根据条件查询心理活动列表 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto">
        select 
            a.id,
            a.activity_name,
            a.activity_type,
            a.activity_intro,
            a.activity_cover,
            a.start_time,
            a.end_time,
            u.real_name as counselorName,
            a.operator,
            u2.login_name as operatorName,
            a.add_date,
            a.is_valid,
            s.id as surveyId,
            s.survey_name,
            ps.struct_name as structName,
            (
                select count(*)
                from
                    psycloud_activity_clocking_record pacr
                    inner join psycloud_user_info u3 on u3.user_id = pacr.user_id
                    inner join psycloud_user_role ur on ur.user_id = u3.user_id
                where pacr.activity_id = a.id and pacr.action_type = 1 and ur.role_id = 3
            ) as clockingInNum
        from psycloud_activity a
            left join psycloud_user_info u on a.counselor = u.user_id
            left join psycloud_survey s on a.survey_id = s.id
            left join psycloud_user u2 on a.operator = u2.user_id
            left join psycloud_structs ps on ps.id = a.struct_id
        where a.is_valid = 1
         and u.is_valid = 1
            <if test="activityName != null and activityName != ''">
                and a.activity_name like concat('%', #{activityName}, '%')
            </if>
            <if test="activityType != null and activityType != 0">
                and a.activity_type = #{activityType}
            </if>
            <if test="structId != null and structId != 0">
                and a.struct_id = #{structId}
            </if>
            <if test="childStructs!=null and childStructs.size()>0">
                and a.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="counselorId != null and counselorId != 0">
                and a.counselor = #{counselorId}
            </if>
            <if test="startDate != null">
                and DATE(a.start_time) >= #{startDate}
            </if>
            <if test="endDate != null">
                and DATE(a.start_time) &lt;= #{endDate}
            </if>
        order by a.start_time desc
    </select>
    
    <!-- 根据id查询心理活动 -->
    <select id="getById" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto">
        select 
            a.id,
            a.activity_name,
            a.activity_type,
            a.activity_intro,
            a.activity_cover,
            a.start_time,
            a.end_time,
            u.user_id as counselorId,
            u.real_name as counselorName,
            a.add_date,
            a.is_valid,
            s.id as surveyId,
            s.survey_name,
            a.struct_id,
            ps.struct_name
        from psycloud_activity a
            left join psycloud_user_info u on a.counselor = u.user_id
            left join psycloud_survey s on a.survey_id = s.id
            left join psycloud_structs ps on ps.id = a.struct_id
        where a.is_valid = 1
          and u.is_valid = 1
          and a.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 查询用户调查问卷是否已做 -->
    <select id="isSurveyDone" parameterType="map" resultType="Integer">
        select count(*)
        from psycloud_survey_record psr
            inner join psycloud_activity_survey_record pasr on psr.id = pasr.survey_record_id
        where psr.is_done = 1
          and psr.survey_id = #{surveyId,jdbcType=INTEGER}
          and psr.user_id = #{userId,jdbcType=INTEGER}
          and pasr.activity_id = #{activityId,jdbcType=INTEGER}
    </select>

    <!-- 导出调查问卷结果-->
    <select id="getExportSurveyTestResult" resultType="java.util.LinkedHashMap" parameterType="cn.psycloud.psyplatform.dto.activityroom.ExportActivitySurveyRecordDto">
        select
            /*+ GROUP_OPT_FLAG(1)*/
            psr2.id as 记录id,
            pui.user_id as 用户id,
            pu.login_name as 用户名,
            pui.real_name as 姓名,
            ps.struct_name as 部门,
            DATE_FORMAT(psr2.record_date, '%Y-%m-%d %H:%i:%s') as 作答时间,
            ps2.survey_name as 问卷名称,
            ${sql}
        from psycloud_survey_question psq
            left join psycloud_survey_result psr on psr.q_id = psq.id
            inner join psycloud_survey_record psr2 on psr2.id = psr.record_id and psq.survey_id = psr2.survey_id
            inner join psycloud_user_info pui on pui.user_id = psr2.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
            left join psycloud_structs ps on ps.id = pui.struct_id
            left join psycloud_survey ps2 on ps2.id = psr2.survey_id
        where psr2.is_valid = 1
            and psr2.is_done = 1
            and pui.is_valid = 1
            and psr2.survey_id = #{surveyId}
            <if test="activityId !=null and activityId !=0">
                and psr2.id in (
                select ptsr3.survey_record_id
                from psycloud_activity_survey_record ptsr3
                    inner join psycloud_activity pt on pt.id = ptsr3.activity_id
                where  pt.id = #{activityId}
                )
            </if>
            <if test="searchLoginName != null and searchLoginName != ''">
                and pu.login_name like concat('%', #{searchLoginName}, '%')
            </if>
            <if test="searchRealName != null and searchRealName != ''">
                and pui.real_name like concat('%', #{searchRealName}, '%')
            </if>
            <if test="startRecordDate != null and startRecordDate != ''">
                and date(psr2.record_date) >= #{startRecordDate}
            </if>
            <if test="endRecordDate != null and endRecordDate != ''">
                and date(psr2.record_date) &lt;= #{endRecordDate}
            </if>
            <if test="questionNumber != null and questionNumber != '' and selectedOption != null and selectedOption != ''">
                and psr2.id in (
                    select tt.record_id
                    from psycloud_survey_result tt
                    inner join psycloud_survey_item ss on ss.q_id = tt.q_id and tt.item_id = ss.item_content
                    inner join psycloud_survey_question qq on qq.id = ss.q_id
                    where qq.q_number = #{questionNumber} and tt.item_id = #{selectedOption}
                )
            </if>
        group by pui.user_id,pu.login_name,pui.real_name
        order by pui.user_id,psq.q_number
    </select>

    <!-- 导出问卷调查结果（按选项序号） -->
    <select id="getExportSurveyTestResultByText" resultType="java.util.LinkedHashMap" parameterType="cn.psycloud.psyplatform.dto.activityroom.ExportActivitySurveyRecordDto">
        select
            /*+ GROUP_OPT_FLAG(1)*/
            psr2.id as 记录id,
            pui.user_id as 用户id,
            pu.login_name as 用户名,
            pui.real_name as 姓名,
            ps.struct_name as 部门,
            DATE_FORMAT(psr2.record_date, '%Y-%m-%d %H:%i:%s') as 作答时间,
            ps2.survey_name as 问卷名称,
            ${sql}
        from psycloud_survey_question psq
            inner join psycloud_survey_result psr on psr.q_id = psq.id
            left join psycloud_survey_item psi on psi.q_id = psq.id and psi.item_content = psr.item_id
            inner join psycloud_survey_record psr2 on psr2.id = psr.record_id and psq.survey_id = psr2.survey_id
            inner join psycloud_user_info pui on pui.user_id = psr2.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
            left join psycloud_structs ps on ps.id = pui.struct_id
            left join psycloud_survey ps2 on ps2.id = psr2.survey_id
        where psr2.is_valid = 1
            and psr2.is_done = 1
            and pui.is_valid = 1
            and psr2.survey_id = #{surveyId}
            <if test="activityId !=null and activityId !=0">
                and psr2.id in (
                select ptsr3.survey_record_id
                from psycloud_activity_survey_record ptsr3
                    inner join psycloud_activity pt on pt.id = ptsr3.activity_id
                where  pt.id = #{activityId}
                )
            </if>
            <if test="searchLoginName != null and searchLoginName != ''">
                and pu.login_name like concat('%', #{searchLoginName}, '%')
            </if>
            <if test="searchRealName != null and searchRealName != ''">
                and pui.real_name like concat('%', #{searchRealName}, '%')
            </if>
            <if test="startRecordDate != null and startRecordDate != ''">
                and date(psr2.record_date) >= #{startRecordDate}
            </if>
            <if test="endRecordDate != null and endRecordDate != ''">
                and date(psr2.record_date) &lt;= #{endRecordDate}
            </if>
            <if test="questionNumber != null and questionNumber != '' and selectedOption != null and selectedOption != ''">
                and psr2.id in (
                    select tt.record_id
                    from psycloud_survey_result tt
                    inner join psycloud_survey_item ss on ss.q_id = tt.q_id and tt.item_id = ss.item_content
                    inner join psycloud_survey_question qq on qq.id = ss.q_id
                    where qq.q_number = #{questionNumber} and tt.item_id = #{selectedOption}
                )
            </if>
        group by pui.user_id,pu.login_name,pui.real_name
        order by pui.user_id,psq.q_number
    </select>

    <!-- 获取我的活动 -->
    <select id="getMyActivities" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto">
        (select
            pa.id,
            pa.activity_name,
            pa.activity_type,
            pa.start_time,
            pa.end_time,
            pa.activity_cover
        from psycloud_activity pa
            inner join psycloud_activity_clocking_record pacr on pacr.activity_id = pa.id
        where pa.is_valid = 1 and pacr.action_type=1 and pacr.user_id = #{userId})
        union
        (select
            pa.id,
            pa.activity_name,
            pa.activity_type,
            pa.start_time,
            pa.end_time,
            pa.activity_cover
        from psycloud_activity pa
        where pa.is_valid = 1 and pa.counselor = #{userId})
        order by start_time desc
    </select>

    <!-- 获取我的活动集合：select -->
    <select id="getCounselorActivitiesForSelect" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityForSelectDto">
        select
            pa.id,
            pa.activity_name
        from psycloud_activity pa
        where pa.is_valid = 1 and pa.counselor = #{userId}
        order by start_time desc
    </select>

    <!-- 更新活动总评分 -->
    <update id="updateOverallEvaluation" parameterType="cn.psycloud.psyplatform.entity.activityroom.OverallEvaluationEntity">
            update psycloud_activity
            set overall_evaluation = #{overallEvaluation,jdbcType=VARCHAR}
            where id = #{activityId,jdbcType=INTEGER}
    </update>

    <!-- 获取活动总评分 -->
    <select id="getOverallEvaluation" parameterType="java.lang.Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.OverallEvaluationDto">
        select activity_name, overall_evaluation
        from psycloud_activity
        where id = #{activityId,jdbcType=INTEGER}
    </select>

    <!-- 获取活动报告 -->
    <select id="getActivityReport" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityReportDto">
        select
            a.id as activityId,
            a.activity_name,
            a.start_time,
            a.end_time,
            u.real_name as counselor,
            case a.activity_type
                when 1 then '驻场咨询（轻咨询）'
                when 2 then '驻场咨询（50分钟以上）'
                when 3 then '团体辅导'
                when 4 then '心理关爱活动'
                else ''
                end as activityType,
            (select COUNT(*)
             from psycloud_activity_clocking_record pacr
                 inner join psycloud_user_info pui on pui.user_id = pacr.user_id
                 inner join psycloud_user_role ur on ur.user_id = pui.user_id
             where pacr.activity_id = a.id
               and pacr.action_type = 1
               and pui.is_valid = 1
               and ur.role_id = 3
             ) as clockingInCount,
            (select COUNT(*)
             from psycloud_activity_clocking_record pacr
                 inner join psycloud_user_info pui on pui.user_id = pacr.user_id
                 inner join psycloud_user_role ur on ur.user_id = pui.user_id
             where pacr.activity_id = a.id
               and pacr.action_type = 2
               and pui.is_valid = 1
               and ur.role_id = 3
             ) as clockingOutCount,
            a.overall_evaluation,
            s.survey_name,
            (select COUNT(*)
             from psycloud_survey_record psr
                inner join psycloud_activity_survey_record pasr on psr.id = pasr.survey_record_id
                inner join psycloud_user_info pui on pui.user_id = psr.user_id
                inner join psycloud_user_role ur on ur.user_id = pui.user_id
             where pasr.activity_id = a.id
               and psr.is_done = 1
               and psr.is_valid = 1
               and pui.is_valid = 1
               and ur.role_id = 3
             ) as surveySubmitCount
        from
            psycloud_activity a
                left join psycloud_user_info u on a.counselor = u.user_id
                left join psycloud_survey s on a.survey_id = s.id
        WHERE
            a.is_valid = 1
          and u.is_valid = 1
          and a.id = #{activityId}
    </select>

    <!-- 获取活动参与人员清单 -->
    <select id="getActivityParticipants" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.activityroom.ParticipantDto">
        select
            u.user_id as userId,
            u.login_name as loginName,
            ui.real_name as realName,
            ps.struct_name as structName,
            clock_in.clocking_time as clockingInTime,
            clock_out.clocking_time as clockingOutTime,
            case when survey_record.record_id is not null then 1 else 0 end as isSurveyCompleted
        from (
            select distinct user_id
            from psycloud_activity_clocking_record
            where activity_id = #{activityId,jdbcType=INTEGER}
        ) participants
        inner join psycloud_user u on u.user_id = participants.user_id
        inner join psycloud_user_info ui on ui.user_id = u.user_id
        inner join psycloud_user_role ur on ur.user_id = u.user_id
        left join psycloud_structs ps on ps.id = ui.struct_id
        left join (
            select user_id, clocking_time
            from psycloud_activity_clocking_record
            where activity_id = #{activityId,jdbcType=INTEGER} and action_type = 1
        ) clock_in on clock_in.user_id = participants.user_id
        left join (
            select user_id, clocking_time
            from psycloud_activity_clocking_record
            where activity_id = #{activityId,jdbcType=INTEGER} and action_type = 2
        ) clock_out on clock_out.user_id = participants.user_id
        left join (
            select distinct psr.user_id, pasr.survey_record_id as record_id
            from psycloud_survey_record psr
            inner join psycloud_activity_survey_record pasr on psr.id = pasr.survey_record_id
            where pasr.activity_id = #{activityId,jdbcType=INTEGER} and psr.is_done = 1
        ) survey_record on survey_record.user_id = participants.user_id
        where ui.is_valid = 1 and ur.role_id = 3
        order by clock_in.clocking_time asc, ui.real_name asc
    </select>

    <!-- 获取活动问卷结果数据 -->
    <select id="getActivitySurveyResults" resultType="cn.psycloud.psyplatform.dto.survey.SurveyResultDto">
        select
            psq.q_number as qNumber,
            psq.q_content as qContent,
            psr.item_id as itemId
        from psycloud_survey_result psr
            inner join psycloud_survey_record psr2 on psr2.id = psr.record_id
            inner join psycloud_survey_question psq on psq.id = psr.q_id
            inner join psycloud_activity_survey_record pasr on pasr.survey_record_id = psr2.id
        where pasr.activity_id = #{activityId}
            and psr2.survey_id = #{surveyId}
            and psr2.is_done = 1
            and psr2.is_valid = 1
        order by psq.q_number, psr2.user_id
    </select>

    <!-- 获取活动数据看板基础统计数据 -->
    <select id="getActivityDashboardBasicStats" parameterType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto"
            resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityDashboardDto">
        select
            (select count(distinct a1.id)
             from psycloud_activity a1
             where a1.is_valid = 1
                <if test="startDate != null">
                    and a1.start_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and a1.start_time &lt;= #{endDate}
                </if>
                <if test="structId != null and structId != 0">
                    and a1.struct_id = #{structId}
                </if>
                <if test="childStructs!=null and childStructs.size()>0">
                    and a1.struct_id in
                    <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            ) as activityCount,

            (select count(*)
             from psycloud_activity_clocking_record pacr1
             inner join psycloud_activity a2 on a2.id = pacr1.activity_id
             inner join psycloud_user_info pui on pui.user_id = pacr1.user_id
             inner join psycloud_user_role ur on ur.user_id = pui.user_id
             where a2.is_valid = 1
               and pacr1.action_type = 1
               and pui.is_valid = 1
               and ur.role_id = 3
                <if test="startDate != null">
                    and a2.start_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and a2.start_time &lt;= #{endDate}
                </if>
                <if test="structId != null and structId != 0">
                    and a2.struct_id = #{structId}
                </if>
                <if test="childStructs!=null and childStructs.size()>0">
                    and a2.struct_id in
                    <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            ) as totalClockInCount,

            (select count(*)
             from psycloud_activity_clocking_record pacr2
                 inner join psycloud_activity a3 on a3.id = pacr2.activity_id
                 inner join psycloud_user_info pui on pui.user_id = pacr2.user_id
                 inner join psycloud_user_role ur on ur.user_id = pui.user_id
             where a3.is_valid = 1
               and pacr2.action_type = 2
               and pui.is_valid = 1
               and ur.role_id = 3
                <if test="startDate != null">
                    and a3.start_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and a3.start_time &lt;= #{endDate}
                </if>
                <if test="structId != null and structId != 0">
                    and a3.struct_id = #{structId}
                </if>
                <if test="childStructs!=null and childStructs.size()>0">
                    and a3.struct_id in
                    <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            ) as totalClockOutCount,

            (select count(distinct psr3.id)
             from psycloud_survey_record psr3
             inner join psycloud_user_info pui on pui.user_id = psr3.user_id
             inner join psycloud_user_role ur on ur.user_id = pui.user_id
             inner join psycloud_activity_survey_record pasr3 on pasr3.survey_record_id = psr3.id
             inner join psycloud_activity a4 on a4.id = pasr3.activity_id
             where a4.is_valid = 1 and psr3.is_valid = 1 and psr3.is_done = 1 and pui.is_valid = 1 and ur.role_id = 3
                <if test="startDate != null">
                    and a4.start_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and a4.start_time &lt;= #{endDate}
                </if>
                <if test="structId != null and structId != 0">
                    and a4.struct_id = #{structId}
                </if>
                <if test="childStructs!=null and childStructs.size()>0">
                    and a4.struct_id in
                    <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            ) as surveyCompletedCount
    </select>

    <!-- 获取活动类型统计数据 -->
    <select id="getActivityTypeStats" parameterType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto"
            resultType="cn.psycloud.psyplatform.dto.activityroom.ActivityTypeStatDto">
        select
            a.activity_type as activityType,
            case a.activity_type
                when 1 then '驻场咨询（轻咨询）'
                when 2 then '驻场咨询（50分钟以上）'
                when 3 then '团体辅导'
                when 4 then '心理关爱活动'
                else '其他'
            end as activityTypeName,
            count(pacr_in.user_id) as totalParticipants,
            coalesce(sum(
                case
                    when pacr_in.clocking_time is not null and pacr_out.clocking_time is not null
                    then timestampdiff(MINUTE, pacr_in.clocking_time, pacr_out.clocking_time)
                    else 0
                end
            ), 0) as totalDuration
        from psycloud_activity a
            left join psycloud_activity_clocking_record pacr_in on pacr_in.activity_id = a.id and pacr_in.action_type = 1
            left join psycloud_activity_clocking_record pacr_out on pacr_out.activity_id = a.id and pacr_out.user_id = pacr_in.user_id and pacr_out.action_type = 2
        where a.is_valid = 1
            <if test="startDate != null">
                and a.start_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and a.start_time &lt;= #{endDate}
            </if>
            <if test="structId != null and structId != 0">
                and a.struct_id = #{structId}
            </if>
            <if test="childStructs!=null and childStructs.size()>0">
                and a.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        group by a.activity_type
        order by a.activity_type
    </select>

    <!-- 获取咨询师统计数据 -->
    <select id="getCounselorStats" parameterType="cn.psycloud.psyplatform.dto.activityroom.ActivityDto"
            resultType="cn.psycloud.psyplatform.dto.activityroom.CounselorStatDto">
        select
            u.user_id as counselorId,
            u.real_name as counselorName,
            count(distinct a.id) as activityCount,
            coalesce(sum(
                case
                    when pacr_in.clocking_time is not null and pacr_out.clocking_time is not null
                    then timestampdiff(MINUTE, pacr_in.clocking_time, pacr_out.clocking_time)
                    else 0
                end
            ), 0) as totalDuration
        from psycloud_activity a
            inner join psycloud_user_info u on u.user_id = a.counselor
            left join psycloud_activity_clocking_record pacr_in on pacr_in.activity_id = a.id and pacr_in.action_type = 1
            left join psycloud_activity_clocking_record pacr_out on pacr_out.activity_id = a.id and pacr_out.user_id = pacr_in.user_id and pacr_out.action_type = 2
        where a.is_valid = 1 and u.is_valid = 1
            <if test="startDate != null">
                and a.start_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and a.start_time &lt;= #{endDate}
            </if>
            <if test="structId != null and structId != 0">
                and a.struct_id = #{structId}
            </if>
            <if test="childStructs!=null and childStructs.size()>0">
                and a.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        group by u.user_id, u.real_name
        order by activityCount desc, totalDuration desc
    </select>
</mapper>
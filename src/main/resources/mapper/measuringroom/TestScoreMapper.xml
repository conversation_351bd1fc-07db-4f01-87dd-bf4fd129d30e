<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.TestScoreDao">
    <!--删除得分记录-->
    <delete id="deleteByRecordId" parameterType="Integer">
        delete from psycloud_test_score where record_id = #{recordId}
    </delete>

    <!--根据量表ID和因子ID删除得分记录-->
    <delete id="deleteByRecordIdAndFactorId" parameterType="map">
        delete from psycloud_test_score where record_id = #{recordId} and factor_id = #{factorId}
    </delete>

    <!--保存测评得分情况-->
    <insert id="addTestScore" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity">
        insert into psycloud_test_score(
            record_id,
            factor_id,
            original_score,
            score,
            is_abnormal,
            abnormal_value,
            warning_level
        )
        values(
            #{recordId,jdbcType=INTEGER},
            #{factorId,jdbcType=INTEGER},
            #{originalScore,jdbcType=DOUBLE},
            #{score,jdbcType=DOUBLE},
            #{isAbnormal,jdbcType=TINYINT},
            #{abnormalValue,jdbcType=DOUBLE},
            #{warningLevel,jdbcType=TINYINT}
         )
    </insert>

    <!--计算因子分-->
    <select id="computeFactorScore" parameterType="map" resultType="BigDecimal">
        select sum(pa.a_score) as totalscore
        from psycloud_answer pa
            inner join psycloud_question pq on pq.id = pa.q_id
            inner join psycloud_test_result ptr on ptr.q_no = pq.q_number and pa.a_no = ptr.a_no
            inner join psycloud_test_record ptr2 on ptr2.id =ptr.record_id and pq.scale_id = ptr2.scale_id
        where ptr.record_id =#{recordId}
          <choose>
              <when test="qIds==null">and  ptr.q_type = 1</when>
             <otherwise>
                and pq.q_number in
                <foreach item="qId" index="index" collection="qIds" open="(" close=")" separator=",">
                    #{qId}
                </foreach>
            </otherwise>
          </choose>
    </select>
    <!--计算因子分：父母养育方式问卷(EMBU)-->
    <resultMap id="computeEmbuFactorScoreMap" type="java.util.LinkedHashMap">
        <result column="a_no" property="a_no" jdbcType="VARCHAR" />
        <result column="q_no" property="q_no" jdbcType="INTEGER" />
        <result column="id" property="q_id" jdbcType="INTEGER" />
    </resultMap>
    <select id="computeEmbuFactorScore" parameterType="map" resultMap="computeEmbuFactorScoreMap">
        select
            ptr.a_no,
            ptr.q_no,
            pq.id
        from psycloud_test_result ptr
            inner join psycloud_test_record ptr2 on ptr2.id = ptr.record_id
            inner join psycloud_question pq on pq.scale_id = ptr2.scale_id and ptr.q_no = pq.q_number
        where ptr.record_id =#{recordId} and pq.q_number in
        <foreach item="qId" index="index" collection="qIds" open="(" close=")" separator=",">
            #{qId}
        </foreach>
    </select>

    <!--计算多级因子分-->
    <select id="computeMultiFactorScore" parameterType="map" resultType="BigDecimal">
        select sum(pts.score)
        from psycloud_test_score pts
        where pts.record_id = #{recordId} and pts.factor_id in
        <foreach item="factorId" index="index" collection="factorIds" open="(" close=")" separator=",">
            #{factorId}
        </foreach>
    </select>

    <!--更新预警级别-->
    <update id="updateWarningLevel" parameterType="map">
        update psycloud_test_score set warning_level = #{warningLevel,jdbcType=TINYINT} where record_id = #{recordId,jdbcType=INTEGER} and factor_id = #{factorId,jdbcType=INTEGER}
    </update>

    <resultMap id="testScoreResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto">
        <result column="record_id" property="recordId" jdbcType="INTEGER" />
        <result column="original_score" property="originalScore" jdbcType="DOUBLE" />
        <result column="score" property="score" jdbcType="DOUBLE" />
        <result column="is_abnormal" property="isAbnormal" jdbcType="TINYINT" />
        <result column="abnormal_value" property="abnormalValue" jdbcType="DOUBLE" />
        <result column="factor_id" property="factor.id" jdbcType="INTEGER" />
        <result column="factor_no" property="factor.factorNo" jdbcType="TINYINT" />
        <result column="factor_type" property="factor.factorType" jdbcType="TINYINT" />
        <result column="factor_name" property="factor.factorName" jdbcType="VARCHAR" />
        <result column="factor_en" property="factor.factorEn" jdbcType="VARCHAR" />
        <result column="scale_id" property="testRecord.scale.id" jdbcType="INTEGER" />
        <result column="scale_name" property="testRecord.scale.scaleName" jdbcType="VARCHAR" />
        <result column="min_score" property="factor.minScore" jdbcType="DOUBLE" />
        <result column="max_score" property="factor.maxScore" jdbcType="DOUBLE" />
        <result column="avg_score" property="factor.avgScore" jdbcType="DOUBLE" />
        <result column="standard_score" property="factor.standardScore" jdbcType="DOUBLE" />
        <result column="is_lie" property="factor.isLie" jdbcType="TINYINT" />
        <result column="user_id" property="testRecord.user.userId" jdbcType="INTEGER" />
        <result column="login_name" property="testRecord.user.loginName" jdbcType="VARCHAR" />
        <result column="real_name" property="testRecord.user.realName" jdbcType="VARCHAR" />
        <result column="structFullName" property="testRecord.user.structFullName" jdbcType="VARCHAR" />
        <result column="state" property="testRecord.state" jdbcType="TINYINT" />
        <result column="start_time" property="testRecord.startTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="testRecord.endTime" jdbcType="TIMESTAMP" />
        <result column="timeInterval" property="testRecord.timeInterval" jdbcType="INTEGER" />
        <result column="interpretation" property="testRecord.interpretation" jdbcType="LONGVARCHAR" />
        <result column="role_id" property="testRecord.user.role.roleId" jdbcType="INTEGER" />
        <result column="role_name" property="testRecord.user.role.roleName" jdbcType="VARCHAR" />
        <result column="score_condition" property="scoreAbnormalCondition" jdbcType="VARCHAR" />
        <result column="warning_level" property="warningLevel" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getTestScoreList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto" resultMap="testScoreResultMap">
        select
            pts.record_id,
            pts.original_score,
            pts.score,
            pts.is_abnormal,
            pts.abnormal_value,
            pf.id as factor_id,
            pf.factor_no,
            pf.factor_type,
            pf.factor_name,
            pf.factor_en,
            pf.scale_id,
            ps.scale_name,
            pf.min_score,
            pf.max_score,
            pf.avg_score,
            pf.standard_score,
            pf.is_lie,
            pu.user_id,
            pu.login_name,
            pui.real_name,
            f_GetStructFullName(pui.struct_id) as structFullName,
            ptr.state,
            ptr.start_time,
            ptr.end_time,
            TimeStampDiff(second,ptr.start_time,ptr.end_time) as timeInterval,
            ptr.interpretation,
            pur.role_id,
            pr.role_name,
            pts.warning_level,
            case pfa.score_condition when 1 then '>' when 2 then '≥' when 3 then '﹤' when 4 then '≤' when 5 then '=' end as score_condition
        from psycloud_test_score pts
            inner join psycloud_factor pf on pf.id = pts.factor_id
            left join psycloud_factor_abnormal pfa on pfa.factor_id = pf.id
            inner join psycloud_test_record ptr on ptr.id = pts.record_id
            inner join psycloud_scale ps on ps.id = ptr.scale_id
            inner join psycloud_user pu on pu.user_id = ptr.user_id
            inner join psycloud_user_info pui on pui.user_id= pu.user_id
            inner join psycloud_user_role pur on pur.user_id = pui.user_id
            inner join psycloud_role pr on pr.id = pur.role_id
            left join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
            left join psycloud_task pt on pt.id = ptr2.task_id
        where ptr.is_valid =1 and pui.is_valid = 1
            <if test="recordId !=null and recordId !=0">and pts.record_id = #{recordId}</if>
            <if test="isAbnormal !=null">and pts.is_abnormal = #{isAbnormal}</if>
            <if test="taskId !=null and taskId !=0">and pt.id = #{taskId}</if>
            <if test="scaleId !=null and scaleId != 0">and ps.id = #{scaleId}</if>
            <if test="scaleIds !=null and scaleIds != ''">and ps.id in (${scaleIds})</if>
            <if test="factorId != null and factorId !=''">and pf.id = #{factorId}</if>
            <if test="factorIds !=null and factorIds!=''">and pf.id in (${factorIds})</if>
            <if test="roleId != null and roleId==2">and pur.role_id not in(1,3,4)</if>
            <if test="roleId != null and roleId != 0 and roleId!=2">and pur.role_id = #{roleId}</if>
            <if test="startTime !=null and endTime !=null">and ptr.start_time &gt;= #{startTime} and ptr.start_time &lt;= #{endTime}</if>
            <if test="loginName !=null and loginName !=''">and pu.login_name like CONCAT('%',#{login_name},'%')</if>
            <if test="realName !=null and realName !=''">and pui.real_name like CONCAT('%',#{real_name},'%')</if>
            <if test="sex != null and sex !=''">and pui.sex =#{sex} </if>
            <if test="warningLevel!=null and warningLevel!=0">and pts.warning_level = #{warningLevel}</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            order by ptr.id desc,pf.factor_no
    </select>

    <!--导出测评得分-->
    <select id="getExportTestScore" resultType="java.util.LinkedHashMap" parameterType="cn.psycloud.psyplatform.dto.measuringroom.ExportTestScoreDto">
        select
            /*+ GROUP_OPT_FLAG(1)*/
            pu.login_name as 用户名,
            pui.real_name as 姓名,
            pui.sex as 性别,
            TIMESTAMPDIFF(YEAR,pui.birth,CURDATE()) as 年龄,
            f_GetStructFullName(ps.id) as 所属组织,
            ${sql}
        from psycloud_test_score pts
            inner join psycloud_test_record ptr on ptr.id = pts.record_id
            inner join psycloud_factor pf on pf.id = pts.factor_id
            inner join psycloud_user_info pui on pui.user_id = ptr.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
            inner join psycloud_structs ps on ps.id = pui.struct_id
        where ptr.is_valid = 1
            and pui.is_valid = 1
            and ptr.state in (1,2)
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="taskId !=null and taskId !=0">
                and ptr.id in (
                    select ptr2.record_id
                    from psycloud_task_record ptr2
                    inner join psycloud_task pt on pt.id = ptr2.task_id
                    where  pt.id = #{taskId}
                )
            </if>
            <if test="scaleId != null and scaleId!=0">and ptr.scale_id = #{scaleId}</if>
            <if test="scaleIds !=null and scaleId != ''">and ptr.scale_id in (${scaleIds})</if>
            <if test="factorIds !=null and factorIds!=''">and pf.id in (${factorIds})</if>
        group by pu.login_name,pui.real_name,pui.sex,pui.birth
        order by ptr.id,pf.factor_no
    </select>
    <!--导出测评选项-->
    <select id="getExportTestResult" resultType="java.util.LinkedHashMap" parameterType="cn.psycloud.psyplatform.dto.measuringroom.ExportTestScoreDto">
        select
            /*+ GROUP_OPT_FLAG(1)*/
            pu.login_name as 用户名,
            pui.real_name as 姓名,
            pui.sex as 性别,
            TIMESTAMPDIFF(YEAR,pui.birth,CURDATE()) as 年龄,
            f_GetStructFullName(pui.struct_id) as 所属组织,
            ${sql}
        from psycloud_test_result ptr
            inner join psycloud_test_record ptr2 on ptr2.id = ptr.record_id
            inner join psycloud_user_info pui on pui.user_id = ptr2.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where ptr2.is_valid = 1
            and pui.is_valid = 1
            and (ptr2.state = 1 or ptr2.state = 2)
            and ptr2.scale_id = #{scaleId}
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        <if test="taskId !=null and taskId !=0">
            and ptr2.id in (
            select ptr3.record_id
            from psycloud_task_record ptr3
            inner join psycloud_task pt on pt.id = ptr3.task_id
            where  pt.id = #{taskId}
            )
        </if>
        group by pu.login_name,pui.real_name,pui.sex,pui.birth
        order by ptr2.id,ptr.q_no
    </select>

    <!--导出测评选项得分-->
    <select id="getExportTestResultScore" resultType="java.util.LinkedHashMap" parameterType="cn.psycloud.psyplatform.dto.measuringroom.ExportTestScoreDto">
        select
            /*+ GROUP_OPT_FLAG(1)*/
            pu.login_name as 用户名,
            pui.real_name as 姓名,
            pui.sex as 性别,
            TIMESTAMPDIFF(YEAR,pui.birth,CURDATE()) as 年龄,
            f_GetStructFullName(pui.struct_id) as 所属组织,
            ${sql}
        from psycloud_answer pa
            inner join psycloud_question pq on pq.id = pa.q_id
            inner join psycloud_test_result ptr on ptr.q_no = pq.q_number and pa.a_no = ptr.a_no
            inner join psycloud_test_record ptr2 on ptr2.id =ptr.record_id and pq.scale_id = ptr2.scale_id
            inner join psycloud_user_info pui on pui.user_id = ptr2.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where ptr2.is_valid = 1
            and pui.is_valid = 1
            and (ptr2.state = 1 or ptr2.state = 2)
            and ptr2.scale_id = #{scaleId}
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="taskId !=null and taskId !=0">
                and ptr2.id in (
                select ptr3.record_id
                from psycloud_task_record ptr3
                inner join psycloud_task pt on pt.id = ptr3.task_id
                where  pt.id = #{taskId}
                )
            </if>
        group by pu.login_name,pui.real_name,pui.sex,pui.birth
        order by ptr2.id,ptr.q_no
    </select>

    <!-- 导出调查问卷结果-->
    <select id="getExportSurveyTestResult" resultType="java.util.LinkedHashMap" parameterType="cn.psycloud.psyplatform.dto.measuringroom.ExportTestScoreDto">
        select
            /*+ GROUP_OPT_FLAG(1)*/
            pui.user_id as 用户id,
            pu.login_name as 用户名,
            pui.real_name as 姓名,
            pui.sex as 性别,
            TIMESTAMPDIFF(YEAR,pui.birth,CURDATE()) as 年龄,
            f_GetStructFullName(pui.struct_id) as 所属组织,
            ${sql}
        from psycloud_survey_question psq
            left join psycloud_survey_result psr on psr.q_id = psq.id
            inner join psycloud_survey_record psr2 on psr2.id = psr.record_id and psq.survey_id = psr2.survey_id
            inner join psycloud_user_info pui on pui.user_id = psr2.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where psr2.is_valid = 1
            and psr2.is_done = 1
            and pui.is_valid = 1
            and psr2.survey_id = #{surveyId}
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="taskId !=null and taskId !=0">
                and psr2.id in (
                select ptsr3.survey_record_id
                from psycloud_task_survey_record ptsr3
                    inner join psycloud_task pt on pt.id = ptsr3.task_id
                where  pt.id = #{taskId}
                )
            </if>
            group by pui.user_id,pu.login_name,pui.real_name,pui.sex,pui.birth
            order by pui.user_id,psq.q_number
    </select>

    <select id="getTestScoreListForStat" parameterType="map" resultType="cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto">
        select
            pts.record_id,
            pts.original_score,
            pts.score
        from psycloud_test_score pts
            inner join psycloud_factor pf on pf.id = pts.factor_id
            inner join psycloud_test_record ptr on ptr.id = pts.record_id
        where ptr.is_valid =1 and pf.is_valid=1
        <if test="recordIds !=null and recordIds !=''">and ptr.id in (${recordIds})</if>
        <if test="factorId != null and factorId !=''">and pf.id = #{factorId}</if>
    </select>

    <!-- 更新《心理健康素养》总分 -->
    <update id="updateXlsyTotalScorer" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto">
        update psycloud_test_score set score=  #{score,jdbcType=DOUBLE},original_score= #{originalScore,jdbcType=DOUBLE} where record_id = #{recordId} and factor_id=#{factorId}
    </update>
</mapper>
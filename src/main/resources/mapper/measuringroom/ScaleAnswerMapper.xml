<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.ScaleAnswerDao">
    <!--根据答案id查询答案所属题目id和答案序号-->
    <resultMap id="getByIdMap" type="java.util.HashMap">
        <result column="q_id" property="q_id" jdbcType="INTEGER" />
        <result column="a_no" property="a_no" jdbcType="INTEGER" />
    </resultMap>
    <select id="getById" parameterType="Long" resultMap="getByIdMap">
        select q_id,a_no from psycloud_answer where id = #{aId}
    </select>
    
    <!--根据答案id删除-->
    <delete id="deleteByAid" parameterType="Long">
        delete from psycloud_answer where id = #{aId,jdbcType=BIGINT}
    </delete>
    
    <!--根据题目id删除-->
    <delete id="deleteByQid" parameterType="Integer">
        delete from psycloud_answer where q_id = #{qId,jdbcType=BIGINT}
    </delete>
    
    <!--删除答案后更新答案序号-->
    <update id="updateAno" parameterType="java.util.HashMap">
        update psycloud_answer set a_no = a_no - 1 where a_no &gt; #{aNo,jdbcType=INTEGER} and q_id = #{qId,jdbcType=INTEGER}
    </update>
    
    <!--添加答案-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity">
        insert into psycloud_answer(
            a_content,
            a_score,
            a_no,
            q_id
        )
        values(
            #{aContent,jdbcType=VARCHAR},
            #{aScore,jdbcType=DOUBLE},
            #{aNo,jdbcType=INTEGER},
            #{qId,jdbcType=INTEGER}
        )
    </insert>

    <!-- 修改答案 -->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity">
        update
            psycloud_answer
        set
            a_content = #{aContent,jdbcType=VARCHAR},
            a_score = #{aScore,jdbcType=DOUBLE}
        where q_id = #{qId,jdbcType=INTEGER} and a_no = #{aNo,jdbcType=INTEGER}
    </update>

    <!--获取答案总数-->
    <select id="getAnswerCount" parameterType="Integer">
        select count(id) from psycloud_answer where q_id=#{qId}
    </select>

    <!--获取答案的最大数-->
    <select id="getMaxANo" parameterType="Integer" resultType="Integer">
        select max(pa.a_no)
        from psycloud_answer pa
                 inner join psycloud_question pq on pq.id = pa.q_id
        where pq.scale_id = #{scaleId}
    </select>
    
    <!--根据题目查询答案集合-->
    <select id="getListByQId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity">
        select id,a_content,a_score,a_no,q_id from psycloud_answer where q_id = #{qId}
    </select>
</mapper>
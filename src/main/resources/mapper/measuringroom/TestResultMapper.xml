<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.TestResultDao">
    <!--删除答案-->
    <delete id="deleteResultByRecordId">
        delete from psycloud_test_result where record_id = #{recordId}
    </delete>

    <!--保存选择题答案-->
    <insert id="saveResult" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TestResultEntity">
        insert into psycloud_test_result(
            record_id,
            q_no,
            a_no,
            q_type
        )
        values(
            #{recordId,jdbcType=INTEGER},
            #{qNo,jdbcType=INTEGER},
            #{aNo,jdbcType=VARCHAR},
            #{qType,jdbcType=TINYINT}
            )
    </insert>

    <!--根据测评记录id查询测评结果选项-->
    <resultMap id="testResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.TestResultDto">
        <result column="a_score" property="aScore" jdbcType="DOUBLE" />
        <result column="a_no" property="aNos" jdbcType="VARCHAR" />
        <result column="q_no" property="qNo" jdbcType="INTEGER" />
        <result column="record_id" property="recordId" jdbcType="INTEGER" />
        <result column="q_type" property="qType" jdbcType="TINYINT" />
    </resultMap>
    <select id="getList" parameterType="Integer" resultMap="testResultMap">
        -- 单选题
        select pa.a_score,ptr.a_no,ptr.q_no,pq.scale_id,ptr.record_id,pq.id as q_id,ptr.q_type
        from psycloud_answer pa
            inner join psycloud_question pq on pq.id = pa.q_id
            inner join psycloud_test_result ptr on ptr.q_no = pq.q_number and pa.a_no = ptr.a_no
            inner join psycloud_test_record ptr2 on ptr2.id =ptr.record_id and pq.scale_id = ptr2.scale_id
        where ptr.q_type = 1 and ptr.record_id = #{recordId}
    </select>

    <!--根据条件查询答案记录：Disc性格测试-->
    <select id="getDiscList" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.measuringroom.TestResultDto">
        select
            pa.a_score,
            ptr.a_no,
            ptr.q_no,
            pq.scale_id,
            ptr.record_id,
            pq.id as q_id,
            ptr.q_type,
            pd.factor_id
        from psycloud_answer pa
             inner join psycloud_question pq on pq.id = pa.q_id
             inner join psycloud_test_result ptr on ptr.q_no = pq.q_number and pa.a_no = ptr.a_no
             inner join psycloud_test_record ptr2 on ptr2.id =ptr.record_id and pq.scale_id = ptr2.scale_id
             inner join psycloud_disc pd on pd.answer_id = pa.id
        where ptr.q_type = 1 and ptr.record_id = #{recordId}
    </select>
    <!--根据条件查询答案记录：九型人格测试问卷-->
    <select id="getNineHouseList" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.measuringroom.TestResultDto">
        select
            pa.a_score,
            ptr.a_no,
            ptr.q_no,
            pq.scale_id,
            ptr.record_id,
            pq.id as q_id,
            ptr.q_type,
            pn.factor_id
        from psycloud_answer pa
            inner join psycloud_question pq on pq.id = pa.q_id
            inner join psycloud_test_result ptr on ptr.q_no = pq.q_number and pa.a_no = ptr.a_no
            inner join psycloud_test_record ptr2 on ptr2.id =ptr.record_id and pq.scale_id = ptr2.scale_id
            inner join psycloud_ninehouse pn on pn.answer_id = pa.id
        WHERE ptr.q_type = 1 AND ptr.record_id = #{recordId}
    </select>

    <!--根据条件查询答案记录：父母养育方式问卷(EMBU)-->
    <select id="getEmbuList" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.measuringroom.TestResultDto">
        select
            ptr.record_id,
            ptr.q_no,
            ptr.a_no,
            0 as a_score,
            '' as completion,
            1 as q_type
        from psycloud_test_result ptr
        where ptr.record_id = #{recordId}
        order by q_no asc
    </select>
    
    <select id="getAnswerScore" parameterType="map" resultType="BigDecimal">
        select a_score from psycloud_answer where q_id =#{qId} and a_no = #{aNo}
    </select>
</mapper>
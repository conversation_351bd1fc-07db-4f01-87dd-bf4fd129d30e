<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.CalcDao">
    <!--获取《卡特尔14PF》常模-->
    <resultMap id="get14pfNormMap" type="java.util.LinkedHashMap">
        <result column="agent" property="agent" jdbcType="VARCHAR" />
        <result column="1" property="1" jdbcType="INTEGER" />
        <result column="2" property="2" jdbcType="INTEGER" />
        <result column="3" property="3" jdbcType="INTEGER" />
        <result column="4" property="4" jdbcType="INTEGER" />
        <result column="5" property="5" jdbcType="INTEGER" />
        <result column="6" property="6" jdbcType="INTEGER" />
        <result column="7" property="7" jdbcType="INTEGER" />
        <result column="8" property="8" jdbcType="INTEGER" />
        <result column="9" property="9" jdbcType="INTEGER" />
        <result column="10" property="10" jdbcType="INTEGER" />
    </resultMap>
    <select id="get14pfNorm" parameterType="String" resultMap="get14pfNormMap">
        select agent,1,2,3,4,5,6,7,8,9,10 from psycloud_14pf_norm where lx=#{lx} order by id
    </select>
    <!--获取《卡特尔14PF》结果解释-->
    <resultMap id="get14pfExplainMap" type="java.util.Map">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="interpretation" property="interpretation" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="type_name" property="type_name" jdbcType="VARCHAR" />
        <result column="factor_id" property="factor_id" jdbcType="INTEGER" />
    </resultMap>
    <select id="get14pfExplain" parameterType="String" resultMap="get14pfExplainMap">
        select id,interpretation,type,type_name,factor_id from psycloud_14pf_explain where id in (${ids}) order by id asc
    </select>

    <!--获取《卡特尔16PF》常模-->
    <resultMap id="get16pfNormMap" type="java.util.LinkedHashMap">
        <result column="0" property="0" jdbcType="INTEGER" />
        <result column="1" property="1" jdbcType="INTEGER" />
        <result column="2" property="2" jdbcType="INTEGER" />
        <result column="3" property="3" jdbcType="INTEGER" />
        <result column="4" property="4" jdbcType="INTEGER" />
        <result column="5" property="5" jdbcType="INTEGER" />
        <result column="6" property="6" jdbcType="INTEGER" />
        <result column="7" property="7" jdbcType="INTEGER" />
        <result column="8" property="8" jdbcType="INTEGER" />
        <result column="9" property="9" jdbcType="INTEGER" />
        <result column="10" property="10" jdbcType="INTEGER" />
        <result column="11" property="11" jdbcType="INTEGER" />
        <result column="12" property="12" jdbcType="INTEGER" />
        <result column="13" property="13" jdbcType="INTEGER" />
        <result column="14" property="14" jdbcType="INTEGER" />
        <result column="15" property="15" jdbcType="INTEGER" />
        <result column="16" property="16" jdbcType="INTEGER" />
        <result column="17" property="17" jdbcType="INTEGER" />
        <result column="18" property="18" jdbcType="INTEGER" />
        <result column="19" property="19" jdbcType="INTEGER" />
        <result column="20" property="20" jdbcType="INTEGER" />
        <result column="21" property="21" jdbcType="INTEGER" />
        <result column="22" property="22" jdbcType="INTEGER" />
        <result column="23" property="23" jdbcType="INTEGER" />
        <result column="24" property="24" jdbcType="INTEGER" />
        <result column="25" property="25" jdbcType="INTEGER" />
        <result column="26" property="26" jdbcType="INTEGER" />
        <result column="factor" property="factor" jdbcType="VARCHAR" />
    </resultMap>
    <select id="get16pfNorm" parameterType="Integer" resultMap="get16pfNormMap">
        select 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,factor from psycloud_16pf_norm where sex =#{sex} order by id
    </select>
    <!--获取《卡特尔16PF》结果解释-->
    <resultMap id="get16pfExplainMap" type="java.util.HashMap">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="interpretation" property="interpretation" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="type_name" property="type_name" jdbcType="VARCHAR" />
        <result column="occupation" property="occupation" jdbcType="VARCHAR" />
        <result column="factor_id" property="factor_id" jdbcType="INTEGER" />
    </resultMap>
    <select id="get16pfExplain" parameterType="String" resultMap="get16pfExplainMap">
        select id,interpretation,type,type_name,occupation,factor_id from psycloud_16pf_explain where id IN(${ids}) order by id asc
    </select>

    <!--获取《艾森克人格问卷少年式》结果解释-->
    <resultMap id="getEPQCExplainMap" type="java.util.LinkedHashMap">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="interpretation" property="interpretation" jdbcType="VARCHAR" />
        <result column="advice" property="advice" jdbcType="VARCHAR" />
        <result column="grade" property="grade" jdbcType="VARCHAR" />
        <result column="dimension" property="dimension" jdbcType="VARCHAR" />
        <result column="factor_id" property="factor_id" jdbcType="INTEGER" />
    </resultMap>
    <select id="getEPQCExplain" parameterType="String" resultMap="getEPQCExplainMap">
        select id,interpretation,advice,grade,dimension,factor_id from psycloud_epqc_ex where id in(${ids}) order by id asc
    </select>

    <!--获取《scl-90量表》常模-->
    <select id="getSclNorm" resultType="java.util.LinkedHashMap">
        select M,SD from psycloud_scl90_norm where id &lt;= 10 or id = 13 order by id asc
    </select>

    <!--获取mbti结果解释-->
    <resultMap id="getMBTIExplainMap" type="String">
        <result column="interpretation" property="interpretation" jdbcType="LONGVARCHAR" />
    </resultMap>
    <select id="getMBTIExplain" parameterType="String" resultMap="getMBTIExplainMap">
        select interpretation from psycloud_mbti_explain WHERE type = #{typeName}
    </select>

    <!--获取mbti因子得分-->
    <select id="getQidsByRecordIdAndFactorName" parameterType="map" resultType="String">
        select qids
        from psycloud_factor pf
            inner join psycloud_test_record ptr on ptr.scale_id = pf.scale_Id
        where pf.factor_name = #{factorName} and ptr.id =  #{recordId}
    </select>

    <resultMap id="getTestResultForMBTIMap" type="java.util.LinkedHashMap">
        <result column="record_id" property="record_id" jdbcType="INTEGER" />
        <result column="q_no" property="q_no" jdbcType="INTEGER" />
        <result column="a_no" property="a_no" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getTestResultForMBTI" parameterType="map" resultMap="getTestResultForMBTIMap">
        select
            ptr.record_id,
            ptr.q_no,
            ptr.a_no
        from psycloud_test_result ptr
        where ptr.record_id = #{recordId}
          and ptr.q_no in (${qIds})
    </select>

    <!--获取《MMPI》分量表-->
    <resultMap id="getMmpiLBMap" type="java.util.LinkedHashMap">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="lb" property="lb" jdbcType="VARCHAR" />
        <result column="lb_tf" property="lb_tf" jdbcType="TINYINT" />
        <result column="question" property="question" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getMmpiLB" resultMap="getMmpiLBMap">
        select id,lb,lb_tf,question from psycloud_mmpi_lb
    </select>
    <!--获取《MMPI》常模-->
    <resultMap id="getMmpiNormMap" type="java.util.LinkedHashMap">
        <result column="Q" property="Q" jdbcType="DOUBLE" />
        <result column="L" property="L" jdbcType="DOUBLE" />
        <result column="F" property="F" jdbcType="DOUBLE" />
        <result column="K" property="K" jdbcType="DOUBLE" />
        <result column="Hs" property="Hs" jdbcType="DOUBLE" />
        <result column="D" property="D" jdbcType="DOUBLE" />
        <result column="Hy" property="Hy" jdbcType="DOUBLE" />
        <result column="Pd" property="Pd" jdbcType="DOUBLE" />
        <result column="Mf" property="Mf" jdbcType="DOUBLE" />
        <result column="Pa" property="Pa" jdbcType="DOUBLE" />
        <result column="Pt" property="Pt" jdbcType="DOUBLE" />
        <result column="Sc" property="Sc" jdbcType="DOUBLE" />
        <result column="Ma" property="Ma" jdbcType="DOUBLE" />
        <result column="Si" property="Si" jdbcType="DOUBLE" />
        <result column="MAS" property="MAS" jdbcType="DOUBLE" />
        <result column="Dy" property="Dy" jdbcType="DOUBLE" />
        <result column="Do" property="Do" jdbcType="DOUBLE" />
        <result column="Re" property="Re" jdbcType="DOUBLE" />
        <result column="Cn" property="Cn" jdbcType="DOUBLE" />
    </resultMap>
    <select id="getMmpiNorm" resultMap="getMmpiNormMap" parameterType="Integer">
        select Q,L,F,K,Hs,D,Hy,Pd,Mf,Pa,Pt,Sc,Ma,Si,MAS,Dy,Do,Re,Cn
        from psycloud_mmpi_norm
        <where>
            <choose>
                <when test="id eq 0">id &lt; 3</when>
                <otherwise>id &gt; 2</otherwise>
            </choose>
        </where>
    </select>
    <!--获取《MMPI》结果解释-->
    <resultMap id="getMmpiExplainMap" type="java.util.LinkedHashMap">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="interpretation" property="interpretation" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="type_name" property="type_name" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getMmpiExplain" resultMap="getMmpiExplainMap">
        select id,interpretation,type,type_name FROM psycloud_mmpi_explain
    </select>

    <!--获取《RCCP通用职业匹配测试量表》结果解释-->
    <resultMap id="getRccpExplainMap" type="java.util.LinkedHashMap">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="type_name" property="type_name" jdbcType="VARCHAR" />
        <result column="work" property="work" jdbcType="VARCHAR" />
        <result column="zhiye" property="zhiye" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getRccpExplain" resultMap="getRccpExplainMap">
        select id,type,type_name,work,zhiye from psycloud_rccp_explain order by id
    </select>

    <!--获取《瑞文标准推理》常模-->
    <resultMap id="getSpmNormMap" type="java.util.LinkedHashMap">
        <result column="age" property="age" jdbcType="INTEGER" />
        <result column="level5" property="level5" jdbcType="INTEGER" />
        <result column="level10" property="level10" jdbcType="INTEGER" />
        <result column="level25" property="level25" jdbcType="INTEGER" />
        <result column="level50" property="level50" jdbcType="INTEGER" />
        <result column="level75" property="level75" jdbcType="INTEGER" />
        <result column="level90" property="level90" jdbcType="INTEGER" />
        <result column="level95" property="level95" jdbcType="INTEGER" />
    </resultMap>
    <select id="getSpmNorm" parameterType="Integer" resultMap="getSpmNormMap">
        select age,level5,level10,level25,level50,level75,level90,level95 from psycloud_spm_norm where age = #{age}
    </select>

    <!--获取《斯特劳力气质类型》结果解释-->
    <resultMap id="getStiExplainMap" type="java.util.LinkedHashMap">
        <result column="interpretation" property="interpretation" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getStiExplain" parameterType="Integer" resultMap="getStiExplainMap">
        select interpretation,type from psycloud_sti_explain where id= #{id}
    </select>

    <!--获取《学习焦虑量表》常模-->
    <select id="getXxjlNorm" parameterType="Integer" resultType="java.util.LinkedHashMap">
        select 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15 from psycloud_xxjl_norm where age=#{age}
    </select>

    <!--获取《职业人格》结果解释-->
    <resultMap id="getZylxExplainMap" type="java.util.LinkedHashMap">
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="type_name" property="type_name" jdbcType="VARCHAR" />
        <result column="te_dian" property="te_dian" jdbcType="VARCHAR" />
        <result column="xing_ge" property="xing_ge" jdbcType="VARCHAR" />
        <result column="zhi_ye" property="zhi_ye" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getZylxExplain" resultMap="getZylxExplainMap">
        select type,type_name,te_dian,xing_ge,zhi_ye from psycloud_zylx_ex order by id
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorDao">
    <resultMap id="factorResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="factor_no" property="factorNo" jdbcType="TINYINT" />
        <result column="factor_type" property="factorType" jdbcType="TINYINT" />
        <result column="scale_id" property="scaleId" jdbcType="INTEGER" />
        <result column="factor_name" property="factorName" jdbcType="VARCHAR" />
        <result column="factor_en" property="factorEn" jdbcType="VARCHAR" />
        <result column="factor_short_name" property="factorShortName" jdbcType="VARCHAR" />
        <result column="formula_id" property="formulaId" jdbcType="TINYINT" />
        <result column="qids" property="qIds" jdbcType="VARCHAR" />
        <result column="factor_ids" property="factorIds" jdbcType="VARCHAR" />
        <result column="compute" property="compute" jdbcType="VARCHAR" />
        <result column="min_score" property="minScore" jdbcType="DOUBLE" />
        <result column="max_score" property="maxScore" jdbcType="DOUBLE" />
        <result column="avg_score" property="avgScore" jdbcType="DOUBLE" />
        <result column="standard_score" property="standardScore" jdbcType="DOUBLE" />
        <result column="is_lie" property="isLie" jdbcType="TINYINT" />
        <result column="is_valid" property="isValid" jdbcType="TINYINT" />
        <collection property="abnormalConditions" column="id" select="cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorAbnormalConditionDao.getListByFactorId" />
    </resultMap>
    <!--根据量表id查询因子集合-->
    <select id="getFactorsByScaleId" parameterType="Integer" resultMap="factorResultMap">
        select
            id,
            factor_no,
            factor_type,
            scale_id,
            factor_name,
            factor_en,
            factor_short_name,
            formula_id,
            qids,
            factor_ids,
            compute,
            min_score,
            max_score,
            avg_score,
            standard_score,
            is_lie,
            is_valid
        from psycloud_factor
        where is_valid = 1 and scale_id = #{scaleId}
        order by factor_no
    </select>
    <!--获取select列表的因子集合-->
    <select id="getListForSelect" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorEntity" parameterType="cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto">
        select id,factor_name
        from psycloud_factor
        where is_valid =1
            <if test="scaleId!=null and scaleId!=0">and scale_id=${scaleId}</if>
            <if test="scaleIds!=null and scaleIds!=''">and scale_id in (${scaleIds})</if>
            <if test="factorType!=null and factorType!=0">and factor_type = #{factorType}</if>
    </select>

    <resultMap id="getListForExportMap" type="java.util.LinkedHashMap">
        <result column="factor_name" property="factor_name" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getListForExport" resultMap="getListForExportMap" parameterType="cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto">
        select factor_name
        from psycloud_factor
        where is_valid=1
            <if test="scaleId!=null and scaleId!=0">and scale_id=#{scaleId}</if>
            <if test="scaleIds!=null and scaleIds!=''">and scale_id in (${scaleIds})</if>
            <if test="factorType!=null and factorType!=0">and factor_type = #{factorType}</if>
        order by factor_no
    </select>
    <!--添加因子-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorEntity">
        insert into psycloud_factor(
            factor_no,
            factor_type,
            scale_id,
            factor_name,
            factor_en,
            factor_short_name,
            formula_id,
            qids,
            factor_ids,
            compute,
            min_score,
            max_score,
            avg_score,
            standard_score,
            is_lie,
            is_valid
        )
        values(
            #{factorNo,jdbcType=TINYINT},
            #{factorType,jdbcType=TINYINT},
            #{scaleId,jdbcType=INTEGER},
            #{factorName,jdbcType=VARCHAR},
            #{factorEn,jdbcType=VARCHAR},
            #{factorShortName,jdbcType=VARCHAR},
            #{formulaId,jdbcType=TINYINT},
            #{qIds,jdbcType=VARCHAR},
            #{factorIds,jdbcType=VARCHAR},
            #{compute,jdbcType=VARCHAR},
            #{minScore,jdbcType=DOUBLE},
            #{maxScore,jdbcType=DOUBLE},
            #{avgScore,jdbcType=DOUBLE},
            #{standardScore,jdbcType=DOUBLE},
            #{isLie,jdbcType=TINYINT},
             1
        )
    </insert>

    <!--修改因子-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorEntity">
        update
            psycloud_factor
        set
            factor_type = #{factorType,jdbcType=TINYINT},
            factor_name = #{factorName,jdbcType=VARCHAR},
            factor_en = #{factorEn,jdbcType=VARCHAR},
            factor_short_name = #{factorShortName,jdbcType=VARCHAR},
            formula_id = #{formulaId,jdbcType=TINYINT},
            qids =  #{qIds,jdbcType=VARCHAR},
            factor_ids = #{factorIds,jdbcType=VARCHAR},
            compute= #{compute,jdbcType=VARCHAR},
            min_score = #{minScore,jdbcType=DOUBLE},
            max_score = #{maxScore,jdbcType=DOUBLE},
            avg_score = #{avgScore,jdbcType=DOUBLE},
            standard_score = #{standardScore,jdbcType=DOUBLE},
            is_lie = #{isLie,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--获取因子总数-->
    <select id="getFactorCount" parameterType="Integer" resultType="Integer">
        select count(id) from psycloud_factor where is_valid = 1 and scale_id = #{scaleId}
    </select>

    <!--删除因子-->
    <update id="delete" parameterType="Integer">
        update psycloud_factor set is_valid = 0 where id = #{id}
    </update>

    <!--根据因子id查询因子序号和所属量表id-->
    <resultMap id="getByIdMap" type="java.util.HashMap">
        <result column="scale_id" property="scale_id" jdbcType="INTEGER" />
        <result column="factor_no" property="factor_no" jdbcType="INTEGER" />
    </resultMap>
    <select id="getById" parameterType="Integer" resultMap="getByIdMap">
        select scale_id,factor_no from psycloud_factor where id = #{factorId}
    </select>

    <!--根据因子id查询因子名称-->
    <select id="getFactorNameById" parameterType="Integer" resultType="String">
        select factor_name from psycloud_factor where id = #{factorId}
    </select>

    <!--删除因子后更新因子序号-->
    <update id="updateFactorNo" parameterType="map">
        update psycloud_factor set factor_no = factor_no - 1 where factor_no &gt; #{factorNo,jdbcType=TINYINT} and scale_id = #{scaleId,jdbcType=INTEGER}
    </update>

    <!-- 上移 Start  -->
    <!-- 上移其它   -->
    <update id="moveUpOther" parameterType="map">
        update psycloud_factor set factor_no =#{factorNo,jdbcType=TINYINT} where factor_no=  #{factorNo,jdbcType=TINYINT}- 1 and scale_id = #{scaleId,jdbcType=INTEGER}
    </update>
    <!-- 上移自己   -->
    <update id="moveUpSelf" parameterType="map">
        update psycloud_factor set factor_no= #{factorNo,jdbcType=TINYINT} - 1 WHERE id = #{factorId,jdbcType=INTEGER}
    </update>
    <!-- 上移 End   -->
    <!--  下移 Start  -->
    <!-- 下移其它    -->
    <update id="moveDownOther" parameterType="map">
        update psycloud_factor set factor_no =#{factorNo,jdbcType=TINYINT} where factor_no = #{factorNo,jdbcType=TINYINT}+1 and scale_id =#{scaleId,jdbcType=INTEGER}
    </update>
    <!-- 下移自己   -->
    <update id="moveDownSelf" parameterType="map">
        update psycloud_factor set factor_no =#{factorNo,jdbcType=TINYINT}+1 where id = #{factorId,jdbcType=INTEGER}
    </update>
    <!--  下移 End  -->
</mapper>
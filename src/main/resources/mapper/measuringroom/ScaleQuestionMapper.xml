<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao">
    <!--删除题目-->
    <update id="delete" parameterType="Integer">
        update psycloud_question set is_valid = 0 where id = #{qId,jdbcType=INTEGER}
    </update>
    <!--根据题目id查询题目所属量表id和题目序号-->
    <resultMap id="getByIdResultMap" type="java.util.HashMap">
        <result column="scale_id" property="scale_id" jdbcType="INTEGER" />
        <result column="q_number" property="q_number" jdbcType="INTEGER" />
    </resultMap>
    <select id="getById" parameterType="Integer" resultMap="getByIdResultMap">
        select scale_id,q_number from psycloud_question where is_valid =1 and id = #{qId}
    </select>
    <!--删除题目后更新题目序号-->
    <update id="updateQno" parameterType="java.util.HashMap">
        update psycloud_question set q_number = q_number -1 where is_valid = 1 and q_number &gt; #{qNo,jdbcType=INTEGER} and scale_id =#{scaleId,jdbcType=INTEGER}
    </update>

    <!--获取题目数 -->
    <select id="getQuestionCount" parameterType="Integer" resultType="Integer">
        select count(id) from psycloud_question where is_valid=1 and scale_id=#{scaleId}
    </select>
    
    <!--添加题目-->
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_question(
            q_content,
            q_number,
            q_type,
            scale_id,
            is_valid
        )
        values(
            #{qContent,jdbcType=VARCHAR},
            #{qNumber,jdbcType=INTEGER},
            #{qType,jdbcType=TINYINT},
            #{scaleId,jdbcType=INTEGER},
            1
        )
    </insert>
    <!--修改题目-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity">
        update
            psycloud_question
        set
            q_content = #{qContent,jdbcType=VARCHAR},
            q_type = #{qType,jdbcType=TINYINT}
        where
            id =#{id,jdbcType=INTEGER}
    </update>

    <!-- 上移 Start  -->
    <!-- 上移其它   -->
    <update id="moveUpOther" parameterType="map">
        update psycloud_question set q_number =#{qNo,jdbcType=INTEGER} where q_number=  #{qNo,jdbcType=INTEGER}- 1 and scale_id = #{scaleId,jdbcType=INTEGER}

    </update>
    <!-- 上移自己   -->
    <update id="moveUpSelf" parameterType="map">
        update psycloud_question set q_number= #{qNo,jdbcType=INTEGER} - 1 WHERE id = #{id,jdbcType=INTEGER}
    </update>
    <!-- 上移 End   -->

    <!--  下移 Start  -->
    <!-- 下移其它    -->
    <update id="moveDownOther" parameterType="map">
        update psycloud_question set q_number =#{qNo,jdbcType=INTEGER} where q_number = #{qNo,jdbcType=INTEGER}+1 and scale_id =#{scaleId,jdbcType=INTEGER}
    </update>
    <!-- 下移自己   -->
    <update id="moveDownSelf" parameterType="map">
        update psycloud_question set q_number =#{qNo,jdbcType=INTEGER}+1 where id = #{id,jdbcType=INTEGER}
    </update>
    <!--  下移 End  -->

    <!--获取题目集合 Duallist-->
    <select id="getListByScaleIdForDuallist" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity">
        select q_number from psycloud_question where is_valid =1 and scale_id = #{scaleId} order by q_number
    </select>

    <resultMap id="questionResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.ScaleQuestionDto">
        <collection property="listAnswers" select="cn.psycloud.psyplatform.dao.measuringroom.ScaleAnswerDao.getListByQId" column="id" />
    </resultMap>
    <select id="getListByScaleId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity">
        select id,q_number,q_content,q_type from psycloud_question where is_valid =1 and scale_id=#{scaleId} order by q_number
    </select>

    <select id="getListByScaleIdForTestIng" parameterType="Integer" resultMap="questionResultMap">
        select id,q_number,q_content,q_type from psycloud_question where is_valid =1 and scale_id=#{scaleId} order by q_number
    </select>

    <!--导出测评选项数据时查询选项题目-->
    <resultMap id="getListForExportMap" type="java.util.LinkedHashMap">
        <result column="q_number" property="q_number" jdbcType="INTEGER" />
    </resultMap>
    <select id="getListForExport" resultMap="getListForExportMap" parameterType="Integer">
        select q_number from psycloud_question where is_valid=1 and scale_id = #{scaleId} order by q_number
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.ScaleTypeDao">
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.ScaleTypeDto" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleTypeEntity">
        select
            id,
            scale_type_name as scaleTypeName,
            sort
        from psycloud_scale_type
        where is_valid=1
            <if test="scaleTypeName!=null and scaleTypeName!= ''">and scale_type_name like CONCAT('%',#{scaleTypeName},'%')</if>
        order by sort desc
    </select>
    
    <resultMap id="typeAndscalesResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.ScaleTypeDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="scale_type_name" property="scaleTypeName" jdbcType="VARCHAR" />
        <result column="sort" property="sort" jdbcType="INTEGER" />
        <result column="is_valid" property="isValid" jdbcType="TINYINT" />
    </resultMap>
    <select id="getAllScalesByType" resultMap="typeAndscalesResultMap">
        select
            id,
            scale_type_name,
            sort,
            is_valid
        from psycloud_scale_type
        where is_valid = 1
        order by sort desc
    </select>
    
    <update id="delete" parameterType="Integer">
        update psycloud_scale_type set is_valid = 0 where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleTypeEntity">
        insert into psycloud_scale_type(
            scale_type_name,
            sort,
            is_valid
        ) 
        values(
               #{scaleTypeName,jdbcType=VARCHAR},
               #{sort,jdbcType=INTEGER},
               1
        )
    </insert>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleTypeEntity">
        update psycloud_scale_type
        set
            scale_type_name = #{scaleTypeName},
            sort = #{sort}
        where id = #{id}
    </update>
</mapper>
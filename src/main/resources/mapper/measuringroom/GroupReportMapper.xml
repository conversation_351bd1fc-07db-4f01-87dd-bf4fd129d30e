<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.GroupReportDao">

    <!-- 获取量表信息 -->
    <select id="getScaleInfo" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$ScaleInfoDto">
        select
            id as scaleId,
            scale_name as scaleName,
            scale_intro as scaleIntro,
            scale_guide as scaleGuide,
            need_time as needTime
        from psycloud_scale
        where id = #{scaleId}
          and is_valid = 1
    </select>

    <!-- 获取测评总体情况统计 -->
    <select id="getOverallSituation" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$OverallSituationDto">
        select
            count(*) as totalCount,
            sum(case when tr.state = 1 then 1 else 0 end) as completedCount,
            sum(case when tr.state = 0 then 1 else 0 end) as uncompletedCount
        from psycloud_test_record tr
            inner join psycloud_task_record ttr on tr.id = ttr.record_id
            inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
            inner join psycloud_user u on tr.user_id = u.user_id
            inner join psycloud_user_info pui on pui.user_id = u.user_id
        where tr.is_valid = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
    </select>

    <!-- 按部门统计完成情况 -->
    <select id="getDepartmentCompletionStats" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$CompletionStatDto">
        select
            s.struct_name as categoryName,
            count(*) as totalCount,
            sum(case when tr.state = 1 then 1 else 0 end) as completedCount,
            sum(case when tr.state = 0 then 1 else 0 end) as uncompletedCount,
            round(sum(case when tr.state = 1 then 1 else 0 end) * 100.0 / count(*), 2) as completionRate
        from psycloud_test_record tr
            inner join psycloud_task_record ttr on tr.id = ttr.record_id
            inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
            inner join psycloud_user u on tr.user_id = u.user_id
            inner join psycloud_user_info pui on pui.user_id = u.user_id
            inner join psycloud_structs s on pui.struct_id = s.id
        where tr.is_valid = 1
          and s.is_valid = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
        group by s.id, s.struct_name
        order by s.struct_name
    </select>

    <!-- 按性别统计完成情况 -->
    <select id="getGenderCompletionStats" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$CompletionStatDto">
        select
            coalesce(pui.sex, '未知') as categoryName,
            count(*) as totalCount,
            sum(case when tr.state = 1 then 1 else 0 end) as completedCount,
            sum(case when tr.state = 0 then 1 else 0 end) as uncompletedCount,
            round(sum(case when tr.state = 1 then 1 else 0 end) * 100.0 / count(*), 2) as completionRate
        from psycloud_test_record tr
            inner join psycloud_task_record ttr on tr.id = ttr.record_id
            inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
            inner join psycloud_user u on tr.user_id = u.user_id
            inner join psycloud_user_info pui on pui.user_id = u.user_id
        where tr.is_valid = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
        group by coalesce(pui.sex, '未知')
        order by categoryName
    </select>

    <!-- 按年龄段统计完成情况 -->
    <select id="getAgeCompletionStats" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$CompletionStatDto">
        select
            case
                when timestampdiff(year, pui.birth, curdate()) &lt; 25 then '25岁以下'
                when timestampdiff(year, pui.birth, curdate()) between 25 and 35 then '25-35岁'
                when timestampdiff(year, pui.birth, curdate()) between 36 and 45 then '36-45岁'
                when timestampdiff(year, pui.birth, curdate()) between 46 and 55 then '46-55岁'
                when timestampdiff(year, pui.birth, curdate()) > 55 then '55岁以上'
                else '未知'
            end as categoryName,
            count(*) as totalCount,
            sum(case when tr.state = 1 then 1 else 0 end) as completedCount,
            sum(case when tr.state = 0 then 1 else 0 end) as uncompletedCount,
            round(sum(case when tr.state = 1 then 1 else 0 end) * 100.0 / count(*), 2) as completionRate
        from psycloud_test_record tr
            inner join psycloud_task_record ttr on tr.id = ttr.record_id
            inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
            inner join psycloud_user u on tr.user_id = u.user_id
            inner join psycloud_user_info pui on pui.user_id = u.user_id
        where tr.is_valid = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
        group by case
                when timestampdiff(year, pui.birth, curdate()) &lt; 25 then '25岁以下'
                when timestampdiff(year, pui.birth, curdate()) between 25 and 35 then '25-35岁'
                when timestampdiff(year, pui.birth, curdate()) between 36 and 45 then '36-45岁'
                when timestampdiff(year, pui.birth, curdate()) between 46 and 55 then '46-55岁'
                when timestampdiff(year, pui.birth, curdate()) > 55 then '55岁以上'
                else '未知'
            end
        order by case categoryName
                when '25岁以下' then 1
                when '25-35岁' then 2
                when '36-45岁' then 3
                when '46-55岁' then 4
                when '55岁以上' then 5
                else 6
            end
    </select>

    <!-- 按组织统计预警等级分布 -->
    <select id="getOrganizationWarningStats" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$OrganizationWarningStatDto">
        select
            s.id as structId,
            s.struct_name as structName,
            count(distinct tr.user_id) as totalCount,
            sum(case when ts.warning_level = 1 then 1 else 0 end) as greenCount,
            sum(case when ts.warning_level = 2 then 1 else 0 end) as blueCount,
            sum(case when ts.warning_level = 3 then 1 else 0 end) as yellowCount,
            sum(case when ts.warning_level = 4 then 1 else 0 end) as orangeCount,
            sum(case when ts.warning_level = 5 then 1 else 0 end) as redCount,
            round(sum(case when ts.warning_level = 1 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as greenRate,
            round(sum(case when ts.warning_level = 2 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as blueRate,
            round(sum(case when ts.warning_level = 3 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as yellowRate,
            round(sum(case when ts.warning_level = 4 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as orangeRate,
            round(sum(case when ts.warning_level = 5 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as redRate
        from psycloud_test_record tr
        inner join psycloud_task_record ttr on tr.id = ttr.record_id
        inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
        inner join psycloud_user u on tr.user_id = u.user_id
        inner join psycloud_user_info pui on pui.user_id = u.user_id
        inner join psycloud_structs s on pui.struct_id = s.id
        left join psycloud_test_score ts on tr.id = ts.record_id
        where tr.is_valid = 1
          and s.is_valid = 1
          and tr.state = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
        group by s.id, s.struct_name
        order by s.struct_name
    </select>

    <!-- 按性别统计预警等级分布 -->
    <select id="getGenderWarningStats" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$GenderWarningStatDto">
        select
            coalesce(pui.sex, '未知') as gender,
            count(distinct tr.user_id) as totalCount,
            sum(case when ts.warning_level = 1 then 1 else 0 end) as greenCount,
            sum(case when ts.warning_level = 2 then 1 else 0 end) as blueCount,
            sum(case when ts.warning_level = 3 then 1 else 0 end) as yellowCount,
            sum(case when ts.warning_level = 4 then 1 else 0 end) as orangeCount,
            sum(case when ts.warning_level = 5 then 1 else 0 end) as redCount,
            round(sum(case when ts.warning_level = 1 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as greenRate,
            round(sum(case when ts.warning_level = 2 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as blueRate,
            round(sum(case when ts.warning_level = 3 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as yellowRate,
            round(sum(case when ts.warning_level = 4 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as orangeRate,
            round(sum(case when ts.warning_level = 5 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as redRate
        from psycloud_test_record tr
        inner join psycloud_task_record ttr on tr.id = ttr.record_id
        inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
        inner join psycloud_user u on tr.user_id = u.user_id
        inner join psycloud_user_info pui on pui.user_id = u.user_id
        left join psycloud_test_score ts on tr.id = ts.record_id
        where tr.is_valid = 1
          and tr.state = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
        group by coalesce(pui.sex, '未知')
        order by gender
    </select>

    <!-- 按年龄段统计预警等级分布 -->
    <select id="getAgeWarningStats" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$AgeWarningStatDto">
        select
            case
                when timestampdiff(year, pui.birth, curdate()) &lt; 25 then '25岁以下'
                when timestampdiff(year, pui.birth, curdate()) between 25 and 35 then '25-35岁'
                when timestampdiff(year, pui.birth, curdate()) between 36 and 45 then '36-45岁'
                when timestampdiff(year, pui.birth, curdate()) between 46 and 55 then '46-55岁'
                when timestampdiff(year, pui.birth, curdate()) > 55 then '55岁以上'
                else '未知'
            end as ageGroup,
            count(distinct tr.user_id) as totalCount,
            sum(case when ts.warning_level = 1 then 1 else 0 end) as greenCount,
            sum(case when ts.warning_level = 2 then 1 else 0 end) as blueCount,
            sum(case when ts.warning_level = 3 then 1 else 0 end) as yellowCount,
            sum(case when ts.warning_level = 4 then 1 else 0 end) as orangeCount,
            sum(case when ts.warning_level = 5 then 1 else 0 end) as redCount,
            round(sum(case when ts.warning_level = 1 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as greenRate,
            round(sum(case when ts.warning_level = 2 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as blueRate,
            round(sum(case when ts.warning_level = 3 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as yellowRate,
            round(sum(case when ts.warning_level = 4 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as orangeRate,
            round(sum(case when ts.warning_level = 5 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as redRate
        from psycloud_test_record tr
        inner join psycloud_task_record ttr on tr.id = ttr.record_id
        inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
        inner join psycloud_user u on tr.user_id = u.user_id
        inner join psycloud_user_info pui on pui.user_id = u.user_id
        left join psycloud_test_score ts on tr.id = ts.record_id
        where tr.is_valid = 1
          and tr.state = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
        group by case
                when timestampdiff(year, pui.birth, curdate()) &lt; 25 then '25岁以下'
                when timestampdiff(year, pui.birth, curdate()) between 25 and 35 then '25-35岁'
                when timestampdiff(year, pui.birth, curdate()) between 36 and 45 then '36-45岁'
                when timestampdiff(year, pui.birth, curdate()) between 46 and 55 then '46-55岁'
                when timestampdiff(year, pui.birth, curdate()) > 55 then '55岁以上'
                else '未知'
            end
        order by case ageGroup
                when '25岁以下' then 1
                when '25-35岁' then 2
                when '36-45岁' then 3
                when '46-55岁' then 4
                when '55岁以上' then 5
                else 6
            end
    </select>

    <!-- 获取各组织红码橙码统计 -->
    <select id="getCriticalWarningStats" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$CriticalWarningStatDto">
        select
            s.id as structId,
            s.struct_name as structName,
            count(distinct tr.user_id) as totalCount,
            sum(case when ts.warning_level = 5 then 1 else 0 end) as redCount,
            sum(case when ts.warning_level = 4 then 1 else 0 end) as orangeCount,
            sum(case when ts.warning_level in (4, 5) then 1 else 0 end) as criticalCount,
            round(sum(case when ts.warning_level = 5 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as redRate,
            round(sum(case when ts.warning_level = 4 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as orangeRate,
            round(sum(case when ts.warning_level in (4, 5) then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as criticalRate
        from psycloud_test_record tr
        inner join psycloud_task_record ttr on tr.id = ttr.record_id
        inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
        inner join psycloud_user u on tr.user_id = u.user_id
        inner join psycloud_user_info pui on pui.user_id = u.user_id
        inner join psycloud_structs s on pui.struct_id = s.id
        left join psycloud_test_score ts on tr.id = ts.record_id
        where tr.is_valid = 1
          and s.is_valid = 1
          and tr.state = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
        group by s.id, s.struct_name
        having sum(case when ts.warning_level in (4, 5) then 1 else 0 end) > 0
        order by criticalRate desc, s.struct_name
    </select>

    <!-- 按因子统计预警等级分布 -->
    <select id="getFactorWarningStats" parameterType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto" 
            resultType="cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto$FactorWarningStatDto">
        select
            pf.id as factorId,
            pf.factor_name as factorName,
            count(distinct tr.user_id) as totalCount,
            sum(case when ts.warning_level = 1 then 1 else 0 end) as greenCount,
            sum(case when ts.warning_level = 2 then 1 else 0 end) as blueCount,
            sum(case when ts.warning_level = 3 then 1 else 0 end) as yellowCount,
            sum(case when ts.warning_level = 4 then 1 else 0 end) as orangeCount,
            sum(case when ts.warning_level = 5 then 1 else 0 end) as redCount,
            round(sum(case when ts.warning_level = 1 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as greenRate,
            round(sum(case when ts.warning_level = 2 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as blueRate,
            round(sum(case when ts.warning_level = 3 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as yellowRate,
            round(sum(case when ts.warning_level = 4 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as orangeRate,
            round(sum(case when ts.warning_level = 5 then 1 else 0 end) * 100.0 / count(distinct tr.user_id), 2) as redRate
        from psycloud_test_record tr
        inner join psycloud_task_record ttr on tr.id = ttr.record_id
        inner join psycloud_task_user ttu on ttr.task_id = ttu.task_id and tr.user_id = ttu.user_id
        inner join psycloud_user u on tr.user_id = u.user_id
        inner join psycloud_user_info pui on pui.user_id = u.user_id
        inner join psycloud_test_score ts on tr.id = ts.record_id
        inner join psycloud_factor pf on ts.factor_id = pf.id
        where tr.is_valid = 1
          and tr.state = 1
          and pf.is_valid = 1
        <if test="taskId != null">
            and ttr.task_id = #{taskId}
        </if>
        <if test="scaleId != null">
            and tr.scale_id = #{scaleId}
        </if>
        <if test="childStructs != null and childStructs.size() > 0">
            and pui.struct_id in
            <foreach item="childStructId" collection="childStructs" open="(" separator="," close=")">
                #{childStructId}
            </foreach>
        </if>
        <if test="startTimeBegin != null">
            and tr.start_time >= #{startTimeBegin}
        </if>
        <if test="startTimeEnd != null">
            and tr.start_time &lt;= #{startTimeEnd}
        </if>
        group by pf.id, pf.factor_name
        order by pf.factor_no, pf.factor_name
    </select>

</mapper> 
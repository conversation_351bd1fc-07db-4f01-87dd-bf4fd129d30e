<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorAbnormalConditionDao">
    <!--删除-->
    <delete id="delete" parameterType="Integer">
        delete from psycloud_factor_abnormal where id = #{id}
    </delete>

    <!--添加-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity">
        insert into psycloud_factor_abnormal(
            factor_id,
            age_condition,
            age_value,
            sex_condition,
            score_condition,
            score_value
        )
        values(
            #{factorId,jdbcType=INTEGER},
            #{ageCondition,jdbcType=INTEGER},
            #{ageValue,jdbcType=INTEGER},
            #{sexCondition,jdbcType=CHAR},
            #{scoreCondition,jdbcType=INTEGER},
            #{scoreValue,jdbcType=DOUBLE}
        )
    </insert>

    <!--<resultMap id="acResultMap" type="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity">
        <result column="sex_condition" property="sexCondition" jdbcType="CHAR" typeHandler="cn.psycloud.psyplatform.handlers.CustomStringTypeHandler"/>
    </resultMap>-->
    <!--获取异常条件集合-->
    <select id="getListByFactorId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity">
        select
            id,
            factor_id,
            age_condition,
            age_value,
            sex_condition,
            score_condition,
            score_value
        from psycloud_factor_abnormal
        where factor_id=#{factorId}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorExplainDao">
    <!--删除因子解释-->
    <delete id="delete" parameterType="Integer">
        delete from psycloud_factor_explain where id = #{id}
    </delete>

    <!--添加因子解释-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity">
        insert into psycloud_factor_explain(
            factor_id,
            start_value,
            end_value,
            warning_level,
            interpretation
        )
        values(
            #{factorId,jdbcType=INTEGER},
            #{startValue,jdbcType=DOUBLE},
            #{endValue,jdbcType=DOUBLE},
            #{warningLevel,jdbcType=TINYINT},
            #{interpretation,jdbcType=VARCHAR}
        )
    </insert>

    <!--修改因子解释-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity">
        update psycloud_factor_explain
        set
            start_value = #{startValue,jdbcType=DOUBLE},
            end_value = #{endValue,jdbcType=DOUBLE},
            warning_level = #{warningLevel,jdbcType=TINYINT},
            interpretation = #{interpretation,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <resultMap id="explainResultMap" type="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="factor_id" property="factorId" jdbcType="INTEGER" />
        <result column="start_value" property="startValue" jdbcType="DOUBLE" />
        <result column="end_value" property="endValue" jdbcType="DOUBLE" />
        <result column="interpretation" property="interpretation" jdbcType="VARCHAR" />
        <result column="factor_name" property="factorName" jdbcType="VARCHAR" />
        <result column="warning_level" property="warningLevel" jdbcType="TINYINT" />
    </resultMap>
    <!--根据因子id查询因子解释-->
    <select id="getListById" parameterType="Integer" resultMap="explainResultMap">
        select
            pfe.id,
            pfe.factor_id,
            pfe.start_value,
            pfe.end_value,
            pfe.interpretation,
            pf.factor_name,
            pfe.warning_level
        from psycloud_factor_explain pfe
            inner join psycloud_factor pf on pf.id = pfe.factor_id
        where factor_id = #{factorId}
    </select>
    <!--根据因子id集合查询因子结果解释集合-->
    <select id="getListByIds" parameterType="String" resultMap="explainResultMap">
        select
            pfe.id,
            pfe.factor_id,
            pfe.start_value,
            pfe.end_value,
            pfe.interpretation,
            pf.factor_name,
            pfe.warning_level
        from psycloud_factor_explain pfe
            inner join psycloud_factor pf on pf.id = pfe.factor_id
        where factor_id IN (${ids})
    </select>

    <!--根据量表id查询因子结果解释集合-->
    <select id="getListByScaleId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity">
        select
            pfe.start_value,
            pfe.end_value,
            pfe.interpretation,
            pfe.warning_level,
            pfe.factor_id
        from psycloud_factor_explain pfe
                 inner join psycloud_factor pf on pf.id = pfe.factor_id
        where pf.scale_id =#{scaleId}
    </select>
</mapper>
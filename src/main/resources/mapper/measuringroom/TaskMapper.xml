<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.TaskDao">
    <!--添加测评任务-->
    <insert id="addTask" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TaskEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_task(
            task_name,
            start_time,
            end_time,
            person_in_charge,
            task_type,
            is_limit_testcount,
            limit_testcount,
            result_view_rule,
            is_valid,
            is_survey,
            survey_id,
            task_kind,
            show_background,
            background_url
        )
        values(
            #{taskName,jdbcType=VARCHAR},
            #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP},
            #{personInCharge,jdbcType=INTEGER},
            #{taskType,jdbcType=TINYINT},
            #{isLimitTestCount,jdbcType=TINYINT},
            #{limitTestCount,jdbcType=INTEGER},
            #{resultViewRule,jdbcType=TINYINT},
            1,
            #{isSurvey,jdbcType=TINYINT},
            #{surveyId,jdbcType=INTEGER},
            #{taskKind,jdbcType=TINYINT},
            #{showBackground,jdbcType=TINYINT},
            #{backgroundUrl,jdbcType=VARCHAR}
        )
    </insert>
    <!--修改测评任务-->
    <update id="updateTask" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TaskEntity">
        update psycloud_task
        set
            task_name = #{taskName,jdbcType=VARCHAR},
            start_time = #{startTime,jdbcType=TIMESTAMP},
            end_time = #{endTime,jdbcType=TIMESTAMP},
            result_view_rule = #{resultViewRule,jdbcType=TINYINT},
            is_survey = #{isSurvey,jdbcType=TINYINT},
            survey_id = #{surveyId,jdbcType=INTEGER},
            show_background = #{showBackground,jdbcType=TINYINT},
            background_url = #{backgroundUrl,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--验证测评任务名称是否重复-->
    <select id="isExist" parameterType="map" resultType="Integer">
        select count(*) from psycloud_task where is_valid = 1 and task_name = #{taskName} and task_kind = #{taskKind}
    </select>

    <!--添加测评对象-->
    <insert id="addTaskUser" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TaskUserEntity">
        delete from psycloud_task_user where task_id =#{taskId} and user_id = #{userId};
        insert into psycloud_task_user(
            task_id,
            user_id
        )
        values(
            #{taskId,jdbcType=INTEGER},
            #{userId,jdbcType=INTEGER}
        )
    </insert>

    <!--添加测评任务量表-->
    <insert id="addTaskScale" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TaskScaleEntity">
        insert into psycloud_task_scale(
            task_id,
            scale_id
        )
        values(
            #{taskId,jdbcType=INTEGER},
            #{scaleId,jdbcType=INTEGER}
        )
    </insert>

    <!-- 添加问卷调查任务里的问卷 -->
    <insert id="addTaskSurvey" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyEntity">
        insert into psycloud_task_survey(
            task_id,
            survey_id
        )
        values(
            #{taskId,jdbcType=INTEGER},
            #{surveyId,jdbcType=INTEGER}
        )
    </insert>

    <insert id="addTaskRecord" parameterType="map">
        insert into psycloud_task_record(task_id,record_id) VALUES(#{taskId,jdbcType=INTEGER},#{recordId,jdbcType=INTEGER})
    </insert>

    <resultMap id="taskResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.TaskDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="task_name" property="taskName" jdbcType="VARCHAR" />
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="person_in_charge" property="personInCharge" jdbcType="INTEGER" />
        <result column="task_type" property="taskType" jdbcType="TINYINT" />
        <result column="is_valid" property="isValid" jdbcType="TINYINT" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR" />
        <result column="real_name" property="realName" jdbcType="VARCHAR" />
        <result column="is_limit_testcount" property="isLimitTestCount" jdbcType="TINYINT" />
        <result column="limit_testcount" property="limitTestCount" jdbcType="INTEGER" />
        <result column="result_view_rule" property="resultViewRule" jdbcType="TINYINT" />
        <result column="is_survey" property="isSurvey" jdbcType="TINYINT" />
        <result column="survey_id" property="surveyId" jdbcType="INTEGER" />
        <result column="survey_name" property="surveyName" jdbcType="VARCHAR" />
        <result column="task_kind" property="taskKind" jdbcType="TINYINT" />
        <result column="show_background" property="showBackground" jdbcType="TINYINT" />
        <result column="background_url" property="backgroundUrl" jdbcType="VARCHAR" />
        <collection property="scales" select="getScalesByTaskId" column="id" />
        <collection property="surveys" select="getSurveysByTaskId" column="id" />
    </resultMap>
    <!--查询测评任务集合-->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskDto" resultMap="taskResultMap">
        select
            psycloud_task.id,
            psycloud_task.task_name,
            psycloud_task.start_time,
            psycloud_task.end_time,
            psycloud_task.person_in_charge,
            psycloud_task.task_type,
            psycloud_task.is_valid,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            psycloud_task.is_limit_testcount,
            psycloud_task.limit_testcount,
            psycloud_task.result_view_rule,
            psycloud_task.is_survey,
            psycloud_survey.id as survey_id,
            psycloud_survey.survey_name
        from psycloud_task
            inner join psycloud_user on psycloud_user.user_id = psycloud_task.person_in_charge
            inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
            left join psycloud_survey  on psycloud_survey.id = psycloud_task.survey_id
        where psycloud_task.is_valid=1
            <if test="taskName != null and taskName !=''">and psycloud_task.task_name like CONCAT('%',#{taskName},'%')</if>
            <if test="taskKind != null and taskKind != 0">and psycloud_task.task_kind = #{taskKind}</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and psycloud_user_info.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        order by psycloud_task.id desc
    </select>

    <select id="getListForSelect" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskDto" resultType="cn.psycloud.psyplatform.entity.measuringroom.TaskEntity">
        select pt.id,pt.task_name
        from psycloud_task pt
            inner join psycloud_user pu on pu.user_id = pt.person_in_charge
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
        where pt.is_valid = 1
        <if test="taskKind != null and taskKind != 0">and pt.task_kind = #{taskKind}</if>
        <if test="childStructs!=null and childStructs.size()>0">
            and pui.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--根据测评任务id查询量表-->
    <select id="getScalesByTaskId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity">
        select
            ps.id,
            ps.scale_name,
            ps.scale_alias,
            ps.scale_intro,
            ps.thumbnail
        from psycloud_task_scale pts
            inner join psycloud_scale ps on ps.id = pts.scale_id
        where pts.task_id = #{taskId,jdbcType=INTEGER} and ps.is_valid = 1
    </select>

    <!-- 根据问卷调查任务id查询问卷集合 -->
    <select id="getSurveysByTaskId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.survey.SurveyEntity">
        select
            ps.id,
            ps.survey_name
        from psycloud_task_survey pts
            inner join psycloud_survey ps on ps.id = pts.survey_id
        where pts.task_id = #{taskId,jdbcType=INTEGER} and ps.is_valid = 1
    </select>

    <resultMap id="taskUserMap" type="cn.psycloud.psyplatform.dto.anteroom.UserDto">
        <result column="role_name" property="role.roleName" jdbcType="VARCHAR"/>
    </resultMap>
    <!--根据测评任务id查询测评对象-->
    <select id="getUsersByTaskId" parameterType="Integer" resultMap="taskUserMap">
        select
            pu.user_id,
            pu.login_name,
            pui.real_name,
            pr.role_name,
            f_GetStructFullName(pui.struct_id) as structFullName
        from psycloud_task_user ptu
            inner join psycloud_user pu on pu.user_id = ptu.user_id
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
            inner join psycloud_user_role pur on pur.user_id = pui.user_id
            inner join psycloud_role pr on pr.id = pur.role_id
        WHERE ptu.task_id = #{taskId,jdbcType=INTEGER} and pui.is_valid=1
    </select>

    <!--删除测评任务-->
    <update id="deleteById" parameterType="Integer">
        update psycloud_task set is_valid = 0 where id = #{taskId,jdbcType=INTEGER}
    </update>

    <!--插入任务包含的组织-->
    <insert id="addTaskStruct" parameterType="map">
        insert into psycloud_task_structs(
            task_id,
            struct_id
        ) values(
            #{taskId,jdbcType=INTEGER},
            #{structId,jdbcType=INTEGER}
        )
    </insert>

    <!--根据测评任务id查询任务信息-->
    <select id="getById" parameterType="Integer" resultMap="taskResultMap">
        select
            psycloud_task.id,
            psycloud_task.task_name,
            psycloud_task.start_time,
            psycloud_task.end_time,
            psycloud_task.person_in_charge,
            psycloud_task.task_type,
            psycloud_task.is_valid,
            psycloud_user.login_name,
            psycloud_user_info.real_name,
            psycloud_task.is_limit_testcount,
            psycloud_task.limit_testcount,
            psycloud_task.result_view_rule,
            psycloud_task.is_survey,
            psycloud_survey.id as survey_id,
            psycloud_survey.survey_name,
            psycloud_task.task_kind,
            psycloud_task.show_background,
            psycloud_task.background_url
        from psycloud_task
                 inner join psycloud_user on psycloud_user.user_id = psycloud_task.person_in_charge
                 inner join psycloud_user_info on psycloud_user_info.user_id = psycloud_user.user_id
                 left join psycloud_survey  on psycloud_survey.id = psycloud_task.survey_id
        where psycloud_task.is_valid=1 and psycloud_task.id =#{taskId,jdbcType=INTEGER}
    </select>

    <!--获取我的测评任务-->
    <select id="getMyTasks" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TaskDto" resultMap="taskResultMap">
        select
            pt.id,
            pt.task_name,
            pt.start_time,
            pt.end_time,
            pt.task_type,
            pt.is_limit_testcount,
            pt.limit_testcount
        from psycloud_task pt
        where pt.is_valid = 1
            and pt.task_type = 1
            and pt.id in (
                select pts.task_id
                from psycloud_task_structs pts
                    inner join psycloud_user_info pui on pui.struct_id = pts.struct_id
                where pui.user_id  =#{visitorId}
                union
                select ptr.task_id
                from psycloud_task_record ptr
                    inner join psycloud_test_record ptr2 on ptr2.id = ptr.record_id
                where ptr2.user_id = #{visitorId}
            )
        <if test="taskName != null and taskName !=''">and task_name like CONCAT('%',#{taskName},'%')</if>
        <if test="taskKind != null and taskKind != 0">and pt.task_kind = #{taskKind}</if>
         order by pt.id desc
    </select>

    <!--判断任务中的量表是否已经做过-->
    <select id="isScaleDone" parameterType="map" resultType="cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity">
        select
            ptr.id,
            ptr.state
        from psycloud_test_record ptr
            inner join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
        where ptr.is_valid = 1
            and ptr.scale_id = #{scaleId}
            and ptr.user_id = #{userId}
            and ptr2.task_id = #{taskId}
    </select>

    <!--根据任务Id获取记录Id-->
    <select id="getRecordIdByTaskId" parameterType="map" resultType="Integer">
        select ptr.id
        from psycloud_test_record ptr
                 inner join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
        where ptr.is_valid =1
          and ptr2.task_id = #{taskId}
          and ptr.user_id = #{userId}
          and ptr.scale_id = #{scaleId}
    </select>

    <!--根据测评记录ID查询结果查看规则-->
    <select id="getResultViewRuleByRecordId" parameterType="Integer" resultType="Integer">
        select pt.result_view_rule
        from psycloud_task pt
            inner join psycloud_task_record ptr on ptr.task_id = pt.id
            inner join psycloud_test_record ptr2 on ptr2.id = ptr.record_id
        where ptr2.id = #{recordId}
    </select>

    <!--判断用户是不是属于当前测评任务-->
    <select id="isTaskValid" parameterType="map" resultType="Integer">
        select count(*)
        from psycloud_task_structs pts
            inner join psycloud_task pt on pt.id = pts.task_id
            inner join psycloud_user_info pui on pui.struct_id = pts.struct_id
        where pt.task_type = 1 and pt.id = #{taskId} and pui.user_id = #{userId}
    </select>

    <!-- 判断测评任务里的调查问卷是否完成 -->
    <select id="isTaskSurveyDone" parameterType="map" resultType="Integer">
        select count(psr.id)
        from psycloud_survey_record psr
                 inner join psycloud_task_survey_record ptsr on ptsr.survey_record_id = psr.id
        where psr.is_done =1
          and ptsr.task_id =#{taskId}
          and psr.user_id = #{userId}
    </select>

    <!-- 判断问卷调查任务里的问卷是否完成 -->
    <select id="isSurveyOfTaskDone" parameterType="map" resultType="cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity">
        select psr.id,psr.is_done
        from psycloud_survey_record psr
            inner join psycloud_task_record ptr on ptr.record_id = psr.id
        where psr.is_valid = 1
          and psr.survey_id = #{surveyId}
          and psr.user_id = #{userId}
          and ptr.task_id = #{taskId}
    </select>

    <!-- 查询用户的测评任务里面未完成的量表 -->
    <select id="getScaleListUndone" parameterType="map" resultType="cn.psycloud.psyplatform.dto.measuringroom.ScaleUndoneDto">
        select
            ptr.id as record_id,
            ps.id as scale_id,
            ps.scale_name,
            pt.task_type,
            ptr2.task_id
        from psycloud_test_record ptr
            inner join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
            inner join psycloud_task pt on pt.id = ptr2.task_id
            inner join psycloud_scale ps on ps.id = ptr.scale_id
        where ptr.state = 0
            and ptr.is_valid =1
            and ps.is_valid = 1
            and ptr2.task_id = #{taskId}
            and ptr.user_id = #{userId}
    </select>

    <resultMap id="taskSurveyStatMap" type="cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyStatDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="task_name" property="taskName" jdbcType="VARCHAR" />
        <result column="surveyId" property="surveyId" jdbcType="INTEGER" />
        <result column="survey_name" property="surveyName" jdbcType="VARCHAR" />
        <collection property="taskSurvey" ofType="cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyEntity">
            <result column="id" property="taskId" jdbcType="INTEGER" />
            <result column="surveyId" property="surveyId" jdbcType="INTEGER"/>
        </collection>
        <collection property="surveyRecords" select="getSurveyRecordsBySurveyId" column="{surveyId=surveyId, taskId=id}" />
        <collection property="surveyResults" select="getSurveyResultsBySurveyId" column="{surveyId=surveyId, taskId=id}"/>
    </resultMap>

    <select id="getTaskSurveyStat" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyEntity" resultMap="taskSurveyStatMap">
        select pt.id,pt.task_name,ps.id as surveyId, ps.survey_name
        from psycloud_task pt
            inner join psycloud_task_survey pts on pts.task_id = pt.id
            inner join psycloud_survey ps on ps.id = pts.survey_id
        where pt.id = #{taskId} and pts.survey_id = #{surveyId}
    </select>

    <select id="getSurveyRecordsBySurveyId" parameterType="map" resultType="cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity">
        select psr.id,psr.is_done
        from psycloud_survey_record psr
            inner join psycloud_task_record ptr on ptr.record_id = psr.id
        where psr.is_valid =1
          and survey_id = #{surveyId}
          and ptr.task_id = #{taskId}
    </select>

    <select id="getSurveyResultsBySurveyId" parameterType="map" resultType="cn.psycloud.psyplatform.dto.survey.SurveyResultDto">
        select psq.q_number,psq.q_content,psr.item_id
        from psycloud_survey_result psr
            inner join psycloud_survey_question psq on psq.id = psr.q_id
            inner join psycloud_task_record ptr on ptr.record_id = psr.record_id
        where ptr.task_id = #{taskId} and psq.survey_id = #{surveyId}
    </select>

    <!--根据测评记录ID获取任务ID-->
    <select id="getTaskIdByRecordId" parameterType="Integer" resultType="Integer">
        select task_id
        from psycloud_task_record
        where record_id = #{recordId,jdbcType=INTEGER}
        limit 1
    </select>
</mapper>
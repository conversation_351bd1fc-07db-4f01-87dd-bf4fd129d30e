<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.ScaleDao">
    <update id="delete" parameterType="Integer">
        update psycloud_scale set is_valid = 0 where id= #{id}
    </update>

    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity">
        insert into psycloud_scale(
            scale_name,
            scale_type,
            scale_alias,
            scale_intro,
            scale_guide,
            need_time,
            test_limit,
            age_limit,
            is_done,
            create_date,
            is_recommend,
            is_valid,
            thumbnail,
            test_count
        )
        values(
            #{scaleName,jdbcType=VARCHAR},
            #{scaleTypeId,jdbcType=INTEGER},
            #{scaleAlias,jdbcType=VARCHAR},
            #{scaleIntro,jdbcType=LONGVARCHAR},
            #{scaleGuide,jdbcType=LONGVARCHAR},
            #{needTime,jdbcType=INTEGER},
            #{testLimit,jdbcType=VARCHAR},
            #{ageLimit,jdbcType=VARCHAR},
            0,
            #{createDate,jdbcType=TIMESTAMP},
            #{isRecommend,jdbcType=TINYINT},
            1,
             #{thumbnail,jdbcType=VARCHAR},
            0
            )
    </insert>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity">
        update psycloud_scale
        set
            scale_name = #{scaleName,jdbcType=VARCHAR},
            scale_type = #{scaleTypeId,jdbcType=INTEGER},
            scale_alias = #{scaleAlias,jdbcType=VARCHAR},
            scale_intro =#{scaleIntro,jdbcType=LONGVARCHAR},
            scale_guide = #{scaleGuide,jdbcType=LONGVARCHAR},
            need_time = #{needTime,jdbcType=INTEGER},
            test_limit = #{testLimit,jdbcType=VARCHAR},
            age_limit = #{ageLimit,jdbcType=VARCHAR},
            is_recommend =#{isRecommend,jdbcType=TINYINT},
            thumbnail = #{thumbnail,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateScaleDone" parameterType="map">
        update psycloud_scale set is_done = #{state,jdbcType=TINYINT} where id = #{scaleId,jdbcType=INTEGER}
    </update>

    <update id="setSort" parameterType="map">
        update psycloud_scale set sort =#{sort,jdbcType=INTEGER} where id = #{scaleId,jdbcType=INTEGER}
    </update>

    <resultMap id="scaleResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.ScaleDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="scale_type_name" property="scaleTypeName" jdbcType="VARCHAR"/>
        <result column="scale_name" property="scaleName" jdbcType="VARCHAR"/>
        <result column="scale_type" property="scaleTypeId" jdbcType="INTEGER"/>
        <result column="scale_alias" property="scaleAlias" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="scale_intro" property="scaleIntro" jdbcType="VARCHAR"/>
        <result column="scale_guide" property="scaleGuide" jdbcType="VARCHAR"/>
        <result column="need_time" property="needTime" jdbcType="INTEGER"/>
        <result column="test_limit" property="testLimit" jdbcType="VARCHAR"/>
        <result column="age_limit" property="ageLimit" jdbcType="VARCHAR"/>
        <result column="is_done" property="isDone" jdbcType="TINYINT"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="is_recommend" property="isRecommend" jdbcType="TINYINT"/>
        <result column="is_valid" property="isValid" jdbcType="TINYINT"/>
        <result column="thumbnail" property="thumbnail" jdbcType="VARCHAR"/>
        <result column="test_count" property="testCount" jdbcType="INTEGER"/>
        <result column="q_count" property="qCount" jdbcType="INTEGER"/>
        <collection property="listCharts" column="id" select="cn.psycloud.psyplatform.dao.measuringroom.ScaleChartsDao.getListByScaleId" />
    </resultMap>
    <select id="getById" parameterType="Integer" resultMap="scaleResultMap">
        select
            ps.id,
            pst.scale_type_name,
            ps.scale_name,
            ps.scale_type,
            ps.scale_alias,
            ps.sort,
            ps.scale_intro,
            ps.scale_guide,
            ps.need_time,
            ps.test_limit,
            ps.age_limit,
            ps.is_done,
            ps.create_date,
            ps.is_recommend,
            ps.is_valid,
            ps.thumbnail,
            ps.test_count,
            (select count(*) from psycloud_question where is_valid = 1 and scale_id = ps.id) as q_count
        from psycloud_scale ps
            inner join psycloud_scale_type pst on pst.id = ps.scale_type
        WHERE ps.is_valid = 1 and ps.id = #{scaleId}
    </select>
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.ScaleDto" resultMap="scaleResultMap">
        select
            ps.id,
            pst.scale_type_name,
            ps.scale_name,
            ps.scale_type,
            ps.scale_alias,
            ps.sort,
            ps.scale_intro,
            ps.scale_guide,
            ps.need_time,
            ps.test_limit,
            ps.age_limit,
            ps.is_done,
            ps.create_date,
            ps.is_recommend,
            ps.is_valid,
            ps.thumbnail,
            ps.test_count,
            (select count(*) from psycloud_question where is_valid = 1 and scale_id = ps.id) as q_count
        from psycloud_scale ps
            inner join psycloud_scale_type pst on pst.id = ps.scale_type
        where ps.is_valid = 1
            <if test="isDone!=null">and is_done=#{isDone,jdbcType=TINYINT}</if>
            <if test="isRecommend!=null">and is_recommend=#{isRecommend,jdbcType=TINYINT}</if>
            <if test="scaleTypeId!=null and scaleTypeId!=0">and scale_type=#{scaleTypeId,jdbcType=INTEGER}</if>
            <if test="scaleName!=null and scaleName!=''">and scale_name like CONCAT('%',#{scaleName},'%')</if>
    </select>

    <select id="getCountByName" parameterType="String" resultType="Integer">
        select count(scale_name) from psycloud_scale where is_valid =1 and scale_name = #{scaleName}
    </select>

    <select id="getListByTaskId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity">
        select
            ps.id,
            ps.scale_name
        from psycloud_scale ps
            inner join psycloud_task_scale pts on pts.scale_Id = ps.id
        where ps.is_done = 1
            and ps.is_valid = 1
            and pts.task_id = #{taskId}
    </select>

    <select id="getListForSelect" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity">
        select
            id,
            scale_name
        from psycloud_scale
        where is_valid =1
          and is_done =1
    </select>
    
    <select id="getListForIndex" resultType="cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity">
        select
            id,
            scale_name,
            thumbnail
        from psycloud_scale
        where is_valid = 1
            and is_done = 1
            and is_recommend = 1
        order by sort
        limit 4
    </select>

    <resultMap id="scaleExportResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.ScaleDto">
        <result column="scale_name" property="scaleName" jdbcType="VARCHAR"/>
        <result column="scale_guide" property="scaleGuide" jdbcType="VARCHAR"/>
        <collection property="listQuestions" select="cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao.getListByScaleId" column="id"/>
    </resultMap>
    <select id="getScaleForExport" parameterType="Integer" resultMap="scaleExportResultMap">
        select
            id,
            scale_name,
            scale_guide
        from psycloud_scale
        where is_valid=1 and is_done=1 and id=#{scaleId}
    </select>
</mapper>
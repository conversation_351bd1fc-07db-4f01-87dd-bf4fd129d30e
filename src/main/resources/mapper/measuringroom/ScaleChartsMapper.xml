<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.ScaleChartsDao">
    <!-- 保存测评报告图表类型 -->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.measuringroom.ScaleChartsEntity">
        insert into psycloud_scale_charts(
            scale_id,
            factor_type,
            chart_type
        )
        values(
            #{scaleId,jdbcType=INTEGER},
            #{factorType,jdbcType=INTEGER},
            #{chartType,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 根据量表删除关联的图表类型 -->
    <delete id="deleteByScaleId" parameterType="Integer">
        delete from psycloud_scale_charts where scale_id = #{scaleId}
    </delete>

    <!-- 根据量表和因子类型删除关联的图表类型 -->
    <delete id="deleteByScaleIdAndFactorType">
        delete from psycloud_scale_charts where scale_id = #{scaleId} and factor_type = #{factorType}
    </delete>

    <!-- 根据量表获取关联的测评报告图表类型 -->
    <select id="getListByScaleId" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.measuringroom.ScaleChartsDto">
        select scale_id, factor_type, chart_type
        from psycloud_scale_charts
        where scale_id = #{scaleId}
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.anteroom.UserTokenDao">
    <resultMap id="tokenResultMap" type="cn.psycloud.psyplatform.dto.anteroom.UserTokenDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR" />
        <result column="token" property="token" jdbcType="VARCHAR" />
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR" />
        <result column="user_id" property="user.userId" jdbcType="INTEGER" />
        <result column="role_id" property="user.roleId" jdbcType="INTEGER" />
        <result column="real_name" property="user.realName" jdbcType="VARCHAR" />
        <result column="head_img" property="user.headPic" jdbcType="VARCHAR" />
        <result column="struct_id" property="user.structId" jdbcType="INTEGER" />
        <result column="mobile" property="user.mobile" jdbcType="VARCHAR" />
        <result column="is_mobile_bind" property="user.isMobileBind" jdbcType="TINYINT" />
    </resultMap>
    <sql id="tb">psycloud_token</sql>

    <delete id="deleteByToken" parameterType="String">
        delete from <include refid="tb" /> where token = #{token}
    </delete>

    <delete id="deleteByLoginName" parameterType="String">
        delete from <include refid="tb" /> where login_name = #{loginName}
    </delete>

    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.anteroom.UserTokenEntity" useGeneratedKeys="true" keyProperty="id"  >
        insert into psycloud_token(
            login_name,
            token,
            create_date,
            ip_address
        )
        values (
           #{loginName},
           #{token},
           now(),
           #{ipAddress}
       )
    </insert>

    <select id="getByToken" resultMap="tokenResultMap" parameterType="String">
        select
            pt.id,
            pt.login_name,
            pt.token,
            pt.create_date,
            pt.ip_address ,
            pu.user_id,
            pur.role_id,
            pui.real_name,
            pui.head_img,
            pui.struct_id,
            pui.mobile,
            pui.is_mobile_bind
        from psycloud_token pt
                 inner join psycloud_user pu on pu.login_name = pt.login_name
                 inner join psycloud_user_info pui on pui.user_id = pu.user_id
                 inner join psycloud_user_role pur on pur.user_id = pu.user_id
        where pui.is_valid = 1 and pt.token = #{token}
    </select>
</mapper>
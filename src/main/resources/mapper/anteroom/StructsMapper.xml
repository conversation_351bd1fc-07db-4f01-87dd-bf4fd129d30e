<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.anteroom.StructsDao">
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.anteroom.StructsEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_structs(
            struct_name,
            parent_id,
            is_valid
        )
        values (
           #{structName,jdbcType=VARCHAR},
           #{parentId,jdbcType=INTEGER},
           1
        )
    </insert>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.anteroom.StructsEntity">
        update
        psycloud_structs
        <set>
            <if test="structName!=null and structName!=''">struct_name = #{structName,jdbcType=VARCHAR}</if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="delete" parameterType="Integer">
        update
            psycloud_structs
        set
            is_valid = 0
        where id =#{id}
    </update>

    <delete id="truncateStructs">
        truncate table psycloud_structs
    </delete>

    <resultMap id="structsResultMap" type="cn.psycloud.psyplatform.dto.anteroom.StructsDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="struct_name" property="structName" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="is_valid" property="IsValid" jdbcType="TINYINT"/>
    </resultMap>

    <select id="getList" parameterType="cn.psycloud.psyplatform.entity.anteroom.StructsEntity" resultMap="structsResultMap">
        select
        id,
        struct_name,
        parent_id,
        is_valid
        from psycloud_structs
        where is_valid=1
        <if test="parentId!=null and parentId!=0">and parent_id=#{parentId}</if>
    </select>

    <resultMap id="subStructsResultMap" type="cn.psycloud.psyplatform.dto.anteroom.StructsDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="struct_name" property="structName" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="is_valid" property="IsValid" jdbcType="TINYINT"/>
        <collection property="child" javaType="ArrayList" column="id" select="getChildList" />
    </resultMap>

    <select id="getChildList" resultMap="subStructsResultMap" parameterType="Integer">
        select
            id,
            struct_name,
            parent_id,
            is_valid
        from psycloud_structs
        where is_valid =1 and parent_id=#{parentId}
    </select>

    <select id="getById" parameterType="Integer" resultMap="structsResultMap">
        select
            id,
            struct_name,
            parent_id,
            is_valid
        from psycloud_structs
        where is_valid =1
          and id = #{id}
    </select>

    <update id="moveStruct">
        update
            psycloud_structs
        set
            parent_id=#{targetNode}
        where
            id=#{node}
    </update>

    <!-- 根据组织名称和父id获取组织id -->
    <select id="getStructIdByStructName" parameterType="map" resultType="Integer">
        select id from psycloud_structs where parent_id = #{parentId} and struct_name = #{structName}
    </select>

    <!-- 根据组织名称获取组织id -->
    <select id="getIdByStructName" parameterType="String" resultType="Integer">
        select id
        from psycloud_structs
        where struct_name = #{structName} and is_valid = 1
    </select>
</mapper>
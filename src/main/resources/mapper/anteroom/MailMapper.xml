<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.anteroom.MailDao">
    <resultMap id="mailResultMap" type="cn.psycloud.psyplatform.dto.anteroom.MailDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="msg_title" property="msgTitle" jdbcType="VARCHAR"/>
        <result column="msg_content" property="msgContent" jdbcType="VARCHAR"/>
        <result column="from_name" property="fromName" jdbcType="VARCHAR"/>
        <result column="send_date" property="sendDate" jdbcType="TIMESTAMP"/>
        <result column="from_user" property="fromUser" jdbcType="INTEGER"/>
        <result column="is_read" property="isRead" jdbcType="TINYINT"/>
        <result column="from_head_img" property="fromHeadPic" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="getList" resultMap="mailResultMap" parameterType="cn.psycloud.psyplatform.dto.anteroom.MailDto">
        select
            pm.id,
            pm.msg_title,
            pm.msg_content,
            pui.real_name as from_name ,
            pm.send_date,
            pm.from_user,
            pm.is_read,
            pui.head_img as from_head_img
        from psycloud_mail pm
            inner join psycloud_user_info pui on pui.user_id = pm.from_user
        where pm.is_valid = 1
            <if test="toUser != null and toUser != 0">
                and  pm.to_user = #{toUser}
            </if>
            <if test="isRead != null and isRead != ''">
                and  pm.is_read = #{isRead}
            </if>
        order by pm.send_date desc
    </select>

    <!--根据id查询消息-->
    <select id="getById" parameterType="Integer" resultMap="mailResultMap">
        select
            pm.id,
            pm.msg_title,
            pm.msg_content,
            pui.real_name as from_name ,
            pm.send_date,
            pm.from_user,
            pm.is_read,
            pui.head_img as from_head_img
        from psycloud_mail pm
            inner join psycloud_user_info pui on pui.user_id = pm.from_user
        where pm.is_valid = 1 and pm.id = #{id}
    </select>

    <!--发送站内消息-->
    <insert id="sendMsg" parameterType="cn.psycloud.psyplatform.entity.anteroom.MailEntity">
        insert into psycloud_mail(
            msg_title,
            msg_content,
            to_user,
            from_user,
            is_read,
            send_date,
            is_valid
        )
        values(
            #{msgTitle,jdbcType=VARCHAR},
            #{msgContent,jdbcType=VARCHAR},
            #{toUser,jdbcType=INTEGER},
            #{fromUser,jdbcType=INTEGER},
            0,
            #{sendDate,jdbcType=TIMESTAMP},
            1
        )
    </insert>

    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_mail set is_valid = 0 where id = #{id,jdbcType=INTEGER}
    </update>

    <!--更改已读状态-->
    <update id="updateReadState" parameterType="Integer">
        update psycloud_mail set is_read =1 where id= #{id,jdbcType=INTEGER}
    </update>
</mapper>
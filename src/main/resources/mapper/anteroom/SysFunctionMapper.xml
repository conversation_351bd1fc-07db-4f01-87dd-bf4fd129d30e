<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.anteroom.SysFunctionDao">
    <resultMap id="sysfunctionResultMap" type="cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="function_code" property="functionCode" jdbcType="VARCHAR" />
        <result column="parent_code" property="parentCode" jdbcType="VARCHAR" />
        <result column="function_name" property="functionName" jdbcType="VARCHAR" />
        <result column="url" property="url" jdbcType="VARCHAR" />
        <result column="icon" property="icon" jdbcType="VARCHAR" />
        <result column="sort" property="sort" jdbcType="INTEGER" />
        <result column="level" property="level" jdbcType="TINYINT" />
        <result column="function_type" property="functionType" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ztreeResultMap" type="cn.psycloud.psyplatform.dto.core.ZtreeData">
        <result column="function_code" property="id" jdbcType="VARCHAR"/>
        <result column="function_name" property="name" jdbcType="VARCHAR"/>
        <result column="parent_code" property="pId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tb">psycloud_function</sql>

    <select id="isParent" parameterType="String" resultType="Integer">
        select COUNT(*)
        from <include refid="tb"/>
        where parent_code = #{parentCode}
    </select>

    <select id="getList" resultMap="sysfunctionResultMap" parameterType="cn.psycloud.psyplatform.entity.anteroom.SysFunctionEntity">
        select
        id,
        function_code,
        parent_code,
        function_name,
        url,
        icon,
        sort,
        level,
        function_type
        from <include refid="tb"/>
        where is_valid =1
        <if test="parentCode != null and parentCode != ''">
            and parent_code = #{parentCode}
        </if>
    </select>

    <select id="getListForZTree" resultMap="ztreeResultMap">
        select
        id,
        function_code,
        parent_code,
        function_name,
        url,
        icon,
        sort,
        level,
        function_type
        from <include refid="tb"/>
        where is_valid =1
    </select>

    <select id="getSysFunctionByName" parameterType="String" resultType="cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto">
        select distinct
            pf.function_code,
            pf.function_name,
            pf.parent_code,
            pf.function_type,
            pf.url,
            pf.icon,
            pf.sort,
            pf.level,
            pf.is_valid,
            pu.login_name
        from psycloud_user pu
                 inner join psycloud_user_role pur on pur.user_id = pu.user_id
                 inner join psycloud_role pr on pr.ID = pur.role_id
                 inner join psycloud_grant pg on pg.role_id = pr.id
                 inner join psycloud_function pf on pf.function_code = pg.function_code
        where pf.is_valid = 1
          and pr.is_valid = 1
          and pf.function_code != '00'
          <if test="loginName != null and loginName != ''">and pu.login_name =#{loginName}</if>
        order by pf.parent_code,pf.function_code,pf.sort
    </select>

    <select id="getGrantByRole" resultType="cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto" parameterType="Integer">
        select distinct
            hf.function_code,
            hf.function_name,
            hf.parent_code,
            hf.function_type,
            hf.url,
            hf.icon,
            hf.sort,
            hf.level,
            hr.id as roleId
        from psycloud_role hr
            inner join psycloud_grant hg  on hg.role_id = hr.id
            inner join psycloud_function hf on hf.function_code = hg.function_code
        where hf.is_valid = 1
            and hr.is_valid =1
            and hf.function_code != '00'
        <if test="roleId != null and roleId !=0">and hr.id = #{roleId}</if>
        order by hf.sort
    </select>

    <select id="getGrantForZTree" resultMap="ztreeResultMap" parameterType="Integer">
        select distinct
            hf.function_code,
            hf.function_name,
            hf.parent_code,
            hf.function_type,
            hf.url,
            hf.icon,
            hf.sort,
            hf.level
        from psycloud_role hr
                 inner join psycloud_grant hg  on hg.role_id = hr.id
                 inner join psycloud_function hf on hf.function_code = hg.function_code
        where hf.is_valid = 1
          and hr.id = #{roleId}
        order by hf.sort
    </select>
</mapper>
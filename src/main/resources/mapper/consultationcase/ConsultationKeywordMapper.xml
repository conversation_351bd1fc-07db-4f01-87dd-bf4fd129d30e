<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.consultationcase.ConsultationKeywordDao">
    <!-- 添加关键词 -->
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.consultationcase.ConsultationKeywordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_consultation_keywords (
            keyword, is_valid
        )
        values (
            #{keyword,jdbcType=VARCHAR},
            1
        )
    </insert>

    <!-- 更新关键词 -->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.consultationcase.ConsultationKeywordEntity">
        update psycloud_consultation_keywords
        set keyword = #{keyword,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER} and is_valid = 1
    </update>

    <!-- 删除关键词 -->
    <delete id="delete" parameterType="java.lang.Integer">
        update psycloud_consultation_keywords 
        set is_valid = 0 
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 根据关键词查询 -->
    <select id="getByKeyword" resultType="cn.psycloud.psyplatform.entity.consultationcase.ConsultationKeywordEntity" parameterType="java.lang.String">
        select id, keyword, is_valid
        from psycloud_consultation_keywords
        where keyword = #{keyword,jdbcType=VARCHAR} and is_valid = 1
    </select>

    <!-- 查询关键词列表 -->
    <select id="getList" resultType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationKeywordDto">
        select id, keyword, is_valid
        from psycloud_consultation_keywords
        where is_valid = 1
        <if test="keyword != null and keyword != ''">
            and keyword like concat('%', #{keyword}, '%')
        </if>
    </select>

    <!-- 查询所有关键词 -->
    <select id="getAllKeywords" resultType="java.lang.String">
        select keyword
        from psycloud_consultation_keywords
        where is_valid = 1
    </select>

    <!-- 检查关键词是否存在（排除指定ID） -->
    <select id="isKeywordExistsExcludeId" resultType="java.lang.Integer">
        select count(*)
        from psycloud_consultation_keywords
        where keyword = #{keyword,jdbcType=VARCHAR} 
        and is_valid = 1
        <if test="excludeId != null and excludeId > 0">
            and id != #{excludeId,jdbcType=BIGINT}
        </if>
    </select>

</mapper> 
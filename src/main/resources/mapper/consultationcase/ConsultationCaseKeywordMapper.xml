<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.consultationcase.ConsultationCaseKeywordDao">
    <!-- 添加个案关键词关联 -->
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseKeywordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_consultation_case_keywords (
            case_id, keyword
        )
        values (
            #{caseId,jdbcType=INTEGER},
            #{keyword,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 根据个案ID查询关键词列表 -->
    <select id="getKeywordsByCaseId" resultType="java.lang.String" parameterType="java.lang.Integer">
        select keyword
        from psycloud_consultation_case_keywords
        where case_id = #{caseId,jdbcType=INTEGER}
        order by id asc
    </select>

    <!-- 根据个案ID删除关键词关联记录 -->
    <delete id="deleteByCaseId" parameterType="java.lang.Integer">
        delete from psycloud_consultation_case_keywords
        where case_id = #{caseId,jdbcType=INTEGER}
    </delete>

</mapper> 
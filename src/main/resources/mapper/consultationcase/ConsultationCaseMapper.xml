<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.consultationcase.ConsultationCaseDao">
    <!-- 添加心理咨询案例 -->
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_consultation_case (
            consultation_date, consultation_form, consultation_duration, consultation_type,
            visitor_gender, visitor_age, marital_status, has_children, consultation_field,
            problem_summary, consultation_keywords, is_workplace_issue, workplace_description,
            has_psychological_risk, risk_level, risk_description, follow_up_suggestion, other_suggestion,
            counselor, create_time, is_valid,user_id, struct_id
        )
        values (
            #{consultationDate,jdbcType=TIMESTAMP},
            #{consultationForm,jdbcType=INTEGER},
            #{consultationDuration,jdbcType=INTEGER},
            #{consultationType,jdbcType=INTEGER},
            #{visitorGender,jdbcType=VARCHAR},
            #{visitorAge,jdbcType=INTEGER},
            #{maritalStatus,jdbcType=INTEGER},
            #{hasChildren,jdbcType=INTEGER},
            #{consultationField,jdbcType=INTEGER},
            #{problemSummary,jdbcType=VARCHAR},
            #{consultationKeywords,jdbcType=VARCHAR},
            #{isWorkplaceIssue,jdbcType=BOOLEAN},
            #{workplaceDescription,jdbcType=VARCHAR},
            #{hasPsychologicalRisk,jdbcType=BOOLEAN},
            #{riskLevel,jdbcType=INTEGER},
            #{riskDescription,jdbcType=VARCHAR},
            #{followUpSuggestion,jdbcType=INTEGER},
            #{otherSuggestion,jdbcType=VARCHAR},
            #{counselor,jdbcType=INTEGER},
            #{createTime,jdbcType=TIMESTAMP},
            1,
            #{userId,jdbcType=INTEGER},
            #{structId,jdbcType=INTEGER}
        )
    </insert>

    <!-- 修改心理咨询案例 -->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseEntity">
        update psycloud_consultation_case
        set
            user_id = #{userId,jdbcType=INTEGER},
            consultation_date = #{consultationDate,jdbcType=TIMESTAMP},
            consultation_form = #{consultationForm,jdbcType=INTEGER},
            consultation_duration = #{consultationDuration,jdbcType=INTEGER},
            consultation_type = #{consultationType,jdbcType=INTEGER},
            visitor_gender = #{visitorGender,jdbcType=VARCHAR},
            visitor_age = #{visitorAge,jdbcType=INTEGER},
            marital_status = #{maritalStatus,jdbcType=INTEGER},
            has_children = #{hasChildren,jdbcType=INTEGER},
            consultation_field = #{consultationField,jdbcType=INTEGER},
            problem_summary = #{problemSummary,jdbcType=VARCHAR},
            consultation_keywords = #{consultationKeywords,jdbcType=VARCHAR},
            is_workplace_issue = #{isWorkplaceIssue,jdbcType=BOOLEAN},
            workplace_description = #{workplaceDescription,jdbcType=VARCHAR},
            has_psychological_risk = #{hasPsychologicalRisk,jdbcType=BOOLEAN},
            risk_level = #{riskLevel,jdbcType=INTEGER},
            risk_description = #{riskDescription,jdbcType=VARCHAR},
            follow_up_suggestion = #{followUpSuggestion,jdbcType=INTEGER},
            other_suggestion = #{otherSuggestion,jdbcType=VARCHAR},
            struct_id = #{structId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 删除心理咨询案例 -->
    <update id="delete" parameterType="java.lang.Integer">
        update psycloud_consultation_case
        set is_valid = 0
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据条件查询心理咨询案例列表 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto">
        select
            c.id,
            c.consultation_date,
            c.consultation_form,
            c.consultation_duration,
            c.consultation_type,
            c.visitor_gender,
            c.visitor_age,
            c.marital_status,
            c.has_children,
            c.consultation_field,
            c.problem_summary,
            c.consultation_keywords,
            c.is_workplace_issue,
            c.workplace_description,
            c.has_psychological_risk,
            c.risk_level,
            c.risk_description,
            c.follow_up_suggestion,
            c.other_suggestion,
            c.counselor as counselor,
            u.real_name as counselorName,
            c.create_time,
            c.is_valid,
            c.user_id,
            pu.login_name,
            u2.real_name,
            c.struct_id,
            ps.struct_name
        from psycloud_consultation_case c
            inner join psycloud_user_info u on u.user_id = c.counselor
            left join psycloud_user pu on pu.user_id = c.user_id
            left join psycloud_user_info u2 on u2.user_id = c.user_id
            left join psycloud_structs ps on ps.id = c.struct_id
        where c.is_valid = 1
        <if test="counselor!=null and counselor!=0">
            and c.counselor = #{counselor,jdbcType=INTEGER}
        </if>
        <if test="consultationDate!=null">
            and c.consultation_date = #{consultationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="consultationForm!=null">
            and c.consultation_form = #{consultationForm,jdbcType=INTEGER}
        </if>
        <if test="consultationType!=null">
            and c.consultation_type = #{consultationType,jdbcType=INTEGER}
        </if>
        <if test="visitorGender!=null and visitorGender!=''">
            and c.visitor_gender = #{visitorGender,jdbcType=VARCHAR}
        </if>
        <if test="consultationField!=null">
            and c.consultation_field = #{consultationField,jdbcType=INTEGER}
        </if>
        <if test="hasChildren!=null">
            and c.has_children = #{hasChildren,jdbcType=INTEGER}
        </if>
        <if test="maritalStatus!=null">
            and c.marital_status = #{maritalStatus,jdbcType=INTEGER}
        </if>
        <if test="visitorAge!=null">
            and c.visitor_age = #{visitorAge,jdbcType=INTEGER}
        </if>
        <if test="isWorkplaceIssue!=null">
            and c.is_workplace_issue = #{isWorkplaceIssue,jdbcType=INTEGER}
        </if>
        <if test="hasPsychologicalRisk!=null">
            and c.has_psychological_risk = #{hasPsychologicalRisk,jdbcType=INTEGER}
        </if>
        <if test="loginName!=null and loginName!=''">
            and pu.login_name like CONCAT('%',#{loginName},'%')
        </if>
        <if test="realName!=null and realName!=''">
            and u2.real_name like CONCAT('%',#{realName},'%')
        </if>
        <if test="userId!=null and userId!=0">
            and c.user_id = #{userId,jdbcType=INTEGER}
        </if>
        <if test="riskLevel!=null">
            and c.risk_level = #{riskLevel,jdbcType=INTEGER}
        </if>
        <!--<if test="structId!=null and structId!=0">
            and c.struct_id = #{structId,jdbcType=INTEGER}
        </if>-->
        <if test="childStructs!=null and childStructs.size()>0">
            and u2.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by c.create_time desc
    </select>

    <select id="getExportList" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="cn.psycloud.psyplatform.dto.consultationcase.ExportConsultationCaseDto">
        select
            ps.struct_name,
            u.real_name as counselorName,
            pu.login_name,
            u2.real_name,
            c.consultation_date,
            case c.consultation_form when 1 then '驻场咨询' when 2 then '线上咨询' when 3 then '门店咨询' end as consultation_form,
            c.consultation_duration,
            case c.consultation_type when 1 then '首次咨询' when 2 then '第二次咨询' when 3 then '第三次咨询' when 4 then '第四次咨询' when 5 then '第五次咨询' when 6 then '第六次及以上咨询' end as consultation_type,
            c.visitor_gender,
            case c.visitor_age when 1 then '20岁及以下' when 2 then '21-25岁' when 3 then '26-30岁' when 4 then '31-35岁' when 5 then '36-40岁' when 6 then '41-45岁' when 7 then '46-50岁' when 8 then '50岁以上' end as visitor_age,
            case c.marital_status when 1 then '未婚' when 2 then '已婚' end as marital_status,
            case c.has_children when 1 then '有' when 2 then '无' end as has_children,
            case c.consultation_field when 1 then '心理健康' when 2 then '情绪压力' when 3 then '人际关系' when 4 then '恋爱情感' when 5 then '家庭关系' when 6 then '亲子教育' when 7 then '职场发展' when 8 then '个人成长' end as consultation_field,
            c.problem_summary,
            c.consultation_keywords,
            case c.is_workplace_issue when 0 then '否' when 1 then '是' end as is_workplace_issue,
            c.workplace_description,
            case c.has_psychological_risk when 0 then '无' when 1 then '有' end as has_psychological_risk,
            case c.risk_level when 0 then '无风险' when 1 then '低风险' when 2 then '中风险' when 3 then '高风险' end as risk_level,
            c.risk_description,
            c.follow_up_suggestion,
            c.other_suggestion,
            c.create_time
        from psycloud_consultation_case c
        inner join psycloud_user_info u on u.user_id = c.counselor
        left join psycloud_user pu on pu.user_id = c.user_id
        left join psycloud_user_info u2 on u2.user_id = c.user_id
        left join psycloud_structs ps on ps.id = c.struct_id
        where c.is_valid = 1
        <if test="counselor!=null and counselor!=0">
            and c.counselor = #{counselor,jdbcType=INTEGER}
        </if>
        <if test="consultationDate!=null">
            and c.consultation_date = #{consultationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="consultationForm!=null">
            and c.consultation_form = #{consultationForm,jdbcType=INTEGER}
        </if>
        <if test="consultationType!=null">
            and c.consultation_type = #{consultationType,jdbcType=INTEGER}
        </if>
        <if test="visitorGender!=null and visitorGender!=''">
            and c.visitor_gender = #{visitorGender,jdbcType=VARCHAR}
        </if>
        <if test="consultationField!=null">
            and c.consultation_field = #{consultationField,jdbcType=INTEGER}
        </if>
        <if test="hasChildren!=null">
            and c.has_children = #{hasChildren,jdbcType=INTEGER}
        </if>
        <if test="maritalStatus!=null">
            and c.marital_status = #{maritalStatus,jdbcType=INTEGER}
        </if>
        <if test="visitorAge!=null">
            and c.visitor_age = #{visitorAge,jdbcType=INTEGER}
        </if>
        <if test="isWorkplaceIssue!=null">
            and c.is_workplace_issue = #{isWorkplaceIssue,jdbcType=INTEGER}
        </if>
        <if test="hasPsychologicalRisk!=null">
            and c.has_psychological_risk = #{hasPsychologicalRisk,jdbcType=INTEGER}
        </if>
        <if test="loginName!=null and loginName!=''">
            and pu.login_name like CONCAT('%',#{loginName},'%')
        </if>
        <if test="realName!=null and realName!=''">
            and u2.real_name like CONCAT('%',#{realName},'%')
        </if>
        <if test="userId!=null and userId!=0">
            and c.user_id = #{userId,jdbcType=INTEGER}
        </if>
        <if test="riskLevel!=null">
            and c.risk_level = #{riskLevel,jdbcType=INTEGER}
        </if>
        <!--<if test="structId!=null and structId!=0">
            and c.struct_id = #{structId,jdbcType=INTEGER}
        </if>-->
        <if test="childStructs!=null and childStructs.size()>0">
            and u2.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by c.create_time desc
    </select>

    <!-- 获取我的个案集合 -->
    <select id="getMyCases" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto">
        select
            c.id,
            c.consultation_date,
            c.consultation_form,
            c.consultation_duration,
            c.consultation_type,
            c.visitor_gender,
            c.visitor_age,
            c.marital_status,
            c.has_children,
            c.consultation_field,
            c.problem_summary,
            c.consultation_keywords,
            c.is_workplace_issue,
            c.workplace_description,
            c.has_psychological_risk,
            c.risk_level,
            c.risk_description,
            c.follow_up_suggestion,
            c.other_suggestion,
            c.create_time,
            c.is_valid,
            c.user_id,
            pu.login_name,
            u2.real_name,
            c.struct_id,
            ps.struct_name
        from psycloud_consultation_case c
            inner join psycloud_user_info u on u.user_id = c.counselor
            left join psycloud_user pu on pu.user_id = c.user_id
            left join psycloud_user_info u2 on u2.user_id = c.user_id
            left join psycloud_structs ps on ps.id = c.struct_id
        where c.is_valid = 1
            and c.counselor = #{userId,jdbcType=INTEGER}
        <if test="consultationDate!=null">
            and c.consultation_date = #{consultationDate,jdbcType=TIMESTAMP}
        </if>
        <if test="consultationForm!=null">
            and c.consultation_form = #{consultationForm,jdbcType=INTEGER}
        </if>
        <if test="consultationType!=null">
            and c.consultation_type = #{consultationType,jdbcType=INTEGER}
        </if>
        <if test="visitorGender!=null and visitorGender!=''">
            and c.visitor_gender = #{visitorGender,jdbcType=VARCHAR}
        </if>
        <if test="consultationField!=null">
            and c.consultation_field = #{consultationField,jdbcType=INTEGER}
        </if>
        <if test="hasChildren!=null">
            and c.has_children = #{hasChildren,jdbcType=INTEGER}
        </if>
        <if test="maritalStatus!=null">
            and c.marital_status = #{maritalStatus,jdbcType=INTEGER}
        </if>
        <if test="visitorAge!=null">
            and c.visitor_age = #{visitorAge,jdbcType=INTEGER}
        </if>
        <if test="isWorkplaceIssue!=null">
            and c.is_workplace_issue = #{isWorkplaceIssue,jdbcType=INTEGER}
        </if>
        <if test="hasPsychologicalRisk!=null">
            and c.has_psychological_risk = #{hasPsychologicalRisk,jdbcType=INTEGER}
        </if>
        <if test="riskLevel!=null">
            and c.risk_level = #{riskLevel,jdbcType=INTEGER}
        </if>
        <if test="loginName!=null and loginName!=''">
            and pu.login_name like CONCAT('%',#{loginName},'%')
        </if>
        <if test="realName!=null and realName!=''">
            and u2.real_name like CONCAT('%',#{realName},'%')
        </if>
        order by c.create_time desc
    </select>

    <!-- 根据id查询心理咨询案例 -->
    <select id="getById" parameterType="map" resultType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto">
        select
            c.id,
            c.consultation_date,
            c.consultation_form,
            c.consultation_duration,
            c.consultation_type,
            c.visitor_gender,
            c.visitor_age,
            c.marital_status,
            c.has_children,
            c.consultation_field,
            c.problem_summary,
            c.consultation_keywords,
            c.is_workplace_issue,
            c.workplace_description,
            c.has_psychological_risk,
            c.risk_level,
            c.risk_description,
            c.follow_up_suggestion,
            c.other_suggestion,
            c.counselor as counselor,
            u.real_name as counselorName,
            c.create_time,
            c.user_id,
            pu.login_name,
            u2.real_name,
            c.struct_id,
            ps.struct_name
        from psycloud_consultation_case c
            inner join psycloud_user_info u on u.user_id = c.counselor
            left join psycloud_user pu on pu.user_id = c.user_id
            left join psycloud_user_info u2 on u2.user_id = c.user_id
            left join psycloud_structs ps on ps.id = c.struct_id
        where c.id = #{id,jdbcType=INTEGER}
        <if test="counselor!=null and counselor!=0">
            and c.counselor = #{counselor,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 按部门统计咨询个案数量 -->
    <select id="getStatByStruct" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            s.struct_name as structName,
            (
                select count(*)
                from psycloud_consultation_case c
                    inner join psycloud_user_info u on u.user_id = c.user_id
                where c.is_valid = 1 
                    and (u.struct_id = s.id 
                        or u.struct_id in (
                            select ss.id from psycloud_structs ss 
                            where ss.parent_id = s.id and ss.is_valid = 1
                        )
                        <if test="childStructs!=null and childStructs.size()>0">
                            or u.struct_id in
                            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    )
                <if test="startDate != null">
                    and DATE(c.consultation_date) >= DATE(#{startDate})
                </if>
                <if test="endDate != null">
                    and DATE(c.consultation_date) &lt;= DATE(#{endDate})
                </if>
            ) as count
        from psycloud_structs s
        where s.parent_id = #{structId} and s.is_valid = 1
        order by count desc, s.struct_name
    </select>

    <!-- 按咨询形式统计 -->
    <select id="getStatByConsultationForm" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.consultation_form
                when 1 then '驻场咨询'
                when 2 then '线上咨询'
                when 3 then '门店咨询'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.consultation_form
        order by value desc
    </select>

    <!-- 按咨询类型统计 -->
    <select id="getStatByConsultationType" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.consultation_type
                when 1 then '首次咨询'
                when 2 then '第二次咨询'
                when 3 then '第三次咨询'
                when 4 then '第四次咨询'
                when 5 then '第五次咨询'
                when 6 then '第六次及以上咨询'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.consultation_type
        order by value desc
    </select>

    <!-- 按性别统计 -->
    <select id="getStatByGender" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            c.visitor_gender as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1 and c.visitor_gender is not null
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.visitor_gender
        order by value desc
    </select>

    <!-- 按年龄统计 -->
    <select id="getStatByAge" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.visitor_age
                when 1 then '20岁及以下'
                when 2 then '21-25岁'
                when 3 then '26-30岁'
                when 4 then '31-35岁'
                when 5 then '36-40岁'
                when 6 then '41-45岁'
                when 7 then '46-50岁'
                when 8 then '50岁以上'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1 and c.visitor_age is not null
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.visitor_age
        order by value desc
    </select>

    <!-- 按婚姻状态统计 -->
    <select id="getStatByMaritalStatus" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.marital_status
                when 1 then '未婚'
                when 2 then '已婚'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1 and c.marital_status is not null
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.marital_status
        order by value desc
    </select>

    <!-- 按有无子女统计 -->
    <select id="getStatByHasChildren" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.has_children
                when 1 then '有子女'
                when 2 then '无子女'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1 and c.has_children is not null
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.has_children
        order by value desc
    </select>

    <!-- 按咨询领域统计 -->
    <select id="getStatByConsultationField" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.consultation_field
                when 1 then '心理健康'
                when 2 then '情绪压力'
                when 3 then '人际关系'
                when 4 then '恋爱情感'
                when 5 then '家庭关系'
                when 6 then '亲子教育'
                when 7 then '职场发展'
                when 8 then '个人成长'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1 and c.consultation_field is not null
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.consultation_field
        order by value desc
    </select>

    <!-- 按咨询关键词统计 -->
    <select id="getStatByKeywords" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            cck.keyword as keyword,
            count(*) as count
        from psycloud_consultation_case_keywords cck
        inner join psycloud_consultation_case c on c.id = cck.case_id
        where c.is_valid = 1
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by cck.keyword
        order by count desc
        limit 20
    </select>

    <!-- 职场类问题统计 -->
    <select id="getStatByWorkplaceIssue" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.is_workplace_issue
                when 1 then '职场类问题'
                when 0 then '非职场类问题'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1 and c.is_workplace_issue is not null
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.is_workplace_issue
        order by value desc
    </select>

    <!-- 心理风险统计 -->
    <select id="getStatByPsychologicalRisk" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.has_psychological_risk
                when 1 then '有心理风险'
                when 0 then '无心理风险'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1 and c.has_psychological_risk is not null
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.has_psychological_risk
        order by value desc
    </select>

    <!-- 风险等级统计 -->
    <select id="getStatByRiskLevel" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.util.HashMap">
        select
            case c.risk_level
                when 3 then '高风险'
                when 2 then '中风险'
                when 1 then '低风险'
                when 0 then '无风险'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_consultation_case c
        where c.is_valid = 1 and c.risk_level is not null
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
        group by c.risk_level
        order by value desc
    </select>

    <!-- 获取总个案数 -->
    <select id="getTotalCases" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.lang.Integer">
        select count(*) as totalCases
        from psycloud_consultation_case c
        where c.is_valid = 1
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
        <if test="startDate != null">
            and DATE(c.consultation_date) >= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            and DATE(c.consultation_date) &lt;= DATE(#{endDate})
        </if>
    </select>

    <!-- 获取今日新增个案数 -->
    <select id="getTodayCases" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.lang.Integer">
        select count(*) as todayCases
        from psycloud_consultation_case c
        where c.is_valid = 1
        and DATE(c.consultation_date) = CURDATE()
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
    </select>

    <!-- 获取本周新增个案数 -->
    <select id="getWeekCases" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.lang.Integer">
        select count(*) as weekCases
        from psycloud_consultation_case c
        where c.is_valid = 1
        and YEARWEEK(c.consultation_date, 1) = YEARWEEK(CURDATE(), 1)
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
    </select>

    <!-- 获取日均个案数（最近30天） -->
    <select id="getAvgCases" parameterType="cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto" resultType="java.lang.Integer">
        select ROUND(count(*) / 30) as avgCases
        from psycloud_consultation_case c
        where c.is_valid = 1
        and c.consultation_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        <if test="structId != null and structId != 0">
            and c.struct_id = #{structId}
        </if>
    </select>
</mapper>
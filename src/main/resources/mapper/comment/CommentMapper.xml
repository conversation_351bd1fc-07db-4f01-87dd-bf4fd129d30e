<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.comment.CommentDao">
    <!-- 添加评论 -->
    <insert id="addComment" parameterType="cn.psycloud.psyplatform.entity.comment.CommentEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_comment (
            function_type,
            product_id,
            user_id,
            comment_content,
            comment_date,
            is_checked,
            is_valid
        )
        values  (
            #{functionType,jdbcType=INTEGER},
            #{productId,jdbcType=INTEGER},
            #{userId,jdbcType=INTEGER},
            #{commentContent,jdbcType=VARCHAR},
            #{commentDate,jdbcType=TIMESTAMP},
            0,
            1
        )
    </insert>
    <!-- 删除评论 -->
    <update id="deleteComment" parameterType="Integer">
        update
            psycloud_comment
        set is_valid = 0
        where id = #{id}
    </update>
    <!--审核评论 -->
    <update id="checkComment" parameterType="Integer">
        update psycloud_comment set is_checked = 1 where id = #{id}
    </update>

    <!-- 获取咨询师评论 -->
    <select id="getCommentsForCounselorInfo" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.comment.CommentDto">
        select
        ptc.id,
        ptc.function_type,
        ptc.product_id,
        pui2.real_name as product_name,
        ptc.comment_content,
        ptc.comment_date,
        pu.login_name,
        pui.real_name,
        pui.head_img,
        ptc.is_checked,
        ptc.is_valid
        from psycloud_comment ptc
            inner join psycloud_user pu on pu.user_id = ptc.user_id
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
            left join psycloud_user_info pui2 on pui2.user_id = ptc.product_id
        where ptc.is_valid = 1
          and pui.is_valid = 1
          and pui2.is_valid=1
          and ptc.function_type = 4
          and ptc.is_checked = 1
        order by ptc.comment_date desc
    </select>
</mapper>
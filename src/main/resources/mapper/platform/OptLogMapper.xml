<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.OptLogDao">
    <!-- 保存操作日志 -->
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.platform.OptLogEntity">
        insert into psycloud_opt_log(
            user_id,
            opt_date,
            opt_url,
            opt_args,
            opt_classname,
            opt_method
        )
        values (
            #{userId,jdbcType=INTEGER},
            #{optDate,jdbcType=TIMESTAMP},
            #{optUrl,jdbcType=VARCHAR},
            #{optArgs,jdbcType=VARCHAR},
            #{optClassname,jdbcType=VARCHAR},
            #{optMethod,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 获取操作日志记录集合 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.platform.OptLogDto" resultType="cn.psycloud.psyplatform.entity.platform.OptLogEntity">
        select
            id,
            user_id,
            opt_date,
            opt_url,
            opt_args,
            opt_classname,
            opt_method
        from psycloud_opt_log
        <where>
        <if test="startTime !=null and endTime !=null">and opt_date &gt;= #{startTime} and opt_date &lt;= #{endTime}</if>
        </where>
        order by opt_date desc
    </select>

    <!-- 导出操作记录 -->
    <select id="getExportList" parameterType="cn.psycloud.psyplatform.dto.platform.OptLogDto" resultType="cn.psycloud.psyplatform.dto.platform.ExportOptLogDto">
        select
        id,
        user_id,
        opt_date,
        opt_url,
        opt_args,
        opt_classname,
        opt_method
        from psycloud_opt_log
        <where>
            <if test="startTime !=null and endTime !=null">and opt_date &gt;= #{startTime} and opt_date &lt;= #{endTime}</if>
        </where>
        order by opt_date desc
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.DashboardDao">
    <!-- 获取看板数据的主查询  -->
    <select id="getDashboardData" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="cn.psycloud.psyplatform.dto.platform.DashboardDto">
        select
            cs.total_consultations as totalConsultations,
            ROUND((cs.risk_count * 100.0 / case when cs.total_consultations = 0 then 1 else cs.total_consultations end), 2) as riskConsultationRate,
            ast.total_activities as totalActivities,
            ts.total_tests as totalTests,
            ts.completed_tests as completedTestsCount,
            ROUND((ts.completed_tests * 100.0 / case when ts.total_tests = 0 then 1 else ts.total_tests end), 2) as testCompletionRate
        from
        (
            select
                count(*) as total_consultations,
                sum(case when has_psychological_risk = 1 then 1 else 0 end) as risk_count
            from psycloud_consultation_case
            where is_valid = 1
            <if test="structId != null">
                and struct_id = #{structId}
            </if>
            <if test="startDate != null and startDate != ''">
                and consultation_date >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and consultation_date &lt;= CONCAT(#{endDate}, ' 23:59:59')
            </if>
        ) cs,
        (
            select count(*) as total_activities
            from psycloud_activity
            where is_valid = 1
            <if test="structId != null">
                and struct_id = #{structId}
            </if>
            <if test="startDate != null and startDate != ''">
                and start_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
            </if>
        ) ast,
        (
            select
                count(*) as total_tests,
                sum(case when tr.state = 1 then 1 else 0 end) as completed_tests
            from psycloud_test_record tr
            inner join psycloud_user_info u on tr.user_id = u.user_id
            where tr.is_valid = 1
            <if test="startDate != null and startDate != ''">
                and tr.start_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and tr.start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
            </if>
            <if test="childStructs!=null and childStructs.size()>0">
                and u.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        ) ts
    </select>

    <!-- 获取咨询形式分布  -->
    <select id="getConsultationsByForm" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="java.util.Map">
        select
            case consultation_form
                when 1 then '驻场咨询'
                when 2 then '线上咨询'
                when 3 then '门店咨询'
                else '其他'
            end as name,
            count(*) as value
        from psycloud_consultation_case
        where is_valid = 1
        <if test="structId != null">
            and struct_id = #{structId}
        </if>
        <if test="startDate != null and startDate != ''">
            and consultation_date >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and consultation_date &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        group by consultation_form
        order by value desc
    </select>

    <!-- 获取咨询领域分布 -->
    <select id="getConsultationsByField" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="java.util.Map">
        select
            case consultation_field
                when 1 then '心理健康'
                when 2 then '情绪压力'
                when 3 then '人际关系'
                when 4 then '恋爱情感'
                when 5 then '家庭关系'
                when 6 then '亲子教育'
                when 7 then '职场发展'
                when 8 then '个人成长'
                else '其他'
            end as name,
            count(*) as value
        from psycloud_consultation_case
        where is_valid = 1
        <if test="structId != null">
            and struct_id = #{structId}
        </if>
        <if test="startDate != null and startDate != ''">
            and consultation_date >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and consultation_date &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        group by consultation_field
        order by value desc
    </select>

    <!-- 获取心理风险分布 -->
    <select id="getConsultationsByRisk" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="java.util.Map">
        select
            case has_psychological_risk
                when 1 then '有心理风险'
                when 0 then '无心理风险'
                else '未评估'
            end as name,
            count(*) as value
        from psycloud_consultation_case
        where is_valid = 1
        <if test="structId != null">
            and struct_id = #{structId}
        </if>
        <if test="startDate != null and startDate != ''">
            and consultation_date >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and consultation_date &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        group by has_psychological_risk
        order by value desc
    </select>

    <!-- 获取咨询量趋势(按月) -->
    <select id="getConsultationsTrend" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="java.util.Map">
        select
            date_format(consultation_date, '%Y-%m') as month,
            count(*) as value
        from psycloud_consultation_case
        where is_valid = 1
        <if test="structId != null">
            and struct_id = #{structId}
        </if>
        <if test="startDate != null and startDate != ''">
            and consultation_date >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and consultation_date &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        group by date_format(consultation_date, '%Y-%m')
        order by month asc
        LIMIT 12
    </select>

    <!-- 获取活动类型分布 -->
    <select id="getActivitiesByType" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="java.util.Map">
        select
            case activity_type
                when 1 then '驻场轻咨询'
                when 2 then '驻场咨询50分钟以上'
                when 3 then '团体辅导'
                when 4 then '心理关爱活动'
                else '其他活动'
            end as name,
            count(*) as value
        from psycloud_activity
        where is_valid = 1
        <if test="structId != null">
            and struct_id = #{structId}
        </if>
        <if test="startDate != null and startDate != ''">
            and start_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        group by activity_type
        order by value desc
    </select>

    <!-- 获取活动量趋势(按月) -->
    <select id="getActivitiesTrend" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="java.util.Map">
        select
            date_format(start_time, '%Y-%m') as month,
            count(*) as value
        from psycloud_activity
        where is_valid = 1
        <if test="structId != null">
            and struct_id = #{structId}
        </if>
        <if test="startDate != null and startDate != ''">
            and start_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        group by date_format(start_time, '%Y-%m')
        order by month asc
        LIMIT 12
    </select>

    <!-- 获取测评预警等级分布 -->
    <select id="getTestsByWarningLevel" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="java.util.Map">
        select
            case ts.warning_level
                when 1 then '绿码'
                when 2 then '蓝码'
                when 3 then '黄码'
                when 4 then '橙码'
                when 5 then '红码'
                else '未知'
            end as name,
            count(*) as value
        from psycloud_test_score ts
            inner join psycloud_test_record tr on ts.record_id = tr.id
            inner join psycloud_user_info u on tr.user_id = u.user_id
        where tr.is_valid = 1 and tr.state = 1
        <if test="startDate != null and startDate != ''">
            and tr.start_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and tr.start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        <if test="childStructs!=null and childStructs.size()>0">
            and u.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by ts.warning_level
        order by value desc
    </select>

    <!-- 获取测评量趋势(按月) -->
    <select id="getTestsTrend" parameterType="cn.psycloud.psyplatform.dto.platform.DashboardDto" resultType="java.util.Map">
        select
            date_format(tr.start_time, '%Y-%m') as month,
            count(*) as value
        from psycloud_test_record tr
            inner join psycloud_user_info u on tr.user_id = u.user_id
        where tr.is_valid = 1 and tr.state = 1
        <if test="startDate != null and startDate != ''">
            and tr.start_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and tr.start_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        <if test="childStructs!=null and childStructs.size()>0">
            and u.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by DATE_FORMAT(tr.start_time, '%Y-%m')
        order by month asc
        LIMIT 12
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.RoleDao">
    <resultMap id="roleResultMap" type="cn.psycloud.psyplatform.dto.anteroom.RoleDto">
        <result column="id" property="roleId" jdbcType="INTEGER" />
        <result column="role_name" property="roleName" jdbcType="VARCHAR" />
        <result column="add_date" property="addDate" jdbcType="TIMESTAMP" />
        <result column="operator" property="operator" jdbcType="INTEGER" />
        <result column="flag" property="flag" jdbcType="CHAR" />
        <result column="is_valid" property="isValid" jdbcType="TINYINT" />
    </resultMap>

    <sql id="role_field_list">id,role_name,add_date,operator,flag,is_valid</sql>

    <sql id="tb">psycloud_role</sql>

    <select id="getList" resultMap="roleResultMap" parameterType="cn.psycloud.psyplatform.dto.anteroom.RoleDto">
        select <include refid="role_field_list" />
        from <include refid="tb" />
        <where>
            is_valid = 1
            <if test="roleName!=null and roleName!=''">
                and role_name like CONCAT('%',#{roleName},'%')
            </if>
            <if test="flag != null and flag != ''">
                and flag = #{flag}
            </if>
        </where>
    </select>

    <update id="delete" parameterType="java.lang.Integer">
        update <include refid="tb" />
        set
        is_valid = 0
        where id = #{id}
    </update>

    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.anteroom.RoleEntity" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tb" />(
        role_name,
        add_date,
        operator,
        flag,
        is_valid
        )
        values(
        #{roleName,jdbcType=VARCHAR},
        now(),
        #{operator ,jdbcType=INTEGER},
        #{flag,jdbcType=VARCHAR},
        1
        )
    </insert>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.anteroom.RoleEntity">
        update <include refid="tb"/>
        set role_name = #{roleName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="addGrant" parameterType="map">
        insert into psycloud_grant(
            role_id,
            function_code
        )
        values (
                   #{roleId},
                   #{funCode}
               )
    </insert>

    <delete id="clearGrant" parameterType="Integer">
        delete from psycloud_grant where role_id = #{roleId}
    </delete>
</mapper>
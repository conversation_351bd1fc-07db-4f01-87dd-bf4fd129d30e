<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.UserLoginLogDao">
    <insert id="add" parameterType="cn.psycloud.psyplatform.entity.platform.UserLoginLogEntity">
        <!-- 保存登录日志 -->
        insert into psycloud_user_login_logs(
            login_name,
            login_date,
            ip_address,
            device_info,
            state,
            login_way
        )
        values (
            #{loginName,jdbcType=VARCHAR},
            #{loginDate,jdbcType=TIMESTAMP},
            #{ipAddress,jdbcType=VARCHAR},
            #{deviceInfo,jdbcType=VARCHAR},
            #{state,jdbcType=TINYINT},
            #{loginWay,jdbcType=TINYINT}
        )
    </insert>

    <!-- 获取登录日志集合 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.platform.UserLoginLogDto" resultType="cn.psycloud.psyplatform.entity.platform.UserLoginLogEntity">
        select
            id,
            login_name,
            login_date,
            ip_address,
            device_info,
            state,
            login_way
        from psycloud_user_login_logs
        <where>
            <if test="state !=null">and state = #{state}</if>
            <if test="loginWay !=null">and login_way = #{loginWay}</if>
            <if test="startTime !=null and endTime !=null">and login_date &gt;= #{startTime} and login_date &lt;= #{endTime}</if>
        </where>
        order by login_date desc
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.SmsConfigDao">
    <resultMap id="smsConfigResultMap" type="cn.psycloud.psyplatform.entity.platform.SmsConfigEntity">
        <result column="service_name" property="serviceName" jdbcType="VARCHAR"  />
        <result column="sign_name" property="signName" jdbcType="VARCHAR"  />
        <result column="is_login" property="isLogin" jdbcType="INTEGER"  />
        <result column="login_template" property="loginTemplate" jdbcType="VARCHAR"  />
        <result column="is_register" property="isRegister" jdbcType="INTEGER"  />
        <result column="register_template" property="registerTemplate" jdbcType="VARCHAR"  />
        <result column="is_mobile_bind" property="isMobileBind" jdbcType="INTEGER"  />
        <result column="mobile_bind_template" property="mobileBindTemplate" jdbcType="VARCHAR"  />
        <result column="is_modify_pwd" property="isModifyPwd" jdbcType="INTEGER"  />
        <result column="modify_pwd_template" property="modifyPwdTemplate" jdbcType="VARCHAR"  />
        <result column="is_measuring_notify" property="isMeasuringNotify" jdbcType="INTEGER"  />
        <result column="measuring_notify_template" property="measuringNotifyTemplate" jdbcType="VARCHAR"  />
        <result column="is_counseling_order" property="isCounselingOrder" jdbcType="INTEGER"  />
        <result column="counselor_counseling_success_template" property="counselorCounselingSuccessTemplate" jdbcType="VARCHAR"  />
        <result column="counselor_counseling_change_template" property="visitorCounselingSuccessTemplate" jdbcType="VARCHAR"  />
        <result column="visitor_counseling_success_template" property="counselorCounselingChangeTemplate" jdbcType="VARCHAR"  />
        <result column="visitor_counseling_change_template" property="visitorCounselingChangeTemplate" jdbcType="VARCHAR"  />
        <result column="is_trainingcamp_start" property="isTrainingCampStart" jdbcType="INTEGER"/>
        <result column="trainingcamp_start_template" property="trainingCampStartTemplate" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tb">psycloud_sms_config</sql>

    <select id="get" resultMap="smsConfigResultMap">
        select
            service_name,
            sign_name,
            is_login,
            login_template ,
            is_register,
            register_template ,
            is_mobile_bind ,
            mobile_bind_template,
            is_modify_pwd,
            modify_pwd_template ,
            is_measuring_notify,
            measuring_notify_template,
            is_counseling_order ,
            counselor_counseling_success_template,
            counselor_counseling_change_template ,
            visitor_counseling_success_template ,
            visitor_counseling_change_template,
            is_trainingcamp_start,
            trainingcamp_start_template
        from <include refid="tb" />
    </select>

    <update id="save" parameterType="cn.psycloud.psyplatform.entity.platform.SmsConfigEntity">
        update <include refid="tb" />
        set
        service_name= #{serviceName},
        sign_name = #{signName},
        is_login = #{isLogin},
        login_template = #{loginTemplate},
        is_register = #{isRegister },
        register_template = #{registerTemplate},
        is_mobile_bind = #{isMobileBind },
        mobile_bind_template = #{mobileBindTemplate},
        is_modify_pwd = #{isModifyPwd},
        modify_pwd_template = #{modifyPwdTemplate},
        is_measuring_notify = #{isMeasuringNotify},
        measuring_notify_template = #{measuringNotifyTemplate},
        is_counseling_order = #{isCounselingOrder},
        counselor_counseling_success_template = #{counselorCounselingSuccessTemplate},
        counselor_counseling_change_template = #{visitorCounselingSuccessTemplate},
        visitor_counseling_success_template = #{counselorCounselingChangeTemplate},
        visitor_counseling_change_template = #{visitorCounselingChangeTemplate},
        is_trainingcamp_start = #{isTrainingCampStart},
        trainingcamp_start_template = #{trainingCampStartTemplate}
    </update>
</mapper>
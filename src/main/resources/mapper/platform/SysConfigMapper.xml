<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.SysConfigDao">
    <resultMap id="sysConfigResultMap" type="cn.psycloud.psyplatform.dto.platform.SysConfigDto">
        <result column="platform_name" property="platformName" jdbcType="VARCHAR" />
        <result column="org_name" property="orgName" jdbcType="VARCHAR" />
        <result column="is_open_reg" property="isOpenReg" jdbcType="TINYINT" />
        <result column="is_reg_checked" property="isRegChecked" jdbcType="TINYINT" />
        <result column="is_abnormal_notify" property="isAbnormalNotify" jdbcType="TINYINT" />
        <result column="is_counseling_notify" property="isCounselingNotify" jdbcType="TINYINT" />
        <result column="is_sms_enabled" property="isSmsEnabled" jdbcType="TINYINT" />
        <result column="is_points_enabled" property="isPointsEnabled" jdbcType="TINYINT" />
        <result column="is_wechat_enabled" property="isWeChatEnabled" jdbcType="TINYINT" />
        <result column="is_mobile_login_enabled" property="isMobileLoginEnabled" jdbcType="TINYINT" />
        <result column="install_date" property="installDate" jdbcType="TIMESTAMP" />
        <result column="counseling_hotline" property="counselingHotline" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="sysConfig_field_list">
        platform_name,
        org_name,
        is_open_reg,
        is_reg_checked,
        is_abnormal_notify,
        is_counseling_notify,
        is_sms_enabled,
        is_points_enabled,
        is_wechat_enabled,
        install_date,
        is_mobile_login_enabled,
        counseling_hotline
    </sql>
    <sql id="tb">psycloud_sys_config</sql>

    <select id="get" resultMap="sysConfigResultMap">
        select
        <include refid="sysConfig_field_list"/>
        from
        <include refid="tb" />
    </select>

    <update id="update" parameterType="cn.psycloud.psyplatform.entity.platform.SysConfigEntity">
        update <include refid="tb" />
        set
        platform_name = #{platformName,jdbcType=VARCHAR},
        org_name = #{orgName,jdbcType=VARCHAR},
        is_open_reg = #{isOpenReg,jdbcType=TINYINT},
        is_reg_checked = #{isRegChecked,jdbcType=TINYINT},
        is_abnormal_notify = #{isAbnormalNotify,jdbcType=TINYINT},
        is_counseling_notify = #{isCounselingNotify,jdbcType=TINYINT},
        is_sms_enabled = #{isSmsEnabled,jdbcType=TINYINT},
        is_points_enabled = #{isPointsEnabled,jdbcType=TINYINT},
        counseling_hotline = #{counselingHotline,jdbcType=VARCHAR}
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.platform.SmsSendRecordDao">
    <!-- 添加短信发送记录 -->
    <insert id="addRecord" parameterType="cn.psycloud.psyplatform.entity.platform.SmsSendRecordEntity">
        insert into psycloud_sms_send_record(
            mobile,
            sign_name,
            template_code
        )
        values(
            #{mobile,jdbcType=VARCHAR},
            #{signName,jdbcType=VARCHAR},
            #{templateCode,jdbcType=VARCHAR}
        )
    </insert>
    <!--  查询发送记录集合 -->
    <select id="getList"  parameterType="cn.psycloud.psyplatform.dto.platform.SmsSendRecordDto">
        select id,mobile,send_date,sign_name,template_code
        from psycloud_sms_send_record pssr
        order by pssr.send_date desc
    </select>
</mapper>
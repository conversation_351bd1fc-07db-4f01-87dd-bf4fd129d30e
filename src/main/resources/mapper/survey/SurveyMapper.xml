<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.survey.SurveyDao">
    <!-- 删除 -->
    <update id="delete" parameterType="Integer">
        update psycloud_survey set is_valid = 0 where id= #{id}
    </update>
    <!-- 新增 -->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.survey.SurveyEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_survey(
            survey_name,
            is_enabled,
            operator,
            create_date,
            is_done,
            is_valid
        )
        values(
            #{surveyName,jdbcType=VARCHAR},
            #{isEnabled,jdbcType=TINYINT},
            #{operator,jdbcType=INTEGER},
            #{createDate,jdbcType=TIMESTAMP},
            0,
            1
        )
    </insert>

    <!-- 修改 -->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.survey.SurveyEntity">
        update psycloud_survey
        set
            survey_name = #{surveyName,jdbcType=VARCHAR},
            is_enabled = #{isEnabled,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 更新完成状态 -->
    <update id="updateDone" parameterType="map">
        update psycloud_survey set is_done = #{isDone,jdbcType=TINYINT} where id = #{surveyId,jdbcType=INTEGER}
    </update>

    <!-- 根据id查询问卷信息 -->
    <select id="getById" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.survey.SurveyDto">
        select
            ps.id,
            ps.survey_name,
            ps.is_enabled,
            pui.real_name,
            ps.create_date,
            ps.is_done
        from psycloud_survey ps
            inner join psycloud_user_info pui on pui.user_id = ps.operator
        where ps.is_valid = 1 and ps.id= #{surveyId,jdbcType=INTEGER}
    </select>

    <!-- 根据条件查询问卷集合 -->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.survey.SurveyDto" resultType="cn.psycloud.psyplatform.dto.survey.SurveyDto">
        select
            ps.id,
            ps.survey_name,
            ps.is_enabled,
            pui.real_name,
            ps.create_date,
            ps.is_done
        from psycloud_survey ps
            inner join psycloud_user_info pui on pui.user_id = ps.operator
        where ps.is_valid = 1
        <if test="surveyName!=null and surveyName!=''">and ps.survey_name like CONCAT('%',#{surveyName},'%')</if>
    </select>

    <!-- 获取问卷集合 -->
    <select id="getListForSelect" resultType="cn.psycloud.psyplatform.entity.survey.SurveyEntity">
        select
            id,
            survey_name
        from psycloud_survey
        where is_valid =1 and is_done =1 and is_enabled=1
    </select>

    <select id="getListByTaskId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.survey.SurveyEntity">
        select
            ps.id,
            ps.survey_name
        from psycloud_survey ps
            inner join psycloud_task_survey pt on pt.survey_Id = ps.id
        where ps.is_done = 1
          and ps.is_valid = 1
          and pt.task_id = #{taskId}
    </select>

    <select id="getListByMeasuringTaskId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.survey.SurveyEntity">
        select
            ps.id,
            ps.survey_name
        from psycloud_survey ps
                 inner join psycloud_task pt on pt.survey_Id = ps.id
        where ps.is_done = 1
          and ps.is_valid = 1
          and pt.id = #{taskId}
    </select>

    <!-- 判断问卷名称是否存在 -->
    <select id="isSurveyNameExists" parameterType="String">
        select count(survey_name)
        from psycloud_survey
        where survey_name = #{surveyName} and is_valid=1
    </select>
</mapper>
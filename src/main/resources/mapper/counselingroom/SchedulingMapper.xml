<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.counselingroom.SchedulingDao">
    <!--删除排班-->
    <delete id="delete" parameterType="Integer">
        delete from psycloud_counseling_scheduling where id = #{id}
    </delete>
    <!--新增-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.counselingroom.SchedulingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_counseling_scheduling(
            counselor_id,
            start_time,
            end_time,
            schedue_date,
            operator
        )
        values(
            #{counselorId,jdbcType=INTEGER},
            #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP},
            #{schedueDate,jdbcType=TIMESTAMP},
            #{operator,jdbcType=INTEGER}
        )
    </insert>
    <!--修改-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.counselingroom.SchedulingEntity">
        update psycloud_counseling_scheduling
        set
            counselor_id = #{counselorId,jdbcType=INTEGER},
            start_time = #{startTime,jdbcType=TIMESTAMP},
            end_time = #{endTime,jdbcType=TIMESTAMP}
        where id =#{id,jdbcType=INTEGER}
    </update>

    <!--查询排班集合-->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto" resultType="cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto">
        select
            pcs.id,
            pcs.counselor_id,
            pcs.start_time,
            pcs.end_time,
            pui.real_name as counselorName
        from psycloud_counseling_scheduling pcs
            inner join psycloud_user_info pui on pui.user_id = pcs.counselor_id
        where pui.is_valid = 1
            <if test="counselorId!=null and counselorId!=0">and pcs.counselor_id =#{counselorId}</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <!--根据id查询排班信息-->
    <select id="getById" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto">
        select
            pcs.id,
            pcs.counselor_id,
            pcs.start_time,
            pcs.end_time,
            pui.real_name as counselorName
        from psycloud_counseling_scheduling pcs
                 inner join psycloud_user_info pui on pui.user_id = pcs.counselor_id
        where pui.is_valid = 1 and pcs.id= #{id}
    </select>

    <!--验证咨询师排班时间是否重复-->
    <select id="isSchedulingExist" parameterType="cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto" resultType="Integer">
        select count(*)
        from psycloud_counseling_scheduling pcs
        where
            pcs.counselor_id = #{counselorId}
            and (#{startTime} &gt;= pcs.start_time and #{endTime} &lt;= pcs.end_time)
    </select>
    <!--获取预约的排班集合-->
    <select id="getOrderSchedulingList" parameterType="cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto" resultType="cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto">
        select pcs.id,pcs.counselor_id ,pui.real_name as counselorName,pcs.start_time,pcs.end_time
        from psycloud_counseling_scheduling pcs
                 inner join psycloud_user_info pui on pui.user_id = pcs.counselor_id
        where
                pcs.id in (
                select pco.scheduling_id
                from psycloud_counseling_order pco
                where  pco.is_valid = 1 and pco.end_time &gt; #{endTime} and pco.state = 2
            )
          and pcs.end_time &gt; #{endTime} and pui.is_valid = 1
        <if test="counselorId != null and counselorId !=0">and pcs.counselor_id = #{counselorId}</if>
        union all
        select pcs.id,pcs.counselor_id ,pui.real_name as counselorName,pcs.start_time,pcs.end_time
        from psycloud_counseling_scheduling pcs
                 inner join psycloud_user_info pui on pui.user_id = pcs.counselor_id
        where
                pcs.id not in (
                select pco.scheduling_id
                from psycloud_counseling_order pco
                where  pco.is_valid = 1
            )
          and pcs.end_time &gt; #{endTime} and pui.is_valid = 1
        <if test="counselorId != null and counselorId !=0">and pcs.counselor_id = #{counselorId}</if>
    </select>
</mapper>
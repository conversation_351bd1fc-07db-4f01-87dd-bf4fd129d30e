<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.counselingroom.CounselingRecordDao">
    <!--添加咨询记录-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingRecordEntity">
        insert into psycloud_counseling_record(
            order_id,
            real_name,
            counseling_content,
            send_time
        )
        values(
            #{orderId,jdbcType=INTEGER},
            #{realName,jdbcType=VARCHAR},
            #{counselingContent,jdbcType=VARCHAR},
            #{sendTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!--根据预约id查询咨询记录-->
    <select id="getList" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.counselingroom.CounselingRecordEntity">
        select
            order_id,
            real_name,
            counseling_content,
            send_time
        from psycloud_counseling_record
        where order_id = #{id}
        order by send_time
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.counselingroom.CounselingTypeDao">
    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_counseling_type set is_valid = 0 where id = #{id}
    </update>
    <!--新增-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingTypeEntity">
        insert into psycloud_counseling_type(
            counseling_type,
            remark,
            is_valid
        )
        values(
            #{ counselingType,jdbcType=VARCHAR },
            #{ remark,jdbcType=VARCHAR},
            1
        )
    </insert>
    <!--修改-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingTypeEntity">
        update
            psycloud_counseling_type
        set
            counseling_type = #{ counselingType,jdbcType=VARCHAR },
            remark= #{ remark,jdbcType=VARCHAR}
        where
            id = #{id,jdbcType=INTEGER}
    </update>
    <!--根据条件查询集合-->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.counselingroom.CounselingTypeDto" resultType="cn.psycloud.psyplatform.entity.counselingroom.CounselingTypeEntity">
        select id,counseling_type,remark
        from psycloud_counseling_type
        where is_valid = 1
        <if test="counselingType!=null and counselingType!=''">and counseling_type  like CONCAT('%',#{counselingType},'%')</if>
    </select>
</mapper>
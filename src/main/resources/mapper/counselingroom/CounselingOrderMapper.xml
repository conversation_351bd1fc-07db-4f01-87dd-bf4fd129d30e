<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.counselingroom.CounselingOrderDao">
    <!--删除预约-->
    <update id="delete" parameterType="Integer">
        update psycloud_counseling_order set is_valid = 0 where id = #{id}
    </update>
    <!--添加预约-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingOrderEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_counseling_order(
            visitor_id,
            start_time,
            end_time,
            counseling_type_id,
            counseling_way,
            is_point,
            self_comment,
            description,
            scheduling_id,
            handle_info,
            counseling_item_id,
            state,
            add_date,
            operator,
            is_valid
        )
        values(
            #{visitorId,jdbcType=INTEGER},
            #{startTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP},
            #{counselingTypeId,jdbcType=INTEGER},
            #{counselingWay,jdbcType=TINYINT},
            #{isPoint,jdbcType=TINYINT},
            #{selfComment,jdbcType=VARCHAR},
            #{description,jdbcType=VARCHAR},
            #{schedulingId,jdbcType=INTEGER},
            #{handleInfo,jdbcType=VARCHAR},
            #{counselingItemId,jdbcType=INTEGER},
            #{state,jdbcType=TINYINT},
            #{addDate,jdbcType=TIMESTAMP},
            #{operator,jdbcType=INTEGER},
             1
        )
    </insert>
    <!--更新预约-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingOrderEntity">
        update psycloud_counseling_order
        set
            is_point = #{isPoint,jdbcType=TINYINT},
            handle_info = #{handleInfo,jdbcType=VARCHAR},
            state = #{state,jdbcType=TINYINT},
            counseling_item_id = #{counselingItemId,jdbcType=INTEGER}
        where id=#{id,jdbcType=INTEGER}
    </update>
    <!--更新预约状态-->
    <update id="updateState" parameterType="map">
        update psycloud_counseling_order set state = #{state,jdbcType=TINYINT} where id = #{id,jdbcType=INTEGER}
    </update>
    <!--根据条件查询预约信息-->
    <select id="get" parameterType="cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto">
        select
            pco.id,
            pco.visitor_id,
            pco.counseling_way,
            pco.is_point,
            pco.self_comment,
            pco.description,
            pco.handle_info,
            pco.scheduling_id,
            pco.start_time,
            pco.end_time,
            pco.state,
            pcs.counselor_id,
            pui2.real_name as counselorName,
            pui2.head_img as counselorHeadPic,
            pct.counseling_type,
            pui.real_name as visitorName,
            pui.head_img as visitorHeadPic,
            pco.counseling_item_id as counselingItemId,
            pci.counseling_item_name
        from psycloud_counseling_order pco
            inner join psycloud_counseling_scheduling pcs on pcs.id = pco.scheduling_id
            inner join psycloud_user_info pui on pui.user_id = pco.visitor_id
            inner join psycloud_user_info pui2 on pui2.user_id = pcs.counselor_id
            inner join psycloud_counseling_type pct on pct.id = pco.counseling_type_id
            inner join psycloud_counseling_item pci on pci.id = pco.counseling_item_id
        where pco.is_valid =1
            <if test="id !=null and id!=0">and pco.id = #{id}</if>
            <if test="schedulingId !=null and schedulingId !=0">and pco.scheduling_id = #{schedulingId}</if>
            <if test="counselingTypeId !=null and counselingTypeId !=0">and pco.counseling_type_id = #{counselingTypeId}</if>
            <if test="state !=null">and pco.state = #{state}</if>
            <if test="visitorId !=null and visitorId !=0">and pco.visitor_id = #{visitorId}</if>
            <if test="counselorId !=null and counselorId!=0">and pcs.counselor_id = #{counselorId}</if>
    </select>
    <!--根据条件查询集合-->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto">
        select
            pco.id,
            pco.visitor_id,
            pco.counseling_way,
            pco.is_point,
            pco.self_comment,
            pco.description,
            pco.handle_info,
            pco.scheduling_id,
            pco.start_time,
            pco.end_time,
            pco.state,
            pcs.counselor_id,
            pui2.real_name as counselorName,
            pui2.head_img as counselorHeadPic,
            pct.counseling_type,
            pui.real_name as visitorName,
            pui.head_img as visitorHeadPic,
            pco.counseling_item_id as counselingItemId,
            pci.counseling_item_name
        from psycloud_counseling_order pco
            inner join psycloud_counseling_scheduling pcs on pcs.id = pco.scheduling_id
            inner join psycloud_user_info pui on pui.user_id = pco.visitor_id
            inner join psycloud_user_info pui2 on pui2.user_id = pcs.counselor_id
            inner join psycloud_counseling_type pct on pct.id = pco.counseling_type_id
            inner join psycloud_counseling_item pci on pci.id = pco.counseling_item_id
        where pco.is_valid =1
        <if test="id !=null and id!=0">and pco.id = #{id}</if>
        <if test="schedulingId !=null and schedulingId !=0">and pco.scheduling_id = #{schedulingId}</if>
        <if test="counselingTypeId !=null and counselingTypeId !=0">and pco.counseling_type_id = #{counselingTypeId}</if>
        <if test="state !=null">and pco.state = #{state}</if>
        <if test="visitorId !=null and visitorId !=0">and pco.visitor_id = #{visitorId}</if>
        <if test="counselorId !=null and counselorId!=0">and pcs.counselor_id = #{counselorId}</if>
        <if test="childStructs!=null and childStructs.size()>0">
            and pui.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by pco.add_date desc
    </select>

    <!--查询已经结束的预约集合-->
    <resultMap id="orderDoneResultMap" type="cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="visitor_id" property="visitorId" jdbcType="INTEGER" />
        <result column="visitorName" property="visitorName" jdbcType="VARCHAR" />
        <result column="counseling_way" property="counselingType" jdbcType="VARCHAR" />
        <result column="is_point" property="isPoint" jdbcType="TINYINT" />
        <result column="self_comment" property="selfComment" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="handle_info" property="handleInfo" jdbcType="VARCHAR" />
        <result column="state" property="state" jdbcType="TINYINT" />
        <result column="counselor_id" property="counselorId" jdbcType="INTEGER" />
        <result column="counselorName" property="counselorName" jdbcType="VARCHAR" />
        <result column="counseling_type" property="counselingType" jdbcType="VARCHAR" />
        <result column="counselorHeadPic" property="counselorHeadPic" jdbcType="VARCHAR" />
        <result column="scheduling_id" property="schedulingId" jdbcType="INTEGER" />
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="counseling_item_id" property="counselingItemId" jdbcType="INTEGER" />
        <result column="counseling_item_name" property="counselingItemName" jdbcType="VARCHAR" />
        <collection property="counselingContent" column="id" select="cn.psycloud.psyplatform.dao.counselingroom.CounselingRecordDao.getList"/>
    </resultMap>
    <select id="getOrderDoneList" parameterType="cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto" resultMap="orderDoneResultMap">
        select
            pco.id,
            pco.visitor_id,
            pui.real_name as visitorName,
            pco.counseling_way,
            pco.is_point,
            pco.self_comment,
            pco.description,
            pco.handle_info,
            pco.state,
            pcs.counselor_id,
            pui2.real_name as counselorName,
            pct.counseling_type,
            pui2.head_img as counselorHeadPic,
            pco.scheduling_id,
            pco.start_time,
            pco.end_time,
            pco.counseling_item_id,
            pci.counseling_item_name
        from psycloud_counseling_order pco
            inner join psycloud_counseling_scheduling pcs on pcs.id = pco.scheduling_id
            inner join psycloud_user_info pui on pui.user_id = pco.visitor_id
            inner join psycloud_user_info pui2 on pui2.user_id = pcs.counselor_id
            inner join psycloud_counseling_type pct on pct.id = pco.counseling_type_id
            inner join psycloud_counseling_item pci on pci.id = pco.counseling_item_id
        where (pco.state =3 or pcs.end_time &lt; #{currentTime}) and pco.is_valid = 1
            <if test="counselingTypeId != null ">and pco.counseling_type_id = #{counselingTypeId}</if>
            <if test="visitorId != null and visitorId !=0">and pco.visitor_id = #{visitorId}</if>
        order by pco.id desc
    </select>
</mapper>
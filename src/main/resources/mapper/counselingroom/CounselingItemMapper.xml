<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.counselingroom.CounselingItemDao">
    <!--删除-->
    <update id="delete" parameterType="Integer">
        update psycloud_counseling_item set is_valid = 0 where id = #{id,jdbcType=INTEGER}
    </update>
    <!--新增-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingItemEntity">
        insert into psycloud_counseling_item(
            user_id,
            counseling_item_name,
            points,
            is_valid
        )
        values(
            #{userId,jdbcType=INTEGER},
            #{counselingItemName,jdbcType=VARCHAR},
            #{points,jdbcType=INTEGER},
            1
        )
    </insert>
    <!--修改-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingItemEntity">
        update psycloud_counseling_item
        set
            counseling_item_name = #{counselingItemName,jdbcType=VARCHAR},
            points = #{points,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!--查询咨询师咨询类型集合-->
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.counselingroom.CounselingItemDto" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingItemDto">
        select
            pci.id,
            pci.user_id,
            pu.login_name,
            pui.real_name,
            pui.struct_id,
            pci.counseling_item_name,
            pci.points
        from psycloud_counseling_item pci
            inner join psycloud_user pu on pu.user_id = pci.user_id
            inner join psycloud_user_info pui on pui.user_id = pci.user_id
        where pci.is_valid=1
            and pui.is_valid = 1
            <if test="loginName !=null and loginName!=''">and pu.login_name like CONCAT('%',#{loginName},'%')</if>
            <if test="realName !=null and realName!=''">and pui.real_name like CONCAT('%',#{realName},'%')</if>
            <if test="userId!= null and userId!=0">and pci.user_id =#{userId}</if>
    </select>
    <!--根据ID查询咨询类型-->
    <select id="getById" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingItemDto">
        select
            pci.id,
            pci.user_id,
            pu.login_name,
            pui.real_name,
            pui.struct_id,
            pci.counseling_item_name,
            pci.points
        from psycloud_counseling_item pci
                 inner join psycloud_user pu on pu.user_id = pci.user_id
                 inner join psycloud_user_info pui on pui.user_id = pci.user_id
        where pci.is_valid=1
            and pui.is_valid = 1
            and pci.id=#{id}
    </select>
</mapper>
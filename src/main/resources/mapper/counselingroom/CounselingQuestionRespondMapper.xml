<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.counselingroom.CounselingQuestionRespondDao">
    <!--根据条件查询回复集合-->
    <select id="getListByQuestionId" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionRespondDto">
        select
            pcr.id,
            pu.login_name as responderLogin<PERSON>ame,
            pui.head_img as headPic,
            pcr.content,
            pcr.add_date
        from psycloud_counseling_respond pcr
            inner join psycloud_user pu on pu.user_id = pcr.responder
            inner join psycloud_user_info pui on pui.user_id = pcr.responder
        where pcr.is_valid = 1
            and pui.is_valid = 1
            and pcr.question_id = #{questionId}
        order by pcr.add_date desc
    </select>

    <!--发表回复-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionRespondEntity">
        insert into psycloud_counseling_respond(
            question_id,
            responder,
            content,
            add_date,
            is_valid
        )
        values(
            #{questionId,jdbcType=INTEGER},
            #{responder,jdbcType=INTEGER},
            #{content,jdbcType=VARCHAR},
            #{addDate,jdbcType=TIMESTAMP},
            1
        )
    </insert>

    <!--删除回复-->
    <update id="delete" parameterType="Integer">
        update
            psycloud_counseling_respond
        set
            is_valid = 0
        where id = #{id}
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.counselingroom.CounselingQuestionDao">
    <!--删除在线心理答疑-->
    <update id="delete" parameterType="Integer">
        update psycloud_counseling_question set is_valid = 0 where id = #{id}
    </update>

    <!--新增在线心理答疑-->
    <insert id="insert" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_counseling_question(
            questioner,
            counseling_type_id,
            title,
            content,
            is_anonymous,
            img,
            add_date,
            is_valid
        )
        values(
            #{questioner,jdbcType=INTEGER},
            #{counselingTypeId,jdbcType=INTEGER},
            #{title,jdbcType=VARCHAR},
            #{content,jdbcType=VARCHAR},
            #{isAnonymous,jdbcType=TINYINT},
            #{img,jdbcType=VARCHAR},
            #{addDate,jdbcType=TIMESTAMP},
            1
        )
    </insert>

    <!--修改在线心理答疑-->
    <update id="update" parameterType="cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionEntity">
        update psycloud_counseling_question
        set
            counseling_type_id =  #{counselingTypeId,jdbcType=INTEGER},
            title = #{title,jdbcType=VARCHAR},
            content = #{content,jdbcType=VARCHAR},
            is_anonymous = #{isAnonymous,jdbcType=TINYINT},
            img = #{img,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    
    <resultMap id="counselingQuestionResultMap" type="cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="questioner" property="questioner" jdbcType="INTEGER"/>
        <result column="login_name" property="questionerLoginName" jdbcType="VARCHAR"/>
        <result column="head_img" property="headPic" jdbcType="VARCHAR"/>
        <result column="counseling_type_id" property="counselingTypeId" jdbcType="INTEGER"/>
        <result column="counseling_type" property="counselingTypeName" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="is_anonymous" property="isAnonymous" jdbcType="TINYINT"/>
        <result column="img" property="img" jdbcType="VARCHAR"/>
        <result column="add_date" property="addDate" jdbcType="TIMESTAMP"/>
        <result column="respondCount" property="respondCount" jdbcType="INTEGER"/>
        <collection property="listResponds" select="cn.psycloud.psyplatform.dao.counselingroom.CounselingQuestionRespondDao.getListByQuestionId" column="id" />
    </resultMap>
    <!--根据提问id查询提问详细-->
    <select id="getById" parameterType="Integer" resultMap="counselingQuestionResultMap">
        select
            pcq.id,
            pcq.questioner,
            pu.login_name,
            pui.head_img,
            pcq.counseling_type_id,
            pct.counseling_type,
            pcq.title,
            pcq.content,
            pcq.is_anonymous,
            pcq.img,
            pcq.add_date,
            (select count(*) from psycloud_counseling_respond pcr where pcr.is_valid = 1 and pcr.question_id = pcq.id) as respondCount
        from psycloud_counseling_question pcq
            inner join psycloud_user pu on pu.user_id = pcq.questioner
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
            left join psycloud_counseling_type pct on pct.id = pcq.counseling_type_id
        where pcq.is_valid = 1
            and pui.is_valid = 1
            and pct.is_valid = 1
            and pcq.id = #{questionId}
    </select>

    <!--根据条件获取心理答疑列表-->
    <resultMap id="counselingQuestionListResultMap" type="cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionDto">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="questioner" property="questioner" jdbcType="INTEGER"/>
        <result column="login_name" property="questionerLoginName" jdbcType="VARCHAR"/>
        <result column="head_img" property="headPic" jdbcType="VARCHAR"/>
        <result column="counseling_type_id" property="counselingTypeId" jdbcType="INTEGER"/>
        <result column="counseling_type" property="counselingTypeName" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="is_anonymous" property="isAnonymous" jdbcType="TINYINT"/>
        <result column="add_date" property="addDate" jdbcType="TIMESTAMP"/>
        <result column="respondCount" property="respondCount" jdbcType="INTEGER"/>
    </resultMap>
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionDto" resultMap="counselingQuestionListResultMap">
        select
            pcq.id,
            pcq.questioner,
            pu.login_name,
            pui.head_img,
            pcq.counseling_type_id,
            pct.counseling_type,
            pcq.title,
            pcq.content,
            pcq.is_anonymous,
            pcq.add_date,
            (select count(*) from psycloud_counseling_respond pcr where pcr.is_valid = 1 and pcr.question_id = pcq.id) as respondCount
        from psycloud_counseling_question pcq
                 inner join psycloud_user pu on pu.user_id = pcq.questioner
                 inner join psycloud_user_info pui on pui.user_id = pu.user_id
                 left join psycloud_counseling_type pct on pct.id = pcq.counseling_type_id
        where pcq.is_valid = 1
          and pui.is_valid = 1
          and pct.is_valid = 1
            <if test="counselingTypeId != null and counselingTypeId!=0">and pcq.counseling_type_id = #{counselingTypeId}</if>
            <if test="title != null and title !=''">and pcq.title like CONCAT('%',#{title},'%')</if>
            <if test="questioner != null and questioner !=0">AND pcq.questioner = #{questioner}</if>
            <if test="isMyResponds !=null and isMyResponds eq 1">
                and pcq.id in (select pcr.question_id from psycloud_counseling_respond pcr where pcr.responder = #{responder})
            </if>
        order by pcq.add_date desc
    </select>

    <!--数据看板：最新答疑-->
    <select id="getForDashboard" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionDto">
        select
            pu.login_name as questionerLoginName,
            pui.head_img as headPic,
            pcq.title,
            pcq.content,
            pcq.add_date
        from psycloud_counseling_question pcq
            inner join psycloud_user pu on pu.user_id = pcq.questioner
            inner join psycloud_user_info pui on pui.user_id = pu.user_id
        where pcq.is_valid = 1
            and pui.is_valid = 1
        order by pcq.add_date desc
    </select>

    <!--查询最近某段时间内的每天在线答疑量-->
    <select id="getCounselingQuestionCapacitytList" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionCapacityDto" databaseId="mysql">
        select
            a.dt,
            sum(ifnull(b.count,0)) as capacity
        from
        (
            select date_sub(curdate(),interval 0 day) as dt
            union
            select date_sub(curdate(),interval 1 day)
            union
            select date_sub(curdate(),interval 2 day)
            union
            select date_sub(curdate(),interval 3 day)
            union
            select date_sub(curdate(),interval 4 day)
            union
            select date_sub(curdate(),interval 5 day)
            union
            select date_sub(curdate(),interval 6 day)
        ) as a
        left join (
            select
                convert(pcq.add_date,date) as datetemp,
                count(pcq.id) as count
            from psycloud_counseling_question pcq
            where pcq.is_valid = 1
                and datediff(now(), pcq.add_date) &gt;= 0
                and datediff(now(), pcq.add_date) &lt;7
                group by pcq.add_date
        ) as b on a.dt = b.datetemp
        group by a.dt
        order by a.dt
    </select>
    <select id="getCounselingQuestionCapacitytList" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionCapacityDto" databaseId="dm8">
        select
        a.dt,
        sum(ifnull(b.count,0)) as capacity
        from
        (
        select date_sub(curdate(),interval '0' day) as dt
        union
        select date_sub(curdate(),interval '1' day)
        union
        select date_sub(curdate(),interval '2' day)
        union
        select date_sub(curdate(),interval '3' day)
        union
        select date_sub(curdate(),interval '4' day)
        union
        select date_sub(curdate(),interval '5' day)
        union
        select date_sub(curdate(),interval '6' day)
        ) as a
        left join (
        select
        convert(date,pcq.add_date) as datetemp,
        count(pcq.id) as count
        from psycloud_counseling_question pcq
        where pcq.is_valid = 1
        and datediff(day,pcq.add_date,now()) &gt;= 0
        and datediff(day,pcq.add_date,now()) &lt;7
        group by pcq.add_date
        ) as b on a.dt = b.datetemp
        group by a.dt
        order by a.dt
    </select>

    <!--查询最近某段时间内的问题类型的每天咨询量-->
    <select id="getCounselingTypeCapacityList" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingTypeCapacityDto" databaseId="mysql">
        select
            pct.counseling_type as counselingType,
            ifnull(a.count,0) as capacity
        from psycloud_counseling_type pct
            left join (
            select pct.id, count(*) as count
            from psycloud_counseling_type pct
                left join psycloud_counseling_order pco on pco.counseling_type_id = pct.id
            where pco.state in (1,3)
                and datediff(now(), pco.start_time) &gt;= 0
                and datediff(now(), pco.start_time) &lt; 7
            group by pct.id
            ) as a on pct.id = a.id
        order by a.count desc limit 5
    </select>
    <select id="getCounselingTypeCapacityList" resultType="cn.psycloud.psyplatform.dto.counselingroom.CounselingTypeCapacityDto" databaseId="dm8">
        select
            pct.counseling_type as counselingType,
            ifnull(a.count,0) as capacity
        from psycloud_counseling_type pct
            left join (
                select pct.id, count(*) as count
                from psycloud_counseling_type pct
                left join psycloud_counseling_order pco on pco.counseling_type_id = pct.id
                where pco.state in (1,3)
                and datediff(day,pco.start_time,now()) &gt;= 0
                and datediff(day,pco.start_time,now()) &lt; 7
                group by pct.id
            ) as a on pct.id = a.id
        order by a.count desc limit 5
    </select>
</mapper>
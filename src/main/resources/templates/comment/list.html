<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">评价管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">评价列表</a></li>
                    </ol>
                </div>
                <h4 class="page-title">评价列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2 mr-1">
                                    <select class="form-control" style="width:100%;" name="functionType" id="functionType">
                                        <option value="">功能类型</option>
                                        <option value="1">心理训练营</option>
                                        <option value="2">训练营课程</option>
                                        <option value="3">心理微课</option>
                                    </select>
                                </div>
                                <div class="form-group mb-2 mr-1">
                                    <select class="form-control" style="width:100%;" name="isChecked" id="isChecked">
                                        <option value="">审核状态</option>
                                        <option value="1">已审核</option>
                                        <option value="0">未审核</option>
                                    </select>
                                </div>
                                <div class="form-group mb-2 mr-1">
                                    <input type="text" class="form-control" name="commentContent" id="commentContent" placeholder="关键字..." autocomplete="off" />
                                </div>
                                <button type="button" class="btn btn-primary mb-2" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnBatchCheck" type="button" class="btn btn-warning mr-1 mb-2"><i class="fa fa-check mr-1"></i>审核</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mr-1 mb-2"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <table id="tbComments" class="table table-striped">
                        <thead>
                        <tr>
                            <th style="width: 30px;">
                                <div class="custom-control custom-checkbox">
                                    <input id="chkall" class="custom-control-input check" type="checkbox">
                                    <label class="custom-control-label" for="chkall"></label>
                                </div>
                            </th>
                            <th>功能</th>
                            <th>标题</th>
                            <th>评论内容</th>
                            <th>评论人</th>
                            <th>评论时间</th>
                            <th>审核状态</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            //初始化页面权限
            initPage();
            $("#btnQuery").click(function () {
                $('#tbComments').DataTable().ajax.reload();
            });
            //datatables
            $("#tbComments").bsDataTables({
                columns: columns,
                url: '/comment/comment/get_list',
                columnDefs: '',
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //let res = JSON.parse(data);
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //删除(批量)
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids;
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/comment/comment/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            $("#btnBatchCheck").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定审核吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids;
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/comment/comment/check", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });
        let initPage = function () {
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
            '[[${canCheck}]]' === 'true' ? $("#btnBatchCheck").show() : $("#btnBatchCheck").hide();
        };
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "functionType", "bSortable": false, "render":
                    function(data, type, full, meta){
                        if(data === 1) return "训练营";
                        if(data === 2) return "训练营课程";
                        if(data === 3) return "微课";
                        if(data === 4) return "咨询师";
                    }
            },
            { "data": "productName", "bSortable": false },
            { "data": "commentContent", "bSortable": false },
            { "data": "loginName", "bSortable": false },
            { "data": "commentDate", "bSortable": false },
            { "data": "isChecked", "bSortable": false,"render":
                    function (data, type, full, meta){
                        if(data === 1){
                            return '<span class="badge badge-success">是</span>';
                        }
                        else{
                            return '<span class="badge badge-secondary">否</span>';
                        }
                    } },
        ];
        let getQueryCondition = function (data) {
            let param = {};
            param.functionType = $("#functionType").val();
            param.commentContent = $("#commentContent").val();
            param.isChecked = $("#isChecked").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
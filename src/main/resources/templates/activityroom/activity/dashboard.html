<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title>活动数据看板</title>
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动管理</a></li>
                        <li class="breadcrumb-item active">活动数据看板</li>
                    </ol>
                </div>
                <h4 class="page-title">活动数据看板</h4>
            </div>
        </div>
    </div>
    <!-- end page title -->

    <!-- 筛选条件 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form class="form-inline">
                        <select class="form-control mr-2" name="structId" id="structId" style="width: 200px;"></select>
                        <div class="form-group mr-3">
                            <label for="dateRange" class="mr-2 font-weight-semibold">时间范围：</label>
                            <input type="text" class="form-control" id="dateRange" name="dateRange"
                                   placeholder="选择时间范围" style="width: 250px;">
                        </div>
                        <button type="button" class="btn btn-primary" id="btnQuery">
                            <i class="fa fa-search"></i> 查询
                        </button>
                        <button type="button" class="btn btn-light ml-2" id="btnReset">
                            <i class="fa fa-refresh"></i> 重置
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 总体统计 -->
    <div class="row">
        <div class="col-md-3 col-sm-6">
            <div class="card text-center">
                <div class="card-body">
                    <div class="widget-icon bg-primary-lighten text-primary mb-3">
                        <i class="fa fa-calendar"></i>
                    </div>
                    <h2 class="font-weight-normal mb-0" id="activityCount">0</h2>
                    <p class="text-muted mb-0">活动数量</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="card text-center">
                <div class="card-body">
                    <div class="widget-icon bg-success-lighten text-success mb-3">
                        <i class="fa fa-sign-in"></i>
                    </div>
                    <h2 class="font-weight-normal mb-0" id="totalClockInCount">0</h2>
                    <p class="text-muted mb-0">总签到人次</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="card text-center">
                <div class="card-body">
                    <div class="widget-icon bg-info-lighten text-info mb-3">
                        <i class="fa fa-sign-out"></i>
                    </div>
                    <h2 class="font-weight-normal mb-0" id="totalClockOutCount">0</h2>
                    <p class="text-muted mb-0">总签退人次</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="card text-center">
                <div class="card-body">
                    <div class="widget-icon bg-warning-lighten text-warning mb-3">
                        <i class="fa fa-clipboard"></i>
                    </div>
                    <h2 class="font-weight-normal mb-0" id="surveyCompletedCount">0</h2>
                    <p class="text-muted mb-0">完成问卷调查人次</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 按活动类型统计 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">
                        <i class="fa fa-pie-chart text-primary"></i> 按活动类型
                    </h4>
                    <div class="table-responsive">
                        <table class="table table-striped table-centered mb-0" id="activityTypeTable">
                            <thead>
                                <tr>
                                    <th>活动类型</th>
                                    <th>总人次</th>
                                    <th>总时长</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 按咨询师统计 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">
                        <i class="fa fa-user text-primary"></i> 按咨询师
                    </h4>
                    <div class="table-responsive">
                        <table class="table table-striped table-centered mb-0" id="counselorTable">
                            <thead>
                                <tr>
                                    <th>咨询师姓名</th>
                                    <th>活动次数</th>
                                    <th>总时长</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            initSelect('#structId', '/anteroom/structs/get_for_select', {}, '', '选择机构');
            // 初始化日期范围选择器
            initDateRangePicker();

            // 初始化数据
            loadDashboardData();

            // 绑定事件
            $('#btnQuery').click(function() {
                // 清除之前的数据
                clearDashboardData();
                loadDashboardData();
            });

            $('#btnReset').click(function() {
                $('#dateRange').val('');
                // 清除之前的数据
                clearDashboardData();
                loadDashboardData();
            });
        });

        // 初始化日期范围选择器
        function initDateRangePicker() {
            $('#dateRange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    cancelLabel: '清除',
                    applyLabel: '确定',
                    format: 'YYYY-MM-DD',
                    separator: ' 至 ',
                    customRangeLabel: '自定义',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                },
                ranges: {
                    '今天': [moment(), moment()],
                    '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    '最近7天': [moment().subtract(6, 'days'), moment()],
                    '最近30天': [moment().subtract(29, 'days'), moment()],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });

            $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' 至 ' + picker.endDate.format('YYYY-MM-DD'));
            });

            $('#dateRange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        }

        // 清除看板数据
        function clearDashboardData() {
            // 清除统计卡片数据
            $('#activityCount, #totalClockInCount, #totalClockOutCount, #surveyCompletedCount').text('0');
            
            // 清空表格数据
            $('#activityTypeTable tbody').empty();
            $('#counselorTable tbody').empty();
            
            // 添加空数据提示
            $('#activityTypeTable tbody').html('<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>');
            $('#counselorTable tbody').html('<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>');
        }

        // 加载看板数据
        function loadDashboardData() {
            var dateRange = $('#dateRange').val();
            var startDate = null;
            var endDate = null;

            if (dateRange) {
                var dates = dateRange.split(' 至 ');
                if (dates.length === 2) {
                    startDate = dates[0];
                    endDate = dates[1];
                }
            }

            var requestData = {
                startDate: startDate,
                endDate: endDate,
                structId: $('#structId').val()
            };

            // 显示加载状态
            showLoadingState();

            $.ajax({
                url: '/activityroom/activity/get_dashboard_data',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    updateDashboard(response);
                    hideLoadingState();
                },
                error: function(xhr, status, error) {
                    hideLoadingState();
                    layer.msg('加载数据失败，请重试', {icon: 2});
                }
            });
        }

        // 显示加载状态
        function showLoadingState() {
            $('#btnQuery').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 加载中...');
            $('#activityCount, #totalClockInCount, #totalClockOutCount, #surveyCompletedCount').html('<i class="fa fa-spinner fa-spin"></i>');
        }

        // 隐藏加载状态
        function hideLoadingState() {
            $('#btnQuery').prop('disabled', false).html('<i class="fa fa-search"></i> 查询');
        }

        // 更新看板显示
        function updateDashboard(data) {
            // 更新总体统计（带动画效果）
            animateNumber('#activityCount', data.activityCount || 0);
            animateNumber('#totalClockInCount', data.totalClockInCount || 0);
            animateNumber('#totalClockOutCount', data.totalClockOutCount || 0);
            animateNumber('#surveyCompletedCount', data.surveyCompletedCount || 0);

            // 更新活动类型统计表格
            updateActivityTypeTable(data.activityTypeStats || []);

            // 更新咨询师统计表格
            updateCounselorTable(data.counselorStats || []);
        }

        // 数字动画效果
        function animateNumber(selector, targetValue) {
            var $element = $(selector);
            var currentValue = 0;
            var increment = Math.ceil(targetValue / 30);

            var timer = setInterval(function() {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                $element.text(currentValue);
            }, 50);
        }



        // 更新活动类型统计表格
        function updateActivityTypeTable(data) {
            var tbody = $('#activityTypeTable tbody');
            tbody.empty();

            if (data.length === 0) {
                tbody.append('<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>');
                return;
            }

            data.forEach(function(item, index) {
                var badgeClass = getBadgeClass(index);
                var row = '<tr>' +
                    '<td><span class="badge ' + badgeClass + ' mr-1"></span>' + (item.activityTypeName || '') + '</td>' +
                    '<td><strong>' + (item.totalParticipants || 0) + '</strong> 人次</td>' +
                    '<td><span class="text-muted">' + (item.totalDurationFormatted || '0小时0分钟') + '</span></td>' +
                    '</tr>';
                tbody.append(row);
            });
        }

        // 更新咨询师统计表格
        function updateCounselorTable(data) {
            var tbody = $('#counselorTable tbody');
            tbody.empty();

            if (data.length === 0) {
                tbody.append('<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>');
                return;
            }

            data.forEach(function(item, index) {
                var row = '<tr>' +
                    '<td>' + (item.counselorName || '') + '</td>' +
                    '<td><strong>' + (item.activityCount || 0) + '</strong> 次</td>' +
                    '<td><span class="text-muted">' + (item.totalDurationFormatted || '0小时0分钟') + '</span></td>' +
                    '</tr>';
                tbody.append(row);
            });
        }

        // 获取徽章样式类
        function getBadgeClass(index) {
            var classes = ['badge-primary', 'badge-success', 'badge-info', 'badge-warning'];
            return classes[index % classes.length];
        }
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
    <!-- 自定义样式 -->
    <style>
        /* 筛选条件卡片样式 */
        .card {
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
        }

        /* 输入框组样式 */
        .input-group-text {
            border: 1px solid #ced4da;
            background-color: #f8f9fa;
        }

        .form-control:focus {
            border-color: #727cf5;
            box-shadow: 0 0 0 0.2rem rgba(114, 124, 245, 0.25);
        }

        /* 按钮样式 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* 表格样式 */
        .table th {
            font-weight: 600;
            font-size: 0.875rem;
            color: #6c757d;
            background-color: #f8f9fa;
        }

        .table td {
            vertical-align: middle;
            font-size: 0.875rem;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(114, 124, 245, 0.05);
        }

        /* 模态框样式 */
        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
        }

        .modal-header {
            border-radius: 12px 12px 0 0;
        }

        /* 标签样式 */
        .form-label {
            font-weight: 500;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        /* 卡片标题样式 */
        .card-title {
            font-weight: 600;
            color: #495057;
            font-size: 1.1rem !important;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .btn-group {
                flex-direction: column;
            }

            .btn-group .btn {
                margin-bottom: 0.5rem;
            }

            .btn-group .btn:last-child {
                margin-bottom: 0;
            }
        }

        /* 作答详情样式 */
        .answer-details {
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .answer-details .border-left {
            background-color: rgba(114, 124, 245, 0.02);
            border-radius: 0 4px 4px 0;
            transition: all 0.3s ease;
        }

        .answer-details .border-left:hover {
            background-color: rgba(114, 124, 245, 0.05);
            transform: translateX(2px);
        }

        .answer-details .font-weight-bold {
            font-size: 0.85rem;
        }

        .answer-details .text-muted {
            font-size: 0.8rem;
        }

        .answer-details .badge-sm {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }

        .answer-details .text-success {
            color: #28a745 !important;
        }

        .answer-details .fa-check-circle {
            color: #28a745;
        }

        /* 导出下拉菜单样式 */
        .dropdown-menu {
            border-radius: 6px;
            border: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background-color: rgba(114, 124, 245, 0.1);
            color: #727cf5;
            transform: translateX(2px);
        }

        .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        .dropdown-item.disabled {
            opacity: 0.5;
            pointer-events: none;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">活动列表</a></li>
                    </ol>
                </div>
                <h4 class="page-title">活动列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-structId" class="sr-only">选择机构：</label>
                                        <select class="form-control" id="sr-structId" name="sr-structId" style="width:150px;">
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-activityType" class="sr-only">活动类型：</label>
                                        <select class="form-control" id="sr-activityType" name="sr-activityType" style="width:150px;">
                                            <option value="">选择活动类型</option>
                                            <option value="1">驻场咨询（轻咨询）</option>
                                            <option value="2">驻场咨询（50分钟以上）</option>
                                            <option value="3">团体辅导</option>
                                            <option value="4">心理关爱活动</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-counselor" class="sr-only">咨询师：</label>
                                        <select class="form-control" id="sr-counselor" name="sr-counselor" style="width:150px;">
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-dateRange" class="sr-only">时间范围：</label>
                                        <input type="text" class="form-control" id="sr-dateRange" name="sr-dateRange" placeholder="选择时间范围" autocomplete="off" style="width:200px;">
                                    </div>
                                </div>
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-activityName" class="sr-only">活动主题：</label>
                                        <input type="text" class="form-control" id="sr-activityName" name="sr-activityName" placeholder="活动主题" autocomplete="off" style="width:200px;">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-primary mr-1" title="创建活动"><i class="fa fa-plus mr-1"></i>创建活动</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mr-1" title="删除"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbActivityList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th>活动ID</th>
                                <th>所属机构</th>
                                <th>活动主题</th>
                                <th>活动类型</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>驻场咨询师</th>
                                <th>签到人数</th>
                                <th>调查问卷</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
    <!-- modal.二维码 start -->
    <div id="qrcode-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog large">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary text-light">
                    <h5 class="modal-title">活动专属二维码</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <div id="qrcode" style="width: 250px; height: 250px; margin: 0px auto; border: 25px solid rgb(255, 255, 255);">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light mr-2" data-dismiss="modal">关闭</button>
                    <input type="button" class="btn btn-primary" id="download" value="下载" />
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.二维码 end -->
    <!-- modal.活动图库 start -->
    <div id="gallery-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-full-width modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary text-light">
                    <h5 class="modal-title" id="modalActivityName"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light mr-2" data-dismiss="modal">关闭</button>
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.活动图库 end -->
    <!-- 全屏查看原图 start -->
    <div id="fullscreen-view" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.9); z-index: 9999; cursor: pointer;">
        <div style="position: relative; width: 100%; height: 100%;">
            <img id="fullscreen-image" src="" style="max-width: 100%; max-height: 100%; margin: auto; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
            <div id="prev-image" style="position: absolute; left: 20px; top: 50%; transform: translateY(-50%); color: white; font-size: 30px; cursor: pointer; z-index: 10000; opacity: 0.5; transition: opacity 0.3s;">
                <i class="fa fa-chevron-left"></i>
            </div>
            <div id="next-image" style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); color: white; font-size: 30px; cursor: pointer; z-index: 10000; opacity: 0.5; transition: opacity 0.3s;">
                <i class="fa fa-chevron-right"></i>
            </div>
        </div>
    </div>
    <!-- 全屏查看原图 end -->
    <!-- modal.问卷作答记录 start-->
    <div id="myModalSurveyRecord" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary border-0">
                    <h5 class="modal-title text-white">
                        <i class="fa fa-list mr-1"></i>调查问卷作答记录
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 筛选条件卡片 -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-light border-0 py-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0" style="font-size: 1.1rem; font-weight: 600;">
                                    <i class="fa fa-search mr-2 text-primary"></i>筛选条件
                                </h5>
                                <button class="btn btn-sm btn-outline-primary" type="button" data-toggle="collapse"
                                        data-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                                    <i class="fa fa-chevron-up" id="filterToggleIcon"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body collapse show" id="filterCollapse">
                            <div class="row">
                                <div class="col-lg-2 col-md-6 mb-3">
                                    <label for="sr-s-loginName" class="form-label text-muted">用户名</label>
                                    <input type="text" class="form-control" id="sr-s-loginName"
                                           placeholder="用户名" autocomplete="off">
                                </div>
                                <div class="col-lg-2 col-md-6 mb-3">
                                    <label for="sr-s-realName" class="form-label text-muted">姓名</label>
                                    <input type="text" class="form-control" id="sr-s-realName"
                                               placeholder="姓名" autocomplete="off">
                                </div>
                                <div class="col-lg-3 col-md-8 mb-3">
                                    <label for="sr-s-dateRange" class="form-label text-muted">作答时间</label>
                                    <input type="text" class="form-control" id="sr-s-dateRange"
                                           placeholder="请选择作答时间范围" readonly>
                                </div>
                                <div class="col-lg-2 col-md-6 mb-3">
                                    <label for="sr-s-questionNumber" class="form-label text-muted">题目序号</label>
                                    <select class="form-control" id="sr-s-questionNumber">
                                        <option value="">选择题目</option>
                                    </select>
                                </div>
                                <div class="col-lg-2 col-md-6 mb-3">
                                    <label for="sr-s-selectedOption" class="form-label text-muted">选择选项</label>
                                    <select class="form-control" id="sr-s-selectedOption">
                                        <option value="">选择选项</option>
                                    </select>
                                </div>
                                <div class="col-lg-1 col-md-4 mb-3 d-flex align-items-end">
                                    <div class="btn-group w-100" role="group">
                                        <button type="button" class="btn btn-primary btn-sm" id="btnSearchSurvey" title="查询">
                                            <i class="fa fa-search"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="btnResetSurvey" title="重置">
                                            <i class="fa fa-refresh"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-0 py-3">
                            <h5 class="card-title mb-0" style="font-size: 1.1rem; font-weight: 600;">
                                <i class="fa fa-th-list mr-2 text-success"></i>作答记录列表
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                        <style>
                            /* 只针对问卷作答记录表格的样式 */
                            #tbSurveyRecord td, #tbSurveyRecord th {
                                text-align: center !important;
                                vertical-align: middle !important;
                            }
                            /* 问卷作答记录表头样式 - 允许换行显示完整题目 */
                            #tbSurveyRecord th {
                                height: auto !important;
                                min-height: 80px !important;
                                padding: 15px 8px !important;
                                white-space: normal !important;
                                word-wrap: break-word !important;
                                line-height: 1.4 !important;
                                position: relative !important;
                            }
                            #tbSurveyRecord th > div {
                                height: auto !important;
                                overflow: visible !important;
                                display: flex !important;
                                flex-direction: column !important;
                                justify-content: center !important;
                                align-items: center !important;
                                min-height: 50px !important;
                            }
                            /* 问卷作答记录表格内容 - 不允许换行 */
                            #tbSurveyRecord td {
                                white-space: nowrap !important;
                                word-wrap: normal !important;
                                overflow: hidden !important;
                                text-overflow: ellipsis !important;
                                max-width: 200px !important;
                            }
                            #tbSurveyRecord thead tr {
                                height: auto !important;
                            }
                            /* 确保活动列表表头不换行 */
                            #tbActivityList th {
                                white-space: nowrap !important;
                                word-wrap: normal !important;
                            }
                            /* 只对问卷作答记录表格应用DataTable样式覆盖 */
                            .dataTables_wrapper #tbSurveyRecord.dataTable thead th {
                                height: auto !important;
                                white-space: normal !important;
                            }
                        </style>
                        <table class="table table-hover mb-0 text-center" id="tbSurveyRecord">
                            <thead class="thead-light" id="surveyRecordTableHead">
                            <!-- 表头将通过JavaScript动态生成 -->
                            </thead>
                        </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light border-0">
                    <div class="btn-group mr-auto">
                        <button class="btn btn-success" id="btnSurveyStat">
                            <i class="fa fa-area-chart mr-1"></i>结果分析
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-download mr-1"></i>导出结果
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="#" id="btnExportSurveyByText">
                                    <i class="fa fa-font mr-2"></i>按选项内容导出
                                </a>
                                <a class="dropdown-item" href="#" id="btnExportSurveyById">
                                    <i class="fa fa-hashtag mr-2"></i>按选项序号导出
                                </a>
                                <a class="dropdown-item" href="#" id="btnExportSurveyWord">
                                    <i class="fa fa-file-word-o mr-2"></i>批量导出word
                                </a>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                        <i class="fa fa-close mr-1"></i>关闭
                    </button>
                    <input type="hidden" id="hidActivityId" value="0" />
                    <input type="hidden" id="hidSurveyId" value="0" />
                </div>
            </div>
        </div>
    </div>
    <!-- modal.问卷作答记录 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/qrcode/qrcode.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script type="text/javascript">
        let users;
        $(function () {
            //初始化页面权限
            initPage();
            //初始化下拉框
            initSelect('#sr-structId', '/anteroom/structs/get_for_select', {}, '', '选择机构');
            initSelect('#sr-counselor', '/anteroom/user/getCounselorList_for_select', {}, '', '选择咨询师');
            //初始化时间范围控件
            initDateRangePicker();
            //添加活动
            $("#btnAdd").click(function () {
                location.href = "/activityroom/activity/add";
            });
            //查询
            $("#btnQuery").click(function () {
                oTable.draw();
            });
            //活动列表Datatables
            $("#tbActivityList").bsDataTables({
                columns: columns,
                url: '/activityroom/activity/get_list_by_paged',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //查看已签到人员清单
            $("#tbActivityList").on('click', '.user', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/activityroom/clocking/list?activityId=" + data.id;
            });
            //修改
            $("#tbActivityList").on('click', '.edit', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/activityroom/activity/update?id=" + data.id;
            });
            //调查问卷记录
            $("#tbActivityList").on('click', '.survey', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let newActivityId = data.id;
                let newSurveyId = data.surveyId;
                
                // 检查问卷ID是否发生变化
                console.log("当前问卷ID:", currentSurveyId, "新问卷ID:", newSurveyId);
                
                // 如果问卷ID发生变化，清空题目信息
                if (currentSurveyId !== newSurveyId.toString()) {
                    console.log("检测到问卷ID变化，清空题目信息");
                    surveyQuestions = [];
                    // 清空表头
                    $("#surveyRecordTableHead").html('');
                    // 更新当前问卷ID
                    currentSurveyId = newSurveyId.toString();
                }
                
                $("#hidActivityId").val(newActivityId);
                $("#hidSurveyId").val(newSurveyId);

                // 显示模态框
                $("#myModalSurveyRecord").modal('show');
            });

            // 模态框显示时初始化
            $("#myModalSurveyRecord").on('shown.bs.modal', function () {
                if (isModalInitializing) {
                    console.log("模态框正在初始化中，跳过重复初始化");
                    return;
                }

                try {
                    console.log("模态框已显示，开始初始化问卷记录表格");
                    isModalInitializing = true;
                    
                    // 检查表头是否完整
                    let tableHead = $("#surveyRecordTableHead");
                    if (tableHead.length === 0 || tableHead.html().trim() === '') {
                        console.log("检测到表头为空，重新生成");
                        if (surveyQuestions && surveyQuestions.length > 0) {
                            generateTableHeader();
                        }
                    }
                    
                    initSurveyRecord();
                } catch (error) {
                    console.error("初始化问卷记录表格时出错:", error);
                } finally {
                    isModalInitializing = false;
                }
            });

            // 模态框隐藏时清理
            $("#myModalSurveyRecord").on('hidden.bs.modal', function () {
                try {
                    console.log("模态框已隐藏，清理问卷记录表格");

                    // 重置初始化标志
                    isModalInitializing = false;
                    isRebuilding = false;

                    // 安全地销毁DataTable
                    if (oTableSurvey != null) {
                        try {
                            oTableSurvey.destroy();
                        } catch (destroyError) {
                            console.warn("销毁DataTable时出错:", destroyError);
                        }
                        oTableSurvey = null;
                    }

                    // 清空表格内容但保留表头结构
                    $("#tbSurveyRecord tbody").empty();
                    
                    // 重置表格状态，不清空题目信息（题目信息在打开时根据问卷ID决定是否清空）
                    resetSurveyTableState(false);
                    
                    console.log("模态框清理完成");
                } catch (error) {
                    console.error("清理问卷记录表格时出错:", error);
                }
            });
            //问卷结果分析
            $("#btnSurveyStat").click(function () {
                let activityId = $("#hidActivityId").val();
                let surveyId = $("#hidSurveyId").val();
                if (surveyId && surveyId > 0) {
                    location.href = "/activityroom/activity/survey_stat?activityId=" + activityId + "&surveyId=" + surveyId;
                } else {
                    layer.msg('该活动未关联问卷，无法进行结果分析', { icon: 0, time: 2000 });
                }
            });
            // 按选项内容导出（原有功能）
            $("#btnExportSurveyByText").click(function (e) {
                e.preventDefault();
                exportSurveyData(2, '按选项内容导出');
            });
            
            // 按选项序号导出（新增功能）
            $("#btnExportSurveyById").click(function (e) {
                e.preventDefault();
                exportSurveyData(1, '按选项序号导出');
            });
            
            // 批量导出word（新增功能）
            $("#btnExportSurveyWord").click(function (e) {
                e.preventDefault();
                exportSurveyWord();
            });
            
            // 通用导出函数
            function exportSurveyData(exportType, exportName) {
                layer.msg('数据处理中…（' + exportName + '）', {
                    icon: 17, shade: 0.05, time: false
                });
                $("#btnExportSurveyById, #btnExportSurveyByText, #btnExportSurveyWord").addClass("disabled");
                let jsonObj = {};
                jsonObj.activityId = $("#hidActivityId").val();
                jsonObj.surveyId = $("#hidSurveyId").val();
                jsonObj.exportType = exportType; // 1-按选项序号导出，2-按选项文本导出
                
                // 添加筛选条件
                jsonObj.searchLoginName = $.trim($("#sr-s-loginName").val());
                jsonObj.searchRealName = $.trim($("#sr-s-realName").val());
                
                // 处理题目序号：从"第1题（单选题）题目内容"中提取数字
                let questionNumber = $("#sr-s-questionNumber").val();
                if (questionNumber) {
                    let match = questionNumber.match(/第(\d+)题/);
                    jsonObj.questionNumber = match ? match[1] : questionNumber;
                } else {
                    jsonObj.questionNumber = "";
                }
                
                // 处理选项序号：从"1. 选项内容"中提取序号
                let selectedOption = $("#sr-s-selectedOption").val();
                if (selectedOption) {
                    // 提取数字前缀，如从"1. 选项内容"中提取"1"
                    let match = selectedOption.match(/^(\d+)\./);
                    jsonObj.selectedOption = match ? match[1] : selectedOption;
                } else {
                    jsonObj.selectedOption = "";
                }
                
                // 处理作答时间范围
                let dateRange = $("#sr-s-dateRange").val();
                if (dateRange) {
                    let dates = dateRange.split(' 至 ');
                    if (dates.length === 2) {
                        jsonObj.startRecordDate = dates[0];
                        jsonObj.endRecordDate = dates[1];
                    }
                }
                
                $.ajax({
                    type: 'POST',
                    url: '/export/activity_survey_result',
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnExportSurveyById, #btnExportSurveyByText, #btnExportSurveyWord").removeClass("disabled");
                        layer.closeAll();
                        if(res.resultCode === 200) {
                            location.href="/static/upload/temp/"+res.resultMsg;
                            layer.msg(exportName + '成功！', { icon: 1, time: 2000 });
                        }
                        else {
                            layer.msg(exportName + '失败！', { icon: 2, time: 2000 });
                        }
                    },
                    error: function(xhr, status, error) {
                        $("#btnExportSurveyById, #btnExportSurveyByText, #btnExportSurveyWord").removeClass("disabled");
                        layer.closeAll();
                        layer.msg(exportName + '失败！请检查网络连接。', { icon: 2, time: 3000 });
                    }
                });
            }

            // 批量导出word（新增功能）
            function exportSurveyWord() {
                layer.msg('数据处理中…（批量导出word）', {
                    icon: 17, shade: 0.05, time: false
                });
                $("#btnExportSurveyById, #btnExportSurveyByText, #btnExportSurveyWord").addClass("disabled");
                let jsonObj = {};
                jsonObj.activityId = $("#hidActivityId").val();
                jsonObj.surveyId = $("#hidSurveyId").val();
                
                // 添加筛选条件
                jsonObj.searchLoginName = $.trim($("#sr-s-loginName").val());
                jsonObj.searchRealName = $.trim($("#sr-s-realName").val());
                
                // 处理题目序号：从"第1题（单选题）题目内容"中提取数字
                let questionNumber = $("#sr-s-questionNumber").val();
                if (questionNumber) {
                    let match = questionNumber.match(/第(\d+)题/);
                    jsonObj.questionNumber = match ? match[1] : questionNumber;
                } else {
                    jsonObj.questionNumber = "";
                }
                
                // 处理选项序号：从"1. 选项内容"中提取序号
                let selectedOption = $("#sr-s-selectedOption").val();
                if (selectedOption) {
                    // 提取数字前缀，如从"1. 选项内容"中提取"1"
                    let match = selectedOption.match(/^(\d+)\./);
                    jsonObj.selectedOption = match ? match[1] : selectedOption;
                } else {
                    jsonObj.selectedOption = "";
                }
                
                // 处理作答时间范围
                let dateRange = $("#sr-s-dateRange").val();
                if (dateRange) {
                    let dates = dateRange.split(' 至 ');
                    if (dates.length === 2) {
                        jsonObj.startRecordDate = dates[0];
                        jsonObj.endRecordDate = dates[1];
                    }
                }
                
                $.ajax({
                    type: 'POST',
                    url: '/export/activity_survey_word',
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnExportSurveyById, #btnExportSurveyByText, #btnExportSurveyWord").removeClass("disabled");
                        layer.closeAll();
                        if(res.resultCode === 200) {
                            location.href="/static/upload/temp/"+res.resultMsg;
                            layer.msg('批量导出word成功！', { icon: 1, time: 2000 });
                        }
                        else {
                            layer.msg('批量导出word失败！', { icon: 2, time: 2000 });
                        }
                    },
                    error: function(xhr, status, error) {
                        $("#btnExportSurveyById, #btnExportSurveyByText, #btnExportSurveyWord").removeClass("disabled");
                        layer.closeAll();
                        layer.msg('批量导出word失败！请检查网络连接。', { icon: 2, time: 3000 });
                    }
                });
            }
            //活动图库
            $("#tbActivityList").on('click', '.gallery', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#modalActivityName").text(data.activityName);
                $("#gallery-modal .modal-body").empty();
                $.ajax({
                    type: "POST",
                    url: "/activityroom/activity_pic/getPicList",
                    data: 'activityId=' + data.id,
                    dataType: "json",
                    success: function(res) {
                        if(res.length > 0) {
                            let html = '<div class="row">';
                            res.forEach(function(pic) {
                                html += '<div class="col-md-3 mb-3">';
                                html += '<div class="card">';
                                html += '<img src="/static/upload/activity/mobile/' + pic.fileName + '" class="card-img-top view-original" alt="活动图片" style="height: 300px; object-fit: cover; cursor: pointer;" data-original="/static/upload/activity/' + pic.fileName + '">';
                                html += '</div></div>';
                            });
                            html += '</div>';
                            $("#gallery-modal .modal-body").html(html);

                            // 存储所有图片信息
                            window.currentImages = res;
                            window.currentImageIndex = 0;
                            
                            // 绑定图片点击事件
                            $(".view-original").click(function() {
                                let imgSrc = $(this).data("original");
                                window.currentImageIndex = $(this).closest('.col-md-3').index();
                                $("#fullscreen-image").attr("src", imgSrc);
                                $("#fullscreen-view").fadeIn(200);
                                updateNavigationButtons();
                            });
                        } else {
                            $("#gallery-modal .modal-body").html('<div class="text-center text-muted"><i class="fa fa-picture-o fa-3x mb-3"></i><p>暂无活动图片</p></div>');
                        }
                        $("#gallery-modal").modal();
                    },
                    error: function() {
                        layer.msg('获取图片列表失败', { icon: 2, time: 2000 });
                    }
                });
            });
            // 更新导航按钮状态
            function updateNavigationButtons() {
                if (window.currentImages.length <= 1) {
                    $("#prev-image, #next-image").hide();
                } else {
                    $("#prev-image, #next-image").show();
                    if (window.currentImageIndex === 0) {
                        $("#prev-image").hide();
                    }
                    if (window.currentImageIndex === window.currentImages.length - 1) {
                        $("#next-image").hide();
                    }
                }
            }
            // 上一张图片
            $("#prev-image").click(function(e) {
                e.stopPropagation();
                if (window.currentImageIndex > 0) {
                    window.currentImageIndex--;
                    let imgSrc = "/static/upload/activity/" + window.currentImages[window.currentImageIndex].fileName;
                    $("#fullscreen-image").attr("src", imgSrc);
                    updateNavigationButtons();
                }
            });
            // 下一张图片
            $("#next-image").click(function(e) {
                e.stopPropagation();
                if (window.currentImageIndex < window.currentImages.length - 1) {
                    window.currentImageIndex++;
                    let imgSrc = "/static/upload/activity/" + window.currentImages[window.currentImageIndex].fileName;
                    $("#fullscreen-image").attr("src", imgSrc);
                    updateNavigationButtons();
                }
            });
            // 鼠标悬停时显示箭头
            $("#fullscreen-view").hover(
                function() {
                    $("#prev-image, #next-image").css("opacity", "1");
                },
                function() {
                    $("#prev-image, #next-image").css("opacity", "0.5");
                }
            );

            // 点击全屏图片区域关闭
            $("#fullscreen-view").click(function() {
                $(this).fadeOut(200);
            });
            // 阻止图片点击事件冒泡
            $("#fullscreen-image").click(function(e) {
                e.stopPropagation();
            });
            let qrcode;
            let qrcodeName;
            //活动二维码
            $("#tbActivityList").on('click', '.qrcode', function () {
                if (qrcode != undefined) {
                    $("#qrcode").empty();
                }
                let data = oTable.row($(this).parents('tr')).data();
                qrcodeName = data.activityName;
                let activityId = data.id;
                qrcode = new QRCode(document.getElementById("qrcode"), {
                    text: window.location.protocol + "//" + window.location.host + "/app/activity/detail?activityId="+activityId,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H
                });
                $("#qrcode-modal").modal();
            });
            $("#download").click(function () {
                let img = $("#qrcode img")[0]; // 获取要下载的图片
                let url = img.src;                            // 获取图片地址
                let a = document.createElement('a');          // 创建一个a节点插入的document
                let event = new MouseEvent('click');          // 模拟鼠标click点击事件
                a.download = qrcodeName;                // 设置a节点的download属性值
                a.href = url;                                 // 将图片的src赋值给a节点的href
                a.dispatchEvent(event)
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/activityroom/activity/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //实现全选
            $("#chkall").change(function () {
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });

        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        /*活动列表 start*/
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "id", "bSortable": false },
            { "data": "structName", "bSortable": false },
            { "data": "activityName", "bSortable": false },
            { "data": "activityType", "bSortable": false,"render": function (data, type, row, meta) {
                    let labels = "";
                    switch (row.activityType) {
                        case 1:
                            labels = '驻场咨询（轻咨询）';
                            break;
                        case 2:
                            labels = '驻场咨询（50分钟以上）';
                            break;
                        case 3:
                            labels = '团体辅导';
                            break;
                        case 4:
                            labels = '心理关爱活动';
                            break;
                        default:
                            labels = '';
                    }
                    return labels;
                }},
            { "data": "startTime",  "bSortable": false },
            { "data": "endTime","bSortable": false},
            { "data": "counselorName","bSortable": false},
            { "data": "clockingInNum","bSortable": false},
            { "data": "surveyName","bSortable": false}
        ];
        let columnDefs =
            [
                {
                    targets: 10,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        let startDate = row.startTime;
                        let endDate = row.endTime;
                        if (startDate > getDateNowFormat()) {
                            labels = '<span class="badge badge-light">未开始</span>';
                        }
                        if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                            labels = '<span class="badge badge-success">进行中</span>';
                        }
                        if (getDateNowFormat() >= endDate) {
                            labels = '<span class="badge badge-danger">已结束</span>';
                        }
                        return labels;
                    }
                },
                {
                    targets: 11,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        if ('[[${canUpdate}]]' === 'true') {
                            labels += '<button class="btn btn-outline-warning btn-sm edit mr-1" title="修改"><i class="fa fa-edit"></i></button>';
                        }
                        if('[[${canAdd}]]' === 'true'){
                            labels += '<button class="btn btn-outline-primary btn-sm user mr-1" title="人员清单"><i class="fa fa-users"></i></button>';
                        }
                        if('[[${canViewPic}]]' === 'true'){
                            labels += '<button class="btn btn-outline-primary btn-sm gallery mr-1" title="活动图库"><i class="fa fa-picture-o"></i></button>';
                        }
                        if('[[${canDownloadQRCode}]]' === 'true'){
                            labels += '<button class="btn btn-outline-success btn-sm qrcode mr-1" title="活动二维码"><i class="fa fa-qrcode"></i></button>';
                        }
                        if('[[${canViewSurveyRecord}]]' === 'true'){
                            labels += '<button class="btn btn-outline-primary btn-sm survey mr-1" title="问卷记录">问卷记录</button>';
                        }
                        if('[[${canViewReport}]]' === 'true'){
                            labels += '<a href="/activityroom/activity/report?activityId=' + row.id + '" class="btn btn-outline-primary btn-sm mr-1" title="活动报告" target="_blank">活动报告</a>';
                        }
                        return labels;
                    }
                }
            ];
        let getQueryCondition = function (data) {
            let param = {};
            param.activityName = $.trim($("#sr-activityName").val());
            param.activityType = $("#sr-activityType").val();
            param.structId = $("#sr-structId").val();
            param.counselorId = $("#sr-counselor").val();

            // 处理时间范围
            let dateRange = $("#sr-dateRange").val();
            if (dateRange) {
                let dates = dateRange.split(' 至 ');
                if (dates.length === 2) {
                    param.startDate = dates[0];
                    param.endDate = dates[1];
                }
            }

            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };

        // 初始化时间范围选择器
        function initDateRangePicker() {
            $('#sr-dateRange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    cancelLabel: '清除',
                    applyLabel: '确定',
                    format: 'YYYY-MM-DD',
                    separator: ' 至 ',
                    customRangeLabel: '自定义',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                },
                ranges: {
                    '今天': [moment(), moment()],
                    '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    '最近7天': [moment().subtract(6, 'days'), moment()],
                    '最近30天': [moment().subtract(29, 'days'), moment()],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });

            $('#sr-dateRange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' 至 ' + picker.endDate.format('YYYY-MM-DD'));
            });

            $('#sr-dateRange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        }

        /*活动列表 end*/
        let getQueryCondition_s = function (data){
            let param = {};
            param.activityId = $("#hidActivityId").val();
            param.searchLoginName = $.trim($("#sr-s-loginName").val());
            param.searchRealName = $.trim($("#sr-s-realName").val());
            
            // 处理题目序号：从"第1题（单选题）题目内容"中提取数字
            let questionNumber = $("#sr-s-questionNumber").val();
            if (questionNumber) {
                let match = questionNumber.match(/第(\d+)题/);
                param.questionNumber = match ? match[1] : questionNumber;
            } else {
                param.questionNumber = "";
            }
            
            // 处理选项序号：从"1. 选项内容"中提取序号
            let selectedOption = $("#sr-s-selectedOption").val();
            if (selectedOption) {
                // 提取数字前缀，如从"1. 选项内容"中提取"1"
                let match = selectedOption.match(/^(\d+)\./);
                param.selectedOption = match ? match[1] : selectedOption;
            } else {
                param.selectedOption = "";
            }

            // 处理作答时间范围
            let dateRange = $("#sr-s-dateRange").val();
            if (dateRange) {
                let dates = dateRange.split(' 至 ');
                if (dates.length === 2) {
                    param.startRecordDate = dates[0];
                    param.endRecordDate = dates[1];
                }
            }

            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        }
        let oTableSurvey = null;
        let dynamicColumns_s = []; // 动态生成的列定义
        let surveyQuestions = []; // 问卷题目信息
        let isRebuilding = false; // 防止无限重建的标志
        let isModalInitializing = false; // 防止模态框重复初始化的标志
        let currentSurveyId = null; // 当前问卷ID，用于检测问卷变化

        // 初始化基础列定义
        let initializeBasicColumns = function() {
            dynamicColumns_s = [
                { "data": "id" },
                { "data": "surveyName" },
                { "data": "loginName" },
                { "data": "realName" },
                {"data": "recordDate", "bSortable": false, "render":
                        function(data,type,full,meta){
                            return moment(data).format("YYYY-MM-DD HH:mm:ss");
                        }
                },
                {
                    "data": "isDone", "bSortable": false,
                    render: function (data, type, row, meta) {
                        if (data === 1) {
                            return '<span class="badge badge-success badge-pill">已完成</span>';
                        }
                        if (data === 0) {
                            return '<span class="badge badge-light badge-pill">未完成</span>';
                        }
                    }
                }
            ];

            // 设置基础表头
            let headerHtml = '<tr>';
            headerHtml += '<th class="border-0 text-center">记录ID</th>';
            headerHtml += '<th class="border-0 text-center">问卷名称</th>';
            headerHtml += '<th class="border-0 text-center">用户名</th>';
            headerHtml += '<th class="border-0 text-center">姓名</th>';
            headerHtml += '<th class="border-0 text-center">作答时间</th>';
            headerHtml += '<th class="border-0 text-center">状态</th>';
            headerHtml += '</tr>';
            $("#surveyRecordTableHead").html(headerHtml);
        };

        // 重置问卷表格状态
        let resetSurveyTableState = function(clearQuestions = false) {
            // 重置变量
            dynamicColumns_s = [];
            
            // 根据参数决定是否清空题目信息
            if (clearQuestions) {
                surveyQuestions = []; // 清空题目信息，强制重新加载
                console.log("表格状态已重置，清空题目信息");
            } else {
                // 不清空题目信息，保持题目数据
                console.log("表格状态已重置，保留题目信息");
            }
            
            isRebuilding = false;
            isModalInitializing = false;

            // 初始化基础列定义
            initializeBasicColumns();
        };

        // 处理动态表格数据
        let processDynamicTableData = function(res) {
            if (res.data && res.data.length > 0) {
                // 收集所有题目信息
                let allQuestions = new Map();

                res.data.forEach(function(record) {
                    if (record.answerDetails && record.answerDetails.length > 0) {
                        record.answerDetails.forEach(function(detail) {
                            // 兼容不同的字段名称
                            let qNumber = detail.qNumber || detail.QNumber || detail.q_number;
                            let qContent = detail.qContent || detail.QContent || detail.q_content || '';
                            let qType = detail.qType || detail.QType || detail.q_type || 1;

                            if (qNumber && !allQuestions.has(qNumber)) {
                                allQuestions.set(qNumber, {
                                    qNumber: qNumber,
                                    qContent: qContent,
                                    qType: qType
                                });
                            }

                            let columnKey = "question_" + qNumber;

                            // 处理答案内容
                            let answerText = '';

                            // 兼容不同的字段名称
                            let selectedContent = detail.selectedContent || detail.SelectedContent || detail.selected_content || '';
                            let itemId = detail.itemId || detail.ItemId || detail.item_id || '';
                            let otherAnswer = detail.otherAnswer || detail.OtherAnswer || detail.other_answer || '';

                            if (qType === 3) {
                                // 填空题：显示填写的内容
                                if (selectedContent && selectedContent.trim() !== '') {
                                    answerText = selectedContent;
                                } else if (itemId && itemId.trim() !== '') {
                                    answerText = itemId;
                                } else if (otherAnswer && otherAnswer.trim() !== '') {
                                    answerText = otherAnswer;
                                } else {
                                    answerText = '未作答';
                                }
                            } else {
                                // 其他题型：显示选项内容
                                if (itemId && itemId.trim() !== '') {
                                    answerText = itemId;
                                } else if (selectedContent && selectedContent.trim() !== '') {
                                    answerText = selectedContent;
                                } else if (otherAnswer && otherAnswer.trim() !== '') {
                                    answerText = otherAnswer;
                                } else {
                                    answerText = '未作答';
                                }
                            }

                            record[columnKey] = answerText;
                            console.log("处理题目数据:", qNumber, "答案:", answerText, "列键:", columnKey);
                        });
                    }
                });

                // 如果是第一次加载数据且有题目，生成表头和列定义
                if (surveyQuestions.length === 0 && allQuestions.size > 0) {
                    let sortedQuestions = Array.from(allQuestions.values()).sort((a, b) => a.qNumber - b.qNumber);
                    console.log("生成表格结构，题目数量:", allQuestions.size, "题目数据:", sortedQuestions);
                    generateDynamicTableStructure(sortedQuestions);
                    console.log("表格结构生成完成，当前列定义数量:", dynamicColumns_s.length);
                    console.log("表头HTML已生成，检查表头内容:", $("#surveyRecordTableHead").html().length > 0 ? "有内容" : "无内容");
                }
            }
        };

        // 只处理数据，不重新生成表格结构
        let processDynamicTableDataOnly = function(res) {
            if (res.data && res.data.length > 0) {
                res.data.forEach(function(record) {
                    if (record.answerDetails && record.answerDetails.length > 0) {
                        record.answerDetails.forEach(function(detail) {
                            // 兼容不同的字段名称
                            let qNumber = detail.qNumber || detail.QNumber || detail.q_number;
                            let qType = detail.qType || detail.QType || detail.q_type || 1;

                            let columnKey = "question_" + qNumber;

                            // 处理答案内容
                            let answerText = '';

                            // 兼容不同的字段名称
                            let selectedContent = detail.selectedContent || detail.SelectedContent || detail.selected_content || '';
                            let itemId = detail.itemId || detail.ItemId || detail.item_id || '';
                            let otherAnswer = detail.otherAnswer || detail.OtherAnswer || detail.other_answer || '';

                            if (qType === 3) {
                                // 填空题：显示填写的内容
                                if (selectedContent && selectedContent.trim() !== '') {
                                    answerText = selectedContent;
                                } else if (itemId && itemId.trim() !== '') {
                                    answerText = itemId;
                                } else if (otherAnswer && otherAnswer.trim() !== '') {
                                    answerText = otherAnswer;
                                } else {
                                    answerText = '未作答';
                                }
                            } else {
                                // 其他题型：显示选项内容
                                if (itemId && itemId.trim() !== '') {
                                    answerText = itemId;
                                } else if (selectedContent && selectedContent.trim() !== '') {
                                    answerText = selectedContent;
                                } else if (otherAnswer && otherAnswer.trim() !== '') {
                                    answerText = otherAnswer;
                                } else {
                                    answerText = '未作答';
                                }
                            }

                            record[columnKey] = answerText;
                            console.log("只处理数据 - 题目:", qNumber, "答案:", answerText, "列键:", columnKey);
                        });
                    }
                });
            }
        };

        // 检查是否需要重建表格结构
        let checkIfTableNeedsRebuild = function(res) {
            // 如果正在重建中，不再重建
            if (isRebuilding) {
                console.log("正在重建中，跳过重建检查");
                return false;
            }

            // 如果没有题目列定义，且有数据，则需要重建
            if (surveyQuestions.length === 0 && res.data && res.data.length > 0) {
                // 检查数据中是否真的有题目信息
                let hasQuestions = false;
                for (let record of res.data) {
                    if (record.answerDetails && record.answerDetails.length > 0) {
                        hasQuestions = true;
                        break;
                    }
                }
                console.log("检查重建需求 - 有题目数据:", hasQuestions, "当前题目数量:", surveyQuestions.length);
                return hasQuestions;
            }

            console.log("不需要重建 - 题目数量:", surveyQuestions.length, "数据长度:", res.data ? res.data.length : 0);
            return false;
        };

        // 生成动态表格结构
        let generateDynamicTableStructure = function(questions) {
            console.log("开始生成动态表格结构，题目数量:", questions ? questions.length : 0);

            // 保存题目信息
            surveyQuestions = questions || [];

            // 如果还没有基础列，先初始化
            if (!dynamicColumns_s || dynamicColumns_s.length === 0) {
                console.log("初始化基础列定义");
                initializeBasicColumns();
            }

            // 安全地移除之前的题目列（保留基础列）
            if (dynamicColumns_s.length > 6) {
                console.log("移除之前的题目列，当前列数:", dynamicColumns_s.length);
                dynamicColumns_s = dynamicColumns_s.slice(0, 6);
            }

            // 动态添加题目列
            if (surveyQuestions && surveyQuestions.length > 0) {
                console.log("添加题目列，题目数量:", surveyQuestions.length);
                surveyQuestions.forEach(function(question, index) {
                    // 创建闭包来保存当前题目的信息
                    (function(currentQuestion) {
                        if (currentQuestion && currentQuestion.qNumber) {
                            dynamicColumns_s.push({
                                "data": "question_" + currentQuestion.qNumber,
                                "bSortable": false,
                                "render": function(data, type, row, meta) {
                                    if (data && data.trim() !== '' && data !== '未作答') {
                                        // 只显示答案内容，不显示题型标签
                                        return '<span class="text-dark" style="font-size: 0.9rem;">' + data + '</span>';
                                    }
                                    return '<span class="text-muted">未作答</span>';
                                }
                            });
                        }
                    })(question);
                });
            }

            console.log("表格结构生成完成，最终列数:", dynamicColumns_s.length);

            // 生成表头HTML
            generateTableHeader();
        };

        // 生成表头HTML
        let generateTableHeader = function() {
            try {
                console.log("开始生成表头，题目数量:", surveyQuestions ? surveyQuestions.length : 0);

                let headerHtml = '<tr>';
                headerHtml += '<th class="border-0 text-center">记录ID</th>';
                headerHtml += '<th class="border-0 text-center">问卷名称</th>';
                headerHtml += '<th class="border-0 text-center">用户名</th>';
                headerHtml += '<th class="border-0 text-center">姓名</th>';
                headerHtml += '<th class="border-0 text-center">作答时间</th>';
                headerHtml += '<th class="border-0 text-center">状态</th>';

                // 动态添加题目列头
                if (surveyQuestions && surveyQuestions.length > 0) {
                    surveyQuestions.forEach(function(question, index) {
                        if (question && question.qNumber) {
                            console.log("生成表头，题目数据:", question);

                            let typeText = getQuestionTypeText(question.qType || 1);
                            let fullContent = (question.qContent || '').replace(/<[^>]*>/g, '').trim();

                            // HTML转义防止XSS
                            fullContent = $('<div>').text(fullContent).html();

                            headerHtml += '<th class="border-0 text-center" style="min-width: 200px; max-width: 300px;">';
                            headerHtml += '<div class="text-center" style="height: auto; overflow: visible;">';
                            headerHtml += '<div class="font-weight-bold mb-2">Q' + (question.qNumber || 'undefined') + ' [' + typeText + ']</div>';
                            headerHtml += '<div class="small text-muted" style="line-height: 1.4; word-break: break-word; white-space: normal;">' + (fullContent || '题目内容为空') + '</div>';
                            headerHtml += '</div>';
                            headerHtml += '</th>';
                        }
                    });
                } else {
                    console.log("没有题目数据，只生成基础表头");
                }

                headerHtml += '</tr>';
                
                // 确保DOM元素存在后再设置HTML
                let tableHead = $("#surveyRecordTableHead");
                if (tableHead.length > 0) {
                    tableHead.html(headerHtml);
                    console.log("表头生成完成，最终HTML长度:", headerHtml.length);
                    console.log("表头已设置到DOM，当前DOM内容长度:", tableHead.html().length);
                } else {
                    console.error("找不到表头DOM元素");
                }
            } catch (error) {
                console.error("生成表头时出错:", error);
            }
        };

        // 获取题目类型颜色
        let getQuestionTypeColor = function(qType) {
            switch(qType) {
                case 1: return 'info';
                case 2: return 'warning';
                case 3: return 'success';
                case 4: return 'primary';
                case 5: return 'danger';
                default: return 'secondary';
            }
        };

        // 获取题目类型文本
        let getQuestionTypeText = function(qType) {
            switch(qType) {
                case 1: return '单选';
                case 2: return '多选';
                case 3: return '填空';
                case 4: return '排序';
                case 5: return '评分';
                default: return '其他';
            }
        };


        let initSurveyRecord = function () {
            try {
                console.log("开始初始化问卷记录表格");

                // 检查并销毁现有的DataTable
                if (oTableSurvey != null) {
                    console.log("销毁现有的DataTable");
                    try {
                        oTableSurvey.destroy();
                    } catch (destroyError) {
                        console.warn("销毁DataTable时出错:", destroyError);
                    }
                    oTableSurvey = null;
                }

                // 重置表格状态
                resetSurveyTableState(false);

                // 初始化题目下拉框
                initQuestionSelect();

                // 检查是否有题目信息，如果有则直接重建表格
                if (surveyQuestions && surveyQuestions.length > 0) {
                    console.log("检测到已有题目信息，直接重建表格");
                    rebuildTableWithQuestions();
                } else {
                    console.log("没有题目信息，使用基础初始化方法");
                    // 使用简化的初始化方法
                    initBasicDataTable();
                }

                console.log("问卷记录表格初始化完成");
            } catch (error) {
                console.error("初始化问卷记录表格时发生错误:", error);
                throw error;
            }
        };

        // 简化的DataTable初始化方法
        let initBasicDataTable = function() {
            console.log("使用简化方法初始化DataTable");

            // 确保有基础列定义
            let basicColumns = [
                { "data": "id" },
                { "data": "surveyName" },
                { "data": "loginName" },
                { "data": "realName" },
                {"data": "recordDate", "bSortable": false, "render":
                        function(data,type,full,meta){
                            return moment(data).format("YYYY-MM-DD HH:mm:ss");
                        }
                },
                {
                    "data": "isDone", "bSortable": false,
                    render: function (data, type, row, meta) {
                        if (data === 1) {
                            return '<span class="badge badge-success badge-pill">已完成</span>';
                        }
                        if (data === 0) {
                            return '<span class="badge badge-light badge-pill">未完成</span>';
                        }
                    }
                }
            ];

            // 生成基础表头
            let headerHtml = '<tr>';
            headerHtml += '<th class="border-0 text-center">记录ID</th>';
            headerHtml += '<th class="border-0 text-center">问卷名称</th>';
            headerHtml += '<th class="border-0 text-center">用户名</th>';
            headerHtml += '<th class="border-0 text-center">姓名</th>';
            headerHtml += '<th class="border-0 text-center">作答时间</th>';
            headerHtml += '<th class="border-0 text-center">状态</th>';
            headerHtml += '</tr>';
            $("#surveyRecordTableHead").html(headerHtml);

            // 初始化DataTable
            oTableSurvey = $("#tbSurveyRecord").DataTable({
                "ordering": false,
                "processing": true,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "bServerSide":true,
                "paging": true,
                "sAjaxSource": "/survey/surveyrecord/get_activity_surveyrecordlist_with_details",
                "fnServerData": function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition_s(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            try {
                                console.log("接收到的数据:", res);

                                // 检查是否需要重建表格结构
                                if (surveyQuestions.length === 0 && res.data && res.data.length > 0) {
                                    console.log("检测到需要添加题目列，重建表格");
                                    // 处理数据并重建表格结构
                                    processDynamicTableData(res);

                                    // 重建表格
                                    setTimeout(function() {
                                        rebuildTableWithQuestions();
                                    }, 100);
                                    return;
                                } else {
                                    // 只处理数据
                                    processDynamicTableDataOnly(res);
                                }

                                fnCallback(res);
                            } catch (error) {
                                console.error("处理问卷数据时出错:", error);
                                fnCallback(res);
                            }
                        },
                        "error": function(xhr, status, error) {
                            console.error("获取问卷数据失败:", error);
                            fnCallback({data: [], recordsTotal: 0, recordsFiltered: 0});
                        }
                    });
                },
                "columns": basicColumns,
                "columnDefs": [],
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "zeroRecords": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "sProcessing": "加载中...",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });

            // 初始化问卷记录筛选条件的日期选择器
            initSurveyRecordDatePicker();

            // 绑定筛选条件事件
            $("#btnSearchSurvey").off('click').on('click', function() {
                oTableSurvey.ajax.reload();
            });

            $("#btnResetSurvey").off('click').on('click', function() {
                $("#sr-s-loginName").val('');
                $("#sr-s-realName").val('');
                $("#sr-s-dateRange").val('');
                $("#sr-s-questionNumber").val('');
                $("#sr-s-selectedOption").val('');
                oTableSurvey.ajax.reload();
            });

            // 绑定题目变化事件
            $("#sr-s-questionNumber").off('change').on('change', function() {
                loadOptionsForQuestion($(this).val());
            });
        };

        // 重建包含题目列的表格
        let rebuildTableWithQuestions = function() {
            try {
                console.log("重建表格，包含题目列，题目数量:", surveyQuestions.length);

                // 销毁当前表格
                if (oTableSurvey) {
                    try {
                        oTableSurvey.destroy();
                    } catch (destroyError) {
                        console.warn("销毁DataTable时出错:", destroyError);
                    }
                    oTableSurvey = null;
                }

                // 生成完整的列定义
                let fullColumns = [
                    { "data": "id" },
                    { "data": "surveyName" },
                    { "data": "loginName" },
                    { "data": "realName" },
                    {"data": "recordDate", "bSortable": false, "render":
                            function(data,type,full,meta){
                                return moment(data).format("YYYY-MM-DD HH:mm:ss");
                            }
                    },
                    {
                        "data": "isDone", "bSortable": false,
                        render: function (data, type, row, meta) {
                            if (data === 1) {
                                return '<span class="badge badge-success badge-pill">已完成</span>';
                            }
                            if (data === 0) {
                                return '<span class="badge badge-light badge-pill">未完成</span>';
                            }
                        }
                    }
                ];

                // 添加题目列，确保每个列定义都是有效的
                if (surveyQuestions && surveyQuestions.length > 0) {
                    surveyQuestions.forEach(function(question) {
                        if (question && question.qNumber) {
                            let columnDef = {
                                "data": "question_" + question.qNumber,
                                "bSortable": false,
                                "render": function(data, type, row, meta) {
                                    if (data && data.trim() !== '' && data !== '未作答') {
                                        return '<span class="text-dark" style="font-size: 0.9rem;">' + data + '</span>';
                                    }
                                    return '<span class="text-muted">未作答</span>';
                                }
                            };
                            fullColumns.push(columnDef);
                            console.log("添加题目列:", question.qNumber, "列定义:", columnDef);
                        }
                    });
                }

                console.log("最终列定义数量:", fullColumns.length);

                // 生成完整表头
                generateTableHeader();

                // 重新初始化DataTable
                oTableSurvey = $("#tbSurveyRecord").DataTable({
                    "ordering": false,
                    "processing": true,
                    "searching": false,
                    "autoWidth": false,
                    "deferRender": true,
                    "lengthChange": false,
                    "bServerSide":true,
                    "paging": true,
                    "sAjaxSource": "/survey/surveyrecord/get_activity_surveyrecordlist_with_details",
                    "fnServerData": function (sSource, aoData, fnCallback) {
                        let jsonObj = getQueryCondition_s(aoData);
                        $.ajax({
                            "type": "post",
                            "url": sSource,
                            "dataType": "json",
                            "contentType": "application/json",
                            "data": JSON.stringify(jsonObj),
                            "success": function (res) {
                                try {
                                    console.log("重建后接收到的数据:", res);
                                    // 只处理数据，不再重建
                                    processDynamicTableDataOnly(res);
                                    fnCallback(res);
                                } catch (error) {
                                    console.error("重建后处理数据时出错:", error);
                                    fnCallback(res);
                                }
                            },
                            "error": function(xhr, status, error) {
                                console.error("重建后获取数据失败:", error);
                                fnCallback({data: [], recordsTotal: 0, recordsFiltered: 0});
                            }
                        });
                    },
                    "columns": fullColumns,
                    "columnDefs": [],
                    "language": {
                        "sLengthMenu": "每页显示 _MENU_ 条记录",
                        "emptyTable": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                        "zeroRecords": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                        "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                        "infoEmpty": "",
                        "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                        "sSearch": "搜索",
                        "sUrl": "",
                        "sProcessing": "加载中...",
                        "oPaginate": {
                            "sFirst": "首页",
                            "sPrevious": "前一页",
                            "sNext": "后一页",
                            "sLast": "尾页"
                        }
                    }
                });

                console.log("表格重建完成");
            } catch (error) {
                console.error("重建表格时出错:", error);
            }
        };

        let initDataTableWithStructure = function() {
            console.log("初始化DataTable，当前列定义数量:", dynamicColumns_s.length);
            console.log("当前题目数量:", surveyQuestions.length);

            // 确保有有效的列定义
            if (!dynamicColumns_s || dynamicColumns_s.length === 0) {
                console.log("列定义为空，初始化基础列定义");
                initializeBasicColumns();
            }

            // 验证列定义的有效性
            let validColumns = dynamicColumns_s.filter(col => col && col.data);
            if (validColumns.length !== dynamicColumns_s.length) {
                console.warn("发现无效的列定义，过滤后的列数量:", validColumns.length);
                dynamicColumns_s = validColumns;
            }

            console.log("最终使用的列定义:", dynamicColumns_s);

            // 检查表头是否存在，如果不存在则生成
            let currentHeaderHtml = $("#surveyRecordTableHead").html();
            console.log("当前表头内容长度:", currentHeaderHtml ? currentHeaderHtml.length : 0);

            if (!currentHeaderHtml || currentHeaderHtml.trim() === '') {
                console.log("表头为空，重新生成表头");
                if (surveyQuestions.length > 0) {
                    generateTableHeader();
                } else {
                    // 如果没有题目信息，生成基础表头
                    let headerHtml = '<tr>';
                    headerHtml += '<th class="border-0 text-center">记录ID</th>';
                    headerHtml += '<th class="border-0 text-center">问卷名称</th>';
                    headerHtml += '<th class="border-0 text-center">用户名</th>';
                    headerHtml += '<th class="border-0 text-center">姓名</th>';
                    headerHtml += '<th class="border-0 text-center">作答时间</th>';
                    headerHtml += '<th class="border-0 text-center">状态</th>';
                    headerHtml += '</tr>';
                    $("#surveyRecordTableHead").html(headerHtml);
                }
            }

            // 使用动态表格结构
            oTableSurvey = $("#tbSurveyRecord").DataTable({
                "ordering": false,
                "processing": true,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "bServerSide":true,
                "paging": true,
                "sAjaxSource": "/survey/surveyrecord/get_activity_surveyrecordlist_with_details",
                "fnServerData": function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition_s(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            try {
                                console.log("接收到的数据:", res);

                                // 检查是否需要重建表格结构
                                let needsRebuild = checkIfTableNeedsRebuild(res);
                                if (needsRebuild && !isRebuilding) {
                                    console.log("需要重建表格结构");
                                    isRebuilding = true;
                                    // 处理数据并重建表格结构
                                    processDynamicTableData(res);

                                    // 销毁当前表格并重新创建
                                    console.log("销毁当前DataTable并重新创建");
                                    oTableSurvey.destroy();

                                    // 延迟重新创建，确保DOM完全清理
                                    setTimeout(function() {
                                        try {
                                            console.log("重新创建DataTable，新的列定义数量:", dynamicColumns_s.length);
                                            console.log("重新创建时的题目数量:", surveyQuestions.length);

                                            // 确保表头正确生成
                                            if (surveyQuestions.length > 0) {
                                                console.log("重新生成表头");
                                                generateTableHeader();
                                            }

                                            initDataTableWithStructure();
                                            isRebuilding = false;
                                        } catch (error) {
                                            console.error("重新创建DataTable时出错:", error);
                                            isRebuilding = false;
                                        }
                                    }, 200);

                                    return; // 不执行fnCallback，等待重新创建完成
                                } else {
                                    // 只处理数据
                                    processDynamicTableDataOnly(res);
                                }

                                fnCallback(res);
                            } catch (error) {
                                console.error("处理问卷数据时出错:", error);
                                fnCallback(res);
                            }
                        },
                        "error": function(xhr, status, error) {
                            console.error("获取问卷数据失败:", error);
                            fnCallback({data: [], recordsTotal: 0, recordsFiltered: 0});
                        }
                    });
                },
                //配置列要显示的数据
                "columns": dynamicColumns_s.length > 0 ? dynamicColumns_s : [
                    { "data": "id" },
                    { "data": "surveyName" },
                    { "data": "loginName" },
                    { "data": "realName" },
                    { "data": "recordDate" },
                    { "data": "isDone" }
                ],
                "columnDefs": [],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "zeroRecords": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "sProcessing": "加载中...",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });

            // 初始化问卷记录筛选条件的日期选择器
            initSurveyRecordDatePicker();

            // 绑定筛选条件事件
            $("#btnSearchSurvey").off('click').on('click', function() {
                oTableSurvey.ajax.reload();
            });

            $("#btnResetSurvey").off('click').on('click', function() {
                $("#sr-s-loginName").val('');
                $("#sr-s-realName").val('');
                $("#sr-s-dateRange").val('');
                $("#sr-s-questionNumber").val('');
                $("#sr-s-selectedOption").val('');
                oTableSurvey.ajax.reload();
            });
            
            // 绑定题目变化事件
            $("#sr-s-questionNumber").off('change').on('change', function() {
                loadOptionsForQuestion($(this).val());
            });
        };

        // 初始化问卷记录筛选的日期选择器
        let initSurveyRecordDatePicker = function() {
            $('#sr-s-dateRange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    cancelLabel: '清除',
                    applyLabel: '确定',
                    format: 'YYYY-MM-DD',
                    separator: ' 至 ',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    firstDay: 1
                },
                ranges: {
                    '今天': [moment(), moment()],
                    '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    '最近7天': [moment().subtract(6, 'days'), moment()],
                    '最近30天': [moment().subtract(29, 'days'), moment()],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });

            $('#sr-s-dateRange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' 至 ' + picker.endDate.format('YYYY-MM-DD'));
            });

            $('#sr-s-dateRange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        };

        // 存储问卷题目数据
        let surveyQuestionsData = [];
        
        // 初始化题目下拉框
        let initQuestionSelect = function() {
            let surveyId = $("#hidSurveyId").val();
            if (!surveyId || surveyId === '0') {
                $("#sr-s-questionNumber").html('<option value="">无问卷题目</option>');
                $("#sr-s-selectedOption").html('<option value="">选择选项</option>');
                return;
            }
            
            $.ajax({
                "type": "post",
                "url": "/survey/surveyquestion/get_list_by_surveyId",
                "dataType": "json",
                "async": false,
                "data": { surveyId: surveyId },
                success: function(res) {
                    // 存储问卷数据供后续使用
                    surveyQuestionsData = JSON.parse(res);
                                         if (surveyQuestionsData && surveyQuestionsData.length > 0) {
                         let html = '<option value="">选择题目</option>';
                         surveyQuestionsData.forEach(function(question) {
                             let qTypeText = getQuestionTypeFullText(question.qType);
                             // 清理题目内容中的HTML标签
                             let qContent = question.qContent ? question.qContent.replace(/<[^>]*>/g, '').trim() : '';
                             // 限制题目内容长度，避免下拉框过宽
                             if (qContent.length > 30) {
                                 qContent = qContent.substring(0, 30) + '...';
                             }
                             html += '<option value="' + question.qNumber + '">' + 
                                    '第' + question.qNumber + '题（' + qTypeText + '）' + qContent + 
                                    '</option>';
                         });
                        $("#sr-s-questionNumber").html(html);
                    } else {
                        $("#sr-s-questionNumber").html('<option value="">无题目数据</option>');
                        surveyQuestionsData = [];
                    }
                    $("#sr-s-selectedOption").html('<option value="">选择选项</option>');
                },
                error: function() {
                    $("#sr-s-questionNumber").html('<option value="">加载失败</option>');
                    $("#sr-s-selectedOption").html('<option value="">选择选项</option>');
                    surveyQuestionsData = [];
                }
            });
        };
        
        // 根据题目加载选项
        let loadOptionsForQuestion = function(questionNumber) {
            $("#sr-s-selectedOption").html('<option value="">选择选项</option>');
            
            if (!questionNumber || surveyQuestionsData.length === 0) {
                return;
            }
            
            // 从已加载的数据中查找对应题目
            let currentQuestion = surveyQuestionsData.find(q => q.qNumber == questionNumber);
            if (currentQuestion && currentQuestion.listItems && currentQuestion.listItems.length > 0) {
                let html = '<option value="">选择选项</option>';
                currentQuestion.listItems.forEach(function(item) {
                    html += '<option value="' + item.itemContent + '">' + 
                           item.itemNo + '. ' + item.itemContent + 
                           '</option>';
                });
                $("#sr-s-selectedOption").html(html);
            }
        };
        
        // 获取题目类型完整文字（用于筛选下拉框）
        let getQuestionTypeFullText = function(qType) {
            switch(qType) {
                case 1: return '单选题';
                case 2: return '多选题';
                case 3: return '填空题';
                case 4: return '排序题';
                case 5: return '评分题';
                default: return '其他';
            }
        };

        // 初始化筛选条件折叠功能
        $(document).ready(function() {
            $('#filterCollapse').on('show.bs.collapse', function () {
                $('#filterToggleIcon').removeClass('mdi-chevron-down').addClass('mdi-chevron-up');
            });

            $('#filterCollapse').on('hide.bs.collapse', function () {
                $('#filterToggleIcon').removeClass('mdi-chevron-up').addClass('mdi-chevron-down');
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">问卷结果分析</a></li>
                    </ol>
                </div>
                <h4 class="page-title"><i class="fa fa-area-chart mr-1"></i>问卷结果分析</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-print-none">
                    <div class="card-title"><h5>活动名称：<span id="activityName"></span></h5></div>
                </div>
                <!-- end card body-->
                <div class="card-body" id="report">
                    <div class="card-widgets mr-2 mb-4">
                        <a href="/activityroom/activity/list" class="btn btn-outline-primary d-print-none"><i class="fa fa-angle-left"></i></a>
                    </div>
                    <div class="clearfix">
                        <div class="text-center mb-3">
                            <h4 class="m-0 letter-spacing-2">《<span id="surveyName"></span>》结果统计</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="header-title mb-3">总体完成情况</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-centered">
                                    <thead class="thead-light">
                                    <tr>
                                        <th>参与调查总人数</th>
                                        <th>已完成人数</th>
                                        <th>完成率</th>
                                        <th>未完成人数</th>
                                        <th>未完成率</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td class="totalCount">-</td>
                                        <td class="doneCount">-</td>
                                        <td class="doneRate">-</td>
                                        <td class="unDoneCount">-</td>
                                        <td class="unDoneRate">-</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="header-title mb-3">题目选项统计</h4>
                            <div id="questionStats">
                                <!-- 动态生成题目统计内容 -->
                            </div>
                        </div>
                    </div>
                    <!-- 评分题总体统计 -->
                    <div class="row" id="ratingOverallRow" style="display: none;">
                        <div class="col-12">
                            <h4 class="header-title mb-3">评分题总体统计</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-centered">
                                    <thead class="thead-light">
                                    <tr>
                                        <th>所有评分题综合平均分</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td class="ratingOverallAverage">-</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
    <input type="hidden" id="hidActivityId" th:value="${activityId}" />
    <input type="hidden" id="hidSurveyId" th:value="${surveyId}" />
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        $(function () {
            getData();
        });

        let getData = function () {
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            let jsonObj = getQueryCondition();
            $.ajax({
                type: 'POST',
                url: "/activityroom/activity/get_survey_stat",
                data: jsonObj,
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    $("#activityName").html(res.activityName);
                    $("#surveyName").html(res.surveyName);
                    if (res.surveyRecords.length === 0) {
                        layer.msg("未查询到问卷记录数据！", { icon: 0, time: 2000 });
                        $("#report").hide();
                        return;
                    }
                    $(".totalCount").html(res.totalCount);
                    $(".doneCount").html(res.doneCount);
                    $(".doneRate").html(res.doneRate.toFixed(2) + '%');
                    $(".unDoneCount").html(res.unDoneCount);
                    $(".unDoneRate").html(res.unDoneRate.toFixed(2) + '%');

                    // 显示评分题总体平均分
                    if (res.ratingOverallAverage !== null && res.ratingOverallAverage !== undefined) {
                        $(".ratingOverallAverage").html(res.ratingOverallAverage.toFixed(2) + '分');
                        $("#ratingOverallRow").show();
                    } else {
                        $("#ratingOverallRow").hide();
                    }

                    getResultStat(res);
                }
            });
        };

        let getResultStat = function (data) {
            let questionStatsHtml = '';
            if (data.surveyResultStat && data.surveyResultStat.length > 0) {
                data.surveyResultStat.forEach(function(result) {
                    questionStatsHtml += '<div class="mb-4">';
                    questionStatsHtml += '<div class="question_title font-14 mb-3"><strong>第' + result.QNumber + '题：' + result.QContent + '</strong></div>';

                    // 为排序题添加说明
                    if (result.QType === 4) {
                        questionStatsHtml += '<div class="alert alert-info mb-3">';
                        questionStatsHtml += '<small><i class="fa fa-info-circle mr-1"></i>';
                        questionStatsHtml += '排序结果按平均排名从小到大排列，平均排名越小表示优先级越高';
                        questionStatsHtml += '</small>';
                        questionStatsHtml += '</div>';
                    }

                    // 为评分题显示平均分
                    if (result.QType === 5) {
                        questionStatsHtml += '<div class="alert alert-success mb-3">';
                        questionStatsHtml += '<h5 class="mb-0"><i class="fa fa-star mr-1"></i>';
                        questionStatsHtml += '该题平均分：<strong>' + (result.ratingAverage ? result.ratingAverage.toFixed(2) : '0.00') + '分</strong>';
                        questionStatsHtml += '</h5>';
                        questionStatsHtml += '</div>';
                    }

                    questionStatsHtml += '<table class="table table-bordered table-centered">';
                    questionStatsHtml += '<thead class="thead-light">';

                    // 根据题型显示不同的表头
                    if (result.QType === 4) { // 排序题
                        questionStatsHtml += '<tr><th>选项</th><th>参与排序人数</th><th>平均排名</th></tr>';
                    } else if (result.QType === 5) { // 评分题
                        questionStatsHtml += '<tr><th>分值</th><th>选择人数</th><th>选择百分比</th></tr>';
                    } else { // 单选题、多选题
                        questionStatsHtml += '<tr><th>选项</th><th>选择人数</th><th>选择百分比</th></tr>';
                    }

                    questionStatsHtml += '</thead>';
                    questionStatsHtml += '<tbody>';
                    if (result.listResultCounts && result.listResultCounts.length > 0) {
                        // 对排序题按平均排名从小到大排序
                        let sortedResults = result.listResultCounts;
                        if (result.QType === 4) {
                            sortedResults = [...result.listResultCounts].sort(function(a, b) {
                                let rankA = parseFloat(a.selRate) || 999; // 如果解析失败，设为很大的数
                                let rankB = parseFloat(b.selRate) || 999;
                                return rankA - rankB; // 从小到大排序
                            });
                        }

                        sortedResults.forEach(function(countItem, index) {
                            if (result.QType === 4) { // 排序题显示平均排名
                                let rowClass = '';
                                let rankIcon = '';

                                // 为前三名添加特殊样式和图标
                                if (index === 0) {
                                    rowClass = 'table-warning'; // 第一名：金色背景
                                    rankIcon = '<i class="fa fa-trophy text-warning mr-1"></i>';
                                } else if (index === 1) {
                                    rowClass = 'table-info'; // 第二名：蓝色背景
                                    rankIcon = '<i class="fa fa-medal text-info mr-1"></i>';
                                } else if (index === 2) {
                                    rowClass = 'table-success'; // 第三名：绿色背景
                                    rankIcon = '<i class="fa fa-award text-success mr-1"></i>';
                                }

                                questionStatsHtml += '<tr class="' + rowClass + '">';
                                questionStatsHtml += '<td>' + rankIcon + countItem.itemContent + '</td>';
                                questionStatsHtml += '<td>' + countItem.selCount + '</td>';
                                questionStatsHtml += '<td><strong>' + countItem.selRate + '</strong></td>';
                                questionStatsHtml += '</tr>';
                            } else { // 单选题、多选题显示占比进度条
                                let progressWrapper = '';
                                progressWrapper += '<div class="progress-w-percent mb-0">';
                                progressWrapper += '<span class="progress-value">' + countItem.selRate + '</span>';
                                progressWrapper += '<div class="progress progress-sm">';
                                let percentage = parseFloat(countItem.selRate.replace('%', ''));
                                progressWrapper += '<div class="progress-bar bg-primary" role="progressbar" style="width: ' + percentage + '%" aria-valuenow="' + percentage + '" aria-valuemin="0" aria-valuemax="100"></div>';
                                progressWrapper += '</div>';
                                progressWrapper += '</div>';

                                questionStatsHtml += '<tr>';
                                questionStatsHtml += '<td>' + countItem.itemContent + '</td>';
                                questionStatsHtml += '<td>' + countItem.selCount + '</td>';
                                questionStatsHtml += '<td>' + progressWrapper + '</td>';
                                questionStatsHtml += '</tr>';
                            }
                        });
                    } else {
                        questionStatsHtml += '<tr><td colspan="3" class="text-center text-muted">暂无选项数据</td></tr>';
                    }
                    questionStatsHtml += '</tbody>';
                    questionStatsHtml += '</table>';
                    questionStatsHtml += '</div>';
                });
            } else {
                questionStatsHtml = '<div class="text-center text-muted"><p>暂无题目统计数据</p></div>';
            }
            $("#questionStats").html(questionStatsHtml);
        };

        let getQueryCondition = function () {
            let param = {};
            param.activityId = $("#hidActivityId").val();
            param.surveyId = $("#hidSurveyId").val();
            return param;
        };
    </script>
</th:block>
</body>
</html>

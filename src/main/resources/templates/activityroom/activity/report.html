<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .evaluation-item {
            transition: all 0.3s ease;
        }
        .evaluation-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        #evaluationPagination .btn {
            transition: all 0.2s ease;
        }
        #evaluationPagination .btn:hover {
            transform: translateY(-1px);
        }
        .evaluation-stats {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row d-print-none">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">活动报告</a></li>
                    </ol>
                </div>
                <h4 class="page-title">活动报告</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body ml-4 mr-4" id="report">
                    <div class="clearfix">
                        <div class="text-center mt-4 mb-4">
                            <h4 class="m-0 letter-spacing-2 font-weight-500">活动报告</h4>
                        </div>
                    </div>

                    <!-- 活动基本信息 -->
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-primary" style="width: 4px; height: 16px; margin-right: 8px;"></div>
                            <h5 class="mb-0" style="line-height: 20px; transform: translateY(-4px);">活动基本信息</h5>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="mr-1">活动主题：</span>
                                    <span id="activityName" class="text-dark flex-grow-1"></span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="mr-1">活动类型：</span>
                                    <span id="activityType" class="text-dark flex-grow-1"></span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="mr-1">驻场咨询师：</span>
                                    <span id="counselor" class="text-dark flex-grow-1"></span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="mr-1">开始时间：</span>
                                    <span id="startTime" class="text-dark flex-grow-1"></span>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="mr-1">结束时间：</span>
                                    <span id="endTime" class="text-dark flex-grow-1"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <!-- 签到签退数据 -->
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-success" style="width: 4px; height: 16px; margin-right: 8px;"></div>
                            <h5 class="mb-0" style="line-height: 20px; transform: translateY(-4px);">签到签退数据</h5>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12 mb-2">
                                <div class="align-items-center">
                                    <span class="mr-1">签到人数：</span>
                                    <span id="clockingInCount" class="text-dark flex-grow-2 mr-2"></span>
                                    <span class="mr-1">签退人数：</span>
                                    <span id="clockingOutCount" class="text-dark flex-grow-2"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>

                    <!-- 问卷调查数据 -->
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-info" style="width: 4px; height: 16px; margin-right: 8px;"></div>
                            <h5 class="mb-0" style="line-height: 20px; transform: translateY(-4px);">问卷调查数据</h5>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="mr-1">问卷名称：</span>
                                    <span id="surveyName" class="text-dark flex-grow-2 mr-2"></span>
                                    <span class="mr-1">提交人数：</span>
                                    <span id="surveySubmitCount" class="text-dark flex-grow-2"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="divider dashed large mt-2 mb-2"></div>

                    <!-- 参与人员清单 -->
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-secondary" style="width: 4px; height: 16px; margin-right: 8px;"></div>
                            <h5 class="mb-0" style="line-height: 20px; transform: translateY(-4px);">参与人员清单</h5>
                        </div>
                        <div class="mt-3">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped table-sm" id="participantsTable">
                                    <thead class="thead-light">
                                    <tr>
                                        <th>序号</th>
                                        <th>用户名</th>
                                        <th>姓名</th>
                                        <th>所属组织</th>
                                        <th>签到时间</th>
                                        <th>签退时间</th>
                                        <th>是否完成问卷调查</th>
                                    </tr>
                                    </thead>
                                    <tbody id="participantsTableBody">
                                    <!-- 参与人员数据将通过JS动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <!-- 活动点评 -->
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-warning" style="width: 4px; height: 16px; margin-right: 8px;"></div>
                            <h5 class="mb-0" style="line-height: 20px; transform: translateY(-4px);">活动点评</h5>
                        </div>
                        <div class="mb-3 mt-3">
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center mb-2">
                                    <strong class="d-block">活动总评：</strong>
                                </div>
                                <div id="overallEvaluation" class="rounded border border-light p-2"></div>
                            </div>
                        </div>
                        <div>
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center justify-content-between mb-2">
                                    <strong class="d-block">个人点评：</strong>
                                    <div id="evaluationStats" class="evaluation-stats">
                                        <!-- 统计信息将通过JS动态填充 -->
                                    </div>
                                </div>
                                <div id="selfEvaluations" class="mt-2">
                                    <!-- 个人点评列表将通过JS动态填充 -->
                                </div>
                                <div id="evaluationPagination" class="text-center mt-3" style="display: none;">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="showMoreEvaluations">
                                        <i class="mdi mdi-chevron-down mr-1"></i>显示更多
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm ml-2" id="showLessEvaluations" style="display: none;">
                                        <i class="mdi mdi-chevron-up mr-1"></i>收起
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- end card -->
        </div> <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script type="text/javascript">
        let activityId = getUrlParam('activityId');
        let initReport = function () {
            layer.msg('请稍后…', { icon: 17, shade: 0.2, time: false });
            $.ajax({
                type: "GET",
                url: "/activityroom/activity/get_report?activityId=" + activityId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (res) {
                    layer.closeAll();
                    let activityReport = res;

                    // 活动基本信息
                    $("#activityName").text(activityReport.activityName);
                    $("#startTime").text(moment(activityReport.startTime).format('YYYY-MM-DD HH:mm'));
                    $("#endTime").text(moment(activityReport.endTime).format('YYYY-MM-DD HH:mm'));
                    $("#counselor").text(activityReport.counselor);
                    $("#activityType").text(activityReport.activityType);

                    // 签到签退数据
                    $("#clockingInCount").text(activityReport.clockingInCount != null ? activityReport.clockingInCount : "");
                    $("#clockingOutCount").text(activityReport.clockingOutCount != null ? activityReport.clockingOutCount : "");

                    // 活动点评
                    $("#overallEvaluation").text(activityReport.overallEvaluation || "暂无活动总评");
                    // 个人点评（自评列表）- 使用分页显示
                    renderSelfEvaluations(activityReport.selfEvaluations || []);

                    // 问卷调查数据
                    $("#surveyName").text(activityReport.surveyName || "");
                    $("#surveySubmitCount").text(activityReport.surveySubmitCount != null ? activityReport.surveySubmitCount : "");

                    // 参与人员清单
                    let participantsTableBody = "";
                    if (activityReport.participants && activityReport.participants.length > 0) {
                        activityReport.participants.forEach(function(participant, idx) {
                            let clockingInTime = participant.clockingInTime ? moment(participant.clockingInTime).format('YYYY-MM-DD HH:mm:ss') : '-';
                            let clockingOutTime = participant.clockingOutTime ? moment(participant.clockingOutTime).format('YYYY-MM-DD HH:mm:ss') : '-';
                            let isSurveyCompleted = participant.isSurveyCompleted ?
                                '<span class="badge badge-success">是</span>' :
                                '<span class="badge badge-secondary">否</span>';

                            participantsTableBody += `
                                <tr>
                                    <td>${idx + 1}</td>
                                    <td>${participant.loginName || '-'}</td>
                                    <td>${participant.realName || '-'}</td>
                                    <td>${participant.structName || '-'}</td>
                                    <td>${clockingInTime}</td>
                                    <td>${clockingOutTime}</td>
                                    <td>${isSurveyCompleted}</td>
                                </tr>`;
                        });
                    } else {
                        participantsTableBody = `
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="mdi mdi-account-remove-outline mb-2" style="font-size: 2rem;"></i>
                                    <p class="mb-0">暂无参与人员</p>
                                </td>
                            </tr>`;
                    }
                    $("#participantsTableBody").html(participantsTableBody);
                }
            });
        };

        // 个人点评分页显示相关变量和函数
        let allSelfEvaluations = [];
        let currentDisplayCount = 5; // 初始显示数量
        const pageSize = 5; // 每次加载数量

        /**
         * 渲染个人点评列表（支持分页）
         * @param {Array} evaluations 点评数据
         */
        function renderSelfEvaluations(evaluations) {
            allSelfEvaluations = evaluations;
            currentDisplayCount = Math.min(pageSize, evaluations.length);

            updateEvaluationDisplay();
            updatePaginationControls();
        }

        /**
         * 更新点评显示
         */
        function updateEvaluationDisplay() {
            let selfEvaluationsHtml = "";

            if (allSelfEvaluations.length > 0) {
                // 显示当前页的点评
                const displayEvaluations = allSelfEvaluations.slice(0, currentDisplayCount);
                displayEvaluations.forEach(function(item, idx) {
                    selfEvaluationsHtml += `
                        <div class="card mb-2 border-primary evaluation-item" data-index="${idx}">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0">
                                        <span class="font-weight-bold font-14">${item.realName || '未知用户'}</span>
                                    </h6>
                                    <small class="text-muted ml-auto">#${idx + 1}</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <div>
                                        <span>点评内容：</span>
                                        <span class="ml-2">${item.evaluationContent || '暂无点评内容'}</span>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <span>点评标签：</span>
                                        <span class="ml-2">
                                            ${item.tags ? item.tags.split(',').map(tag =>
                                                `<span class="badge badge-primary-lighten badge-pill mr-1">${tag}</span>`
                                            ).join('') : '<span class="text-muted">暂无标签</span>'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>`;
                });
            } else {
                selfEvaluationsHtml = `
                    <div class="text-center text-muted py-4">
                        <i class="mdi mdi-comment-remove-outline mb-2" style="font-size: 2rem;"></i>
                        <p class="mb-0">暂无个人点评</p>
                    </div>`;
            }

            $("#selfEvaluations").html(selfEvaluationsHtml);

            // 更新统计信息
            if (allSelfEvaluations.length > 0) {
                $("#evaluationStats").html(`共 ${allSelfEvaluations.length} 条点评，当前显示 ${currentDisplayCount} 条`);
            } else {
                $("#evaluationStats").html('');
            }
        }

        /**
         * 更新分页控制按钮
         */
        function updatePaginationControls() {
            const $pagination = $("#evaluationPagination");
            const $showMore = $("#showMoreEvaluations");
            const $showLess = $("#showLessEvaluations");

            if (allSelfEvaluations.length <= pageSize) {
                // 数据量少，不需要分页
                $pagination.hide();
            } else {
                $pagination.show();

                // 显示更多按钮
                if (currentDisplayCount < allSelfEvaluations.length) {
                    $showMore.show();
                } else {
                    $showMore.hide();
                }

                // 收起按钮
                if (currentDisplayCount > pageSize) {
                    $showLess.show();
                } else {
                    $showLess.hide();
                }
            }
        }

        // 绑定分页按钮事件
        $(document).on('click', '#showMoreEvaluations', function() {
            currentDisplayCount = Math.min(currentDisplayCount + pageSize, allSelfEvaluations.length);
            updateEvaluationDisplay();
            updatePaginationControls();

            // 平滑滚动到新加载的内容
            setTimeout(function() {
                const $newItems = $('.evaluation-item').slice(currentDisplayCount - pageSize);
                if ($newItems.length > 0) {
                    $('html, body').animate({
                        scrollTop: $newItems.first().offset().top - 100
                    }, 300);
                }
            }, 100);
        });

        $(document).on('click', '#showLessEvaluations', function() {
            currentDisplayCount = pageSize;
            updateEvaluationDisplay();
            updatePaginationControls();

            // 滚动回到点评区域顶部
            $('html, body').animate({
                scrollTop: $('#selfEvaluations').offset().top - 100
            }, 300);
        });

        $(function () {
            initReport();
        });
    </script>
</th:block>
</body>
</html>
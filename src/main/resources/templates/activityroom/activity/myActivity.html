<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">我的活动</a></li>
                    </ol>
                </div>
                <h4 class="page-title">我的活动</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-activityName" class="sr-only">活动主题：</label>
                                        <input type="text" class="form-control" id="sr-activityName" name="sr-activityName" placeholder="活动主题" autocomplete="off" style="width:200px;">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="tbActivityList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th>活动主题</th>
                                <th>活动类型</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>驻场咨询师</th>
                                <th>调查问卷</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
    <!-- modal.二维码 start -->
    <div id="qrcode-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary text-light">
                    <h5 class="modal-title">活动专属二维码</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <div id="qrcode" style="width: 250px; height: 250px; margin: 0px auto; border: 25px solid rgb(255, 255, 255);">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light mr-2" data-dismiss="modal">关闭</button>
                    <input type="button" class="btn btn-primary" id="download" value="下载" />
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.二维码 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/qrcode/qrcode.min.js}"></script>
    <script type="text/javascript">
        let users;
        $(function () {
            //初始化页面权限
            initPage();
            //添加活动
            $("#btnAdd").click(function () {
                location.href = "/activityroom/activity/add";
            });
            //查询
            $("#btnQuery").click(function () {
                oTable.draw();
            });
            //活动列表Datatables
            $("#tbActivityList").bsDataTables({
                columns: columns,
                url: '/activityroom/activity/get_list_by_paged',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //查看已签到人员清单
            $("#tbActivityList").on('click', '.user', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/activityroom/clocking/list?activityId=" + data.id;
            });
            //修改
            $("#tbActivityList").on('click', '.edit', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/activityroom/activity/update?id=" + data.id;
            });
            $("#tbActivityList").on('click', '.survey', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/activityroom/activity/survey_record_list?activityId=" + data.id;
            });
            let qrcode;
            let qrcodeName;
            //活动二维码
            $("#tbActivityList").on('click', '.qrcode', function () {
                if (qrcode != undefined) {
                    $("#qrcode").empty();
                }
                let data = oTable.row($(this).parents('tr')).data();
                qrcodeName = data.activityName;
                let activityId = data.id;
                qrcode = new QRCode(document.getElementById("qrcode"), {
                    text: window.location.protocol + "//" + window.location.host + "/app/activity/detail?activityId="+activityId,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H
                });
                $("#qrcode-modal").modal();
            });
            $("#download").click(function () {
                let img = $("#qrcode img")[0]; // 获取要下载的图片
                let url = img.src;                            // 获取图片地址
                let a = document.createElement('a');          // 创建一个a节点插入的document
                let event = new MouseEvent('click');          // 模拟鼠标click点击事件
                a.download = qrcodeName;                // 设置a节点的download属性值
                a.href = url;                                 // 将图片的src赋值给a节点的href
                a.dispatchEvent(event)
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/activityroom/activity/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //实现全选
            $("#chkall").change(function () {
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });

        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        /*活动列表 start*/
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "activityName", "bSortable": false },
            { "data": "activityType", "bSortable": false,"render": function (data, type, row, meta) {
                    let labels = "";
                    switch (row.activityType) {
                        case 1:
                            labels = '驻场咨询（轻咨询）';
                            break;
                        case 2:
                            labels = '驻场咨询（50分钟以上）';
                            break;
                        case 3:
                            labels = '团体辅导';
                            break;
                        case 4:
                            labels = '心理关爱活动';
                            break;
                        default:
                            labels = '';
                    }
                    return labels;
                }},
            { "data": "startTime",  "bSortable": false },
            { "data": "endTime","bSortable": false},
            { "data": "counselorName","bSortable": false},
            { "data": "surveyName","bSortable": false}
        ];
        let columnDefs =
            [
                {
                    targets: 7,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        let startDate = row.startTime;
                        let endDate = row.endTime;
                        if (startDate > getDateNowFormat()) {
                            labels = '<span class="badge badge-light">未开始</span>';
                        }
                        if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                            labels = '<span class="badge badge-success">进行中</span>';
                        }
                        if (getDateNowFormat() >= endDate) {
                            labels = '<span class="badge badge-danger">已结束</span>';
                        }
                        return labels;
                    }
                },
                {
                    targets: 8,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        if ('[[${canUpdate}]]' === 'true') {
                            labels += '<button class="btn btn-outline-warning btn-sm edit mr-1" title="修改"><i class="fa fa-edit"></i></button>';
                        }
                        if('[[${canAdd}]]' === 'true'){
                            labels += '<button class="btn btn-outline-primary btn-sm user mr-1" title="人员清单">人员清单</button>';
                            labels += '<button class="btn btn-outline-primary btn-sm survey mr-1">问卷调查记录</button>';
                        }
                        if('[[${canDownloadQRCode}]]' === 'true'){
                            labels += '<button class="btn btn-outline-success btn-sm qrcode" title="活动二维码"><i class="fa fa-qrcode"></i></button>';
                        }
                        return labels;
                    }
                }
            ];
        let getQueryCondition = function (data) {
            let param = {};
            param.activityName = $.trim($("#sr-activityName").val());
            param.activityType = $("#sr-activityType").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        /*活动列表 end*/
    </script>
</th:block>
</body>
</html>
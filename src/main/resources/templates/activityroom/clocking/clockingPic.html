<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
    <style>
        /* 上传控件样式优化 */
        .file-input {
            width: 100% !important;
            min-width: 300px;
            display: block;
        }
        .file-input .file-preview {
            width: 100% !important;
            margin-bottom: 0;
            border: none;
        }
        .file-input .file-drop-zone {
            width: 100% !important;
            min-height: 120px;
            border: 2px dashed #e9ecef;
            border-radius: 4px;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            margin: 0;
        }
        .file-input .file-drop-zone:hover {
            border-color: #007bff;
            background-color: #f1f8ff;
        }
        .file-input .file-preview-frame {
            margin: 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            width: calc(25% - 16px);
            float: left;
        }
        .file-input .file-preview-frame:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .file-input .file-preview-image {
            height: 160px;
            object-fit: cover;
            width: 100%;
        }
        .file-input .file-actions {
            padding: 8px;
            clear: both;
        }
        .file-input .btn-file {
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: 500;
        }
        .file-input .file-caption {
            border-radius: 4px;
            padding: 8px;
            width: 100%;
        }
        .file-input .file-preview-thumbnails {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            margin: 0 -8px;
        }
        .file-input .file-preview-frame {
            margin: 8px;
            flex: 0 0 calc(25% - 16px);
            max-width: calc(25% - 16px);
        }
        .file-input .file-preview-frame .kv-file-content {
            width: 100%;
            height: 160px;
            overflow: hidden;
        }
        .file-input .file-preview-frame .kv-file-content img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .file-input .file-preview-frame .file-footer-buttons {
            position: absolute;
            top: 0;
            right: 0;
            padding: 4px;
            background: rgba(0,0,0,0.5);
            border-radius: 0 4px 0 4px;
        }
        .file-input .file-preview-frame .file-footer-buttons .btn {
            padding: 2px 6px;
            font-size: 12px;
            color: #fff;
            background: transparent;
            border: none;
        }
        .file-input .file-preview-frame .file-footer-buttons .btn:hover {
            background: rgba(255,255,255,0.1);
        }
        
        /* 图片展示区域样式优化 */
        .pic-list-card .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
        }
        .pic-list-card .card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .pic-list-card .card.selected {
            box-shadow: 0 0 0 2px #007bff;
        }
        .pic-list-card .card-img-top {
            border-radius: 8px;
        }

        /* 复选框样式 */
        .pic-list-card .custom-control {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 2;
            margin: 0;
            padding-left: 1.75rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .pic-list-card .card:hover .custom-control {
            opacity: 1;
        }
        .pic-list-card .card.selected .custom-control {
            opacity: 1;
        }
        .pic-list-card .custom-control-label::before {
            border-radius: 4px;
            border: 2px solid #fff;
            width: 20px;
            height: 20px;
            top: 0;
            left: -1.75rem;
            background-color: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .pic-list-card .custom-control-label::after {
            width: 20px;
            height: 20px;
            top: 0;
            left: -1.75rem;
        }
        .pic-list-card .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #007bff;
            border-color: #007bff;
        }
        .pic-list-card .card:hover .custom-control-label::before {
            background-color: rgba(255, 255, 255, 0.4);
        }

        /* 操作区域样式 */
        .pic-list-card .card-img-overlay.d-flex {
            background: rgba(0,0,0,0.5) !important;
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: auto;
        }
        .pic-list-card .card:hover .card-img-overlay.d-flex {
            opacity: 1;
        }
        .pic-list-card .card.selected .card-img-overlay.d-flex {
            opacity: 1;
        }

        /* 查看按钮样式 */
        .pic-list-card .btn-view {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            color: #333;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 4px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            opacity: 0.9;
        }
        .pic-list-card .btn-view:hover {
            background: #fff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            opacity: 1;
        }
        .pic-list-card .btn-view i {
            margin-right: 4px;
        }
        
        /* 批量删除按钮样式 */
        .btn-batch-delete {
            padding: 6px 16px;
            font-weight: 500;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .btn-batch-delete:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* 空数据提示样式 */
        .empty-tip .alert {
            border-radius: 4px;
            padding: 16px;
            margin: 0;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        .empty-tip .alert i {
            font-size: 1.2em;
            margin-right: 8px;
            color: #6c757d;
        }

        /* 选中状态样式 */
        .pic-list-card .card:hover .pic-actions {
            opacity: 1;
        }
        .pic-list-card .card.selected {
            box-shadow: 0 0 0 2px #007bff;
        }
        .pic-list-card .card.selected .pic-actions {
            opacity: 1;
        }
        .pic-list-card .card-img-overlay.d-flex {
            background: rgba(0,0,0,0.5) !important;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: auto;
        }

        /* 修改悬停选择器 */
        .pic-list-card .card:hover .card-img-overlay.d-flex {
            opacity: 1;
        }

        /* 修复选中状态样式 */
        .pic-list-card .card.selected .card-img-overlay.d-flex {
            opacity: 1;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">签到图片管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">图片库</h4>
            </div>
        </div>
    </div>
    <!-- end page title -->
    
    <!-- 上传区域 -->
    <div class="row" id="uploadArea" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="form-group w-100 mb-4">
                        <input type="file" name="file" id="txt_file" class="file-loading" multiple />
                        <input id="hidPic" type="hidden" value="" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 mb-2">
            <select class="form-control" name="structId" id="structId" style="width: 200px"></select>
            <input type="hidden" id="hidStructId" value="0" />
        </div>
    </div>
    <!-- 空数据提示 -->
    <div class="row empty-tip" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="alert alert-info" role="alert">
                        <i class="fa fa-info-circle"></i>暂无图片，请先<a href="javascript:void(0);" onclick="$('#btnUpload').click();" class="alert-link text-primary">上传</a>一张吧!
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图片展示区域 -->
    <div class="row">
        <div class="col-12">
            <div class="card pic-list-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div></div>
                    <div>
                        <button type="button" class="btn btn-primary btn-sm mr-1" id="btnUpload"><i class="fa fa-upload mr-1"></i> 上传</button>
                        <button type="button" class="btn btn-danger btn-sm btn-batch-delete" onclick="batchDelete()"><i class="fa fa-trash mr-1"></i> 删除</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row" id="picContainer">
                        <!-- 图片将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>

<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script type="text/javascript">
        let fileInput = function () {
            let oFile = new Object();
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/clocking_pic',
                    uploadExtraData: function (previewId, index) {
                        return {
                            structId: $("#hidStructId").val()
                        };
                    },
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-primary", //按钮样式
                    dropZoneEnabled: true, //是否显示拖拽区域
                    dropZoneTitle: '拖拽文件到这里...', //拖拽区域提示文字
                    dropZoneClickTitle: '<br/>或点击选择文件', //点击拖拽区域提示文字
                    maxFileCount: 10, //最大文件数量
                    multiple: true, //允许多文件上传
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！",
                    showRemove: true, //显示移除按钮
                    showPreview: true, //显示预览
                    showBrowse: true, //显示浏览按钮
                    browseLabel: '选择文件', //浏览按钮文字
                    removeLabel: '移除', //移除按钮文字
                    msgPlaceholder: '选择文件...', //占位符文字
                    uploadAsync: true,          // 异步上传
                    batchUpload: false,         // 关闭批量上传模式
                    fileActionSettings: {
                        showRemove: true,
                        showUpload: true,
                        showZoom: true,
                        showDrag: true
                    }
                }).on('filepreajax', function(event, previewId, index) {
                    // 上传前检查是否选择了机构
                    if ($("#hidStructId").val() === "0" || $("#hidStructId").val() === "") {
                        layer.msg('请先选择机构', { icon: 2, time: 2000 });
                        return false; // 阻止上传
                    }
                    // 上传前
                    layer.msg("正在上传...", { icon: 16, time: 0, shade: 0.3 });
                }).on('fileuploaded', function(event, data, previewId, index) {
                    // 单个文件上传成功，不显示提示
                    var res = data.response;
                    if (res.resultCode !== 200) {
                        // 如果上传失败，记录错误信息
                        if (!window.uploadErrors) {
                            window.uploadErrors = [];
                        }
                        window.uploadErrors.push(res.resultMsg || "上传失败");
                    }
                }).on('fileuploaderror', function(event, data, msg) {
                    // 上传错误，记录错误信息
                    if (!window.uploadErrors) {
                        window.uploadErrors = [];
                    }
                    window.uploadErrors.push("上传出错：" + msg);
                }).on('fileerror', function(event, data, msg) {
                    // 文件错误，记录错误信息
                    if (!window.uploadErrors) {
                        window.uploadErrors = [];
                    }
                    window.uploadErrors.push("文件错误：" + msg);
                }).on('filebatchuploadcomplete', function(event, files, extra) {
                    // 所有文件上传完成
                    layer.closeAll();
                    
                    // 检查是否有错误
                    if (window.uploadErrors && window.uploadErrors.length > 0) {
                        // 如果有错误，显示所有错误信息
                        layer.msg(window.uploadErrors.join("<br>"), { 
                            icon: 2, 
                            time: 3000,
                            offset: 't',
                            shade: 0.3
                        });
                    } else {
                        // 如果全部成功
                        layer.msg("所有文件上传成功", { 
                            icon: 1, 
                            time: 2000,
                            offset: 't'
                        });
                        getPic();
                    }
                    
                    // 清除错误记录
                    window.uploadErrors = [];
                    
                    // 重置上传控件到初始状态
                    control.fileinput('clear');
                    control.fileinput('reset');
                });
            };
            return oFile;
        };
        let getPic = function(){
            layer.msg('图片加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            let jsonObj = {};
            jsonObj.structId = $("#structId").val() == null ? 0 : $("#structId").val();
            $.ajax({
                type: "POST",
                url: "/activityroom/clocking/get_clocking_pic_list",
                data: JSON.stringify(jsonObj),
                contentType: "application/json",
                dataType: "json",
                success: function (data) {
                    layer.closeAll();
                    if(data.length > 0) {
                        let picList = data;
                        let html = '';
                        picList.forEach(function(pic) {
                            html += '<div class="col-md-3 mb-3">';
                            html += '<div class="card" data-id="' + pic.id + '">';
                            html += '<div class="position-relative">';
                            html += '<img src="/static/upload/clocking/mobile/' + pic.fileName + '" class="card-img-top" alt="签到图片" style="height: 200px; object-fit: cover;">';
                            html += '<div class="custom-control custom-checkbox">';
                            html += '<input type="checkbox" class="custom-control-input pic-checkbox" id="pic' + pic.id + '" value="' + pic.id + '">';
                            html += '<label class="custom-control-label" for="pic' + pic.id + '"></label>';
                            html += '</div>';
                            html += '<div class="card-img-overlay d-flex align-items-center justify-content-center">';
                            html += '<button class="btn btn-view" onclick="event.stopPropagation(); viewOriginalImage(\'' + pic.fileName + '\')">';
                            html += '<i class="fa fa-search-plus"></i> 查看原图';
                            html += '</button>';
                            html += '</div>';
                            html += '</div>';
                            html += '</div>';
                            html += '</div>';
                        });
                        $('#picContainer').html(html);
                        $('.pic-checkbox').off('change');
                        // 添加复选框变化处理函数
                        $('.pic-checkbox').on('change', function(e) {
                            e.stopPropagation(); // 阻止事件冒泡
                            let card = $(this).closest('.card');
                            if (this.checked) {
                                card.addClass('selected');
                            } else {
                                card.removeClass('selected');
                            }
                        });

                        // 添加查看按钮点击事件
                        $('.btn-view').on('click', function(e) {
                            e.stopPropagation(); // 阻止事件冒泡
                        });

                        // 添加批量删除按钮点击事件
                        $('.btn-batch-delete').on('click', function(e) {
                            e.stopPropagation(); // 阻止事件冒泡
                            batchDelete();
                        });
                        
                        $('.empty-tip').hide();
                        $('.pic-list-card').show();
                    } else {
                        $('#picContainer').html('');
                        $('.empty-tip').show();
                        $('.pic-list-card').hide();
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    layer.msg(jqXHR.responseText, {icon: 2});
                }
            });
        };

        // 批量删除
        function batchDelete() {
            let selectedIds = [];
            // 获取所有选中的复选框
            $('.card.selected').each(function() {
                let id = $(this).data('id');
                if (id) {
                    selectedIds.push(id);
                }
            });
            
            if(selectedIds.length === 0) {
                layer.msg('请选择要删除的图片', {icon: 2});
                return;
            }
            
            layer.confirm('确定要删除选中的图片吗？', {
                btn: ['确定','取消']
            }, function(){
                console.log('发送到服务器的ID:', selectedIds.join(',')); // 添加调试信息
                $.ajax({
                    type: "POST",
                    url: "/activityroom/clocking/batch_del_pic",
                    data: {ids: selectedIds.join(',')},
                    dataType: "json",
                    success: function(res) {
                        if(res.resultCode === 200) {
                            layer.msg(res.resultMsg, {icon: 1, time: 2000}, function() {
                                getPic(); // 刷新图片列表
                            });
                        } else {
                            layer.msg(res.resultMsg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('删除失败，请稍后重试', {icon: 2});
                    }
                });
            });
        }

        // 添加查看原图功能
        function viewOriginalImage(fileName) {
            // 创建图片查看层
            layer.open({
                type: 1,
                title: '查看原图',
                area: ['auto', '80%'],
                shadeClose: true,
                shade: 0.8,
                content: '<div class="text-center p-3" style="height: 100%; display: flex; align-items: center; justify-content: center; overflow: hidden;"><img src="/static/upload/clocking/mobile/' + fileName + '" style="max-width: 100%; max-height: 100%; object-fit: contain;" onload="adjustLayerSize(this)"></div>',
                success: function(layero, index) {
                    // 添加关闭按钮
                    layero.find('.layui-layer-setwin').html('<a class="layui-layer-close" href="javascript:;"><i class="fa fa-times"></i></a>');
                    // 移除滚动条
                    layero.find('.layui-layer-content').css('overflow', 'hidden');
                }
            });
        }

        // 调整弹窗大小
        function adjustLayerSize(img) {
            let layer = $(img).closest('.layui-layer');
            let imgWidth = img.naturalWidth;
            let imgHeight = img.naturalHeight;
            let maxWidth = window.innerWidth * 0.8;
            let maxHeight = window.innerHeight * 0.8;
            
            // 计算图片显示尺寸
            let displayWidth = imgWidth;
            let displayHeight = imgHeight;
            
            // 如果图片尺寸超过最大限制，等比例缩放
            if (displayWidth > maxWidth || displayHeight > maxHeight) {
                let ratio = Math.min(maxWidth / displayWidth, maxHeight / displayHeight);
                displayWidth *= ratio;
                displayHeight *= ratio;
            }
            
            // 设置弹窗宽度（图片宽度 + 内边距）
            layer.width(displayWidth + 32);
            // 设置弹窗高度（图片高度 + 内边距 + 标题栏高度）
            layer.height(displayHeight + 32 + 42);
            
            // 重新定位弹窗到屏幕中央
            layer.css({
                'left': (window.innerWidth - layer.width()) / 2 + 'px',
                'top': (window.innerHeight - layer.height()) / 2 + 'px'
            });
        }

        $(function () {
            let oFileInput = new fileInput();
            oFileInput.Init("txt_file");

            // 初始化机构下拉框
            initSelect('#structId', '/anteroom/structs/get_for_select', {}, '' ,'选择机构');
            $('#structId').change(function() {
                // 机构改变时重新获取图片列表
                $('#picContainer').html('');
                $('#hidStructId').val($(this).val());
                getPic();
            });
            getPic();
            // 添加上传按钮点击事件
            $('#btnUpload').on('click', function() {
                // 检查是否选择了机构
                if ($("#hidStructId").val() === "0" || $("#hidStructId").val() === "") {
                    layer.msg('请先选择机构', { icon: 2, time: 2000 });
                    return;
                }
                $('#uploadArea').slideToggle();
            });
            $(".file-footer-buttons").on('click', '.kv-file-upload', function () {
                // 机构检查已在filepreajax事件中处理，这里不再需要重复检查
            });
            $(".file-input").on('click', '.fileinput-upload', function () {
                // 机构检查已在filepreajax事件中处理，这里不再需要重复检查
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">活动签到人员清单</a></li>
                    </ol>
                </div>
                <h4 class="page-title">活动签到人员清单</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="clearfix">
                        <div class="float-left">
                            活动主题：<span class="m-0 font-weight-normal" th:text="${activity.activityName}"></span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <form class="form-inline mb-2" action="">
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-actionType" class="sr-only">操作类型：</label>
                                        <select class="form-control" id="sr-actionType" name="sr-actionType" style="width:150px;">
                                            <option value="">选择操作类型</option>
                                            <option value="1">签到</option>
                                            <option value="2">签退</option>
                                        </select>
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary mr-1" id="btnQuery"><i class="fa fa-search"></i></button>
                                            <a class="btn btn-light" href="#" onclick="window.history.go(-1)" title="返回上一页">返回</a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table id="tbClockingList" class="table">
                                    <thead>
                                    <tr>
                                        <th>用户名</th>
                                        <th>姓名</th>
                                        <th>所属组织</th>
                                        <th>操作时间</th>
                                        <th>操作类型</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            //查询
            $("#btnQuery").click(function () {
                oTable.draw();
            });
            //活动列表Datatables
            $("#tbClockingList").bsDataTables({
                columns: columns,
                url: '/activityroom/clocking/get_list_by_paged',
                columnDefs: [],
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
        });
        /*活动列表 start*/
        let columns = [
            { "data": "loginName", "bSortable": false },
            { "data": "realName", "bSortable": false },
            { "data": "structFullName", "bSortable": false },
            { "data": "clockingTime", "bSortable": false },
            {
                "data": "actionType", "bSortable": false, "render": function (data, type, row, meta) {
                    let labels = "";
                    switch (row.actionType) {
                        case 1:
                            labels = '签到';
                            break;
                        case 2:
                            labels = '签退';
                            break;
                        default:
                            labels = '';
                    }
                    return labels;
                }
            }
        ];
        let getQueryCondition = function (data) {
            let param = {};
            param.activityId = getUrlParam("activityId");
            param.actionType = $("#sr-actionType").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
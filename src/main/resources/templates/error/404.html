<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
<head>
    <meta charset="utf-8" />
    <title>页面丢失了</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- App favicon -->
    <link rel="shortcut icon" th:href="@{/static/images/favicon.ico}">
    <!-- App css -->
    <link th:href="@{/static/css/icons.min.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/app.min-1.0.css}" rel="stylesheet" type="text/css" />
</head>
<body class="authentication-bg">
<div class="account-pages mt-5 mb-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5">
                <div class="card">
                    <!-- Logo -->
                    <div class="card-header pt-4 pb-4 text-center bg-primary">
                        <a th:href="@{/home/<USER>">
                            <span class="text-white font-28 font-weight-bold">系统提示</span>
                        </a>
                    </div>

                    <div class="card-body p-4">
                        <div class="text-center">
                            <h1 class="text-error">404</h1>
                            <h4 class="text-uppercase text-danger mt-3">资源未找到</h4>
                            <p class="text-muted mt-3">
                                抱歉，您访问的资源不存在。请确认您输入的信息是否正确！
                            </p>
                            <button type="button" class="btn btn-primary mt-3 goHome">返回首页</button>
                        </div>
                    </div> <!-- end card-body-->
                </div>
                <!-- end card -->
            </div> <!-- end col -->
        </div>
        <!-- end row -->
    </div>
    <!-- end container -->
</div>
<!-- end page -->
<footer class="footer footer-alt">
    &copy; Psy-Cloud
</footer>
<!-- App js -->
<script th:src="@{/static/js/app.min.js}"></script>
<script type="text/javascript">
    $(function () {
        $(".goHome").click(function () {
            if (window.location.href.indexOf('app') >= 1 || window.location.href.indexOf('App') >= 1) {
                location.href = "/app/home/<USER>";
            }
            else {
                location.href = "/home/<USER>";
            }
        });
    });
</script>
</body>
</html>

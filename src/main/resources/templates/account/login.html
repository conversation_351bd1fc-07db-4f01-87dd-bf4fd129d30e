<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAuth.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/captcha/css/verify.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<div layout:fragment="content">
    <h4 class="mt-4 mb-4">平台登录</h4>
    <ul class="nav nav-tabs nav-bordered mb-3">
        <li class="nav-item">
            <a href="#accountLogin" data-toggle="tab" aria-expanded="true" class="nav-link active">
                <span class="d-block">账号密码登录</span>
            </a>
        </li>
        <li class="nav-item" th:if="${ pageData.sysConfigDto.isSmsEnabled eq 1 && pageData.sysConfigDto.smsConfig.isLogin eq 1}">
            <a href="#smsLogin" data-toggle="tab" aria-expanded="false" class="nav-link">
                <span class="d-block">短信登录</span>
            </a>
        </li>
    </ul>
    <div class="tab-content">
        <div class="tab-pane active" id="accountLogin">
            <p class="text-muted mb-4"></p>
            <!-- form -->
            <form id="frmLogin">
                <div class="form-group mb-3">
                    <label for="loginName" class="sr-only">用户名</label>
                    <input id="loginName" class="form-control" name="loginName" type="text" autocomplete="off" placeholder="用户名">
                </div>
                <div class="form-group mb-3">
                    <label for="password" class="sr-only">密码</label>
                    <input id="password" class="form-control" name="password" type="password" placeholder="密码">
                </div>
                <div class="form-group">
                    <div id="captcha"></div>
                    <input id="hidcaptcha" name="hidcaptcha" type="text" style="width:0; height:0; border:none;" value="0" />
                </div>
                <div class="form-group mb-3">
                    <div class="custom-control custom-checkbox">
                        <input id="isRemember" class="custom-control-input" type="checkbox">
                        <label class="custom-control-label" for="isRemember">记住登录状态</label>
                    </div>
                </div>
                <div class="form-group mb-0 text-center" id="wrapper-signin">
                    <button id="signIn" class="btn btn-primary btn-block" type="button">登录</button>
                </div>
            </form>
        </div>
        <div class="tab-pane" id="smsLogin">
            <p class="text-muted mb-4"></p>
            <form id="frmSendSms">
                <div class="form-group mb-3">
                    <label for="mobile" class="sr-only">手机号码</label>
                    <input id="mobile" class="form-control" name="mobile" type="text" autocomplete="off" placeholder="手机号码" maxlength="11">
                </div>
                <div class="form-group mb-3">
                    <div class="input-group">
                        <input id="verifySms" name="verifySms" type="text" class="form-control" placeholder="输入短信验证码" autocomplete="off">
                        <div class="input-group-append">
                            <input id="btnSendVerifySms" class="btn btn-light" type="button" value="发送短信" />
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <div id="captchaSms"></div>
                    <input id="hidCaptchaSms" name="hidCaptchaSms" type="text" style="width:0; height:0; border:none;" value="0" />
                </div>
                <div class="form-group mb-0 text-center" id="wrapper-sms-signin">
                    <button id="btnSmsLogin" class="btn btn-primary btn-block" type="submit">登录</button>
                </div>
            </form>
        </div>
    </div>
    <input id="hidSignName" type="hidden" th:value="${pageData.sysConfigDto.smsConfig.signName}" />
    <input id="hidTemplateCode" type="hidden" th:value="${pageData.sysConfigDto.smsConfig.loginTemplate}" />
    <!-- Modal -->
    <div id="modalPwd" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index: 99999;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title"><i class="fa fa-key mr-1"></i>密码修改</h5>
                </div>
                <div class="modal-body p-3">
                    <form id="frmModifyPwd">
                        <div class="form-group">
                            <label for="originalPwd" class="col-form-label">当前密码</label>
                            <input id="originalPwd" name="originalPwd" class="form-control" type="password">
                        </div>
                        <div class="form-group">
                            <label for="newPwd" class="col-form-label">新密码</label>
                            <input id="newPwd" name="newPwd" class="form-control" type="password" aria-describedby="pwdHelp">
                            <small id="pwdHelp" class="form-text text-muted">长度至少8位以上，由数字、大小写字母和特殊字符组成</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm_newPwd" class="col-form-label">确认新密码</label>
                            <input id="confirm_newPwd" name="confirm_newPwd" class="form-control" type="password">
                        </div>
                        <input type="submit" id="btnSave" class="btn btn-primary btn-block" value="确定" />
                        <input type="hidden" id="hidUid" value="0">
                    </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/captcha/js/verify.js}"></script>
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/jsencrypt.min.js}"></script>
    <script th:src="@{/static/js/pages/smsLogin-1.2.js}"></script>
    <script type="text/javascript">
        $(function () {
            document.onkeydown = function (event) {
                if (event && event.keyCode === 13) {
                    signIn();
                }
            };
            $("#wrapper-signin").on('click', '#signIn', function () {
                signIn();
            });
            let captchawidth = $("#loginName").parent().width();
            $("#captcha").slideVerify({
                type: 1,		//类型
                vOffset: 5,	//误差量，根据需求自行调整
                barSize: {
                    width: '' + captchawidth + '',
                    height: '39px',
                },
                ready: function () {
                },
                success: function () {
                    $("#hidcaptcha").val("1");
                },
                error: function () {

                }
            });
            $.validator.addMethod('check_password', function (value, element, param) {
                let strPwd = $('#newPwd').val();
                let regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[a-zA-Z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,}$/;
                return regex.test(strPwd);
            }, "密码不符合要求");
            $("#frmModifyPwd").validate({
                rules: {
                    originalPwd: {
                        required: true,
                        remote: {
                            type: "POST",
                            url: "/account/verify_pwd_by_userId",
                            dataType: "json",
                            data: {
                                originalPwd: function () {
                                    return $("#originalPwd").val();
                                },
                                userId:function(){
                                    return $("#hidUid").val()
                                }
                            },
                            dataFilter: function (data, type) {
                                data = JSON.parse(data);
                                if (data.resultCode === 200) {
                                    return true;
                                }
                                else
                                    return false;
                            }
                        }
                    },
                    newPwd: {
                        required: true,
                        check_password: true
                    },
                    confirm_newPwd: {
                        required: true,
                        equalTo: "#newPwd"
                    }
                },
                messages: {
                    originalPwd: {
                        required: "请输入当前密码",
                        remote: "当前密码输入错误"
                    },
                    newPwd: {
                        required: "请输入新密码",
                        check_password: "密码不符合要求"
                    },
                    confirm_newPwd: {
                        required: "请输入确认密码", equalTo: "两次输入密码不一致"
                    }
                },
                submitHandler: function () {
                    $("#btnSave").val("保存中……");
                    $("#btnSave").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.originalPwd = $("#originalPwd").val();
                    jsonObj.newPwd = $("#newPwd").val();
                    jsonObj.userId= $("#hidUid").val();
                    $.post("/account/modifypwd", jsonObj,
                        function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />设置成功，请妥善保管登录密码！' ,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function () {
                                        location.reload();
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }, 'json'
                    );
                }
            });
        });
        function checkForm() {
            let loginName = $.trim($("#loginName").val());
            let password = $("#password").val();
            if (loginName === "") {
                layer.msg('请输入用户名', { icon: 0, time: 2000 });
                return false;
            }
            else if (password === "") {
                layer.msg('请输入密码', { icon: 0, time: 2000 });
                return false;
            }
            else if ($("#hidcaptcha").val() === "0") {
                layer.msg('请滑动解锁完成验证', { icon: 0, time: 2000 });
                return false;
            }
            else
                return true;
        }
        function signIn() {
            if (checkForm()) {
                $("#wrapper-signin").empty();
                $("#wrapper-signin").html('<button id="signIn" class="btn btn-primary btn-block" type="button" disabled><span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>登录中...</button>');
                let jsonObj = {};
                jsonObj.loginName = $("#loginName").val();
                jsonObj.pwd = encryptPassword($("#password").val());
                jsonObj.isRemember = $("#isRemember").prop("checked") ? 1 : 0;
                $.ajax({
                    type: 'POST',
                    url: '/account/login/',
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        if (res.resultCode === 100) { //账号密码错误
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                        else if(res.resultCode === 101){
                            layer.confirm(res.resultMsg, {
                                btn: ['前往修改','暂不修改'] //按钮
                            }, function(){
                                location.href = '/anteroom/personal/modifypwd';
                            }, function(){
                                let returnUrl = getUrlParam('returnUrl');
                                location.href = returnUrl == null ? "/home/<USER>" : decodeURIComponent(returnUrl);
                            });
                        }
                        else if(res.resultCode === 108){
                            layer.open({
                                content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>' + res.resultMsg +''
                                ,btn:['前往修改']
                                , style: 'background-color:#ffffff; border:none;'
                                , shadeClose: false
                                , time: 0
                                ,yes:function(index){
                                    layer.close(index);
                                    $("#hidUid").val(res.data);
                                    $("#modalPwd").modal();
                                }
                            });
                        }
                        else if(res.resultCode === 110){
                            layer.alert('登录失败，还剩余'+res.data+'次登录机会。'
                                , {icon: 2,closeBtn:0, yes: function (index) {
                                    location.reload();
                                }
                            });
                        }
                        else if(res.resultCode === 109){
                            layer.msg(res.resultMsg, { icon: 2, time: 3000 });
                        }
                        else {
                            let returnUrl = getUrlParam('returnUrl');
                            location.href = returnUrl == null ? "/home/<USER>" : decodeURIComponent(returnUrl);
                        }
                        $("#wrapper-signin").html('<button id="signIn" class="btn btn-primary btn-block" type="button">登录</button>');
                    },
                    error: function (data) {
                        layer.msg("系统发生错误");
                        $("#wrapper-signin").html('<button id="signIn" class="btn btn-primary btn-block" type="button">登录</button>');
                    }
                });
            }
        }
        /**
         * 对密码进行加密处理
         * @param password 原始密码
         * @returns 加密后的密码
         */
        function encryptPassword(password) {
            const publicKey = getPublicKey();
            return RSAEncrypt(password, publicKey);
        }
        /**
         * 从服务器获取RSA公钥
         * @returns 公钥字符串
         */
        function getPublicKey() {
            let publicKey = '';
            $.ajax({
                url: '/account/getPublicKey',
                type: 'GET',
                contentType:'application/json',
                dataType: "json",
                async: false,
                success: function(res) {
                    if(res.resultCode === 200){
                        publicKey = res.data;
                    }
                    else{
                        layer.msg('登录失败，请重试！', { icon: 2, time: 3000 });
                    }
                }
            });
            return publicKey;
        }
        /**
         * 使用RSA公钥加密数据
         * @param password 要加密的密码
         * @param publicKey RSA公钥
         * @returns 加密后的密文
         */
        function RSAEncrypt(password, publicKey) {
            let encrypt = new JSEncrypt();
            encrypt.setPublicKey(publicKey);
            return encrypt.encrypt(password);
        }
    </script>
</th:block>

</body>
</html>
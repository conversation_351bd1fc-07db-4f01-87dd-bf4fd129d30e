<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">绩效数据</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">绩效数据管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">绩效数据列表</a></li>
                    </ol>
                </div>
                <h4 class="page-title">绩效数据列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mr-1">
                                    <label for="structName" class="sr-only">所属组织：</label>
                                    <div>
                                        <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择组织" />
                                        <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                            <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                            <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                        </div>
                                        <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="0" />
                                    </div>
                                </div>
                                <div class="form-group mr-1">
                                    <label for="sr-loginName" class="sr-only">用户名：</label>
                                    <input type="text" class="form-control" id="sr-loginName" name="sr-loginName" placeholder="用户名" autocomplete="off" style="width:200px;">
                                </div>
                                <div class="form-group mr-1">
                                    <label for="sr-realName" class="sr-only">姓名：</label>
                                    <input type="text" class="form-control" id="sr-realName" name="sr-realName" placeholder="姓名" autocomplete="off" style="width:200px;">
                                </div>
                                <div class="form-group mr-1">
                                    <label for="sr-yearly" class="sr-only">年度：</label>
                                    <select class="form-control" id="sr-yearly" name="sr-yearly" style="width:100px;">
                                        <option value="">年度</option>
                                        <option value="2025">2025</option>
                                        <option value="2024">2024</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnBatchDel" type="button" class="btn btn-danger mr-1" title="删除"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbUserPerformanceList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>所属组织</th>
                                <th>年度</th>
                                <th>1月</th>
                                <th>2月</th>
                                <th>3月</th>
                                <th>4月</th>
                                <th>5月</th>
                                <th>6月</th>
                                <th>7月</th>
                                <th>8月</th>
                                <th>9月</th>
                                <th>10月</th>
                                <th>11月</th>
                                <th>12月</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script type="text/javascript">
        $(function () {
            //初始化页面权限
            initPage();
            //查询
            $("#btnQuery").click(function () {
                oTable.draw();
            });
            $("#structName").click(function () {
                initTree();
            });
            //列表Datatables
            $("#tbUserPerformanceList").bsDataTables({
                columns: columns,
                url: '/userPerformance/list',
                columnDefs: '',
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });

            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/userPerformance/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //实现全选
            $("#chkall").change(function () {
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });

        let initPage = function () {
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        /*列表 start*/
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "loginName", "bSortable": false },
            { "data": "realName", "bSortable": false },
            { "data": "structFullName", "bSortable": false },
            { "data": "yearly", "bSortable": false },
            { "data": "month01", "bSortable": false },
            { "data": "month02", "bSortable": false },
            { "data": "month03", "bSortable": false },
            { "data": "month04", "bSortable": false },
            { "data": "month05", "bSortable": false },
            { "data": "month06", "bSortable": false },
            { "data": "month07", "bSortable": false },
            { "data": "month08", "bSortable": false },
            { "data": "month09", "bSortable": false },
            { "data": "month10", "bSortable": false },
            { "data": "month11", "bSortable": false },
            { "data": "month12", "bSortable": false }
        ];
        let getQueryCondition = function (data) {
            let param = {};
            param.structId = $("#hidStructParentID").val();
            param.loginName = $.trim($("#sr-loginName").val());
            param.realName = $("#sr-realName").val();
            param.yearly = $("#sr-yearly").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
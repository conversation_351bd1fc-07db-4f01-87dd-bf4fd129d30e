<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">绩效数据</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">绩效数据管理</a></li>
                        <li class="breadcrumb-item active">导入绩效数据</li>
                    </ol>
                </div>
                <h4 class="page-title">导入绩效数据</h4>
            </div>
        </div>
    </div>
    <div class="card bg-primary text-white">
        <div class="card-body">
            <div class="card-widgets">
                <a href="#" data-toggle="remove">&times;</a>
            </div>
            <div class="collapse pt-3 show">
                <ul class="list-unstyled pl-2">
                    <li class="mb-2">请先下载导入模板：<a th:href="@{/static/template/绩效数据导入模板.xlsx}" class="text-warning">绩效数据导入模板<i class="fa fa-download ml-2"></i></a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card p-3">
                <div class="card-body">
                    <div class="form-group row mb-3">
                        <label class="mr-2">上传绩效数据表格</label>
                        <input type="file" name="file" id="txt_file" class="file-loading" />
                    </div>
                </div> <!-- end card-body-->
                <div class="card-footer">
                    <div class="panel panel-default hide" id="wrapper-user">
                        <div class="panel-heading">
                            <h5 class="panel-title"><i class="fa fa-table"></i> 导入结果</h5>
                            <div class="alert alert-info mt-3" id="import-summary" style="display: none;">
                                <i class="fa fa-info-circle"></i> <span id="import-message"></span>
                            </div>
                        </div>
                        <div class="panel-body">
                            <table id="tbUser" class="table table-centered table-hover">
                                <thead class="thead-light">
                                <tr>
                                    <th width="20%">用户名</th>
                                    <th width="20%">姓名</th>
                                    <th width="15%">导入状态</th>
                                    <th width="45%">失败原因</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        $(function () {
            let oFileInput = new fileInput();
            oFileInput.Init("txt_file");
        });
        let resData;
        let fileInput = function () {
            let oFile = {};
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/userPerformance/import',
                    allowedFileExtensions: ['xls', 'xlsx'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-warning btn-sm", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "导入",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        let result = res.data;
                        $("#import-message").html(
                            `已成功导入<span class="text-success font-weight-bold">${result.successCount}</span>条，` +
                            `失败<span class="text-danger font-weight-bold">${result.failCount}</span>条`
                        );
                        $("#import-summary").show();

                        resData = result.failList;
                        initTD();
                        $("#wrapper-user").removeClass("hide").addClass("show");

                        // 显示成功提示
                        layer.msg(res.resultMsg, {icon: 1});
                    } else {
                        // 显示错误提示
                        layer.msg(res.resultMsg, {icon: 2});
                    }
                });
            };
            return oFile;
        };
        let oTableUser = null;
        let initTD = function () {
            if (oTableUser != null) {
                oTableUser.destroy();
            }
            oTableUser = $("#tbUser").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": true,
                "pageLength": 30,
                "data": resData,
                "columns": [
                    { "data": "loginName" },
                    { "data": "realName" },
                    {
                        "data": "state", "bSortable": false,
                        render: function (data, type, row, meta) {
                            if (data === 1) {
                                return '<span class="badge badge-success badge-pill">导入成功</span>';
                            }
                            if (data === 0) {
                                return '<span class="badge badge-light badge-pill">导入失败</span>';
                            }
                        }
                    },
                    {
                        "data": "msg", "bSortable": false
                    }
                ],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": "暂无相关数据",
                    "zeroRecords": "对不起，查询不到任何相关数据",
                    "info": "当前显示 _START_ 到 _END_ 条，共 _TOTAL_ 条记录",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>
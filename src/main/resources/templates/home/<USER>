<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .homepage-container {
            padding: 40px 0;
        }
        
        .welcome-section {
            text-align: center;
            color: #333;
            margin-bottom: 100px;
        }
        
        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .welcome-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 60px;
        }
        
        .feature-card {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            padding: 30px 20px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            text-decoration: none;
            display: block;
            text-align: center;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }
        
        .card-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            color: white;
        }
        
        .card-info {
            background: linear-gradient(135deg, #9ea2d8, #727cf5);
        }
        
        .card-danger {
            background: linear-gradient(135deg, #ff7043, #f4511e);
        }
        
        .card-warning {
            background: linear-gradient(135deg, #ffb74d, #ff9800);
        }
        
        .card-success {
            background: linear-gradient(135deg, #66bb6a, #4caf50);
        }
        
        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .card-description {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .welcome-title {
                font-size: 1.8rem;
            }
            
            .homepage-container {
                padding: 20px 0;
            }
            
            .feature-card {
                margin-bottom: 20px;
                padding: 25px 15px;
            }
            
            .card-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <th:block layout:fragment="content">
        <div class="homepage-container">
            <div class="container">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <h1 class="welcome-title">欢迎使用壹点灵EAP档案系统</h1>
                    <p class="welcome-subtitle">为您提供专业、便捷、高效的心理健康服务</p>
                </div>
                
                <!-- 功能卡片区域 -->
                <div class="row admin hide feature-cards">
                    <div class="col-lg-3 col-md-6 col-sm-6 mb-4">
                        <a th:href="@{/activityroom/activity/add}" class="feature-card">
                            <div class="card-icon card-info">
                                <i class="fa fa-gift"></i>
                            </div>
                            <h4 class="card-title">创建活动</h4>
                            <p class="card-description">快速创建心理活动</p>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 col-sm-6 mb-4">
                        <a th:href="@{/activityroom/activity/list}" class="feature-card">
                            <div class="card-icon card-danger">
                                <i class="fa fa-calendar-check-o"></i>
                            </div>
                            <h4 class="card-title">活动管理</h4>
                            <p class="card-description">统一管理所有心理活动</p>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 col-sm-6 mb-4">
                        <a th:href="@{/counselingroom/consultationcase/list}" class="feature-card">
                            <div class="card-icon card-warning">
                                <i class="fa fa-user-md"></i>
                            </div>
                            <h4 class="card-title">个案管理</h4>
                            <p class="card-description">专业的心理咨询个案管理</p>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 col-sm-6 mb-4">
                        <a th:href="@{/survey/survey/list}" target="_blank" class="feature-card">
                            <div class="card-icon card-success">
                                <i class="fa fa-bar-chart"></i>
                            </div>
                            <h4 class="card-title">调查问卷</h4>
                            <p class="card-description">科学评估和数据分析支持</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </th:block>
    <th:block layout:fragment="common_js">
        <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
        <script type="text/javascript">
            $(function () {
                let roleId = '[[${user.role.roleId}]]';
                if(roleId === '3'){
                    $(".visitor").removeClass('hide');
                }
                else{
                    $(".admin").removeClass('hide');
                }
            });
        </script>
    </th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .keyword-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 10px;
        }

        .tag-item {
            padding: 5px 15px;
            border-radius: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .tag-item:hover {
            background-color: #e9ecef;
        }

        .tag-item.selected {
            background-color: #727cf5;
            color: white;
            border-color: #727cf5;
        }

        .selected-keywords {
            min-height: 40px;
            border: 1px dashed #dee2e6;
            border-radius: 5px;
            padding: 10px;
            background-color: #f8f9fa;
        }

        .selected-tag {
            display: inline-block;
            padding: 5px 10px;
            margin: 2px;
            background-color: #727cf5;
            color: white;
            border-radius: 15px;
            font-size: 14px;
            position: relative;
        }

        .selected-tag.custom {
            background-color: #28a745;
        }

        .selected-tag .remove-btn {
            margin-left: 8px;
            cursor: pointer;
            font-weight: bold;
        }

        .selected-tag .remove-btn:hover {
            color: #ffebee;
        }

        .empty-keywords {
            color: #6c757d;
            font-style: italic;
        }
        
        /* 关键词验证错误样式 */
        .selected-keywords + label.error {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
            display: block;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询个案管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">编辑咨询个案</a></li>
                    </ol>
                </div>
                <h4 class="page-title">编辑咨询个案</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <form id="frmConsultationCase" action="#" class="col-lg-8">
                        <div class="card-body">
                            <div class="form-group mb-3">
                                <label for="structId">所属机构：</label>
                                <select class="form-control" id="structId" name="structId">
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="loginName">员工工号：</label>
                                <input type="text" id="loginName" name="loginName" class="form-control" placeholder="请输入员工工号">
                                <input type="hidden" id="userId" name="userId" value="0">
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationDate">咨询日期：</label>
                                <input type="date" id="consultationDate" name="consultationDate" class="form-control" required>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationForm">咨询形式：</label>
                                <select id="consultationForm" name="consultationForm" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">驻场咨询</option>
                                    <option value="2">线上咨询</option>
                                    <option value="3">门店咨询</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationDuration">咨询时长(分钟)：</label>
                                <input type="number" id="consultationDuration" name="consultationDuration" class="form-control" required>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationType">咨询类型：</label>
                                <select id="consultationType" name="consultationType" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">首次咨询</option>
                                    <option value="2">第二次咨询</option>
                                    <option value="3">第三次咨询</option>
                                    <option value="4">第四次咨询</option>
                                    <option value="5">第五次咨询</option>
                                    <option value="6">第六次及以上咨询</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="visitorGender">来访性别：</label>
                                <select id="visitorGender" name="visitorGender" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="visitorAge">来访年龄：</label>
                                <select id="visitorAge" name="visitorAge" class="form-control" required>
                                    <option value="">请选择年龄</option>
                                    <option value="1">20岁及以下</option>
                                    <option value="2">21-25</option>
                                    <option value="3">26-30</option>
                                    <option value="4">31-35</option>
                                    <option value="5">36-40</option>
                                    <option value="6">41-45</option>
                                    <option value="7">46-50</option>
                                    <option value="8">50岁以上</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="maritalStatus">婚姻状态：</label>
                                <select id="maritalStatus" name="maritalStatus" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">未婚</option>
                                    <option value="2">已婚</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="hasChildren">来访有无子女：</label>
                                <select id="hasChildren" name="hasChildren" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">有</option>
                                    <option value="2">无</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationField">咨询领域：</label>
                                <select id="consultationField" name="consultationField" class="form-control" required>
                                    <option value="">请选择</option>
                                    <option value="1">心理健康</option>
                                    <option value="2">情绪压力</option>
                                    <option value="3">人际关系</option>
                                    <option value="4">恋爱情感</option>
                                    <option value="5">家庭关系</option>
                                    <option value="6">亲子教育</option>
                                    <option value="7">职场发展</option>
                                    <option value="8">个人成长</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="problemSummary">问题概述：</label>
                                <textarea id="problemSummary" name="problemSummary" class="form-control" required minlength="100" maxlength="500" rows="6" placeholder="请详细描述问题，至少100字"></textarea>
                            </div>
                            <div class="form-group mb-3">
                                <label for="consultationKeywords">咨询关键词：</label>
                                
                                <!-- 关键词选择区域 -->
                                <div class="mb-2">
                                    <small class="text-muted">关键词选择：</small>
                                    <div class="keyword-tags" id="databaseKeywords">
                                        <!-- 从数据库动态加载的关键词会显示在这里 -->
                                        <div class="text-muted">加载中...</div>
                                    </div>
                                </div>
                                
                                <!-- 自定义关键词输入区域 -->
                                <div class="input-group mb-2">
                                    <input type="text" id="keywordInput" class="form-control" placeholder="输入自定义关键词" maxlength="50">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-primary" onclick="addCustomKeyword()">添加</button>
                                    </div>
                                </div>
                                
                                <!-- 已选择关键词显示区域 -->
                                <div class="mb-2">
                                    <small class="text-muted">已选择的关键词：</small>
                                    <div class="selected-keywords" id="selectedKeywords">
                                        <span class="empty-keywords">暂无选择关键词</span>
                                    </div>
                                </div>
                                
                                <input type="hidden" id="consultationKeywords" name="consultationKeywords" value="">
                            </div>
                            <div class="form-group mb-3 form-inline">
                                <label>是否属于职场类相关问题：</label>
                                <div class="radio-group form-inline">
                                    <div class="custom-control custom-radio mr-1">
                                        <input type="radio" id="isWorkplaceIssue1" name="isWorkplaceIssue" class="custom-control-input" value="1" onchange="toggleWorkplaceDesc()">
                                        <label class="custom-control-label" for="isWorkplaceIssue1">是</label>
                                    </div>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="isWorkplaceIssue0" name="isWorkplaceIssue" class="custom-control-input" value="0" onchange="toggleWorkplaceDesc()" checked>
                                        <label class="custom-control-label" for="isWorkplaceIssue0">否</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-3" id="workplaceDescGroup" style="display: none;">
                                <label for="workplaceDescription">职场问题描述：</label>
                                <textarea id="workplaceDescription" name="workplaceDescription" class="form-control" rows="5" maxlength="500"></textarea>
                            </div>
                            <div class="form-group mb-3 form-inline">
                                <label>来访是否有心理风险：</label>
                                <div class="radio-group form-inline">
                                    <div class="custom-control custom-radio mr-1">
                                        <input type="radio" id="hasPsychologicalRisk1" name="hasPsychologicalRisk" class="custom-control-input" value="1" onchange="toggleRiskDesc()">
                                        <label class="custom-control-label" for="hasPsychologicalRisk1">是</label>
                                    </div>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="hasPsychologicalRisk0" name="hasPsychologicalRisk" class="custom-control-input" value="0" onchange="toggleRiskDesc()" checked>
                                        <label class="custom-control-label" for="hasPsychologicalRisk0">否</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mb-3" id="riskLevelGroup" style="display: none;">
                                <label for="riskLevel">风险等级：</label>
                                <select id="riskLevel" name="riskLevel" class="form-control">
                                    <option value="">请选择</option>
                                    <option value="0">无风险</option>
                                    <option value="1">低风险</option>
                                    <option value="2">中风险</option>
                                    <option value="3">高风险</option>
                                </select>
                            </div>
                            <div class="form-group mb-3" id="riskDescGroup" style="display: none;">
                                <label for="riskDescription">风险程度简要描述：</label>
                                <textarea id="riskDescription" name="riskDescription" class="form-control" rows="5" maxlength="500"></textarea>
                            </div>
                            <div class="form-group mb-3">
                                <label for="followUpSuggestion">后续建议：</label>
                                <select id="followUpSuggestion" name="followUpSuggestion" class="form-control" required onchange="toggleOtherSuggestion()">
                                    <option value="">请选择</option>
                                    <option value="1">无需跟进</option>
                                    <option value="2">定期咨询</option>
                                    <option value="3">转介就医</option>
                                    <option value="4">其他</option>
                                </select>
                            </div>
                            <div class="form-group mb-3" id="otherSuggestionGroup" style="display: none;">
                                <label for="otherSuggestion">其他建议：</label>
                                <textarea id="otherSuggestion" name="otherSuggestion" class="form-control" rows="5" maxlength="500"></textarea>
                            </div>
                        </div>
                        <div class="card-footer">
                            <input type="submit" class="btn btn-primary" id="btnSave" value="保存" />
                            <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                        </div>
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        // 全局变量
        let allKeywords = [];
        let selectedKeywords = [];
        
        let id = getUrlParam("id");
        $(function () {
            //初始化页面
            initPage();
            $("#frmConsultationCase").validate({
                rules: {
                    structId: { required: true },
                    loginName: {
                        required: true,
                        remote: {
                            type: "GET",
                            url: "/anteroom/user/getByLoginName",
                            dataType: "json",
                            contentType: "application/json",
                            data: {
                                loginName: function() {
                                    return $.trim($("#loginName").val());
                                }
                            },
                            dataFilter: function (data, type) {
                                if (data === null || data === "") {
                                    return false;
                                }
                                else{
                                    let res = JSON.parse(data);
                                    $("#userId").val(res);
                                    return true;
                                }
                            }
                        }
                    },
                    consultationDate: { required: true },
                    consultationForm: { required: true },
                    consultationDuration: { required: true, number: true, min: 1 },
                    consultationType: { required: true },
                    visitorGender: { required: true },
                    visitorAge: { required: true },
                    maritalStatus: { required: true },
                    hasChildren: { required: true },
                    consultationField: { required: true },
                    problemSummary: { required: true, minlength: 100, maxlength: 500 },
                    consultationKeywords: { 
                        required: true,
                        minlength: 1
                    },
                    riskLevel: {
                        required: function() {
                            return $('input[name="hasPsychologicalRisk"]:checked').val() === '1';
                        }
                    },
                    riskDescription: {
                        required: function() {
                            return $('input[name="hasPsychologicalRisk"]:checked').val() === '1';
                        }
                    },
                    followUpSuggestion: { required: true }
                },
                messages: {
                    structId: { required: "请选择所属机构" },
                    loginName: {
                        required: "请输入来访者工号",
                        remote: "该来访者工号不存在"
                    },
                    consultationDate: { required: "请选择咨询日期" },
                    consultationForm: { required: "请选择咨询形式" },
                    consultationDuration: { 
                        required: "请输入咨询时长",
                        number: "请输入有效的数字",
                        min: "咨询时长必须大于0"
                    },
                    consultationType: { required: "请选择咨询类型" },
                    visitorGender: { required: "请选择来访性别" },
                    visitorAge: { required: "请选择来访年龄" },
                    maritalStatus: { required: "请选择婚姻状态" },
                    hasChildren: { required: "请选择是否有子女" },
                    consultationField: { required: "请选择咨询领域" },
                    problemSummary: {
                        required: "请填写问题概述",
                        minlength: "问题概述至少需要100字",
                        maxlength: "问题概述不能超过500字"
                    },
                    consultationKeywords: {
                        required: "请至少选择一个关键词",
                        minlength: "请至少选择一个关键词"
                    },
                    riskLevel: { required: "请选择风险等级" },
                    riskDescription: { required: "请填写风险程度简要描述" },
                    followUpSuggestion: { required: "请选择后续建议" }
                },
                errorPlacement: function(error, element) {
                    // 特殊处理关键词验证错误
                    if (element.attr('name') === 'consultationKeywords') {
                        error.insertAfter('#selectedKeywords');
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function () {
                    // 更新关键词字段
                    updateKeywordsInput();
                    
                    let jsonObj = {
                        "id": id,
                        "structId": $("#structId").val(),
                        "userId": $("#userId").val(),
                        "consultationDate": $("#consultationDate").val(),
                        "consultationForm": $("#consultationForm").val(),
                        "consultationDuration": $("#consultationDuration").val(),
                        "consultationType": $("#consultationType").val(),
                        "visitorGender": $("#visitorGender").val(),
                        "visitorAge": $("#visitorAge").val(),
                        "maritalStatus": $("#maritalStatus").val(),
                        "hasChildren": $("#hasChildren").val(),
                        "consultationField": $("#consultationField").val(),
                        "problemSummary": $("#problemSummary").val(),
                        "consultationKeywords": getCheckedKeywords(),
                        "isWorkplaceIssue": $("input[name='isWorkplaceIssue']:checked").val(),
                        "workplaceDescription": $("#workplaceDescription").val(),
                        "hasPsychologicalRisk": $("input[name='hasPsychologicalRisk']:checked").val(),
                        "riskLevel": $("#riskLevel").val(),
                        "riskDescription": $("#riskDescription").val(),
                        "followUpSuggestion": $("#followUpSuggestion").val(),
                        "otherSuggestion": $("#otherSuggestion").val()
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/consultationcase/update',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });

        function initPage() {
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.2, time: false
            });
            
            // 先加载关键词
            loadKeywords();
            
            // 初始化关键词验证
            setTimeout(function() {
                if ($("#frmConsultationCase").validate()) {
                    $("#frmConsultationCase").validate().element("#consultationKeywords");
                }
            }, 500);
            
            // 获取个案数据
            $.get("/counselingroom/consultationcase/get?id=" + id, function (data) {
                layer.closeAll();
                initSelect('#structId', '/anteroom/structs/get_for_select',{},data.structName,'选择机构');
                // 填充表单数据
                $("#loginName").val(data.loginName);
                $("#userId").val(data.userId);
                $("#consultationDate").val(data.consultationDate);
                $("#consultationForm").val(data.consultationForm);
                $("#consultationDuration").val(data.consultationDuration);
                $("#consultationType").val(data.consultationType);
                $("#visitorGender").val(data.visitorGender);
                $("#visitorAge").val(data.visitorAge);
                $("#maritalStatus").val(data.maritalStatus);
                $("#hasChildren").val(data.hasChildren);
                $("#consultationField").val(data.consultationField);
                $("#problemSummary").val(data.problemSummary);

                // 设置关键词（在关键词加载完成后）
                if (data.consultationKeywords) {
                    const keywords = data.consultationKeywords.split(',');
                    // 延迟设置选中状态，确保关键词已加载
                    setTimeout(() => {
                        keywords.forEach(keyword => {
                            // 添加到选中数组
                            addKeywordToArray(keyword, 'database');
                            // 标记标签为选中状态
                            $(`.tag-item[data-value="${keyword}"]`).addClass('selected');
                        });
                        updateSelectedKeywordsDisplay();
                        updateKeywordsInput();
                    }, 100);
                }

                // 设置职场问题
                if (data.isWorkplaceIssue === 1) {
                    $("#isWorkplaceIssue1").prop('checked', true);
                    $("#workplaceDescription").val(data.workplaceDescription);
                    toggleWorkplaceDesc();
                }

                // 设置心理风险
                if (data.hasPsychologicalRisk === 1) {
                    $("#hasPsychologicalRisk1").prop('checked', true);
                    $("#riskLevel").val(data.riskLevel);
                    $("#riskDescription").val(data.riskDescription);
                    toggleRiskDesc();
                }

                // 设置后续建议
                $("#followUpSuggestion").val(data.followUpSuggestion);
                if (data.followUpSuggestion === 4) {
                    $("#otherSuggestion").val(data.otherSuggestion);
                    toggleOtherSuggestion();
                }
            });

            // 初始化字数统计
            $('textarea').on('input', function() {
                const $textarea = $(this);
                const maxLength = $textarea.attr('maxlength');
                const currentLength = $textarea.val().length;
                const $counter = $textarea.siblings('.char-counter');
                
                if ($counter.length === 0) {
                    $textarea.after(`<span class="char-counter">${currentLength}/${maxLength}</span>`);
                } else {
                    $counter.text(`${currentLength}/${maxLength}`);
                }
            });


        }

        // 从数据库加载关键词
        function loadKeywords() {
            $.ajax({
                type: 'GET',
                url: '/counselingroom/keywords/all',
                success: function(response) {
                    if (response.resultCode === 200 && response.data) {
                        allKeywords = response.data.map(keyword => ({ keyword: keyword }));
                        renderKeywords();
                    } else {
                        console.error('加载关键词失败:', response.resultMsg);
                        $('#databaseKeywords').html('<div class="text-muted">加载失败</div>');
                    }
                },
                error: function() {
                    console.error('加载关键词请求失败');
                    $('#databaseKeywords').html('<div class="text-muted">加载失败</div>');
                }
            });
        }

        // 渲染关键词
        function renderKeywords() {
            const container = $('#databaseKeywords');
            let html = '';
            
            if (allKeywords.length === 0) {
                html = '<div class="text-muted">暂无关键词</div>';
            } else {
                allKeywords.forEach(keyword => {
                    html += `<div class="tag-item" data-value="${keyword.keyword}">
                                ${keyword.keyword}
                             </div>`;
                });
            }
            
            container.html(html);
            
            // 绑定点击事件
            $('.keyword-tags .tag-item').on('click', function() {
                toggleKeyword($(this));
            });
        }

        // 切换关键词选择状态
        function toggleKeyword($tag) {
            const keyword = $tag.data('value');
            
            if ($tag.hasClass('selected')) {
                // 取消选择
                $tag.removeClass('selected');
                removeKeywordFromArray(keyword);
            } else {
                // 选择
                $tag.addClass('selected');
                addKeywordToArray(keyword, 'database');
            }
            
            updateSelectedKeywordsDisplay();
            updateKeywordsInput();
        }

        // 添加自定义关键词
        function addCustomKeyword() {
            const input = $('#keywordInput');
            const keyword = input.val().trim();
            
            if (!keyword) {
                layer.msg('请输入关键词', { icon: 2, time: 1500 });
                return;
            }
            
            if (keyword.length > 50) {
                layer.msg('关键词长度不能超过50个字符', { icon: 2, time: 1500 });
                return;
            }
            
            // 检查本地是否重复
            if (isKeywordExists(keyword)) {
                layer.msg('该关键词已存在', { icon: 2, time: 1500 });
                input.focus();
                return;
            }
            
            // 先添加到本地
            addKeywordToArray(keyword, 'custom');
            input.val('');
            updateSelectedKeywordsDisplay();
            updateKeywordsInput();
            
            // 异步保存到数据库
            saveKeywordToDatabase(keyword);
        }

        // 保存关键词到数据库
        function saveKeywordToDatabase(keyword) {
            $.ajax({
                type: 'POST',
                url: '/counselingroom/keywords/add',
                data: JSON.stringify({ keyword: keyword }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.resultCode === 200) {
                        console.log('关键词已保存到数据库:', keyword);
                        // 重新加载关键词库
                        loadKeywords();
                    } else {
                        console.log('关键词已存在于数据库:', keyword);
                    }
                },
                error: function() {
                    console.error('保存关键词到数据库失败:', keyword);
                }
            });
        }

        // 检查关键词是否已存在
        function isKeywordExists(keyword) {
            // 检查已选择的关键词
            if (selectedKeywords.some(item => item.keyword === keyword)) {
                return true;
            }
            
            // 检查所有数据库中的关键词
            return allKeywords.some(item => item.keyword === keyword);
        }

        // 添加关键词到数组
        function addKeywordToArray(keyword, type) {
            if (!isKeywordExistsInSelected(keyword)) {
                selectedKeywords.push({
                    keyword: keyword,
                    type: type
                });
            }
        }

        // 从数组中移除关键词
        function removeKeywordFromArray(keyword) {
            selectedKeywords = selectedKeywords.filter(item => item.keyword !== keyword);
        }

        // 移除关键词
        function removeKeyword(keyword) {
            // 如果是数据库关键词，需要同时更新标签的选择状态
            const keywordObj = selectedKeywords.find(item => item.keyword === keyword);
            if (keywordObj && keywordObj.type === 'database') {
                $(`.keyword-tags .tag-item[data-value="${keyword}"]`).removeClass('selected');
            }
            
            removeKeywordFromArray(keyword);
            updateSelectedKeywordsDisplay();
            updateKeywordsInput();
        }

        // 更新已选择关键词的显示
        function updateSelectedKeywordsDisplay() {
            const container = $('#selectedKeywords');
            
            if (selectedKeywords.length === 0) {
                container.html('<span class="empty-keywords">暂无选择关键词</span>');
                return;
            }
            
            let html = '';
            selectedKeywords.forEach(item => {
                const cssClass = item.type === 'custom' ? 'selected-tag custom' : 'selected-tag';
                html += `<span class="${cssClass}">
                            ${item.keyword}
                            <span class="remove-btn" onclick="removeKeyword('${item.keyword}')">&times;</span>
                         </span>`;
            });
            
            container.html(html);
        }

        // 更新隐藏字段的值
        function updateKeywordsInput() {
            const keywords = selectedKeywords.map(item => item.keyword).join(',');
            $('#consultationKeywords').val(keywords);
            
            // 清除之前的错误提示
            $('label.error[for="consultationKeywords"]').remove();
            
            // 触发验证
            if ($("#frmConsultationCase").validate()) {
                $("#frmConsultationCase").validate().element("#consultationKeywords");
            }
        }

        // 检查关键词是否已在选中列表中
        function isKeywordExistsInSelected(keyword) {
            return selectedKeywords.some(item => item.keyword === keyword);
        }

        // 获取选中的关键词
        function getCheckedKeywords() {
            return $('#consultationKeywords').val();
        }

        // 更新关键词（保持向后兼容）
        function updateKeywords() {
            updateKeywordsInput();
        }

        function toggleWorkplaceDesc() {
            const isWorkplace = $('input[name="isWorkplaceIssue"]:checked').val();
            $('#workplaceDescGroup').toggle(isWorkplace === '1');
        }

        function toggleRiskDesc() {
            const hasRisk = $('input[name="hasPsychologicalRisk"]:checked').val();
            $('#riskDescGroup').toggle(hasRisk === '1');
            $('#riskLevelGroup').toggle(hasRisk === '1');

            // 根据是否有心理风险来设置必填属性
            if (hasRisk === '1') {
                $('#riskLevel').attr('required', true);
                $('#riskDescription').attr('required', true);
            } else {
                $('#riskLevel').removeAttr('required');
                $('#riskDescription').removeAttr('required');
            }
        }

        function toggleOtherSuggestion() {
            const suggestion = $('select[name="followUpSuggestion"]').val();
            $('#otherSuggestionGroup').toggle(suggestion === '4');
        }
    </script>
</th:block>
</body>
</html>
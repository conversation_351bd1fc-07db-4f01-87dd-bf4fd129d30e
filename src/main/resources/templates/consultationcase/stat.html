<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
    <style>
        /* 自定义样式 */
        .card {
            transition: all 0.3s ease;
        }

        .bg-opacity-20 {
            background-color: rgba(255,255,255,0.2) !important;
        }

        .text-white-50 {
            color: rgba(255,255,255,0.7) !important;
        }

        .btn-loading {
            position: relative;
            pointer-events: none;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .badge {
            font-size: 0.75em;
            padding: 0.375em 0.75em;
        }

        /* 表单标签样式 */
        .form-group label {
            font-weight: 600;
            color: #495057;
        }

        /* 数据概览卡片样式 */
        #overviewCards .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        #overviewCards .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 25px rgba(0, 0, 0, 0.12);
        }

        #overviewCards .avatar-sm {
            height: 40px;
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #overviewCards .avatar-title {
            font-size: 18px;
        }

        #overviewCards h3 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #495057;
        }

        #overviewCards h5 {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 图表交互提示样式 */
        .chart-container {
            position: relative;
        }

        .chart-container::before {
            content: '点击查看详情';
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
            pointer-events: none;
        }

        .chart-container:hover::before {
            opacity: 1;
        }

        /* 图表卡片悬停效果 */
        #chartsContainer .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询个案管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">咨询看板</a></li>
                    </ol>
                </div>
                <h4 class="page-title">咨询数据看板</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form class="form-inline">
                        <select class="form-control mr-2" name="structId" id="structId" style="width: 200px;"></select>
                        <div class="form-group mr-3">
                            <label for="dateRange" class="mr-2 font-weight-semibold">时间范围：</label>
                            <input type="text" class="form-control" id="dateRange" name="dateRange"
                                   placeholder="选择时间范围" style="width: 250px;">
                        </div>
                        <button type="button" class="btn btn-primary" id="btnQuery">
                            <i class="fa fa-search"></i> 查询
                        </button>
                        <button type="button" class="btn btn-light ml-2" id="btnReset">
                            <i class="fa fa-refresh"></i> 重置
                        </button>
                        

                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局空数据提示 -->
    <div id="globalEmptyTip" class="row" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center" style="padding: 100px 20px;">
                    <i class="fa fa-info-circle fa-4x text-primary mb-4"></i>
                    <h4 class="mb-3">请选择机构查看咨询数据统计</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据概览卡片 -->
    <div id="overviewCards" class="row mb-3" style="display: none;">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h5 class="text-muted font-weight-normal mt-0" title="总咨询个案数">总咨询人次</h5>
                            <h3 class="my-2 py-1" id="totalCases">0</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success mr-2"><i class="fa fa-arrow-up"></i></span>
                                <span>累计统计</span>
                            </p>
                        </div>
                        <div class="col-4">
                            <div class="text-right">
                                <div class="avatar-sm rounded-circle bg-primary">
                                    <i class="fa fa-users avatar-title font-22 text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h5 class="text-muted font-weight-normal mt-0" title="今日新增个案数">今日新增</h5>
                            <h3 class="my-2 py-1" id="todayCases">0</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-primary mr-2"><i class="fa fa-calendar"></i></span>
                                <span>今日统计</span>
                            </p>
                        </div>
                        <div class="col-4">
                            <div class="text-right">
                                <div class="avatar-sm rounded-circle bg-info">
                                    <i class="fa fa-plus avatar-title font-22 text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h5 class="text-muted font-weight-normal mt-0" title="本周新增个案数">本周新增</h5>
                            <h3 class="my-2 py-1" id="weekCases">0</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-warning mr-2"><i class="fa fa-clock-o"></i></span>
                                <span>本周统计</span>
                            </p>
                        </div>
                        <div class="col-4">
                            <div class="text-right">
                                <div class="avatar-sm rounded-circle bg-warning">
                                    <i class="fa fa-line-chart avatar-title font-22 text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h5 class="text-muted font-weight-normal mt-0" title="日均人次">日均人次</h5>
                            <h3 class="my-2 py-1" id="avgCases">0</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success mr-2"><i class="fa fa-bar-chart"></i></span>
                                <span>日均统计</span>
                            </p>
                        </div>
                        <div class="col-4">
                            <div class="text-right">
                                <div class="avatar-sm rounded-circle bg-success">
                                    <i class="fa fa-calculator avatar-title font-22 text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 统计图表 -->
    <div id="chartsContainer" style="display: none;">
        <!-- 图表使用提示 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-info border-0" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border-radius: 10px;">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle bg-white bg-opacity-20 p-2 mr-3">
                            <i class="fa fa-mouse-pointer text-white"></i>
                        </div>
                        <div>
                            <h6 class="mb-1 text-white font-weight-bold">
                                <i class="fa fa-lightbulb mr-1"></i>交互提示
                            </h6>
                            <p class="mb-0 text-white-50 small">
                                点击图表中的任意数据项，即可跳转到相应的咨询个案列表页面进行详细查看
                                <i class="fa fa-external-link ml-1"></i>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 1. 心理风险统计 -->
        <div class="row">
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">心理风险统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>心理健康风险人数比例
                        </p>
                        <div id="psychologicalRiskChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <!-- 2. 心理风险等级分布 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">心理风险等级分布</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>不同风险等级的人数分布情况
                        </p>
                        <div id="riskLevelChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <!-- 3. 单个关键词使用排行 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">单个关键词使用排行（前20）</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>单个关键词使用频次排行，洞察热点问题
                        </p>
                        <div id="keywordsTable" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th width="15%">序号</th>
                                        <th width="40%">关键词</th>
                                        <th width="25%">使用次数</th>
                                        <th width="20%">占比</th>
                                    </tr>
                                </thead>
                                <tbody id="keywordsTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 4. 各部门咨询人次统计 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">各部门咨询人次统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>展示各部门员工咨询使用人次，帮助了解不同部门使用心理健康服务需求
                        </p>
                        <div class="chart-container">
                            <div id="structChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 5. 按咨询形式分布统计 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">按咨询形式分布统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>分析不同咨询形式的使用情况
                        </p>
                        <div class="chart-container">
                            <div id="consultationFormChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 6. 按咨询领域分布统计 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">按咨询领域分布统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>主要咨询问题领域
                        </p>
                        <div class="chart-container">
                            <div id="consultationFieldChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 7. 职场类问题统计 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">职场类问题统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>工作相关心理问题比例
                        </p>
                        <div id="workplaceIssueChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 8. 按性别分布统计 -->
        <div class="row">
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">按性别分布统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>男女咨询比例分析
                        </p>
                        <div id="genderChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <!-- 9. 按婚姻状态分布统计 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">按婚姻状态分布统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>已婚与未婚人群对比
                        </p>
                        <div id="maritalStatusChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <!-- 10. 按有无子女分布统计 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">按有无子女分布统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>育儿压力相关分析
                        </p>
                        <div id="hasChildrenChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 11. 按年龄分布统计 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">按年龄分布统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>不同年龄段咨询需求
                        </p>
                        <div id="ageChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <!-- 12. 按咨询类型分布统计 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">按咨询类型分布统计</h5>
                        <p class="text-muted small mb-3">
                            <i class="fa fa-info-circle mr-1"></i>咨询次数分布情况
                        </p>
                        <div id="consultationTypeChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div> <!-- end chartsContainer -->

</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/pages/main.js}"></script>
    <script type="text/javascript">
        $(function (){
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            initSelectAndSelectedFirst('#structId', '/anteroom/structs/get_for_select',{},'','选择机构');
            initDateRangePicker();

            // 初始显示提示信息
            showSelectStructTip();

            // 初始化tooltip
            $('[data-toggle="tooltip"]').tooltip();

            // 绑定事件
            $('#btnQuery').click(function() {
                let structIdValue = $('#structId').val();
                if (!structIdValue || structIdValue === '') {
                    layer.msg('请先选择机构', {icon: 2});
                    return;
                }
                // 清除之前的数据和图表
                clearDashboardData();
                loadDashboardData();
            });
            $('#btnReset').click(function() {
                $('#structId').val('').trigger('change');
                $('#dateRange').val('');
                // 清除之前的数据和图表
                clearDashboardData();
                showSelectStructTip();
            });
            setTimeout(function() {
                loadDashboardData();
            }, 3000);
        });

        // 显示选择机构提示
        function showSelectStructTip() {
            // 显示全局空数据提示
            $('#globalEmptyTip').show();
            // 隐藏所有图表容器
            $('#chartsContainer').hide();
            // 隐藏数据概览卡片
            $('#overviewCards').hide();
        }



        // 初始化日期范围选择器
        function initDateRangePicker() {
            $('#dateRange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    cancelLabel: '清除',
                    applyLabel: '确定',
                    format: 'YYYY-MM-DD',
                    separator: ' 至 ',
                    customRangeLabel: '自定义',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                },
                ranges: {
                    '今天': [moment(), moment()],
                    '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    '最近7天': [moment().subtract(6, 'days'), moment()],
                    '最近30天': [moment().subtract(29, 'days'), moment()],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });

            $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' 至 ' + picker.endDate.format('YYYY-MM-DD'));
            });

            $('#dateRange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        }
        
        // 清除看板数据
        function clearDashboardData() {
            // 隐藏所有图表容器
            $('#chartsContainer').hide();
            // 隐藏数据概览卡片
            $('#overviewCards').hide();
            // 清空图表
            if (Highcharts && Highcharts.charts) {
                Highcharts.charts.forEach(function(chart) {
                    if (chart) {
                        chart.destroy();
                    }
                });
            }
            // 清空关键词表格
            $('#keywordsTableBody').empty();
            // 清空数据概览卡片中的数字
            $('#totalCases').text('0');
            $('#todayCases').text('0');
            $('#weekCases').text('0');
            $('#avgCases').text('0');
        }
        
        function loadDashboardData() {
            let structIdValue = $('#structId').val();
            if (!structIdValue || structIdValue === '') {
                layer.msg('请先选择机构', {icon: 2});
                return;
            }

            let dateRange = $('#dateRange').val();
            let startDate = null;
            let endDate = null;

            if (dateRange) {
                let dates = dateRange.split(' 至 ');
                if (dates.length === 2) {
                    startDate = dates[0];
                    endDate = dates[1];
                }
            }

            let requestData = {
                startDate: startDate,
                endDate: endDate,
                structId: parseInt(structIdValue)
            };
            // 使用真实数据接口
            $.ajax({
                url: '/counselingroom/consultationcase/get_dashboard_data',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    layer.closeAll();
                    updateDashboard(response);
                },
                error: function(xhr, status, error) {
                    layer.closeAll();
                    layer.msg('加载数据失败，请检查网络连接或联系管理员', {icon: 2});
                }
            });
        }



        // 跳转到咨询个案列表页面
        function jumpToListPage(params) {
            let url = '/counselingroom/consultationcase/list';
            let queryParams = [];
            
            // 处理部门参数：优先使用部门名称，其次使用部门ID，最后使用当前选中的机构ID
            if (params.structName) {
                queryParams.push('structName=' + encodeURIComponent(params.structName));
            } else {
                let structId = params.structId || $('#structId').val();
                if (structId) {
                    queryParams.push('structId=' + encodeURIComponent(structId));
                }
            }
            
            let dateRange = $('#dateRange').val();
            if (dateRange) {
                queryParams.push('dateRange=' + encodeURIComponent(dateRange));
            }
            
            // 根据不同的筛选条件添加参数
            if (params.consultationForm) {
                queryParams.push('consultationForm=' + encodeURIComponent(params.consultationForm));
            }
            if (params.consultationField) {
                queryParams.push('consultationField=' + encodeURIComponent(params.consultationField));
            }
            if (params.consultationType) {
                queryParams.push('consultationType=' + encodeURIComponent(params.consultationType));
            }
            if (params.visitorGender) {
                queryParams.push('visitorGender=' + encodeURIComponent(params.visitorGender));
            }
            if (params.visitorAge) {
                queryParams.push('visitorAge=' + encodeURIComponent(params.visitorAge));
            }
            if (params.maritalStatus) {
                queryParams.push('maritalStatus=' + encodeURIComponent(params.maritalStatus));
            }
            if (params.hasChildren) {
                queryParams.push('hasChildren=' + encodeURIComponent(params.hasChildren));
            }
            if (params.riskLevel !== undefined) {
                queryParams.push('riskLevel=' + encodeURIComponent(params.riskLevel));
            }
            if (params.isWorkplaceIssue !== undefined) {
                queryParams.push('isWorkplaceIssue=' + encodeURIComponent(params.isWorkplaceIssue));
            }
            if (params.hasPsychologicalRisk !== undefined) {
                queryParams.push('hasPsychologicalRisk=' + encodeURIComponent(params.hasPsychologicalRisk));
            }
            
            if (queryParams.length > 0) {
                url += '?' + queryParams.join('&');
            }
            
            // 在新标签页中打开
            window.open(url, '_blank');
        }

        // 处理饼图点击事件
        function handlePieChartClick(containerId, name) {
            let params = {};
            
            // 根据图表类型确定参数
            switch (containerId) {
                case 'genderChart':
                    params.visitorGender = name;
                    break;
                case 'maritalStatusChart':
                    params.maritalStatus = name === '已婚' ? '2' : '1';
                    break;
                case 'hasChildrenChart':
                    params.hasChildren = name === '有子女' ? '1' : '2';
                    break;
                case 'psychologicalRiskChart':
                    params.hasPsychologicalRisk = name === '有心理风险' ? '1' : '0';
                    break;
            }
            
            jumpToListPage(params);
        }

        // 处理环形图点击事件
        function handleDonutChartClick(containerId, name) {
            let params = {};
            
            // 根据图表类型确定参数
            switch (containerId) {
                case 'consultationFormChart':
                    if (name === '驻场咨询') params.consultationForm = '1';
                    else if (name === '线上咨询') params.consultationForm = '2';
                    else if (name === '门店咨询') params.consultationForm = '3';
                    break;
                case 'hasChildrenChart':
                    params.hasChildren = name === '有子女' ? '1' : '2';
                    break;
                case 'workplaceIssueChart':
                    params.isWorkplaceIssue = name === '职场类问题' ? '1' : '0';
                    break;
                case 'riskLevelChart':
                    if (name === '无风险') params.riskLevel = '0';
                    else if (name === '低风险') params.riskLevel = '1';
                    else if (name === '中风险') params.riskLevel = '2';
                    else if (name === '高风险') params.riskLevel = '3';
                    break;
            }
            
            jumpToListPage(params);
        }

        // 处理柱状图点击事件
        function handleColumnChartClick(containerId, name) {
            let params = {};
            
            // 根据图表类型确定参数
            switch (containerId) {
                case 'consultationTypeChart':
                    if (name === '首次咨询') params.consultationType = '1';
                    else if (name === '第二次咨询') params.consultationType = '2';
                    else if (name === '第三次咨询') params.consultationType = '3';
                    else if (name === '第四次咨询') params.consultationType = '4';
                    else if (name === '第五次咨询') params.consultationType = '5';
                    else if (name === '第六次及以上咨询') params.consultationType = '6';
                    break;
            }
            
            jumpToListPage(params);
        }

        // 处理水平条形图点击事件
        function handleHorizontalBarChartClick(containerId, name) {
            let params = {};
            
            // 根据图表类型确定参数
            switch (containerId) {
                case 'ageChart':
                    if (name === '20岁及以下') params.visitorAge = '1';
                    else if (name === '21-25岁') params.visitorAge = '2';
                    else if (name === '26-30岁') params.visitorAge = '3';
                    else if (name === '31-35岁') params.visitorAge = '4';
                    else if (name === '36-40岁') params.visitorAge = '5';
                    else if (name === '41-45岁') params.visitorAge = '6';
                    else if (name === '46-50岁') params.visitorAge = '7';
                    else if (name === '50岁以上') params.visitorAge = '8';
                    break;
            }
            
            jumpToListPage(params);
        }

        // 处理面积图点击事件
        function handleAreaChartClick(containerId, name) {
            let params = {};
            
            // 根据图表类型确定参数
            switch (containerId) {
                case 'consultationFieldChart':
                    if (name === '心理健康') params.consultationField = '1';
                    else if (name === '情绪压力') params.consultationField = '2';
                    else if (name === '人际关系') params.consultationField = '3';
                    else if (name === '恋爱情感') params.consultationField = '4';
                    else if (name === '家庭关系') params.consultationField = '5';
                    else if (name === '亲子教育') params.consultationField = '6';
                    else if (name === '职场发展') params.consultationField = '7';
                    else if (name === '个人成长') params.consultationField = '8';
                    break;
            }
            
            jumpToListPage(params);
        }

        // 处理半圆饼图点击事件
        function handleSemiCircleChartClick(containerId, name) {
            let params = {};
            
            // 根据图表类型确定参数
            switch (containerId) {
                case 'genderChart':
                    params.visitorGender = name;
                    break;
            }
            
            jumpToListPage(params);
        }

        // 处理风险等级图表点击事件
        function handleRiskLevelChartClick(name) {
            let params = {};
            
            if (name === '无风险') params.riskLevel = '0';
            else if (name === '低风险') params.riskLevel = '1';
            else if (name === '中风险') params.riskLevel = '2';
            else if (name === '高风险') params.riskLevel = '3';
            
            jumpToListPage(params);
        }

        // 更新看板显示
        function updateDashboard(data) {
            // 隐藏全局空数据提示
            $('#globalEmptyTip').hide();

            // 更新数据概览卡片
            updateOverviewCards(data);

            // 显示数据概览卡片和图表容器
            $('#overviewCards').show();
            $('#chartsContainer').show();

            // 添加卡片悬停效果
            addCardHoverEffects();
            
            // 为图表容器添加交互提示
            addChartInteractionTips();

            // 按机构统计 - 柱状图
            renderStructChart(data.structStats || []);

            // 按咨询形式统计 - 环形图
            renderDonutChart('consultationFormChart', '咨询形式分布', data.consultationFormStats || []);

            // 按咨询类型统计 - 柱状图
            renderColumnChart('consultationTypeChart', '咨询类型分布', data.consultationTypeStats || []);

            // 按性别统计 - 半圆饼图
            renderSemiCircleChart('genderChart', '性别分布', data.genderStats || []);

            // 按年龄统计 - 条形图
            renderHorizontalBarChart('ageChart', '年龄分布', data.ageStats || []);

            // 按婚姻状态统计 - 饼图
            renderPieChart('maritalStatusChart', '婚姻状态分布', data.maritalStatusStats || []);

            // 按有无子女统计 - 环形图
            renderDonutChart('hasChildrenChart', '有无子女分布', data.hasChildrenStats || []);

            // 按咨询领域统计 - 面积图
            renderAreaChart('consultationFieldChart', '咨询领域分布', data.consultationFieldStats || []);

            // 单个关键词使用排行 - 词云风格表格
            renderKeywordsCloud(data.keywordsStats || []);

            // 职场类问题统计 - 环形图
            renderDonutChart('workplaceIssueChart', '职场类问题', data.workplaceIssueStats || []);

            // 心理风险统计 - 饼图
            renderPieChart('psychologicalRiskChart', '心理风险', data.psychologicalRiskStats || []);

            // 心理风险等级分布 - 环形图
            renderRiskLevelChart('riskLevelChart', '风险等级分布', data.riskLevelStats || []);
        }

        // 渲染机构统计柱状图
        function renderStructChart(data) {
            if (!data || data.length === 0) {
                return;
            }

            // 过滤掉数据为0的部门
            let filteredData = data.filter(item => item.count > 0);
            
            if (filteredData.length === 0) {
                return;
            }

            let categories = filteredData.map(item => item.structName);
            let values = filteredData.map(item => item.count);

            Highcharts.chart('structChart', {
                chart: {
                    type: 'bar'
                },
                title: {
                    text: null
                },
                xAxis: {
                    categories: categories,
                    title: {
                        text: null
                    }
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: '咨询人次',
                        align: 'high'
                    },
                    labels: {
                        overflow: 'justify'
                    }
                },
                tooltip: {
                    valueSuffix: ' 个'
                },
                plotOptions: {
                    bar: {
                        dataLabels: {
                            enabled: true
                        },
                        cursor: 'pointer',
                        point: {
                            events: {
                                click: function () {
                                    // 传递部门名称，让后台根据名称查询部门ID和子部门
                                    jumpToListPage({
                                        structName: this.category
                                    });
                                }
                            }
                        }
                    }
                },
                legend: {
                    enabled: false
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: '咨询人次',
                    data: values,
                    color: '#727cf5',
                    borderColor: '#ffffff',
                    borderWidth: 1
                }]
            });
        }

        // 渲染饼图
        function renderPieChart(containerId, title, data) {
            if (!data || data.length === 0) {
                return;
            }

            // 定义协调的颜色方案
            let colors = [
                '#727cf5', // 主蓝色
                '#0acf97', // 绿色
                '#fa5c7c', // 粉红色
                '#ffbc00', // 黄色
                '#39afd1', // 青色
                '#6c757d', // 灰色
                '#fd7e14', // 橙色
                '#6f42c1', // 紫色
                '#20c997', // 青绿色
                '#e83e8c'  // 玫红色
            ];

            let seriesData = data.map((item, index) => ({
                name: item.name,
                y: item.value,
                color: colors[index % colors.length]
            }));

            Highcharts.chart(containerId, {
                chart: {
                    type: 'pie',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br/>数量: <b>{point.y}</b>',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#727cf5',
                    borderRadius: 6,
                    shadow: true
                },
                accessibility: {
                    point: {
                        valueSuffix: '%'
                    }
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: {
                            enabled: true,
                            format: '<b>{point.name}</b>: {point.percentage:.1f}%',
                            style: {
                                fontSize: '11px',
                                fontWeight: 'normal',
                                color: '#495057'
                            }
                        },
                        showInLegend: false,
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        point: {
                            events: {
                                click: function () {
                                    handlePieChartClick(containerId, this.name);
                                }
                            }
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: title,
                    data: seriesData
                }]
            });
        }

        // 渲染柱状图
        function renderColumnChart(containerId, title, data) {
            if (!data || data.length === 0) {
                return;
            }

            let categories = data.map(item => item.name);
            let values = data.map(item => item.value);

            Highcharts.chart(containerId, {
                chart: {
                    type: 'column'
                },
                title: {
                    text: null
                },
                xAxis: {
                    categories: categories,
                    crosshair: true
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: '数量'
                    }
                },
                tooltip: {
                    headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                        '<td style="padding:0"><b>{point.y} 个</b></td></tr>',
                    footerFormat: '</table>',
                    shared: true,
                    useHTML: true
                },
                plotOptions: {
                    column: {
                        pointPadding: 0.2,
                        borderWidth: 0,
                        dataLabels: {
                            enabled: true
                        },
                        cursor: 'pointer',
                        point: {
                            events: {
                                click: function () {
                                    handleColumnChartClick(containerId, this.category);
                                }
                            }
                        }
                    }
                },
                legend: {
                    enabled: false
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: title,
                    data: values,
                    color: '#0acf97',
                    borderColor: '#ffffff',
                    borderWidth: 1
                }]
            });
        }

        // 渲染环形图
        function renderDonutChart(containerId, title, data) {
            if (!data || data.length === 0) {
                return;
            }

            let colors = ['#727cf5', '#0acf97', '#fa5c7c', '#ffbc00', '#39afd1'];
            let seriesData = data.map((item, index) => ({
                name: item.name,
                y: item.value,
                color: colors[index % colors.length]
            }));

            Highcharts.chart(containerId, {
                chart: {
                    type: 'pie',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br/>数量: <b>{point.y}</b>'
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: {
                            enabled: true,
                            format: '<b>{point.name}</b><br/>{point.percentage:.1f}%',
                            style: {
                                fontSize: '11px',
                                color: '#495057'
                            }
                        },
                        innerSize: '50%', // 环形图
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        point: {
                            events: {
                                click: function () {
                                    handleDonutChartClick(containerId, this.name);
                                }
                            }
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: title,
                    data: seriesData
                }]
            });
        }

        // 渲染半圆饼图
        function renderSemiCircleChart(containerId, title, data) {
            if (!data || data.length === 0) {
                return;
            }

            let colors = ['#fa5c7c', '#727cf5'];
            let seriesData = data.map((item, index) => ({
                name: item.name,
                y: item.value,
                color: colors[index % colors.length]
            }));

            Highcharts.chart(containerId, {
                chart: {
                    type: 'pie',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br/>数量: <b>{point.y}</b>'
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: {
                            enabled: true,
                            format: '<b>{point.name}</b><br/>{point.percentage:.1f}%',
                            style: {
                                fontSize: '11px',
                                color: '#495057'
                            }
                        },
                        startAngle: -90,
                        endAngle: 90,
                        center: ['50%', '75%'],
                        size: '110%',
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        point: {
                            events: {
                                click: function () {
                                    handleSemiCircleChartClick(containerId, this.name);
                                }
                            }
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: title,
                    data: seriesData
                }]
            });
        }

        // 渲染水平条形图
        function renderHorizontalBarChart(containerId, title, data) {
            if (!data || data.length === 0) {
                return;
            }

            let categories = data.map(item => item.name);
            let values = data.map(item => item.value);
            let colors = ['#727cf5', '#0acf97', '#fa5c7c', '#ffbc00', '#39afd1', '#6c757d'];

            Highcharts.chart(containerId, {
                chart: {
                    type: 'bar',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                xAxis: {
                    categories: categories,
                    title: {
                        text: null
                    }
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: '数量',
                        align: 'high'
                    }
                },
                tooltip: {
                    valueSuffix: ' 个'
                },
                plotOptions: {
                    bar: {
                        dataLabels: {
                            enabled: true
                        },
                        colorByPoint: true,
                        colors: colors,
                        cursor: 'pointer',
                        point: {
                            events: {
                                click: function () {
                                    handleHorizontalBarChartClick(containerId, this.category);
                                }
                            }
                        }
                    }
                },
                legend: {
                    enabled: false
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: title,
                    data: values
                }]
            });
        }

        // 渲染面积图
        function renderAreaChart(containerId, title, data) {
            if (!data || data.length === 0) {
                return;
            }

            let categories = data.map(item => item.name);
            let values = data.map(item => item.value);

            Highcharts.chart(containerId, {
                chart: {
                    type: 'area'
                },
                title: {
                    text: null
                },
                xAxis: {
                    categories: categories,
                    tickmarkPlacement: 'on',
                    title: {
                        enabled: false
                    }
                },
                yAxis: {
                    title: {
                        text: '数量'
                    }
                },
                tooltip: {
                    split: true,
                    valueSuffix: ' 个'
                },
                plotOptions: {
                    area: {
                        stacking: 'normal',
                        lineColor: '#666666',
                        lineWidth: 1,
                        marker: {
                            lineWidth: 1,
                            lineColor: '#666666'
                        },
                        dataLabels: {
                            enabled: true
                        },
                        cursor: 'pointer',
                        point: {
                            events: {
                                click: function () {
                                    handleAreaChartClick(containerId, this.category);
                                }
                            }
                        }
                    }
                },
                legend: {
                    enabled: false
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: title,
                    data: values,
                    color: '#39afd1',
                    fillColor: {
                        linearGradient: {
                            x1: 0,
                            y1: 0,
                            x2: 0,
                            y2: 1
                        },
                        stops: [
                            [0, '#39afd1'],
                            [1, 'rgba(57, 175, 209, 0.1)']
                        ]
                    },
                    lineColor: '#39afd1'
                }]
            });
        }

        // 渲染风险等级分布图表
        function renderRiskLevelChart(containerId, title, data) {
            if (!data || data.length === 0) {
                return;
            }

            // 定义风险等级专用的颜色方案
            let riskColors = {
                '无风险': '#0acf97',    // 绿色 - 安全
                '低风险': '#ffbc00',    // 黄色 - 注意
                '中风险': '#fd7e14',    // 橙色 - 警告
                '高风险': '#fa5c7c'     // 红色 - 危险
            };

            let seriesData = data.map(item => ({
                name: item.name,
                y: item.value,
                color: riskColors[item.name] || '#6c757d'
            }));

            Highcharts.chart(containerId, {
                chart: {
                    type: 'pie',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br/>数量: <b>{point.y}</b>',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#727cf5',
                    borderRadius: 6,
                    shadow: true
                },
                accessibility: {
                    point: {
                        valueSuffix: '%'
                    }
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: {
                            enabled: true,
                            format: '<b>{point.name}</b><br/>{point.percentage:.1f}%',
                            style: {
                                fontSize: '11px',
                                fontWeight: 'normal',
                                color: '#495057'
                            }
                        },
                        innerSize: '40%', // 环形图
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        showInLegend: true,
                        point: {
                            events: {
                                click: function () {
                                    handleRiskLevelChartClick(this.name);
                                }
                            }
                        }
                    }
                },
                legend: {
                    align: 'right',
                    verticalAlign: 'middle',
                    layout: 'vertical',
                    itemStyle: {
                        fontSize: '11px',
                        color: '#495057'
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: title,
                    data: seriesData
                }]
            });
        }



        // 渲染关键词表格
        function renderKeywordsCloud(data) {
            let tbody = $('#keywordsTableBody');
            tbody.empty();

            if (!data || data.length === 0) {
                return;
            }

            // 计算总使用次数用于百分比计算
            let totalCount = data.reduce((sum, item) => sum + item.count, 0);

            data.forEach(function(item, index) {
                let rank = index + 1;
                let percentage = totalCount > 0 ? ((item.count / totalCount) * 100).toFixed(1) : 0;

                // 前三名用不同颜色的badge，其他用统一颜色
                let rankBadge = '';
                if (rank === 1) {
                    rankBadge = '<span class="badge" style="background-color: #FFD700; color: #fff;">' + rank + '</span>'; // 金色
                } else if (rank === 2) {
                    rankBadge = '<span class="badge" style="background-color: #C0C0C0; color: #fff;">' + rank + '</span>'; // 银色
                } else if (rank === 3) {
                    rankBadge = '<span class="badge" style="background-color: #CD7F32; color: #fff;">' + rank + '</span>'; // 铜色
                } else {
                    rankBadge = '<span class="badge badge-secondary">' + rank + '</span>'; // 灰色
                }

                tbody.append(
                    '<tr>' +
                    '<td>' + rankBadge + '</td>' +
                    '<td>' + (item.keyword || '未知') + '</td>' +
                    '<td><span class="badge badge-primary">' + item.count + '</span></td>' +
                    '<td>' + percentage + '%</td>' +
                    '</tr>'
                );
            });
        }

        // 渲染关键词表格
        function renderKeywordsTable(data) {
            let tbody = $('#keywordsTableBody');
            tbody.empty();

            if (!data || data.length === 0) {
                return;
            }

            data.forEach(function(item) {
                tbody.append(
                    '<tr>' +
                    '<td>' + (item.keyword || '未知') + '</td>' +
                    '<td><span class="badge badge-primary">' + item.count + '</span></td>' +
                    '</tr>'
                );
            });
        }

        // 更新数据概览卡片
        function updateOverviewCards(data) {
            // 使用后端返回的真实统计数据
            let totalCases = data.totalCases || 0;           // 总咨询人次
            let todayCases = data.todayCases || 0;           // 今日新增
            let weekCases = data.weekCases || 0;             // 本周新增
            let avgCases = data.avgCases || 0;               // 日均人次

            // 更新卡片数据，添加数字动画效果
            animateNumber('#totalCases', totalCases);
            animateNumber('#todayCases', todayCases);
            animateNumber('#weekCases', weekCases);
            animateNumber('#avgCases', avgCases);
        }

        // 数字动画效果
        function animateNumber(selector, targetNumber) {
            let $element = $(selector);
            let currentNumber = 0;
            let increment = Math.ceil(targetNumber / 50); // 50步完成动画

            let timer = setInterval(function() {
                currentNumber += increment;
                if (currentNumber >= targetNumber) {
                    currentNumber = targetNumber;
                    clearInterval(timer);
                }
                $element.text(currentNumber.toLocaleString());
            }, 30);
        }

        // 添加卡片悬停效果
        function addCardHoverEffects() {
            $('.card').hover(
                function() {
                    $(this).css('transform', 'translateY(-5px)');
                },
                function() {
                    $(this).css('transform', 'translateY(0)');
                }
            );

            // 为筛选表单元素添加焦点效果
            $('#structId, #dateRange').focus(function() {
                $(this).css('border-color', '#667eea');
                $(this).css('box-shadow', '0 0 0 0.2rem rgba(102, 126, 234, 0.25)');
            }).blur(function() {
                $(this).css('border-color', '#e3eaef');
                $(this).css('box-shadow', 'none');
            });

            // 为按钮添加点击效果
            $('#btnQuery').click(function() {
                $(this).addClass('btn-loading');
                setTimeout(() => {
                    $(this).removeClass('btn-loading');
                }, 1000);
            });
        }

        // 为图表容器添加交互提示
        function addChartInteractionTips() {
            // 为所有图表容器添加chart-container类
            $('[id$="Chart"]').each(function() {
                if (!$(this).parent().hasClass('chart-container')) {
                    $(this).wrap('<div class="chart-container"></div>');
                }
            });
        }

    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询个案管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">我的个案</a></li>
                    </ol>
                </div>
                <h4 class="page-title">我的个案</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <button type="button" id="btnSearch" class="btn btn-primary mr-1">
                                <i class="fa fa-search mr-1"></i>搜索
                            </button>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnDownload" type="button" class="btn btn-primary mb-2 mr-1"><i class="fa fa-download mr-1"></i>导出(Excel)</button>
                                <button id="btnAdd" type="button" class="btn btn-success mb-2 mr-1"><i class="fa fa-plus mr-1"></i>新增</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2"><i class="fa fa-trash-o"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <table id="tbConsultationCase" class="table table-striped" width="100%">
                        <thead>
                        <tr>
                            <th style="width: 30px;">
                                <div class="custom-control custom-checkbox">
                                    <input id="chkall" class="custom-control-input check" type="checkbox">
                                    <label class="custom-control-label" for="chkall"></label>
                                </div>
                            </th>
                            <th align="left">所属机构</th>
                            <th align="left">咨询日期</th>
                            <th align="left">员工工号</th>
                            <th align="left">员工姓名</th>
                            <th align="left">咨询时长(分钟)</th>
                            <th align="left">咨询形式</th>
                            <th align="left">咨询领域</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- 筛选条件模态框 -->
    <div class="modal fade" id="searchModal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="searchModalLabel">筛选条件</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="searchForm">
                        <div class="form-group row">
                            <label class="col-4 col-form-label">所属机构：</label>
                            <div class="col-8">
                                <select class="form-control" id="structId" name="structId">
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询日期：</label>
                            <div class="col-8">
                                <input type="date" class="form-control" id="consultationDate" name="consultationDate">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">员工工号：</label>
                            <div class="col-8">
                                <input type="text" class="form-control" id="loginName" name="loginName">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">员工姓名：</label>
                            <div class="col-8">
                                <input type="text" class="form-control" id="realName" name="realName">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">性别：</label>
                            <div class="col-8">
                                <select class="form-control" id="visitorGender" name="visitorGender">
                                    <option value="">全部</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询类型：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationType" name="consultationType">
                                    <option value="">全部</option>
                                    <option value="1">首次咨询</option>
                                    <option value="2">第二次咨询</option>
                                    <option value="3">第三次咨询</option>
                                    <option value="4">第四次咨询</option>
                                    <option value="5">第五次咨询</option>
                                    <option value="6">第六次及以上咨询</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询形式：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationForm" name="consultationForm">
                                    <option value="">全部</option>
                                    <option value="1">驻场咨询</option>
                                    <option value="2">线上咨询</option>
                                    <option value="3">门店咨询</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询领域：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationField" name="consultationField">
                                    <option value="">全部</option>
                                    <option value="1">心理健康</option>
                                    <option value="2">情绪压力</option>
                                    <option value="3">人际关系</option>
                                    <option value="4">恋爱情感</option>
                                    <option value="5">家庭关系</option>
                                    <option value="6">亲子教育</option>
                                    <option value="7">职场发展</option>
                                    <option value="8">个人成长</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">年龄：</label>
                            <div class="col-8">
                                <select class="form-control" id="visitorAge" name="visitorAge">
                                    <option value="">全部</option>
                                    <option value="1">20岁及以下</option>
                                    <option value="2">21-25岁</option>
                                    <option value="3">26-30岁</option>
                                    <option value="4">31-35岁</option>
                                    <option value="5">36-40岁</option>
                                    <option value="6">41-45岁</option>
                                    <option value="7">46-50岁</option>
                                    <option value="8">50岁以上</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">婚姻状态：</label>
                            <div class="col-8">
                                <select class="form-control" id="maritalStatus" name="maritalStatus">
                                    <option value="">全部</option>
                                    <option value="1">未婚</option>
                                    <option value="2">已婚</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">有无子女：</label>
                            <div class="col-8">
                                <select class="form-control" id="hasChildren" name="hasChildren">
                                    <option value="">全部</option>
                                    <option value="1">有</option>
                                    <option value="2">无</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">是否有心理风险：</label>
                            <div class="col-8">
                                <select class="form-control" id="hasPsychologicalRisk" name="hasPsychologicalRisk">
                                    <option value="">全部</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">风险等级：</label>
                            <div class="col-8">
                                <select class="form-control" id="riskLevel" name="riskLevel">
                                    <option value="">全部</option>
                                    <option value="0">无风险</option>
                                    <option value="1">低风险</option>
                                    <option value="2">中风险</option>
                                    <option value="3">高风险</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">是否职场问题：</label>
                            <div class="col-8">
                                <select class="form-control" id="isWorkplaceIssue" name="isWorkplaceIssue">
                                    <option value="">全部</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="btnQuery">查询</button>
                    <button type="button" class="btn btn-light" id="btnReset">重置</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 查看详情模态框 -->
    <div class="modal fade" id="viewDetailModal" tabindex="-1" role="dialog" aria-labelledby="viewDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="viewDetailModalLabel">
                        <i class="fa fa-eye mr-2"></i>咨询个案详情
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <!-- 基本信息卡片 -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary-lighten">
                                    <h5 class="card-title mb-0">
                                        <i class="fa fa-info-circle text-primary mr-2"></i>基本信息
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td class="font-weight-bold" style="width: 120px;">所属机构：</td>
                                                    <td><span id="structName"></span></td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">咨询日期：</td>
                                                    <td><span id="detailConsultationDate"></span></td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">咨询形式：</td>
                                                    <td><span id="detailConsultationForm"></span></td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">咨询时长：</td>
                                                    <td><span id="detailConsultationDuration"></span>分钟</td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">咨询类型：</td>
                                                    <td><span id="detailConsultationType"></span></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td class="font-weight-bold" style="width: 120px;">来访年龄：</td>
                                                    <td><span id="detailVisitorAge"></span></td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">来访性别：</td>
                                                    <td><span id="detailVisitorGender"></span></td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">婚姻状态：</td>
                                                    <td><span id="detailMaritalStatus"></span></td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">有无子女：</td>
                                                    <td><span id="detailHasChildren"></span></td>
                                                </tr>
                                                <tr>
                                                    <td class="font-weight-bold">咨询领域：</td>
                                                    <td><span id="detailConsultationField"></span></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 问题描述卡片 -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary-lighten">
                                    <h5 class="card-title mb-0">
                                        <i class="fa fa-comment text-info mr-2"></i>问题描述
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="mb-3">
                                                <div class="mt-1">
                                                    <span id="detailProblemSummary"></span>
                                                </div>
                                            </div>
                                            <div class="mb-1">
                                                <label class="font-weight-bold">咨询关键词：</label>
                                                <div class="mt-1" id="detailConsultationKeywords">
                                                    <!-- 关键词将通过JavaScript动态添加 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 特殊情况卡片 -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary-lighten">
                                    <h5 class="card-title mb-0">
                                        <i class="fa fa-exclamation-triangle text-warning mr-2"></i>特殊情况
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="mt-1">
                                                    是否职场问题：<span class="badge badge-primary" id="detailIsWorkplaceIssue"></span>
                                                </div>
                                            </div>
                                            <div class="mb-3" id="workplaceDescriptionRow" style="display: none;">
                                                <label class="font-weight-bold">职场问题描述：</label>
                                                <div class="mt-1">
                                                    <span id="detailWorkplaceDescription"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="mt-1">
                                                    是否有心理风险：<span class="badge badge-primary" id="detailHasPsychologicalRisk"></span>
                                                </div>
                                            </div>
                                            <div class="mb-3" id="riskDescriptionRow" style="display: none;">
                                                <label class="font-weight-bold">风险等级及风险描述：</label>
                                                <div class="mt-1">
                                                    <span id="detailRiskDescription"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 后续建议卡片 -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary-lighten">
                                    <h5 class="card-title mb-0">
                                        <i class="fa fa-lightbulb-o text-success mr-2"></i>后续建议
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="mb-3">
                                                <span id="detailFollowUpSuggestion"></span>
                                            </div>
                                            <div class="mb-3" id="otherSuggestionRow" style="display: none;">
                                                <label class="font-weight-bold">其他建议：</label>
                                                <div class="mt-1">
                                                    <span id="detailOtherSuggestion"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 时间信息卡片 -->
                            <div class="card">
                                <div class="card-header bg-primary-lighten">
                                    <h5 class="card-title mb-0">
                                        <i class="fa fa-clock-o text-secondary mr-2"></i>时间信息
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td class="font-weight-bold" style="width: 120px;">个案添加时间：</td>
                                                    <td><span id="detailCreateTime"></span></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        // 枚举值转换函数
        const EnumConverter = {
            consultationForm: {
                1: '驻场咨询',
                2: '线上咨询',
                3: '门店咨询'
            },
            consultationType: {
                1: '首次咨询',
                2: '第二次咨询',
                3: '第三次咨询',
                4: '第四次咨询',
                5: '第五次咨询',
                6: '第六次及以上咨询'
            },
            consultationField: {
                1: '心理健康',
                2: '情绪压力',
                3: '人际关系',
                4: '恋爱情感',
                5: '家庭关系',
                6: '亲子教育',
                7: '职场发展',
                8: '个人成长'
            },
            visitorAge: {
                1: '20岁及以下',
                2: '21-25岁',
                3: '26-30岁',
                4: '31-35岁',
                5: '36-40岁',
                6: '41-45岁',
                7: '46-50岁',
                8: '50岁以上'
            },
            maritalStatus: {
                1: '未婚',
                2: '已婚'
            },
            hasChildren: {
                1: '有',
                2: '无'
            },
            followUpSuggestion: {
                1: '无需跟进',
                2: '定期咨询',
                3: '转介就医',
                4: '其他'
            },
            riskLevel: {
                0: '无风险',
                1: '低风险',
                2: '中风险',
                3: '高风险'
            }
        };

        // 获取枚举值对应的文本
        function getEnumText(enumObj, value) {
            return enumObj[value] || '';
        }

        // 处理条件显示字段
        function handleConditionalFields(data) {
            // 处理职场问题
            const isWorkplaceIssue = data.isWorkplaceIssue === 1 ? '是' : '否';
            $("#workplaceDescriptionRow").toggle(data.isWorkplaceIssue === 1);
            $("#detailIsWorkplaceIssue").text(isWorkplaceIssue);
            $("#detailWorkplaceDescription").text(data.workplaceDescription || '');

            // 处理心理风险
            const hasPsychologicalRisk = data.hasPsychologicalRisk === 1 ? '有' : '无';
            $("#riskDescriptionRow").toggle(data.hasPsychologicalRisk === 1);
            $("#detailHasPsychologicalRisk").text(hasPsychologicalRisk);
            const riskLevel = getEnumText(EnumConverter.riskLevel, data.riskLevel);
            $("#detailRiskDescription").text('【' + riskLevel + '】'  + data.riskDescription || '');

            // 处理后续建议
            const followUpSuggestion = getEnumText(EnumConverter.followUpSuggestion, data.followUpSuggestion);
            $("#otherSuggestionRow").toggle(data.followUpSuggestion === 4);
            $("#detailFollowUpSuggestion").text(followUpSuggestion);
            $("#detailOtherSuggestion").text(data.otherSuggestion || '');
        }

        // 设置基本信息
        function setBasicInfo(data) {
            $("#structName").text(data.structName);
            $("#detailConsultationDate").text(data.consultationDate || '');
            $("#detailCounselorName").text(data.counselorName || '');
            $("#detailConsultationDuration").text(data.consultationDuration || '');
            $("#detailConsultationForm").text(getEnumText(EnumConverter.consultationForm, data.consultationForm));
            $("#detailConsultationType").text(getEnumText(EnumConverter.consultationType, data.consultationType));
            $("#detailConsultationField").text(getEnumText(EnumConverter.consultationField, data.consultationField));
            $("#detailVisitorAge").text(getEnumText(EnumConverter.visitorAge, data.visitorAge));
            $("#detailVisitorGender").text(data.visitorGender || '');
            $("#detailMaritalStatus").text(getEnumText(EnumConverter.maritalStatus, data.maritalStatus));
            $("#detailHasChildren").text(getEnumText(EnumConverter.hasChildren, data.hasChildren));
        }

        // 设置描述信息
        function setDescriptionInfo(data) {
            $("#detailProblemSummary").text(data.problemSummary || '');

            // 处理咨询关键词，使用badge样式
            const keywords = data.consultationKeywords ? data.consultationKeywords.split(',') : [];
            const keywordsHtml = keywords.length > 0 ? keywords.map(keyword =>
                `<span class="badge badge-info mr-1 mb-1">${keyword.trim()}</span>`
            ).join('') : '<span class="text-muted">暂无关键词</span>';
            $("#detailConsultationKeywords").html(keywordsHtml);

            $("#detailCreateTime").text(data.createTime || '');
        }

        $(function () {
            initSelect('#structId', '/anteroom/structs/get_for_select',{},'','选择机构');
            initDataTable();
            // 筛选按钮点击事件
            $("#btnSearch").click(function () {
                $("#searchModal").modal('show');
            });

            // 查询按钮点击事件
            $("#btnQuery").click(function () {
                $('#tbConsultationCase').DataTable().ajax.reload();
                $("#searchModal").modal('hide');
            });

            // 重置按钮点击事件
            $("#btnReset").click(function () {
                resetSearchForm();
            });

            // 新增按钮点击事件
            $("#btnAdd").click(function () {
                location.href = "/counselingroom/consultationcase/add";
            });

            // 修改按钮点击事件
            $("#tbConsultationCase").on('click', '.update', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/counselingroom/consultationcase/update?id=" + data.id;
            });

            // 查看详情按钮点击事件
            $("#tbConsultationCase").on('click', '.view-detail', function () {
                let data = oTable.row($(this).parents('tr')).data();

                // 设置基本信息
                setBasicInfo(data);

                // 设置描述信息
                setDescriptionInfo(data);

                // 处理条件显示字段
                handleConditionalFields(data);

                // 存储当前个案ID到模态框
                $("#viewDetailModal").data('currentCaseId', data.id);

                // 显示模态框
                $("#viewDetailModal").modal('show');
            });

            // 全选/取消全选
            $("#chkall").change(function () {
                $('.checklist').prop("checked", $(this).prop("checked"));
            });

            // 批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }

                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = $("input[name='checklist']:checked").map(function() {
                            return $(this).val();
                        }).get().join(',');

                        if (!ids) {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }

                        $.post("/counselingroom/consultationcase/batch_del", {ids: ids}, function (res) {
                            layer.msg(res.resultMsg, {
                                icon: res.resultCode === 200 ? 1 : 2,
                                time: 2000
                            });
                            if (res.resultCode === 200) {
                                oTable.draw();
                            }
                        }, 'json');
                    }
                });
            });

        });

        // 初始化DataTable的函数
        function initDataTable() {
            $("#tbConsultationCase").bsDataTables({
                columns: columns,
                url: '/counselingroom/consultationcase/get_my_cases',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    console.log('DataTable查询参数:', jsonObj); // 调试信息
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType":"application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            fnCallback(res);
                        }
                    });
                }
            });
        }

        // DataTable列定义
        let columns = [
            {
                "data": "id",
                "render": function (data, type, full, meta) {
                    return '<div class="custom-control custom-checkbox">' +
                        '<input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '">' +
                        '<label class="custom-control-label" for="lbl' + data + '"></label>' +
                        '</div>';
                },
                "bSortable": false
            },
            { "data": "structName", "bSortable": false },
            { "data": "consultationDate", "bSortable": false },
            { "data": "loginName", "bSortable": false },
            { "data": "realName", "bSortable": false },
            { "data": "consultationDuration", "bSortable": false },
            {
                "data": "consultationForm",
                "bSortable": false,
                "render": function(data) {
                    return getEnumText(EnumConverter.consultationForm, data);
                }
            },
            {
                "data": "consultationField",
                "bSortable": false,
                "render": function(data) {
                    return getEnumText(EnumConverter.consultationField, data);
                }
            }
        ];

        // DataTable列定义
        let columnDefs = [{
            targets: 8,
            render: function (data, type, row, meta) {
                let buttons = '';
                buttons += '<button type="button" class="btn btn-outline-warning btn-sm mr-1 update">' +
                    '<i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                buttons += '<button type="button" class="btn btn-outline-primary btn-sm view-detail">' +
                    '<i class="fa fa-eye mr-1"></i>查看</button>';
                return buttons;
            }
        }];

        // 获取查询条件
        let getQueryCondition = function (data) {
            let param = {};
            // 组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });

            const consultationDate = $("#consultationDate").val();
            if (consultationDate) {
                param.consultationDate = consultationDate;
            }
            param.structId= $("#structId").val();
            param.consultationType = $("#consultationType").val();
            param.consultationForm = $("#consultationForm").val();
            param.consultationField = $("#consultationField").val();
            param.visitorGender = $("#visitorGender").val();
            param.visitorAge = $("#visitorAge").val();
            param.maritalStatus = $("#maritalStatus").val();
            param.hasChildren = $("#hasChildren").val();
            param.loginName = $("#loginName").val();
            param.realName = $("#realName").val();
            if($("#riskLevel").val() != '') {
                param.riskLevel = $("#riskLevel").val();
            }
            // 是否职场问题：需要包含0和1的值
            if($("#isWorkplaceIssue").val() !== '') {
                param.isWorkplaceIssue = $("#isWorkplaceIssue").val();
            }
            // 是否有心理风险：需要包含0和1的值
            if($("#hasPsychologicalRisk").val() !== '') {
                param.hasPsychologicalRisk = $("#hasPsychologicalRisk").val();
            }
            return param;
        };

        // 重置查询条件
        function resetSearchForm() {
            $("#searchForm")[0].reset();
        }
    </script>
</th:block>
</body>
</html>
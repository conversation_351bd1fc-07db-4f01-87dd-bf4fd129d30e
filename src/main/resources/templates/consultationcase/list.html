<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">咨询个案管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">咨询个案管理</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <button type="button" id="btnSearch" class="btn btn-primary mr-1">
                                <i class="fa fa-search mr-1"></i>搜索
                            </button>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnDownload" type="button" class="btn btn-primary mb-2 mr-1"><i class="fa fa-download mr-1"></i>导出(Excel)</button>
                                <button id="btnAdd" type="button" class="btn btn-success mb-2 mr-1"><i class="fa fa-plus mr-1"></i>新增</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2"><i class="fa fa-trash-o"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <table id="tbConsultationCase" class="table table-striped" width="100%">
                        <thead>
                        <tr>
                            <th style="width: 30px;">
                                <div class="custom-control custom-checkbox">
                                    <input id="chkall" class="custom-control-input check" type="checkbox">
                                    <label class="custom-control-label" for="chkall"></label>
                                </div>
                            </th>
                            <th align="left">所属机构</th>
                            <th align="left">咨询日期</th>
                            <th align="left">咨询师姓名</th>
                            <th align="left">来访者工号</th>
                            <th align="left">来访者姓名</th>
                            <th align="left">咨询时长(分钟)</th>
                            <th align="left">咨询形式</th>
                            <th align="left">咨询领域</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- 筛选条件模态框 -->
    <div class="modal fade" id="searchModal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="searchModalLabel">筛选条件</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="searchForm">
                        <div class="form-group row">
                            <label class="col-4 col-form-label">所属机构：</label>
                            <div class="col-8">
                                <select class="form-control" id="structId" name="structId">
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询日期：</label>
                            <div class="col-8">
                                <input type="date" class="form-control" id="consultationDate" name="consultationDate">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询师：</label>
                            <div class="col-8">
                                <select class="form-control" id="counselorId" name="counselorId">
                                    <option value="">全部</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">员工工号：</label>
                            <div class="col-8">
                                <input type="text" class="form-control" id="loginName" name="loginName">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">员工姓名：</label>
                            <div class="col-8">
                                <input type="text" class="form-control" id="realName" name="realName">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">性别：</label>
                            <div class="col-8">
                                <select class="form-control" id="visitorGender" name="visitorGender">
                                    <option value="">全部</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询类型：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationType" name="consultationType">
                                    <option value="">全部</option>
                                    <option value="1">首次咨询</option>
                                    <option value="2">第二次咨询</option>
                                    <option value="3">第三次咨询</option>
                                    <option value="4">第四次咨询</option>
                                    <option value="5">第五次咨询</option>
                                    <option value="6">第六次及以上咨询</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询形式：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationForm" name="consultationForm">
                                    <option value="">全部</option>
                                    <option value="1">驻场咨询</option>
                                    <option value="2">线上咨询</option>
                                    <option value="3">门店咨询</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">咨询领域：</label>
                            <div class="col-8">
                                <select class="form-control" id="consultationField" name="consultationField">
                                    <option value="">全部</option>
                                    <option value="1">心理健康</option>
                                    <option value="2">情绪压力</option>
                                    <option value="3">人际关系</option>
                                    <option value="4">恋爱情感</option>
                                    <option value="5">家庭关系</option>
                                    <option value="6">亲子教育</option>
                                    <option value="7">职场发展</option>
                                    <option value="8">个人成长</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">年龄：</label>
                            <div class="col-8">
                                <select class="form-control" id="visitorAge" name="visitorAge">
                                    <option value="">全部</option>
                                    <option value="1">20岁及以下</option>
                                    <option value="2">21-25</option>
                                    <option value="3">26-30</option>
                                    <option value="4">31-35</option>
                                    <option value="5">36-40</option>
                                    <option value="6">41-45</option>
                                    <option value="7">46-50</option>
                                    <option value="8">50岁以上</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">婚姻状态：</label>
                            <div class="col-8">
                                <select class="form-control" id="maritalStatus" name="maritalStatus">
                                    <option value="">全部</option>
                                    <option value="1">未婚</option>
                                    <option value="2">已婚</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">有无子女：</label>
                            <div class="col-8">
                                <select class="form-control" id="hasChildren" name="hasChildren">
                                    <option value="">全部</option>
                                    <option value="1">有</option>
                                    <option value="2">无</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">是否有心理风险：</label>
                            <div class="col-8">
                                <select class="form-control" id="hasPsychologicalRisk" name="hasPsychologicalRisk">
                                    <option value="">全部</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">风险等级：</label>
                            <div class="col-8">
                                <select class="form-control" id="riskLevel" name="riskLevel">
                                    <option value="">全部</option>
                                    <option value="0">无风险</option>
                                    <option value="1">低风险</option>
                                    <option value="2">中风险</option>
                                    <option value="3">高风险</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-form-label">是否职场问题：</label>
                            <div class="col-8">
                                <select class="form-control" id="isWorkplaceIssue" name="isWorkplaceIssue">
                                    <option value="">全部</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="btnQuery">查询</button>
                    <button type="button" class="btn btn-light" id="btnReset">重置</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 查看详情模态框 -->
    <div class="modal fade" id="viewDetailModal" tabindex="-1" role="dialog" aria-labelledby="viewDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-light border-bottom">
                    <h5 class="modal-title" id="viewDetailModalLabel">
                        <i class="fa fa-file-text-o mr-2 text-muted"></i>咨询个案详情
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-4">
                    <!-- 基本信息 -->
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3 pb-2 border-bottom">
                            <i class="fa fa-info-circle text-primary mr-2"></i>
                            <h6 class="mb-0 font-weight-bold text-dark">基本信息</h6>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item mb-2">
                                    <span class="info-label">所属机构</span>
                                    <span class="info-value" id="structName"></span>
                                </div>
                                <div class="info-item mb-2">
                                    <span class="info-label">咨询日期</span>
                                    <span class="info-value" id="detailConsultationDate"></span>
                                </div>
                                <div class="info-item mb-2">
                                    <span class="info-label">咨询形式</span>
                                    <span class="info-value" id="detailConsultationForm"></span>
                                </div>
                                <div class="info-item mb-2">
                                    <span class="info-label">咨询时长</span>
                                    <span class="info-value"><span id="detailConsultationDuration"></span> 分钟</span>
                                </div>
                                <div class="info-item mb-2">
                                    <span class="info-label">咨询类型</span>
                                    <span class="info-value" id="detailConsultationType"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item mb-2">
                                    <span class="info-label">来访年龄</span>
                                    <span class="info-value" id="detailVisitorAge"></span>
                                </div>
                                <div class="info-item mb-2">
                                    <span class="info-label">来访性别</span>
                                    <span class="info-value" id="detailVisitorGender"></span>
                                </div>
                                <div class="info-item mb-2">
                                    <span class="info-label">婚姻状态</span>
                                    <span class="info-value" id="detailMaritalStatus"></span>
                                </div>
                                <div class="info-item mb-2">
                                    <span class="info-label">有无子女</span>
                                    <span class="info-value" id="detailHasChildren"></span>
                                </div>
                                <div class="info-item mb-2">
                                    <span class="info-label">咨询领域</span>
                                    <span class="info-value" id="detailConsultationField"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 问题描述 -->
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3 pb-2 border-bottom">
                            <i class="fa fa-comment-o text-primary mr-2"></i>
                            <h6 class="mb-0 font-weight-bold text-dark">问题描述</h6>
                        </div>
                        <div class="problem-summary-box mb-3">
                            <span id="detailProblemSummary"></span>
                        </div>
                        <div>
                            <div class="mb-2">
                                <small class="text-muted font-weight-medium">咨询关键词</small>
                            </div>
                            <div id="detailConsultationKeywords">
                                <!-- 关键词将通过JavaScript动态添加 -->
                            </div>
                        </div>
                    </div>

                    <!-- 特殊情况 -->
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3 pb-2 border-bottom">
                            <i class="fa fa-exclamation-triangle text-warning mr-2"></i>
                            <h6 class="mb-0 font-weight-bold text-dark">特殊情况</h6>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="special-item mb-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="special-label">是否职场问题</span>
                                        <span class="badge badge-secondary ml-2" id="detailIsWorkplaceIssue"></span>
                                    </div>
                                    <div id="workplaceDescriptionRow" style="display: none;">
                                        <div class="description-box">
                                            <small class="text-muted font-weight-medium d-block mb-1">职场问题描述</small>
                                            <span id="detailWorkplaceDescription"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="special-item mb-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="special-label">是否有心理风险</span>
                                        <span class="badge badge-secondary ml-2" id="detailHasPsychologicalRisk"></span>
                                    </div>
                                    <div id="riskDescriptionRow" style="display: none;">
                                        <div class="description-box">
                                            <small class="text-muted font-weight-medium d-block mb-1">风险等级及描述</small>
                                            <span id="detailRiskDescription"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 后续建议 -->
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3 pb-2 border-bottom">
                            <i class="fa fa-lightbulb-o text-success mr-2"></i>
                            <h6 class="mb-0 font-weight-bold text-dark">后续建议</h6>
                        </div>
                        <div class="suggestion-box mb-3">
                            <span id="detailFollowUpSuggestion" class="font-weight-medium"></span>
                        </div>
                        <div id="otherSuggestionRow" style="display: none;">
                            <div class="description-box">
                                <small class="text-muted font-weight-medium d-block mb-1">其他建议</small>
                                <span id="detailOtherSuggestion"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 时间信息 -->
                    <div class="border-top pt-3">
                        <div class="text-right">
                            <small class="text-muted">
                                <i class="fa fa-clock-o mr-1"></i>个案添加时间：<span id="detailCreateTime" class="font-weight-medium"></span>
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light border-top">
                    <button type="button" class="btn btn-primary btn-sm" id="btnDownloadWord" title="下载Word文档">
                        <i class="fa fa-download mr-1"></i>下载
                    </button>
                    <button type="button" class="btn btn-light btn-sm" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <style>
        /* 自定义样式 */
        .info-item {
            display: flex;
            align-items: center;
            min-height: 28px;
            padding: 6px 12px;
            margin: 2px 0;
            background: #fafbfc;
            border-radius: 4px;
            border-left: 3px solid var(--bs-primary, #727cf5);
            transition: all 0.2s ease;
            opacity: 0.9;
        }
        
        .info-item:hover {
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.05);
            border-left-color: var(--bs-primary, #727cf5);
            opacity: 1;
        }
        
        .info-label {
            color: var(--bs-primary, #727cf5);
            font-size: 13px;
            font-weight: 600;
            width: 90px;
            flex-shrink: 0;
        }
        
        .info-value {
            color: #495057;
            font-size: 13px;
            font-weight: 500;
            flex: 1;
        }
        
        .problem-summary-box {
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.05);
            border: 1px solid rgba(var(--bs-primary-rgb, 114, 124, 245), 0.2);
            border-radius: 6px;
            padding: 16px;
            line-height: 1.6;
            font-size: 14px;
            color: var(--bs-primary, #727cf5);
            min-height: 60px;
        }
        
        .special-label {
            color: #495057;
            font-size: 13px;
            font-weight: 500;
        }
        
        .description-box {
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.03);
            border: 1px solid rgba(var(--bs-primary-rgb, 114, 124, 245), 0.15);
            border-left: 4px solid var(--bs-primary, #727cf5);
            border-radius: 4px;
            padding: 12px;
            margin-top: 8px;
            font-size: 13px;
            color: var(--bs-primary, #727cf5);
            line-height: 1.5;
        }
        
        .suggestion-box {
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.08);
            border: 1px solid rgba(var(--bs-primary-rgb, 114, 124, 245), 0.25);
            border-radius: 6px;
            padding: 16px;
            font-size: 14px;
            color: var(--bs-primary, #727cf5);
            line-height: 1.6;
            min-height: 50px;
        }
        
        .special-item {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            border: 1px solid #e9ecef;
        }
        
        .special-item .badge {
            font-size: 11px;
            padding: 5px 10px;
            font-weight: 600;
        }
        
        .special-item .badge-secondary {
            background: var(--bs-primary, #727cf5) !important;
            color: white !important;
            opacity: 0.9;
        }
        
        #viewDetailModal .modal-body {
            background: #fff;
        }
        
        #viewDetailModal .border-bottom {
            border-color: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.15) !important;
        }
        
        #viewDetailModal h6 {
            font-size: 14px;
            color: var(--bs-primary, #727cf5);
            text-shadow: 0 1px 1px rgba(0,0,0,0.05);
            font-weight: 600;
        }
        
        #viewDetailModal .text-primary {
            color: var(--bs-primary, #727cf5) !important;
        }
        
        #viewDetailModal .text-success {
            color: var(--bs-primary, #727cf5) !important;
        }
        
        #viewDetailModal .text-warning {
            color: var(--bs-primary, #727cf5) !important;
        }
        
        #detailConsultationKeywords .badge {
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.1) !important;
            border: 1px solid rgba(var(--bs-primary-rgb, 114, 124, 245), 0.3) !important;
            color: var(--bs-primary, #727cf5) !important;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        
        #detailConsultationKeywords .badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(var(--bs-primary-rgb, 114, 124, 245), 0.25);
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.15) !important;
        }
        
        .modal-dialog {
            margin: 30px auto;
        }
        
        .modal-content {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,.15);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .modal-header {
            border-radius: 8px 8px 0 0;
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.03);
            border-bottom: 2px solid rgba(var(--bs-primary-rgb, 114, 124, 245), 0.1);
        }
        
        .modal-footer {
            border-radius: 0 0 8px 8px;
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.03);
            border-top: 1px solid rgba(var(--bs-primary-rgb, 114, 124, 245), 0.1);
        }
        
        .modal-body {
            background: #fafbfc;
        }
        
        /* 区块分隔线美化 */
        #viewDetailModal .border-bottom {
            position: relative;
        }
        
        #viewDetailModal .border-bottom::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 50px;
            height: 2px;
            background: var(--bs-primary, #727cf5);
            border-radius: 1px;
            opacity: 0.7;
        }
        
        /* 时间信息样式优化 */
        .border-top {
            border-top: 1px solid rgba(var(--bs-primary-rgb, 114, 124, 245), 0.15) !important;
            background: rgba(var(--bs-primary-rgb, 114, 124, 245), 0.05);
            margin: 0 -1rem;
            padding: 12px 1rem !important;
            border-radius: 0 0 6px 6px;
        }
        
        .border-top .text-muted {
            color: var(--bs-primary, #727cf5) !important;
        }
    </style>
    <script type="text/javascript">
        // 枚举值转换函数
        const EnumConverter = {
            consultationForm: {
                1: '驻场咨询',
                2: '线上咨询',
                3: '门店咨询'
            },
            consultationType: {
                1: '首次咨询',
                2: '第二次咨询',
                3: '第三次咨询',
                4: '第四次咨询',
                5: '第五次咨询',
                6: '第六次及以上咨询'
            },
            consultationField: {
                1: '心理健康',
                2: '情绪压力',
                3: '人际关系',
                4: '恋爱情感',
                5: '家庭关系',
                6: '亲子教育',
                7: '职场发展',
                8: '个人成长'
            },
            visitorAge: {
                1: '20岁及以下',
                2: '21-25岁',
                3: '26-30岁',
                4: '31-35岁',
                5: '36-40岁',
                6: '41-45岁',
                7: '46-50岁',
                8: '50岁以上'
            },
            maritalStatus: {
                1: '未婚',
                2: '已婚'
            },
            hasChildren: {
                1: '有',
                2: '无'
            },
            followUpSuggestion: {
                1: '无需跟进',
                2: '定期咨询',
                3: '转介就医',
                4: '其他'
            },
            riskLevel: {
                0: '无风险',
                1: '低风险',
                2: '中风险',
                3: '高风险'
            }
        };

        // 获取枚举值对应的文本
        function getEnumText(enumObj, value) {
            return enumObj[value] || '';
        }

        // 处理条件显示字段
        function handleConditionalFields(data) {
            // 处理职场问题
            const isWorkplaceIssue = data.isWorkplaceIssue === 1 ? '是' : '否';
            $("#workplaceDescriptionRow").toggle(data.isWorkplaceIssue === 1);
            $("#detailIsWorkplaceIssue").text(isWorkplaceIssue);
            $("#detailWorkplaceDescription").text(data.workplaceDescription || '');

            // 处理心理风险
            const hasPsychologicalRisk = data.hasPsychologicalRisk === 1 ? '有' : '无';
            $("#riskDescriptionRow").toggle(data.hasPsychologicalRisk === 1);
            $("#detailHasPsychologicalRisk").text(hasPsychologicalRisk);
            const riskLevel = getEnumText(EnumConverter.riskLevel, data.riskLevel);
            $("#detailRiskDescription").text('【' + riskLevel + '】'  + data.riskDescription || '');

            // 处理后续建议
            const followUpSuggestion = getEnumText(EnumConverter.followUpSuggestion, data.followUpSuggestion);
            $("#otherSuggestionRow").toggle(data.followUpSuggestion === 4);
            $("#detailFollowUpSuggestion").text(followUpSuggestion);
            $("#detailOtherSuggestion").text(data.otherSuggestion || '');
        }

        // 设置基本信息
        function setBasicInfo(data) {
            $("#structName").text(data.structName);
            $("#detailConsultationDate").text(data.consultationDate || '');
            $("#detailCounselorName").text(data.counselorName || '');
            $("#detailConsultationDuration").text(data.consultationDuration || '');
            $("#detailConsultationForm").text(getEnumText(EnumConverter.consultationForm, data.consultationForm));
            $("#detailConsultationType").text(getEnumText(EnumConverter.consultationType, data.consultationType));
            $("#detailConsultationField").text(getEnumText(EnumConverter.consultationField, data.consultationField));
            $("#detailVisitorAge").text(getEnumText(EnumConverter.visitorAge, data.visitorAge));
            $("#detailVisitorGender").text(data.visitorGender || '');
            $("#detailMaritalStatus").text(getEnumText(EnumConverter.maritalStatus, data.maritalStatus));
            $("#detailHasChildren").text(getEnumText(EnumConverter.hasChildren, data.hasChildren));
        }

        // 设置描述信息
        function setDescriptionInfo(data) {
            $("#detailProblemSummary").text(data.problemSummary || '');
            
            // 处理咨询关键词，使用badge样式
            const keywords = data.consultationKeywords ? data.consultationKeywords.split(',') : [];
            const keywordsHtml = keywords.length > 0 ? keywords.map(keyword => 
                `<span class="badge mr-2 mb-2">${keyword.trim()}</span>`
            ).join('') : '<span class="text-muted small">暂无关键词</span>';
            $("#detailConsultationKeywords").html(keywordsHtml);
            
            $("#detailCreateTime").text(data.createTime || '');
        }

        $(function () {
            // 初始化全局变量
            window.pendingStructId = null;
            window.pendingStructName = null;
            console.log('页面加载 - 初始化全局变量:', {
                pendingStructId: window.pendingStructId,
                pendingStructName: window.pendingStructName
            });
            
            //初始化页面权限
            initPage();
            initSelect('#structId', '/anteroom/structs/get_for_select',{},'','选择机构');
            
            // 处理URL参数 - 如果有部门名称参数需要等API完成后再初始化DataTable
            const urlParams = new URLSearchParams(window.location.search);
            const hasStructName = urlParams.get('structName');
            
            if (hasStructName) {
                console.log('检测到部门名称参数，等待API完成后初始化DataTable');
                // 等待API完成后再初始化DataTable
                initFromUrlParams(function() {
                    console.log('API调用完成，开始初始化DataTable');
                    initDataTable();
                });
            } else {
                console.log('无部门名称参数，直接初始化DataTable');
                // 没有部门名称参数，正常初始化
                initFromUrlParams();
                initDataTable();
            }
            
            // 筛选按钮点击事件
            $("#btnSearch").click(function () {
                $("#searchModal").modal('show');
            });
            
            // 查询按钮点击事件
            $("#btnQuery").click(function () {
                // 清除通过API获取的部门ID，使用用户在界面上选择的筛选条件
                window.pendingStructId = null;
                window.pendingStructName = null;
                $('#tbConsultationCase').DataTable().ajax.reload();
                $("#searchModal").modal('hide');
            });

            // 重置按钮点击事件
            $("#btnReset").click(function () {
                resetSearchForm();
            });
            // 加载咨询师列表
            initSelect('#counselorId', '/anteroom/user/getCounselorList_for_select',{});

            // 新增按钮点击事件
            $("#btnAdd").click(function () {
                location.href = "/counselingroom/consultationcase/add";
            });

            // 修改按钮点击事件
            $("#tbConsultationCase").on('click', '.update', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/counselingroom/consultationcase/update?id=" + data.id;
            });

            // 查看详情按钮点击事件
            $("#tbConsultationCase").on('click', '.view-detail', function () {
                let data = oTable.row($(this).parents('tr')).data();
                
                // 设置基本信息
                setBasicInfo(data);
                
                // 设置描述信息
                setDescriptionInfo(data);
                
                // 处理条件显示字段
                handleConditionalFields(data);
                
                // 存储当前个案ID到模态框
                $("#viewDetailModal").data('currentCaseId', data.id);
                
                // 显示模态框
                $("#viewDetailModal").modal('show');
            });

            // 下载Word文档按钮点击事件
            $("#btnDownloadWord").click(function () {
                // 获取当前显示的个案ID
                let currentCaseId = $("#viewDetailModal").data('currentCaseId');
                if (!currentCaseId) {
                    layer.msg("无法获取个案信息", { icon: 2, time: 2000 });
                    return;
                }
                
                // 显示加载提示
                layer.msg("正在生成Word文档，请稍候...", { icon: 16, time: 0 });
                
                // 发起下载请求
                window.open("/counselingroom/consultationcase/export_word?id=" + currentCaseId, "_blank");
                
                // 关闭加载提示
                setTimeout(function() {
                    layer.closeAll();
                    layer.msg("Word文档生成完成", { icon: 1, time: 2000 });
                }, 2000);
            });

            //导出
            $("#btnDownload").click(function () {
                layer.confirm('确定导出吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        layer.msg('请稍后…', {
                            icon: 17, shade: 0.05, time: false
                        });
                        let param = {};
                        param.structId = $("#structId").val();
                        const consultationDate = $("#consultationDate").val();
                        if (consultationDate) {
                            param.consultationDate = consultationDate;
                        }
                        param.counselorId = $("#counselorId").val();
                        param.consultationType = $("#consultationType").val();
                        param.consultationForm = $("#consultationForm").val();
                        param.consultationField = $("#consultationField").val();
                        param.visitorGender = $("#visitorGender").val();
                        param.loginName = $("#loginName").val();
                        param.realName = $("#realName").val();
                        $.ajax({
                            type: 'POST',
                            url: '/counselingroom/consultationcase/download',
                            data: JSON.stringify(param),
                            contentType:'application/json',
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll();
                                location.href="/static/upload/temp/"+res;
                            }
                        });
                    }
                });
            });

            // 全选/取消全选
            $("#chkall").change(function () {
                $('.checklist').prop("checked", $(this).prop("checked"));
            });

            // 批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = $("input[name='checklist']:checked").map(function() {
                            return $(this).val();
                        }).get().join(',');
                        
                        if (!ids) {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        
                        $.post("/counselingroom/consultationcase/batch_del", {ids: ids}, function (res) {
                            layer.msg(res.resultMsg, { 
                                icon: res.resultCode === 200 ? 1 : 2, 
                                time: 2000 
                            });
                            if (res.resultCode === 200) {
                                oTable.draw();
                            }
                        }, 'json');
                    }
                });
            });

        });

        // 初始化DataTable的函数
        function initDataTable() {
            console.log('开始初始化DataTable');
            $("#tbConsultationCase").bsDataTables({
                columns: columns,
                url: '/counselingroom/consultationcase/get_list_by_paged',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    console.log('DataTable查询参数:', jsonObj); // 调试信息
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType":"application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            fnCallback(res);
                        }
                    });
                }
            });
        }

        // 页面初始化函数
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };

        // 从URL参数初始化筛选条件
        function initFromUrlParams(callback) {
            const urlParams = new URLSearchParams(window.location.search);
            console.log('列表页面接收到的URL参数:', urlParams.toString()); // 调试信息
            
            // 设置机构筛选 - 优先处理部门名称，其次处理部门ID
            const structName = urlParams.get('structName');
            const structId = urlParams.get('structId');
            
            let hasStructParam = false;
            if (structName) {
                console.log('接收到部门名称参数:', structName); // 调试信息
                hasStructParam = true;
                // 直接调用API将部门名称转换为部门ID，等待API完成后再初始化DataTable
                handleStructNameFilter(structName, function(success) {
                    console.log('部门ID转换完成，结果:', success);
                    // 设置其他参数
                    setOtherUrlParams(urlParams);
                    // 回调通知可以初始化DataTable了
                    if (typeof callback === 'function') {
                        callback();
                    }
                });
                return; // 异步处理，直接返回
            } else if (structId) {
                setTimeout(() => {
                    $('#structId').val(structId).trigger('change');
                }, 500);
            }
            
            // 设置其他参数并完成初始化
            setOtherUrlParams(urlParams);
            
            // 如果有任何筛选条件且不是部门名称参数，通知可以初始化DataTable
            const hasOtherParams = structId || urlParams.get('consultationForm') || urlParams.get('consultationField') || 
                urlParams.get('consultationType') || urlParams.get('visitorGender') || urlParams.get('visitorAge') || 
                urlParams.get('maritalStatus') || urlParams.get('hasChildren') || urlParams.get('riskLevel') || 
                urlParams.get('isWorkplaceIssue') || urlParams.get('hasPsychologicalRisk') || urlParams.get('dateRange');
            
            if (typeof callback === 'function') {
                callback();
            }
        }
        
        // 设置其他URL参数的函数
        function setOtherUrlParams(urlParams) {
            const consultationForm = urlParams.get('consultationForm');
            if (consultationForm) {
                $('#consultationForm').val(consultationForm);
            }
            
            const consultationField = urlParams.get('consultationField');
            if (consultationField) {
                $('#consultationField').val(consultationField);
            }
            
            const consultationType = urlParams.get('consultationType');
            if (consultationType) {
                $('#consultationType').val(consultationType);
            }
            
            const visitorGender = urlParams.get('visitorGender');
            if (visitorGender) {
                $('#visitorGender').val(visitorGender);
            }
            
            const visitorAge = urlParams.get('visitorAge');
            if (visitorAge) {
                $('#visitorAge').val(visitorAge);
            }
            
            const maritalStatus = urlParams.get('maritalStatus');
            if (maritalStatus) {
                $('#maritalStatus').val(maritalStatus);
            }
            
            const hasChildren = urlParams.get('hasChildren');
            if (hasChildren) {
                $('#hasChildren').val(hasChildren);
            }
            
            const riskLevel = urlParams.get('riskLevel');
            if (riskLevel) {
                $('#riskLevel').val(riskLevel);
            }
            
            const isWorkplaceIssue = urlParams.get('isWorkplaceIssue');
            if (isWorkplaceIssue !== null) {
                $('#isWorkplaceIssue').val(isWorkplaceIssue);
            }
            
            const hasPsychologicalRisk = urlParams.get('hasPsychologicalRisk');
            if (hasPsychologicalRisk !== null) {
                $('#hasPsychologicalRisk').val(hasPsychologicalRisk);
            }
            
            // 设置日期范围
            const dateRange = urlParams.get('dateRange');
            if (dateRange) {
                const dates = dateRange.split(' 至 ');
                if (dates.length === 2) {
                    $('#consultationDate').val(dates[0]); // 只取开始日期
                }
            }
        }

        // 处理部门名称筛选
        function handleStructNameFilter(structName, callback) {
            console.log('正在查询部门ID，部门名称:', structName);
            
            $.get('/anteroom/structs/getIdByName', {structName: structName }, function(response) {
                if (response) {
                    console.log('成功获取部门ID:', response, '部门名称:', structName);
                    // 将部门ID存储为待查询的部门ID
                    window.pendingStructId = response;
                    // 清除部门名称，避免重复传递
                    window.pendingStructName = null;
                    // 如果有回调函数，执行回调；否则直接触发数据查询
                    if (typeof callback === 'function') {
                        callback(true);
                    } else {
                        $('#tbConsultationCase').DataTable().ajax.reload();
                    }
                } else {
                    console.log('未找到部门，部门名称:', structName);
                    layer.msg('未找到指定部门"' + structName + '"', {icon: 2});
                    if (typeof callback === 'function') {
                        callback(false);
                    }
                }
            }).fail(function(xhr, status, error) {
                console.error('查询部门ID失败:', error);
                layer.msg('查询部门信息失败', {icon: 2});
                if (typeof callback === 'function') {
                    callback(false);
                }
            });
        }

        // DataTable列定义
        let columns = [
            {
                "data": "id", 
                "render": function (data, type, full, meta) {
                    return '<div class="custom-control custom-checkbox">' +
                           '<input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '">' +
                           '<label class="custom-control-label" for="lbl' + data + '"></label>' +
                           '</div>';
                }, 
                "bSortable": false
            },
            { "data": "structName", "bSortable": false },
            { "data": "consultationDate", "bSortable": false },
            { "data": "counselorName", "bSortable": false },
            { "data": "loginName", "bSortable": false },
            { "data": "realName", "bSortable": false },
            { "data": "consultationDuration", "bSortable": false },
            {
                "data": "consultationForm", 
                "bSortable": false,
                "render": function(data) {
                    return getEnumText(EnumConverter.consultationForm, data);
                }
            },
            {
                "data": "consultationField", 
                "bSortable": false,
                "render": function(data) {
                    return getEnumText(EnumConverter.consultationField, data);
                }
            }
        ];

        // DataTable列定义
        let columnDefs = [{
            targets: 9,
            render: function (data, type, row, meta) {
                let buttons = '';
                if ('[[${canUpdate}]]' === 'true') {
                    buttons += '<button type="button" class="btn btn-outline-warning btn-sm mr-1 update">' +
                              '<i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                }
                buttons += '<button type="button" class="btn btn-outline-primary btn-sm view-detail">' +
                          '<i class="fa fa-eye mr-1"></i>查看</button>';
                return buttons;
            }
        }];

        // 获取查询条件
        let getQueryCondition = function (data) {
            let param = {};
            // 组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            
            // 组装筛选条件
            // 优先使用通过API获取的部门ID，其次使用下拉框选择的机构ID
            console.log('getQueryCondition - window.pendingStructId 状态:', window.pendingStructId); // 调试信息
            if (window.pendingStructId) {
                param.structId = window.pendingStructId;
                console.log('查询条件使用API获取的部门ID:', param.structId); // 调试信息
            } else {
                const dropdownStructId = $("#structId").val();
                param.structId = dropdownStructId;
                console.log('查询条件使用下拉框机构ID:', dropdownStructId); // 调试信息
            }
            
            const consultationDate = $("#consultationDate").val();
            if (consultationDate) {
                param.consultationDate = consultationDate;
            }
            param.counselorId = $("#counselorId").val();
            param.consultationType = $("#consultationType").val();
            param.consultationForm = $("#consultationForm").val();
            param.consultationField = $("#consultationField").val();
            param.visitorGender = $("#visitorGender").val();
            param.visitorAge = $("#visitorAge").val();
            param.maritalStatus = $("#maritalStatus").val();
            param.hasChildren = $("#hasChildren").val();
            param.loginName = $("#loginName").val();
            param.realName = $("#realName").val();
            if($("#riskLevel").val() != '') {
                param.riskLevel = $("#riskLevel").val();
            }
            // 是否职场问题：需要包含0和1的值
            if($("#isWorkplaceIssue").val() !== '') {
                param.isWorkplaceIssue = $("#isWorkplaceIssue").val();
            }
            // 是否有心理风险：需要包含0和1的值
            if($("#hasPsychologicalRisk").val() !== '') {
                param.hasPsychologicalRisk = $("#hasPsychologicalRisk").val();
            }
            return param;
        };

        // 重置查询条件
        function resetSearchForm() {
            $("#searchForm")[0].reset();
            // 清除存储的部门相关参数
            window.pendingStructId = null;
            window.pendingStructName = null;
            $('#tbConsultationCase').DataTable().ajax.reload();
        }
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询个案管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">咨询关键词管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">咨询关键词管理</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2">
                                    <div class="input-group">
                                        <label for="sr-keyword" class="sr-only">搜索</label>
                                        <input type="text" class="form-control" id="sr-keyword" placeholder="关键词名称..." autocomplete="off">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-success mb-2 mr-1"><i class="fa fa-plus mr-1"></i>新增</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <table id="tbKeyword" class="table table-striped" width="100%">
                        <thead>
                        <tr>
                            <th style="width: 30px;">
                                <div class="custom-control custom-checkbox">
                                    <input id="chkall" class="custom-control-input check" type="checkbox">
                                    <label class="custom-control-label" for="chkall"></label>
                                </div>
                            </th>
                            <th>关键词</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
    <!-- modal.关键词管理 start -->
    <div id="keyword-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="modal-title"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="frmKeyword" class="pl-3 pr-3" action="#">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="keyword">关键词名称</label>
                            <input id="keyword" name="keyword" class="form-control" type="text" maxlength="5" autocomplete="off" placeholder="请输入关键词名称">
                        </div>
                        <input id="hidID" type="hidden" value="0" />
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                        <input type="submit" class="btn btn-primary" id="btnSave" value="保存" />
                    </div>
                </form>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.关键词管理 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            //初始化页面权限
            initPage();
            $("#btnQuery").click(function () {
                $('#tbKeyword').DataTable().ajax.reload();
            });
            //datatables
            $("#tbKeyword").bsDataTables({
                columns: columns,
                url: '/counselingroom/keywords/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //新增
            $("#btnAdd").click(function () {
                resetForm();
                $("#modal-title").html('<i class="fa fa-plus mr-1"></i>新增关键词');
                $("#keyword-modal").modal();
                // 强制触发remote校验，确保清空之前的校验状态
                setTimeout(function() {
                    $("#keyword").trigger('blur');
                }, 100);
            });
            
            // 模态框关闭时重置表单
            $("#keyword-modal").on('hidden.bs.modal', function () {
                resetForm();
            });
            
            // 关键词输入框获得焦点时触发校验
            $("#keyword").on('focus', function() {
                // 如果输入框有值，触发校验
                if ($(this).val().trim()) {
                    $(this).trigger('blur');
                }
            });
            //修改
            $("#tbKeyword").on('click', '.update', function () {
                resetForm();
                var data = oTable.row($(this).parents('tr')).data();
                $("#hidID").val(data.id);
                $("#keyword").val(data.keyword);
                $("#modal-title").html('<i class="fa fa-pencil-square-o mr-1"></i>修改关键词');
                $("#keyword-modal").modal();
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                var sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        var ids = "";
                        var array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        var jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/counselingroom/keywords/batch_delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });

            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });

            $("#frmKeyword").validate({
                rules: {
                    keyword: {
                        required: true,
                        minlength: 1,
                        maxlength: 5,
                        remote: {
                            type: "POST",
                            url: "/counselingroom/keywords/check_exists",
                            dataType: "json",
                            data: {
                                keyword: function () {
                                    return $("#keyword").val();
                                },
                                id: function () {
                                    return $("#hidID").val();
                                }
                            },
                            dataFilter: function (data, type) {
                                try {
                                    const res = JSON.parse(data);
                                    // 如果返回200表示不存在重复，可以保存
                                    if(res.resultCode === 200) return true;
                                    // 如果返回201表示存在重复，不能保存
                                    if(res.resultCode === 201) return false;
                                    // 其他情况也返回false
                                    return false;
                                } catch(e) {
                                    console.error("Remote validation error:", e);
                                    return false;
                                }
                            },
                            // 确保每次输入都触发校验，不依赖缓存
                            cache: false
                        }
                    }
                },
                messages: {
                    keyword: { 
                        required: "请填写关键词", 
                        minlength: "关键词长度不能少于1个字",
                        maxlength: "关键词长度不能超过5个字",
                        remote: "该关键词已存在，请使用其他关键词" 
                    }
                },
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                },
                highlight: function(element) {
                    $(element).addClass('is-invalid');
                },
                unhighlight: function(element) {
                    $(element).removeClass('is-invalid');
                },
                submitHandler: function () {
                    var jsonObj = {
                        "id": $("#hidID").val(),
                        "keyword": $.trim($("#keyword").val())
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("disabled", true);
                    var url = $("#hidID").val() === "0" ? "/counselingroom/keywords/add" : "/counselingroom/keywords/update";
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                $("#keyword-modal").modal('hide');
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        },
                        error: function(xhr, status, error) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("disabled", false);
                            layer.msg("保存失败，请稍后重试", { icon: 2, time: 2000 });
                            console.error("Save error:", error);
                        }
                    });
                }
            });
        });
        var resetForm = function () {
            // 清除表单数据
            $("#hidID").val(0);
            $("#keyword").val("");
            
            // 清除校验状态和错误提示
            $("#frmKeyword input").removeClass("error is-invalid");
            $("label.error").hide();
            
            // 重置jquery.validate的校验状态
            if ($("#frmKeyword").validate()) {
                $("#frmKeyword").validate().resetForm();
            }
            
            // 清除remote校验的缓存
            $("#keyword").removeData("previousValue");
        };
        var initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        var columns = [
            {"data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "keyword", "bSortable": false }];
        var columnDefs = [{
            targets: 2, render: function (data, type, row, meta) {
                var buttons = "";
                if ('[[${canUpdate}]]' === 'true') buttons +='<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update"><i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                return buttons;
            }
        }];
        var getQueryCondition = function (data) {
            var param = {};
            param.keyword= $("#sr-keyword").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html> 
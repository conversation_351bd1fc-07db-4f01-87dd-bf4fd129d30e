<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">生成测评结果</a></li>
                    </ol>
                </div>
                <h4 class="page-title">生成测评结果</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    请输入测评记录ID
                </div>
                <div class="card-body">
                    <form id="frmCalc" class="form-horizontal col-lg-6">
                        <div class="form-group row mb-3">
                            <label for="recordId" class="col-3 col-form-label">记录ID</label>
                            <div class="col-9">
                                <input id="recordId" name="recordId" class="form-control" type="text" />
                            </div>
                        </div>
                        <div class="form-group mb-0 justify-content-end row">
                            <div class="col-9">
                                <input type="submit" id="btnSave" class="btn btn-primary mr-1" value="提交" />
                            </div>
                        </div>
                    </form>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        $(function () {
            $("#frmCalc").validate({
                rules: {
                    recordId: { required: true }
                },
                messages: {
                    recordId: { required: "请填写记录ID" }
                },
                submitHandler: function () {
                    $("#btnSave").val("提交中……");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/measuringroom/testing/calc',
                        dataType: 'json',
                        data: {recordId: $("#recordId").val()},
                        success: function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                location.reload();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测评结果管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">九型人格结果筛查</a></li>
                    </ol>
                </div>
                <h4 class="page-title">九型人格结果筛查</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2 mr-1">
                                    <select class="form-control" style="width:100%;" name="sr-factor" id="sr-factor">
                                        <option></option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary mb-2" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="tbRecordList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th align="left">用户名</th>
                                <th align="left">姓名</th>
                                <th align="left">所属组织</th>
                                <th align="left">九型人格</th>
                                <th align="left">测试日期</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            initSelectJson("#sr-factor", "/measuringroom/scaleFactor/get_for_select", {"scaleId":10000151});
            $("#btnQuery").click(function () {
                $('#tbRecordList').DataTable().ajax.reload();
            });
            //datatables
            $("#tbRecordList").bsDataTables({
                columns: columns,
                url: '/measuringroom/testing/get_ninehouse_records',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
        });
        let columns = [
            { "data": "loginName", "bSortable": false },
            { "data": "realName", "bSortable": false },
            { "data": "structFullName", "bSortable": false },
            { "data": "factorName", "bSortable": false },
            { "data": "testDate", "bSortable": false},
            {"data": "recordId", "bSortable": false,
                render: function (data, type, row, meta) {
                    return '<span class="badge badge-info badge-pill cursor-pointer"><a class="text-white" href="/measuringroom/testing/report?recordId=' + row.recordId + '" target="_blank">测评报告</a></span>';
                }
            }
        ];
        let columnDefs = [];
        let getQueryCondition = function (data) {
            let param = {};
            param.factorId = $("#sr-factor").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
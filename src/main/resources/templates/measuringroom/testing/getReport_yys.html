<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style type="text/css">
        .ul-logo li {
            float: left;
            display: inline;
            margin-right: 20px;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row d-print-none">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理测试</a></li>
                    </ol>
                </div>
                <h4 class="page-title">心理测试</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body ml-4 mr-4" id="report">
                    <div class="d-print-none">
                        <div class="dropdown card-widgets">
                            <a href="#" class="dropdown-toggle arrow-none" data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a href="javascript:window.print()" class="dropdown-item"><i class="fa fa-print mr-2"></i>打印</a>
                                <a href="javascript:download()" class="dropdown-item"><i class="fa fa-download mr-2"></i>下载报告[word]</a>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="pull-right">
                                <ul class="ul-logo">
                                    <li><img th:src="@{/static/images/bhj.png}" class="img-fluid" width="100" /></li>
                                    <li><img th:src="@{/static/images/ydl.png}" class="img-fluid" width="100" /></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="clearfix">
                        <div class="text-center mt-4 mb-4">
                            <h4 class="m-0 letter-spacing-2 font-weight-500">《<span class="scaleName"></span>》测评报告</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="mb-1">基本信息</h4>
                            <div class="row mt-2">
                                <div class="col-sm-4">
                                    <p class="mb-2"><strong class="mr-2">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</strong><span id="realName"></span></p>
                                    <p class="mb-2"><strong class="mr-2">测试日期：</strong><span id="startDate"></span></p>
                                </div>
                                <div class="col-sm-4">
                                    <p class="mb-2"><strong class="mr-2">所属组织：</strong><span id="fullStructName"></span></p>
                                    <p class="mb-2"><strong class="mr-2">耗&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;时：</strong> <span id="costTime"></span></p>
                                </div>
                                <div class="col-sm-4">
                                    <p class="mb-2"><strong class="mr-2">测试项目：</strong><span class="scaleName"></span></p>
                                    <p class="mb-2"></p>
                                </div>
                            </div>
                        </div> <!-- end col -->
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="mb-1">测评定性</h4>
                            <div id="explain-nature">

                            </div>
                        </div>
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="mb-1">EPQ-RSC测评结果</h4>
                            <div class="mb-2" id="epq-type"></div>
                            <div id="explain-epq" class="mt-4">

                            </div>
                        </div>
                    </div>
                    <div class="divider dashed large mt-2 mb-2 hide"></div>
                    <div class="row hide">
                        <div class="col-12">
                            <h4 class="mb-2">图表分析</h4>
                            <canvas id="epq-container" style="height: 400px; min-width: 600px "></canvas>
                        </div>
                    </div>
                </div>
            </div> <!-- end card -->
            <div class="alert alert-light m-2 hide" role="alert">

            </div>
        </div> <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let record, scale, user, scaleName;
        let factors = [], originalFactors = [], scores = [], standartScores = [],chartsImgArray = [];
        $(function () {
            initReport();
        });
        let initReport = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode != undefined) {
                        if (res.resultCode == 201) {
                            $("#report").hide();
                            $(".alert").removeClass("hide").addClass("show");
                            $(".alert").append('<img class="mr-1" src="/static/images/success.png" width="32" />' + (res.resultMsg == "" ? "您已经做完测试，如需了解详细结果请联系负责咨询师！" : res.resultMsg));
                            return;
                        }
                    }
                    record = res[0].testRecord;
                    scale = res[0].testRecord.scale;
                    user = res[0].testRecord.user;
                    scaleName = scale.scaleName;
                    $(".scaleName").html(scaleName);
                    generalReport(res);
                    if (getUrlParam("savecharts") == 'true') {
                        saveCharts();
                    }
                }
            });
        };
        let generalReport = function (res) {
            getBaseInfo();
            getFactorInfo(res);
            $("#explain-nature").html(res[0].testRecord.interpretation.split('|')[0]);
            $("#epq-type").html(res[0].testRecord.interpretation.split('|')[2]);
            $("#explain-epq").html(res[0].testRecord.interpretation.split('|')[1]);
            getEPQChart(res);
        };
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName == "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structFullName);
            $("#startDate").html(record.startTime);
            $("#costTime").html(formatSeconds(record.timeInterval));
        };
        let getFactorInfo = function (res) {
            factors.splice(0);
            originalFactors.splice(0);
            scores.splice(0);
            standartScores.splice(0);
            $.each(res, function (index, content) {
                let factor = content.factor;
                factors.push(factor.factorName);
                originalFactors.push(content.originalScore);
                standartScores.push(content.abnormalValue);
                scores.push(content.score);
            });
        };
        let getEPQChart = function (res) {
            let chart = Highcharts.chart('epq-container', {
                chart: {
                    type: 'column'
                },
                title: {
                    text: "维度分析图",
                    x: 0
                },
                credits: {
                    enabled: false
                },
                xAxis: {
                    categories: factors,
                    tickmarkPlacement: 'on',
                    lineWidth: 0
                },
                yAxis: {
                    gridLineInterpolation: 'polygon',
                    lineWidth: 0,
                    min: 0
                },
                tooltip: {
                    shared: true,
                    pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.0f}</b><br/>'
                },
                legend: {
                    enabled: false,
                    align: 'center',
                    verticalAlign: 'top',
                    y: 70,
                    layout: 'vertical'
                },
                series: [{
                    name: factors,
                    data: scores,
                    pointPlacement: 'on'
                }]
            });

            let charData = $('#epq-container').highcharts().getSVG();
            canvg('epq-container', charData);
            let chartsImg = $('#epq-container')[0].toDataURL("image/png");
            chartsImgArray.push(chartsImg);
        };
        let download = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.post("/measuringroom/export/create_word_testreport", { "recordId": recordId }, function (res) {
                layer.closeAll();
                location.href = "/measuringroom/export/export_word_testreport?name=" + user.loginName + "_" + scaleName + "&guid=" + res;
            }, 'json');
        };
        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.ChartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };
    </script>
</th:block>
</body>
</html>
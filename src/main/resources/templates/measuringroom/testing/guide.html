<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理测试</a></li>
                    </ol>
                </div>
                <h4 class="page-title">心理测试</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="card bg-light">
        <div class="card-body">
            <div class="card-widgets">
                <a href="#" data-toggle="remove">&times;</a>
            </div>
            <h5 class="card-title mb-0"><i class="fa fa-info-circle mr-1"></i> 测评须知</h5>
            <div class="collapse pt-3 show">
                <ul>
                    <li class="mb-2">
                        测试前请将个人的基本信息进行完善，为了保护您的个人隐私，请修改登录密码。
                    </li>
                    <li class="mb-2">
                        如果测评过程中突发断网、断电、不小心关闭网页等情况，请稍后重新登录系统继续答题。
                    </li>
                    <li class="mb-2">
                        请以平常心态答题，答案没有对与错，不要有太多顾虑，反映自身真实情况即可。咨询老师会为您进行严格保密。
                    </li>
                    <li class="mb-2">
                        测试结果只是参考，不当做标准。
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-12 pl-0 pr-0 ml-0 mr-0">
        <div class="card col-12 pl-0 pr-0">
            <div class="card-header bg-primary text-white font-weight-bold font-14">
                <th:block th:text="${scale.scaleName}" /> <span class="pull-right font-14">限时：<b class="text-danger font-16" th:text="${scale.needTime}"></b> 分钟</span></div>
            <div class="card-body pb-4">
                <h5 class="card-title guide">指导语：</h5>
                <th:block th:utext="${scale.scaleGuide}" />
            </div>
            <div class="card-footer text-center">
                <div class="row mt-2">
                    <div class="col-sm-5 text-left">
                        <a href="javascript:history.go(-1)" class="btn text-muted btn-link font-weight-semibold">
                            <i class="fa fa-long-arrow-left mr-1"></i>返回
                        </a>
                    </div>
                    <div class="col-sm-7">
                        <div class="text-lg-left text-sm-right">
                            <div id="wrapper-start">
                                <button id="btnStart" class="btn btn-danger letter-spacing-2" type="button"><i class="fa fa-hand-o-right mr-1"></i>开始测试</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let taskId = getUrlParam('taskId');
        let type = getUrlParam('type');
        let recordId = getUrlParam("recordId");
        let scaleId = getUrlParam('scaleId');
        $(function () {
            if (type == 1 || type == 2) {
                if (recordId == 'undefined' || recordId === 0) {
                    getRecordId();
                }
            }
            $("#wrapper-start").on('click', '#btnStart', function () {
                startTesting();
            });
        });
        function getRecordId() {
            $.getJSON('/measuringroom/task/get_record?taskId=' + taskId + '&scaleId=' + scaleId + '', function (res) {
                recordId = res;
            });
        }
        function startTesting() {
            $("#wrapper-start").empty();
            $("#wrapper-start").html('<button id="btnStart" class="btn btn-danger" type="button" disabled><span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>请稍后...</button>');
            if (type == 1 || type == 2) {
                if (recordId != 'undefined' && recordId != '' && recordId !== 0) {
                    let jsonObj = { recordId: recordId };
                    let url = "/measuringroom/testing/update_starttime";
                    $.post(url, jsonObj,
                        function (res) {
                            $("#wrapper-start").html('<button id="btnStart" class="btn btn-danger" type="button"><i class="fa fa-hand-o-right mr-1"></i>开始测试</button>');
                            if (res.resultCode === 200) {
                                location.href = '/measuringroom/testing/do_test?recordId=' + recordId;
                            }
                            else {
                                layer.open({ content: '<img src="/static/images/wrong.png" width="30" class="mr-1"/>' + res.resultMsg, btn: '确定' });
                            }
                        }, "json"
                    );
                }
                else {
                    $("#wrapper-start").html('<button id="btnStart" class="btn btn-danger" type="button"><i class="fa fa-hand-o-right mr-1"></i>开始测试</button>');
                    layer.open({ content: '<img src="/static/images/wrong.png" width="30" class="mr-1"/>任务异常！', btn: '确定' });
                }
            }
            if (type == 3) {//自由测试
                let jsonObj = {};
                jsonObj.id = '[[${scale.id}]]';
                jsonObj.ageLimit = '[[${scale.ageLimit}]]';
                jsonObj.needTime = '[[${scale.needTime}]]';
                $.ajax({
                    type: 'POST',
                    url: "/measuringroom/testing/add_record",
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#wrapper-start").html('<button id="btnStart" class="btn btn-danger" type="button"><i class="fa fa-hand-o-right mr-1"></i>开始测试</button>');
                        if (res.resultCode === 200) {
                            location.href = '/measuringroom/testing/do_test?recordId=' + parseInt(res.resultMsg) + '';
                        }
                        else {
                            layer.open({ content: '<img src="/static/images/wrong.png" width="30" class="mr-1"/>系统错误！', btn: '确定' });
                        }
                    }
                });
            }
        };
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row d-print-none">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理测试</a></li>
                    </ol>
                </div>
                <h4 class="page-title">心理测试</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body ml-4 mr-4" id="report">
                    <div class="d-print-none">
                        <div class="dropdown card-widgets">
                            <a href="#" class="dropdown-toggle arrow-none" data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a href="javascript:window.print()" class="dropdown-item"><i class="fa fa-print mr-2"></i>打印</a>
                                <a href="javascript:download()" class="dropdown-item"><i class="fa fa-download mr-2"></i>下载报告[word]</a>
                            </div>
                        </div>
                    </div>
                    <div class="clearfix">
                        <div class="text-center mt-4 mb-4">
                            <h4 class="m-0 letter-spacing-2 font-weight-500">《<span class="scaleName"></span>》测评报告</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="mb-1">基本信息</h4>
                            <div class="row mt-2">
                                <div class="col-sm-4">
                                    <p class="mb-2"><strong class="mr-2">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</strong><span id="realName"></span></p>
                                    <p class="mb-2"><strong class="mr-2">测试日期：</strong><span id="startDate"></span></p>
                                </div>
                                <div class="col-sm-4">
                                    <p class="mb-2"><strong class="mr-2">所属组织：</strong><span id="fullStructName"></span></p>
                                    <p class="mb-2"><strong class="mr-2">耗&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;时：</strong> <span id="costTime"></span></p>
                                </div>
                                <div class="col-sm-4">
                                    <p class="mb-2"><strong class="mr-2">测试项目：</strong><span class="scaleName"></span></p>
                                    <p class="mb-2"></p>
                                </div>
                            </div>
                        </div> <!-- end col -->
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <!-- end row -->
                    <h4 class="mb-1">测评定性</h4>
                    <div class="card-body white-bg p-0" id="explain-nature">

                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <h4 class="mb-1">EPQ人格</h4>
                    <div class="col-12">
                        <div class="mb-2 epq-type"></div>
                        <canvas id="epq-container" style="height: 400px; min-width: 400px"></canvas>
                    </div>
                    <div class="card-body white-bg" id="explain-epq">

                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <h4 class="mb-1">心理健康风险</h4>
                    <div class="jk-12">
                        <div class="mb-2 jk-type"></div>
                        <canvas id="jk-container" style="height: 400px; min-width: 400px "></canvas>
                    </div>
                    <div class="card-body white-bg" id="explain-jk">

                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <h4 class="mb-1">消极应对</h4>
                    <div class="panel-body white-bg" id="explain-xj">

                    </div>
                </div>
            </div> <!-- end card -->
            <div class="alert alert-light m-2 hide" role="alert">

            </div>
        </div> <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let record, scale, user, scaleName;
        let factors = [], originalFactors = [], scores = [], standartScores = [],chartsImgArray = [];
        let natureExplain = [
            '你的气质类型为：安静稳定型，倾向内向性格，情绪稳定。安静稳定型的神经特点：感受性低;耐受性高;不随意反应低;外部表现少;情绪具有稳定性;反应速度慢但灵活。安静稳定型的心理特点：稳重，考虑问题全面；安静，沉默，善于克制自己；善于忍耐。情绪不易外露；注意力稳定而不容易转移，外部动作少而缓慢。安静稳定型的典型表现：这种人又称为安静型，在生活中是一个坚持而稳健的辛勤工作者。由于这些人具有与兴奋过程向均衡的强的抑制，所以行动缓慢而沉着，严格恪守既定的生活秩序和工作制度，不为无所谓的动因而分心。安静稳定型的人态度持重，交际适度，不作空泛的清谈，情感上不易激动，不易发脾气，也不易流露情感，能自治，也不常常显露自己的才能。这种人长时间坚持不懈，有条不紊地从事自己的工作。其不足是有些事情不够灵活，不善于转移自己的注意力。惰性使他因循守旧，表现出固定性有余，而灵活性不足。从容不迫和严肃认真的品德，以及性格的一贯性和确定性。',
            '你的气质类型为：冲动暴躁型，倾向外向性格，情绪不稳定。冲动暴躁型的神经特点：感受性低;耐受性高;不随意反应强;外倾性明显;情绪兴奋性高;抑制能力差;反应快但不灵活。冲动暴躁型的心理特点：坦率热情；精力旺盛，容易冲动；脾气暴躁；思维敏捷；但准确性差；情感外露，但持续时间不长。冲动暴躁型的典型表现：冲动暴躁型又称不可遏止型或战斗型。冲动暴躁型的气质特征是外向性、行动性和直觉性。具有强烈的兴奋过程和比较弱的抑郁过程，情绪易激动，反应迅速，行动敏捷，暴躁而有力；在语言上，表情上，姿态上都有一种强烈而迅速的情感表现。这种人的工作特点带有明显的周期性，埋头于事业，也准备去克服通向目标的重重困难和障碍，冲动暴躁型人一旦就业，往往对本职工作不那么专注，喜欢跳槽，经常更换工作单位，渴望成为自由职业者。这种气质反映在音乐风格中，多有慷慨激昂的激情，有崇高的英雄主义情绪，有突发的强音迸发出强烈的感情。这类型的人的不足是缺乏自制性、粗暴和急躁、易生气、易激动，因此在耐心、沉着和自制力等方面的心理修养不够。',
            '你的气质类型为：活泼愉悦型，倾向外向性格，情绪稳定。活泼愉悦型的神经特点：感受性低；耐受性高；不随意反应性强；可塑性高；情绪兴奋性高；反应速度快而灵活。活泼愉悦型的心理特点：活泼好动，善于交际；思维敏捷；容易接受新鲜事物；情绪情感容易产生也容易变化和消失，容易外露；情绪变化多样；体验不深刻等。活泼愉悦型的典型表现：活泼愉悦型又称活泼型，敏捷好动，善于交际，在新的环境里不感到拘束。在工作学习上富有精力而效率高，表现出机敏的工作能力，善于适应环境变化。在集体中精神愉快，朝气蓬勃，愿意从事合乎实际的事业，能对事业心向神往，能迅速地把握新事物，在有充分自制能力和纪律性的情况下，会表现出巨大的积极性。兴趣广泛，但情感易变，如果事业上不顺利，热情可能消失，其速度与投身事业一样迅速。从事多样化的工作往往成绩卓越。',
            '你的气质类型为：优柔敏感型，倾向内向性格，情绪不稳定。优柔敏感型的神经特点：感受性高，不随意反应低，对情感的体验深刻、有力、持久，而且具有高度的情绪易感性。优柔敏感型的心理特点：优柔敏感型的人一般表现为行为孤僻、不太合群、观察细致、非常敏感、表情忸怩、多愁善感、行动迟缓、优柔寡断，具有明显的内倾性。优柔敏感型的典型表现：对于以人际交往为主的职业，如外交官、政治家、商人等外向性职业，优柔敏感型人都没有适应性和兴趣。而在只需要一个人刻苦奋斗的学术、教育、研究、技术开发和医学等内在要求慎重、细致、周密思考的职业领域，优柔敏感型人就感到适合。但是，也不能说优柔敏感型人对所有的职业都不能适应。优柔敏感型人中的许多人，不是单凭聪明去处理事情，而是把自己所掌握的工作内容在头脑中组合、计算，确定方针，然后在这个范围内一个一个地去实行，把问题处理好。'];
        $(function () {
            initReport();
        });
        let initReport = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode != undefined) {
                        if (res.resultCode == 201) {
                            $("#report").hide();
                            $(".alert").removeClass("hide").addClass("show");
                            $(".alert").append('<img class="mr-1" src="/static/images/success.png" width="32" />' + (res.resultMsg == "" ? "您已经做完测试，如需了解详细结果请联系负责咨询师！" : res.resultMsg));
                            return;
                        }
                    }
                    record = res[0].testRecord;
                    scale = res[0].testRecord.scale;
                    user = res[0].testRecord.user;
                    scaleName = scale.scaleName;
                    $(".scaleName").html(scaleName);
                    generalReport(res);
                    if (getUrlParam("savecharts") == 'true') {
                        saveCharts();
                    }
                }
            });
        };
        let generalReport = function (res) {
            getBaseInfo();
            getFactorInfo(res);
            let e_score = res[1].score;
            let n_score = res[2].score;
            let t_score = res[11].score;
            if ((e_score >= 11 && n_score >= 9) || t_score >= 4.01) {
                $("#explain-nature").html("该人员不适合做无人驾驶安全员。");
            }
            else {
                $("#explain-nature").html("该人员适合做无人驾驶安全员。");
            }
            if (e_score < 11 && n_score < 9) {
                $(".epq-type").html(natureExplain[0]);
            }
            if (e_score >= 11 && n_score >= 9) {
                $(".epq-type").html(natureExplain[1]);
            }
            if (e_score >= 11 && n_score < 9) {
                $(".epq-type").html(natureExplain[2]);
            }
            if (e_score < 11 && n_score >= 9) {
                $(".epq-type").html(natureExplain[3]);
            }
            let tb_epq = res[0].testRecord.interpretation.split('|')[0];
            getEPQChart(res);
            $("#explain-epq").html(tb_epq);

            $(".jk-type").html(res[0].testRecord.interpretation.split('|')[2]);
            $("#explain-jk").html(res[0].testRecord.interpretation.split('|')[1]);
            getJKChart(res);
            $("#explain-xj").html(res[0].testRecord.interpretation.split('|')[3]);
        };
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName == "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structFullName);
            $("#startDate").html(record.startTime);
            $("#costTime").html(formatSeconds(record.timeInterval));
        };
        let getFactorInfo = function (res) {
            factors.splice(0);
            originalFactors.splice(0);
            scores.splice(0);
            standartScores.splice(0);
            $.each(res, function (index, content) {
                let factor = content.factor;
                factors.push(factor.factorName);
                originalFactors.push(content.originalScore);
                standartScores.push(content.abnormalValue);
                scores.push(content.score);
            });
        };
        let getEPQChart = function (res) {
            let epq_factors = factors.slice(0, 3);
            let epq_scores = scores.slice(0, 3);
            let chart = Highcharts.chart('epq-container', {
                chart: {
                    type: 'column'
                },
                title: {
                    text: "",
                    x: 0
                },
                credits: {
                    enabled: false
                },
                xAxis: {
                    categories: epq_factors,
                    tickmarkPlacement: 'on',
                    lineWidth: 0
                },
                yAxis: {
                    gridLineInterpolation: 'polygon',
                    lineWidth: 0,
                    min: 0
                },
                tooltip: {
                    shared: true,
                    pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.0f}</b><br/>'
                },
                legend: {
                    enabled: false,
                    align: 'center',
                    verticalAlign: 'top',
                    y: 70,
                    layout: 'vertical'
                },
                series: [{
                    name: epq_factors,
                    data: epq_scores,
                    pointPlacement: 'on'
                }]
            });

            let charData = $('#epq-container').highcharts().getSVG();
            canvg('epq-container', charData);
            let chartsImg = $('#epq-container')[0].toDataURL("image/png");
            chartsImgArray.push(chartsImg);
        };
        let getJKChart = function (res) {
            let jk_factors = factors.slice(4, 11);
            let jk_scores = scores.slice(4, 11);
            let chart = Highcharts.chart('jk-container', {
                chart: {
                    type: 'column'
                },
                title: {
                    text: "",
                    x: 0
                },
                credits: {
                    enabled: false
                },
                xAxis: {
                    categories: jk_factors,
                    tickmarkPlacement: 'on',
                    lineWidth: 0
                },
                yAxis: {
                    gridLineInterpolation: 'polygon',
                    lineWidth: 0,
                    min: 0
                },
                tooltip: {
                    shared: true,
                    pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.0f}</b><br/>'
                },
                legend: {
                    enabled: false,
                    align: 'center',
                    verticalAlign: 'top',
                    y: 70,
                    layout: 'vertical'
                },
                series: [{
                    name: jk_factors,
                    data: jk_scores,
                    pointPlacement: 'on'
                }]
            });

            let charData = $('#jk-container').highcharts().getSVG();
            canvg('jk-container', charData);
            let chartsImg = $('#jk-container')[0].toDataURL("image/png");
            chartsImgArray.push(chartsImg);
        };
        let download = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.post("/measuringroom/export/create_word_testreport", { "recordId": recordId }, function (res) {
                layer.closeAll();
                location.href = "/measuringroom/export/export_word_testreport?name=" + user.LoginName + "_" + scaleName + "&guid=" + res;
            }, 'json');
        };
        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.ChartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理测试</a></li>
                    </ol>
                </div>
                <h4 class="page-title">心理测试</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="col-12 pl-0 pr-0 ml-0 mr-0 test-body">
        <div class="card col-12 pl-0 pr-0">
            <div class="card-header bg-primary text-white font-14"><span id="scaleName" class="letter-spacing-2"></span><span class="pull-right font-14 font-weight-light time"></span></div>
            <div class="card-body pb-4">
                <div class="progress progress-sm  mb-2">
                    <div class="progress-bar bg-success" role="progressbar"></div>
                </div>
                <div class="question_title mb-2"></div>
                <ul class="list-unstyled question">
                </ul>

            </div>
            <div class="card-footer">
                <div id="wrapper-start">
                    <button id="submitQuestions" class="btn btn-danger btn-rounded btn-sm pull-right" type="button"><i class="fa fa-check mr-1"></i>提交</button>
                </div>
                <button id="preQuestion" class="btn btn-outline-info btn-rounded btn-sm pull-left mr-2" type="button">
                    <i class="fa fa-arrow-circle-left mr-1"></i>上一题
                </button>
                <button id="nextQuestion" class="btn btn-outline-info btn-rounded btn-sm pull-left" type="button">
                    下一题<i class="fa fa-arrow-circle-right ml-1"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="alert alert-danger hide" role="alert" id="alert">
        <span id="alert_msg" class="s14"><i class="fa fa-info-circle mr-1"></i></span>
    </div>
    <div class="row test-body">
        <div class="col-12">
            <div class="card">
                <div class="card-header font-14 hide" id="closeCard">
                    <span>收起答题卡<i class="fa fa-angle-up ml-2"></i></span>
                </div>
                <div class="card-header font-14" id="openCard">
                    <span>展开答题卡<i class="fa fa-angle-down ml-2"></i></span>
                </div>
                <div id="answerCard" class="pb-2" style="display:none;">
                    <div class="card-body form-horizontal">
                        <ul class="list-unstyled">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let scaleId = 0;
        let currentQuestion;
        let questions;
        let itemList = ["1", "2", "3", "4", "5", "6", "7", "8","9","10","11","12"]
        let itemList2 = ["A", "B", "C", "D", "E", "F", "G", "H","I","J","K","L"]
        let activeQuestion = 0; //当前操作的考题编号
        let questioned = 0; //
        let checkQues = []; //已做答的题的集合
        let intDiff = parseInt(60 * 30);//倒计时总秒数量
        let checkCache = {};
        checkCache.recordId = recordId;
        checkCache.checkItemsCache = checkQues;
        $(function () {
            init();
            /*答题卡的切换*/
            $("#openCard").click(function () {
                $("#closeCard").removeClass("hide").addClass("show");
                $("#answerCard").slideDown();
                $(this).removeClass("show").addClass("hide");
            });
            $("#closeCard").click(function () {
                $("#openCard").removeClass("hide").addClass("show");
                $("#answerCard").slideUp();
                $(this).removeClass("show").addClass("hide");
            });
            //上一题
            $("#preQuestion").click(function () {
                if (activeQuestion === 0) return;
                if ((activeQuestion + 1) != questions.length) showQuestion(activeQuestion - 1);
                showQuestion(activeQuestion);
            });
            //进入下一题
            $("#nextQuestion").click(function () {
                if ((activeQuestion + 1) != questions.length) showQuestion(activeQuestion + 1);
                showQuestion(activeQuestion);
            });
            //提交试卷
            $("#submitQuestions").click(function () {
                if(checkQues.length === questions.length){
                    //保存操作
                    save();
                }
                else if(checkQues.length < questions.length){
                    let left = questions.length - checkQues.length;
                    layer.alert("<i class='fa fa-info-o mr-1'></i>已做答：" + checkQues.length + " 道题，还有： " + left + " 道题。", {icon: 0});
                }
                else {
                    layer.alert('测试异常，请尝试重新作答。', { icon: 2, closeBtn: 0 }, function () {
                        location.reload();
                    });
                }
            })
        });
        let init = function () {
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            $.ajax({
                "type": "post",
                "url": "/measuringroom/testing/init",
                "dataType": "json",
                "async": false,
                "data": { recordId: recordId },
                "success": function (data) {
                    let res =JSON.parse(data);
                    let scale = res.scale;
                    $("#scaleName").html(scale.scaleName);
                    intDiff = parseInt(60 * scale.needTime);
                    timer(intDiff);
                    scaleId = scale.id;
                    questions = scale.listQuestions;
                    layer.closeAll();
                }
            });
            answerCard();
            if (window.localStorage.getItem('checkCache_' + recordId) != null) {
                let checkItems = JSON.parse(window.localStorage.getItem('checkCache_' + recordId));
                if (checkItems.recordId === recordId) {
                    layer.confirm('<img src ="/static/images/warning.png" width ="25" class="mr-1" /> 系统检测到您上次的作答未完成，是否继续？', {
                        btn: ['继续答题', '重新开始'] //可以无限个按钮
                        , btn2: function (index, layero) {
                            showQuestion(0);
                        }
                    }, function (index, layero) {
                        checkQues = checkItems.checkItemsCache;
                        checkQues.forEach(item=>{
                            $("#ques" + (item.qNo-1)).removeClass("question_id").addClass("active_question_id");
                            $("#ques" + (item.qNo-1)).removeClass("question_id").addClass("clickQue");
                        });
                        let maxQuestionId = Math.max.apply(Math, checkQues.map(function (i) { return i.id }));
                        showQuestion(maxQuestionId);
                        layer.closeAll();
                    });
                }
            }
            else {
                showQuestion(0);
            }
        };
        let timer = function (intDiff) {
            window.setInterval(function () {
                let day = 0, hour = 0, minute = 0, second = 0; //时间默认值
                if (intDiff > 0) {
                    day = Math.floor(intDiff / (60 * 60 * 24));
                    hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
                    minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
                    second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
                }
                if (minute <= 9) minute = '0' + minute;
                if (second <= 9) second = '0' + second;
                let timeTips = '<i class="fa fa-clock-o mr-1 font-15 font-weight-light"></i>' + day + '' + hour + '' + ':' + minute + '' + ':' + second + '';
                if (intDiff === 0) {
                    $(".test-body").hide();
                    $("#alert").removeClass("hide");
                    $("#alert_msg").append("抱歉，测试时间已到，请重新安排测试！");
                }
                intDiff--;
                $(".time").html(timeTips);
            }, 1000);
        };
        //题目信息
        let showQuestion = function (id) {
            $(".questioned").text(id + 1);
            questioned = (id + 1) / questions.length
            if (activeQuestion != undefined) {
                $("#ques" + activeQuestion).removeClass("question_id").addClass("active_question_id");
            }
            activeQuestion = id;
            $(".question").find(".title").remove();
            $(".question").find(".question_info").remove();
            $(".question").find(".question_info_text").remove();
            $(".question").find(".rating-scale-container").remove();
            $(".question").find(".forced-choice-container").remove();
            let question = questions[id];
            $(".question_title").html("第<span class='ml-1 mr-1 font-16 text-primary'>" + (id + 1) + " / "+ questions.length + "</span>题：" + question.qContent);
            //单选题
            if (question.qType === 1) {
                //父母养育方式问卷(EMBU)，此量表特殊处理。
                if (scaleId === [[${embu}]]) {
                    let items = question.listAnswers;
                    let item = "<span class='title'>父亲</span>";
                    for (let i = 0; i < items.length; i++) {
                        item += "<li class='question_info' onclick='clickTrim(this)' id='fa_item" + i + "'><input type='radio' name='fa_item' value='" + itemList[i] + "'>&nbsp;" + itemList2[i] + "、" + items[i].aContent + "</li>";

                    }
                    item += "<span class='title'>母亲</span>";
                    for (let i = 0; i < items.length; i++) {
                        item += "<li class='question_info' onclick='clickTrim(this)' id='mo_item" + i + "'><input type='radio' name='mo_item' value='" + itemList[i] + "'>&nbsp;" + itemList2[i] + "、" + items[i].aContent + "</li>";
                    }
                    $(".question").append(item);
                    $(".question").attr("id", "question" + id);
                    $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                    for (let i = 0; i < checkQues.length; i++) {
                        if (checkQues[i].id == id) {
                            $("#" + checkQues[i].fa_item).find("input").prop("checked", "checked");
                            $("#" + checkQues[i].mo_item).find("input").prop("checked", "checked");
                            $("#" + checkQues[i].fa_item).addClass("clickTrim");
                            $("#" + checkQues[i].mo_item).addClass("clickTrim");
                            $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                        }
                    }
                }
                else {
                    let items = question.listAnswers;
                    let item = "";
                    for (let i = 0; i < items.length; i++) {
                        item = "<li class='question_info mb-2' onclick='clickTrim(this)' id='item" + i + "'><input type='radio' name='item' value='" + itemList[i] + "'>&nbsp;" + itemList2[i] + "、 " + items[i].aContent+ "</li>";
                        $(".question").append(item);
                    }
                    $(".question").attr("id", "question" + id);
                    $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                    for (let i = 0; i < checkQues.length; i++) {
                        if (checkQues[i].id === id) {
                            $("#" + checkQues[i].item).find("input").prop("checked", "checked");
                            $("#" + checkQues[i].item).addClass("clickTrim");
                            $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                        }
                    }
                }
            }
            //多选题
            if (question.qType === 2) {
                let items = question.listAnswers;
                let item = "";
                for (let i = 0; i < items.length; i++) {
                    item = "<li class='question_info mb-2' onclick='clickTrim(this)' id='item_muti" + i + "'><input type='checkbox' name='item' value='" + itemList[i] + "'>&nbsp;" + itemList2[i] + "、 " + items[i].aContent + "</li>";
                    $(".question").append(item);
                }
                $(".question").attr("id", "question" + id);
                $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                for (let i = 0; i < checkQues.length; i++) {
                    if (checkQues[i].id == id) {
                        let itemNum = checkQues[i].item.split(',');
                        for (let i = 0; i < itemNum.length; i++) {
                            $("#" + itemNum[i]).find("input").prop("checked", "checked");
                            $("#" + itemNum[i]).addClass("clickTrim");
                        }
                        $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    }
                }
            }
            //填空题
            if (question.qType === 3) {
                $(".question").append("<li class='question_info_text' id='item1'><textarea class='form-control' name='completion' id='completion" + id + "'  rows='3' onblur='clickTrim(this)' maxLength='100'></textarea></li>");
                $(".question").attr("id", "question" + id);
                $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                for (let i = 0; i < checkQues.length; i++) {
                    if (checkQues[i].id === id) {
                        $("#" + checkQues[i].item).val(checkQues[i].answer);
                        $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    }
                }
            }
            //评分单选题
            if (question.qType === 4) {
                let items = question.listAnswers;
                let item = "<div class='rating-scale-container'>";
                item += "<div class='rating-scale-labels'>";
                if (items.length > 0) {
                    item += "<span class='rating-label-left'>" + items[0].aContent + "</span>";
                    item += "<span class='rating-label-right'>" + items[items.length - 1].aContent + "</span>";
                }
                item += "</div>";
                item += "<div class='rating-scale-line'></div>";
                item += "<div class='rating-scale-options'>";
                for (let i = 0; i < items.length; i++) {
                    item += "<div class='rating-option' onclick='clickTrim(this)' id='item" + i + "'>";
                    item += "<input type='radio' name='item' value='" + itemList[i] + "'>";
                    item += "<span class='rating-circle'></span>";
                    item += "</div>";
                }
                item += "</div>";
                item += "</div>";
                $(".question").append(item);
                $(".question").attr("id", "question" + id);
                $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                for (let i = 0; i < checkQues.length; i++) {
                    if (checkQues[i].id === id) {
                        $("#" + checkQues[i].item).find("input").prop("checked", "checked");
                        $("#" + checkQues[i].item).addClass("clickTrim");
                        $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    }
                }
            }
            //迫选题
            if (question.qType === 5) {
                let items = question.listAnswers;
                let item = "<div class='forced-choice-container'>";
                item += "<div class='forced-choice-tip'>";
                item += "<i class='fas fa-exclamation-triangle'></i>";
                item += "<span>迫选题：请从以下选项中必须选择一项，不可跳过</span>";
                item += "</div>";
                for (let i = 0; i < items.length; i++) {
                    item += "<div class='forced-choice-option' onclick='clickTrim(this)' id='item" + i + "'>";
                    item += "<input type='radio' name='item' value='" + itemList[i] + "'>";
                    item += "<div class='forced-choice-text'>" + itemList2[i] + "、 " + items[i].aContent + "</div>";
                    item += "</div>";
                }
                item += "</div>";
                $(".question").append(item);
                $(".question").attr("id", "question" + id);
                $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                for (let i = 0; i < checkQues.length; i++) {
                    if (checkQues[i].id === id) {
                        $("#" + checkQues[i].item).find("input").prop("checked", "checked");
                        $("#" + checkQues[i].item).addClass("clickTrim");
                        $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    }
                }
            }
            progress(questions);
            //mbti标准版
            if (scaleId == [[${mbtis}]]) {
                $(".question_info").css('display', 'inline');
                $(".question_info").css('padding', '5px 10px 5px 5px');
                $(".question_info").css('margin-right', '10px');
                $(".question_title p").css('display','block')
            }
        };
        //进度条
        let progress = function () {
            let prog = (checkQues.length) / questions.length;
            $(".progress-bar").width((prog * 100).toString().substr(0, 4) + "%");
        };
        //选中
        let lastClickTime = 0;
        const minInterval = 1000;
        let clickTrim = function(source) {
            let questionType;
            const currentTime = new Date().getTime();
            if (currentTime - lastClickTime >= minInterval){
                // 更新最后点击时间
                lastClickTime = currentTime;
                let id = source.id;
                if (id.indexOf("completion") !== -1) {//填空题
                    questionType = 3;
                    $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    let ques = 0;
                    for (let i = 0; i < checkQues.length; i++) {
                        if (checkQues[i].id === activeQuestion && checkQues[i].item !== id) {
                            ques = checkQues[i].id;
                            checkQues[i].item = id; //获取当前考题的选项ID
                            checkQues[i].aNo = $("#" + id).find("textarea").val(); //获取当前考题的选项值
                        }
                    }
                    if (checkQues.length === 0 || currentQuestion !== activeQuestion && activeQuestion !== ques) {
                        let check = {};
                        check.id = activeQuestion; //获取当前考题的编号
                        check.item = id; //获取当前考题的选项ID
                        check.qNo = activeQuestion + 1;
                        check.aNo = $("#" + id).val(); //获取当前考题的选项值
                        check.qType = 3;
                        check.recordId = recordId;
                        checkQues.push(check);
                    }
                }
                else if (id.indexOf("item_muti") !== -1) { //多选题
                    questionType = 2;
                        $("#" + id).addClass("clickTrim");
                    $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    let ques = 0;
                    for (let i = 0; i < checkQues.length; i++) {
                        if (checkQues[i].id === activeQuestion && checkQues[i].item !== id) {
                            ques = checkQues[i].id;
                            let array1 = [];
                            let array2 = [];
                            $("#question" + activeQuestion).find("input[name=item]:checked").each(function () {
                                array1.push($(this).val());
                                array2.push($(this).parent().attr('id'));
                            });
                            checkQues[i].item = array2.join(','); //获取当前考题的选项ID
                            checkQues[i].aNo = array1.join(',');
                        }
                    }
                    if (checkQues.length === 0 || currentQuestion !== activeQuestion && activeQuestion !== ques) {
                        let check = {};
                        check.id = activeQuestion; //获取当前考题的编号
                        let array1 = [];
                        let array2 = [];
                        $("#question" + activeQuestion).find("input[name=item]:checked").each(function () {
                            array1.push($(this).val());
                            array2.push($(this).parent().attr('id'));
                        });
                        let ids = array1.join(',');
                        check.item = array2.join(','); //获取当前考题的选项ID
                        check.qNo = activeQuestion + 1;
                        check.aNo = ids;
                        check.qType = 2;
                        check.recordId = recordId;
                        checkQues.push(check);
                    }
                }
                else {
                    questionType = 1;
                    $("#" + id).find("input").prop("checked", "checked");
                    $("#" + id).addClass("clickTrim");
                    $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    let ques = 0;
                    if (scaleId === [[${embu}]]) { //父母养育方式问卷(EMBU)，此量表特殊处理。
                        if (id.indexOf('fa_item') !== -1) {
                            for (let i = 0; i < checkQues.length; i++) {
                                if (checkQues[i].id === activeQuestion && checkQues[i].fa_item !== id) {
                                    ques = checkQues[i].id;
                                    checkQues[i].fa_item = id; //获取当前考题的选项ID
                                    checkQues[i].qType = 1;
                                    checkQues[i].fa_answer = $("#" + id).find("input[name=fa_item]:checked").val(); //获取当前考题的选项值
                                }
                            }
                            if (checkQues.length === 0 || currentQuestion !== activeQuestion && activeQuestion !== ques) {
                                let check = {};
                                check.id = activeQuestion; //获取当前考题的编号
                                check.fa_item = id; //获取当前考题的选项ID
                                check.fa_answer = $("#" + id).find("input[name=fa_item]:checked").val();//获取当前考题的选项值
                                check.qType = 1;
                                checkQues.push(check);
                            }
                        }
                        if (id.indexOf('mo_item') !== -1) {
                            for (let i = 0; i < checkQues.length; i++) {
                                if (checkQues[i].id === activeQuestion && checkQues[i].mo_item !== id) {
                                    ques = checkQues[i].id;
                                    checkQues[i].mo_item = id; //获取当前考题的选项ID
                                    checkQues[i].qType = 1;
                                    checkQues[i].mo_answer = $("#" + id).find("input[name=mo_item]:checked").val(); //获取当前考题的选项值
                                }
                            }
                            if (checkQues.length === 0 || currentQuestion !== activeQuestion && activeQuestion !== ques) {
                                let check = {};
                                check.id = activeQuestion; //获取当前考题的编号
                                check.mo_item = id; //获取当前考题的选项ID
                                check.mo_answer = $("#" + id).find("input[name=mo_item]:checked").val();//获取当前考题的选项值
                                check.qType = 1;
                                checkQues.push(check);
                            }
                        }
                    }
                    else {
                        for (let i = 0; i < checkQues.length; i++) {
                            if (checkQues[i].id === activeQuestion && checkQues[i].item !== id) {
                                ques = checkQues[i].id;
                                checkQues[i].item = id; //获取当前考题的选项ID
                                checkQues[i].aNo = $("#" + id).find("input[name=item]:checked").val(); //获取当前考题的选项值
                            }
                        }
                        if (checkQues.length === 0 || currentQuestion !== activeQuestion && activeQuestion !== ques) {
                            let check = {};
                            check.id = activeQuestion; //获取当前考题的编号
                            check.item = id; //获取当前考题的选项ID
                            check.qNo = activeQuestion + 1;
                            check.aNo = $("#" + id).find("input[name=item]:checked").val(); //获取当前考题的选项值
                            check.qType = 1;
                            check.recordId = recordId;
                            checkQues.push(check);
                        }
                        $(".question_info").each(function () {
                            let otherId = $(this).attr("id");
                            if (otherId !== id) {
                                let otherWrapper = $("#" + otherId);
                                otherWrapper.find("input").prop("checked", false);
                                otherWrapper.removeClass("clickTrim");
                            }
                        })
                    }
                }
                currentQuestion = activeQuestion;
                progress();
                if (scaleId !== [[${embu}]] && scaleId !== 10000161 && questionType !== 2 && questionType !== 3) {
                    $("#nextQuestion").click();
                }
                if (window.localStorage) {
                    window.localStorage.setItem('checkCache_' + recordId, JSON.stringify(checkCache));
                }
            }
            else{
                layer.msg("为了确保回答的准确性，请稍作停顿再进行点击！", { icon: 0, time: 1000 });
            }
        };

        /*答题卡*/
        let answerCard = function () {
            $("#answerCard ul").html("");
            $(".question_sum").text(questions.length);
            for (let i = 0; i < questions.length; i++) {
                let questionId = "<li id='ques" + i + "'onclick='saveQuestionState(" + i + ")' class='questionId'>" + (i + 1) + "</li>";
                $("#answerCard ul").append(questionId);
            }
        };
        /*保存考题状态 已做答的状态*/
        let saveQuestionState = function(clickId) {
            showQuestion(clickId);
        };
        let save = function () {
            let url = scaleId === [[${embu}]] ? "/measuringroom/testing/save_result_embu" : "/measuringroom/testing/save_result";
            let jsonObj = {};
            jsonObj.scaleId = scaleId;
            jsonObj.recordId = recordId;
            jsonObj.listResults = checkQues;
            jsonObj.listEmbuResults =checkQues;
            $("#submitQuestions").attr("Disabled", true);
            layer.msg('报告生成中…', {
                icon: 17, shade: 0.2, time: false
            });
            $.ajax({
                type: 'POST',
                url: url,
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    $("#submitQuestions").attr("Disabled", false);
                    if (res.resultCode === 200) {
                        window.localStorage.removeItem('checkCache_' + recordId);
                        if (scaleId == [[${aqy}]]) {
                            location.href = '/measuringroom/testing/report_aqy?recordId=' + recordId + '&savecharts=true';
                        }
                        else if (scaleId == [[${yys}]]) {
                            location.href = '/measuringroom/testing/report_yys?recordId=' + recordId + '&savecharts=true';
                        }
                        else {
                            location.href = '/measuringroom/testing/report?recordId=' + recordId + '&savecharts=true';
                        }
                    }
                    else {
                        layer.msg(res.resultMsg, {icon: 2, time: 2000});
                    }
                }
            });
        }
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/swiper/swiper-3.2.7.min.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/swiper/mySwiper.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">首页</a></li>
                    </ol>
                </div>
                <h4 class="page-title">测量室</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">推荐测试<span class="float-right"><i class="fa fa-info-circle mr-1"></i>tips：点击左右箭头或者拖动类别可滑动查看更多。</span></div>
                <div class="card-body">
                    <div class="swiper-container swiper1">
                        <div class="swiper-wrapper">
                            <th:block th:each="scaleType:${scaleTypes}">
                                <th:block th:if="${scaleTypeStat.index == 0}">
                                    <div class="swiper-slide selected" th:id="${scaleType.id}"  th:text="${scaleType.scaleTypeName}"></div>
                                </th:block>
                                <th:block th:unless="${scaleTypeStat.index == 0}">
                                    <div class="swiper-slide" th:id="${scaleType.id}"  th:text="${scaleType.scaleTypeName}"></div>
                                </th:block>
                            </th:block>
                        </div>
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-button-next"></div>
                    </div>
                    <div class="swiper-container swiper2">
                        <div class="swiper-wrapper" id="scaleList">

                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript" th:src="@{/static/js/plugins/swiper/swiper-3.4.0.jquery.min.js}"></script>
    <script type="text/javascript">
        let swiper1 = new Swiper('.swiper1', {
            //	设置slider容器能够同时显示的slides数量(carousel模式)。
            //	可以设置为number或者 'auto'则自动根据slides的宽度来设定数量。
            //	loop模式下如果设置为'auto'还需要设置另外一个参数loopedSlides。
            slidesPerView: 8,
            paginationClickable: false,//此参数设置为true时，点击分页器的指示点分页器会控制Swiper切换。
            spaceBetween: 10,//slide之间的距离（单位px）。
            freeMode: true,//默认为false，普通模式：slide滑动时只滑动一格，并自动贴合wrapper，设置为true则变为free模式，slide会根据惯性滑动且不会贴合。
            loop: false,//是否可循环
            pagination: '.swiper-pagination',
            paginationType: 'custom',
            prevButton: '.swiper-button-prev',
            nextButton: '.swiper-button-next',
            onTab: function (swiper) {
                let n = swiper1.clickedIndex;
            }
        });

        let swiper2 = new Swiper('.swiper2', {
            //freeModeSticky  设置为true 滑动会自动贴合
            direction: 'horizontal',//Slides的滑动方向，可设置水平(horizontal)或垂直(vertical)。
            loop: false,
            autoHeight: true,//自动高度。设置为true时，wrapper和container会随着当前slide的高度而发生变化。
            onSlideChangeEnd: function (swiper) {  //回调函数，swiper从一个slide过渡到另一个slide结束时执行。
                let n = swiper.activeIndex;
                setCurrentSlide($(".swiper1 .swiper-slide").eq(n), n);
                swiper1.slideTo(n, 500, false);
            }
        });
        let getList = function (type, index) {
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            let jsonObj = {};
            jsonObj.scaleTypeId = type;
            $.post('/measuringroom/scale/get_list_by_type', jsonObj, function (res) {
                let wrapper = $("#scaleList");
                let list_str = '<div class="row swiper-slide swiper-no-swiping">';
                for (let i = 0; i < res.length; i++) {
                    let scale = res[i];
                    list_str += '<div class="col-3"><div class="card d-block"><a href="/measuringroom/testing/guide?scaleId=' + scale.id + '&type=3">';
                    let thumbnail = scale.thumbnail === "" ? "/static/images/nopic.png" : "/static/upload/scale/thumbnail/" + scale.thumbnail;
                    list_str += '<img class="card-img-top" src="' + thumbnail + '" alt="' + scale.scaleName + '">';
                    list_str += '<div class="card-body"><h5 class="card-title text-dark font-weight-normal">' + scale.scaleName + '</h5><button class="btn btn-outline-primary btn-sm">前往测试</button>';
                    list_str += '</div>';
                    list_str += '</a></div></div>';
                };
                list_str += '</div>';
                wrapper.empty();
                wrapper.append(list_str);
                layer.closeAll();
                swiper2.slideTo(index, 500, false);
            });
        }
        let setCurrentSlide = function (ele, index) {
            $(".swiper1 .swiper-slide").removeClass("selected");
            ele.addClass("selected");
            getList(ele[0].id, index);
        }
        $(function () {
            getList(swiper1.slides[0].id, 0);
            swiper1.slides.each(function (index, val) {
                let ele = $(this);
                ele.on("click", function () {
                    setCurrentSlide(ele, index);
                    swiper2.slideTo(index, 500, false);
                });
            });
        });
    </script>
</th:block>
</body>
</html>
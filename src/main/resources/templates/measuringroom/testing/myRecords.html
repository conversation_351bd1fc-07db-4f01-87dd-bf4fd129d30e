<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">个人测评管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">我的测评记录</a></li>
                    </ol>
                </div>
                <h4 class="page-title">我的测评记录</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-12">
                            <form class="form-inline" action="">
                                <div class="form-group mr-2">
                                    <i class="fa fa-search mr-1"></i>搜索：
                                </div>
                                <div class="form-group mr-2">
                                    <label for="sr-scaleName" class="sr-only">量表名称：</label>
                                    <input type="text" class="form-control" id="sr-scaleName" name="sr-scaleName" placeholder="量表名称" autocomplete="off">
                                </div>
                                <div class="form-group mr-2">
                                    <label class="sr-only">测评日期：</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                        </div>
                                        <input type="text" class="form-control" value="" id="recordDate" name="recordDate" placeholder="选择测评日期">
                                    </div>
                                    <input type="hidden" id="hidStartTime" value="" />
                                    <input type="hidden" id="hidEndTime" value="" />
                                </div>
                                <div class="form-group mr-2">
                                    <label for="sr-state" class="sr-only">测评状态：</label>
                                    <select id="sr-state" class="form-control select2">
                                        <option value="">选择测评状态</option>
                                        <option value="0">未完成</option>
                                        <option value="1">已完成</option>
                                        <option value="2">测谎未通过</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <button type="button" class="btn btn-primary mr-1" id="btnSearch"><i class="fa fa-search mr-1"></i>查询</button>
                                    <input type="reset" class="btn btn-light mr-1" id="btnReset" />
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="tbRecordList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th align="left">量表</th>
                                <th align="left">开始时间</th>
                                <th align="left">结束时间</th>
                                <th align="left">状态</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script type="text/javascript">
        $(function () {
            initForm();
            $('#recordDate').val("");
            //datatables
            $("#tbRecordList").bsDataTables({
                columns: columns,
                url: '/measuringroom/testing/get_my_records',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            $("#btnSearch").click(function () {
                oTable.draw();
            });
        });
        let initForm = function () {
            //初始化日期范围选择控件
            let locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " - ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            $('#recordDate').daterangepicker({
                "locale": locale,
                "drops": "down"
            }, function (start, end, label) {
                let startTime, endTime;
                if ((start - end) == 0) {
                    let _end = new Date(end);
                    let year = _end.getFullYear();
                    let month = _end.getMonth();
                    let day = _end.getDate();
                    end = new Date(year, month, day);
                    startTime = start.format('YYYY-MM-DD');
                    endTime = end.format('YYYY-MM-dd');
                }
                else {
                    startTime = start.format('YYYY-MM-DD');
                    endTime = end.format('YYYY-MM-DD');
                }
                $('#recordDate').val(startTime + ' - ' + endTime);
                $("#hidStartTime").val(startTime + ' 00:00:00');
                $("#hidEndTime").val(endTime + ' 23:59:59');
            });
        };
        let columns = [
            { "data": "scaleName", "bSortable": false },
            { "data": "startTime", "bSortable": false},
            {"data": "endTime", "bSortable": false},
            {"data": "state", "bSortable": false,
                render: function (data, type, row, meta) {
                    var lbl="";
                    switch (data) {
                        case 0:
                            lbl += '<span class="badge badge-secondary-lighten badge-pill">未完成</span>';
                            break;
                        case 1:
                            if (row.scaleId === 10000163) {
                                lbl +=  '<span class="badge badge-info badge-pill mr-2">完成</span><span class="badge badge-info badge-pill cursor-pointer mr-2"><a class="text-white" href="/measuringroom/testing/report_aqy?recordId=' + row.id + '" target="_blank">测评报告</a></span>';
                            }
                            else if (row.scaleId === 10000167) {
                                lbl +=  '<span class="badge badge-info badge-pill mr-2">完成</span><span class="badge badge-info badge-pill cursor-pointer mr-2"><a class="text-white" href="/measuringroom/testing/report_yys?recordId=' + row.id + '" target="_blank">测评报告</a></span>';
                            }
                            else {
                                lbl +=  '<span class="badge badge-info badge-pill mr-2">完成</span><span class="badge badge-info badge-pill cursor-pointer mr-2"><a class="text-white" href="/measuringroom/testing/report?recordId=' + row.id + '" target="_blank">测评报告</a></span>';
                            }
                            break;
                        case 2:
                            lbl +=  '<span class="badge badge-danger badge-pill mr-1">测谎未通过</span>';
                            break;
                    }
                    return lbl;
                }
            }];
        let columnDefs = [];
        let getQueryCondition = function (data) {
            let param = {};
            param.scaleName = $("#sr-scaleName").val();
            if ($("#hidStartTime").val() !=''){
                param.startTime = $("#hidStartTime").val();
            }
            if($("#hidEndTime").val() != ''){
                param.endTime = $("#hidEndTime").val();
            }
            if($("#sr-state").val() !=''){
                param.state = $("#sr-state").val();
            }
            param.userId = '[[${userId}]]';
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
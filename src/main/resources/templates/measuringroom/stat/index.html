<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
    <style>
        /* 自定义样式，与数据看板保持一致 */
        .card {
            transition: all 0.3s ease;
        }

        .bg-opacity-20 {
            background-color: rgba(255,255,255,0.2) !important;
        }

        .text-white-50 {
            color: rgba(255,255,255,0.7) !important;
        }



        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .badge {
            font-size: 0.75em;
            padding: 0.375em 0.75em;
        }

        .warning-badge-orange {
            background-color: #fd7e14;
            color: white;
        }

        .warning-badge-blue {
            background-color: #2d5ef2;
            color: white;
        }

        /* 统计卡片数字样式 */
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            margin-bottom: 0;
        }

        /* 报告区域动画效果 */
        #report {
            transition: opacity 0.5s ease-in-out;
        }



        @media print {
            .d-print-none {
                display: none !important;
            }
            
            .print-header {
                display: block !important;
                text-align: center;
                margin-bottom: 2rem;
                padding-bottom: 1rem;
                border-bottom: 2px solid #dee2e6;
            }
            
            .card {
                box-shadow: none !important;
                border: 1px solid #dee2e6 !important;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测评结果管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">团体测评报告</a></li>
                    </ol>
                </div>
                <h4 class="page-title"><i class="fa fa-users mr-2 text-primary"></i>团体测评报告</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-xl-3 col-lg-4 col-md-6 mb-3">
                                <label class="form-label">所属组织：</label>
                                <div class="position-relative">
                                    <input type="text" id="structName" name="structName" readonly value="" 
                                           class="form-control" placeholder="请选择组织" />
                                    <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                        <input type="text" class="form-control bg-light" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                        <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                    </div>
                                    <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 mb-3">
                                <label for="sr-task" class="form-label">测评任务：</label>
                                <select id="sr-task" class="form-control select2" data-toggle="select2">
                                </select>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 mb-3">
                                <label for="sr-scaleName" class="form-label">测评量表：</label>
                                <select id="sr-scaleName" class="form-control select2" data-toggle="select2">
                                </select>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 mb-3">
                                <label for="recordDate" class="form-label">时间范围：</label>
                                <input type="text" class="form-control" value="" id="recordDate" name="recordDate" 
                                       placeholder="选择时间范围">
                                <input type="hidden" id="hidStartTime" value="" />
                                <input type="hidden" id="hidEndTime" value="" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="button" class="btn btn-primary mr-2" id="btnGenerate">
                                    <i class="fa fa-search mr-1"></i>生成报告
                                </button>
                                <button type="reset" class="btn btn-light" id="btnReset">
                                    <i class="fa fa-refresh mr-1"></i>重置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- 报告区域 -->
    <div id="report" style="display: none;">
        <!-- 报告标题 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center">
                    <h3 class="mb-1">《<span class="scaleName"></span>》团体测评报告</h3>
                </div>
            </div>
        </div>
        <!-- 1. 测评工具介绍 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><i class="fa fa-info-circle mr-2"></i>测评工具介绍</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <tbody>
                                <tr>
                                    <td class="font-weight-bold" style="width: 120px;">量表名称</td>
                                    <td class="scaleName"></td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">量表介绍</td>
                                    <td class="scaleIntro"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 2. 统计卡片 -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="mb-1 text-white stat-number totalCount">0</h2>
                                <p class="mb-0 stat-label font-weight-bold">总测评人数</p>
                            </div>
                            <div class="icon-container bg-opacity-20 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fa fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="mb-1 text-white stat-number completedCount">0</h2>
                                <p class="mb-0 stat-label font-weight-bold">完成人数</p>
                            </div>
                            <div class="icon-container bg-opacity-20 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fa fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="mb-1 text-white stat-number uncompletedCount">0</h2>
                                <p class="mb-0 stat-label font-weight-bold">未完成人数</p>
                            </div>
                            <div class="icon-container bg-opacity-20 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fa fa-clock-o fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="mb-1 text-white stat-number completionRate">0%</h2>
                                <p class="mb-0 stat-label font-weight-bold">完成率</p>
                            </div>
                            <div class="icon-container bg-opacity-20 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fa fa-percent fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 3. 完成情况分析 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0"><i class="fa fa-bar-chart mr-2"></i>测评完成情况分析</h5>
                        <a href="javascript:void(0)" id="btnViewRecordList" class="text-white">
                            <i class="fa fa-list mr-1"></i>查看清单
                        </a>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-4">
                            📊 本次测评总人数<strong><span class="totalCount text-primary"></span></strong>人，其中完成<strong><span class="completedCount text-success"></span></strong>人，未完成<strong><span class="uncompletedCount text-warning"></span></strong>人。
                        </p>

                        <div class="row">
                            <!-- 完成情况统计图表 -->
                            <div class="col-12">
                                <h6 class="text-center mb-3">按部门完成情况</h6>
                                <div id="departmentChart" style="height:300px;"></div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-center mb-3">按性别完成情况</h6>
                                <div id="genderChart" style="height:300px;"></div>
                            </div>
                            <div class="col-6">
                                <h6 class="text-center mb-3">按年龄段完成情况</h6>
                                <div id="ageChart" style="height:300px;"></div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <!-- 完成情况统计表格 -->
                            <div class="col-12">
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm">
                                        <thead class="thead-light">
                                        <tr>
                                            <th class="text-center">部门</th>
                                            <th class="text-center">总人数</th>
                                            <th class="text-center">完成人数</th>
                                            <th class="text-center">完成率</th>
                                        </tr>
                                        </thead>
                                        <tbody id="departmentStatsTable">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm">
                                        <thead class="thead-light">
                                        <tr>
                                            <th class="text-center">性别</th>
                                            <th class="text-center">总人数</th>
                                            <th class="text-center">完成人数</th>
                                            <th class="text-center">完成率</th>
                                        </tr>
                                        </thead>
                                        <tbody id="genderStatsTable">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm">
                                        <thead class="thead-light">
                                        <tr>
                                            <th class="text-center">年龄段</th>
                                            <th class="text-center">总人数</th>
                                            <th class="text-center">完成人数</th>
                                            <th class="text-center">完成率</th>
                                        </tr>
                                        </thead>
                                        <tbody id="ageStatsTable">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 4. 测评预警分析 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0"><i class="fa fa-line-chart mr-2"></i>测评预警分析</h5>
                        <a href="javascript:void(0)" id="btnViewFilterList" class="text-white">
                            <i class="fa fa-list mr-1"></i>查看清单
                        </a>
                    </div>
                    <div class="card-body">
                        <!-- 预警等级分布图表 -->
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-center mb-3">按组织预警分布</h6>
                                <div id="orgWarningChart" style="height:300px;"></div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-center mb-3">按性别预警分布</h6>
                                <div id="genderWarningChart" style="height:300px;"></div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-center mb-3">按年龄段预警分布</h6>
                                <div id="ageWarningChart" style="height:300px;"></div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-center mb-3">按因子预警分布</h6>
                                <div id="factorWarningChart" style="height:300px;"></div>
                            </div>
                        </div>

                        <!-- 预警等级统计表格 -->
                        <div class="mt-4">
                            <h6 class="mb-3">
                                <i class="fa fa-exclamation-triangle mr-2 text-warning"></i>组织预警等级统计
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-hover table-sm">
                                    <thead class="thead-light">
                                    <tr>
                                        <th class="text-center">组织名称</th>
                                        <th class="text-center">总数</th>
                                        <th class="text-center">绿码</th>
                                        <th class="text-center">蓝码</th>
                                        <th class="text-center">黄码</th>
                                        <th class="text-center">橙码</th>
                                        <th class="text-center">红码</th>
                                        <th class="text-center">绿码占比</th>
                                        <th class="text-center">红码橙码占比</th>
                                    </tr>
                                    </thead>
                                    <tbody id="orgWarningTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 重点关注统计 -->
                        <div class="mt-4">
                            <h6 class="mb-3">
                                <i class="fa fa-eye mr-2 text-danger"></i>重点关注（红码橙码）统计
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-hover table-sm">
                                    <thead class="thead-light">
                                    <tr>
                                        <th class="text-center">组织名称</th>
                                        <th class="text-center">总数</th>
                                        <th class="text-center">红码数</th>
                                        <th class="text-center">橙码数</th>
                                        <th class="text-center">红码橙码合计</th>
                                        <th class="text-center">红码占比</th>
                                        <th class="text-center">橙码占比</th>
                                        <th class="text-center">重点关注占比</th>
                                    </tr>
                                    </thead>
                                    <tbody id="criticalWarningTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 因子预警等级统计 -->
                        <div class="mt-4">
                            <h6 class="mb-3">
                                <i class="fa fa-pie-chart mr-2 text-info"></i>因子预警等级统计
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-hover table-sm">
                                    <thead class="thead-light">
                                    <tr>
                                        <th class="text-center">因子名称</th>
                                        <th class="text-center">总数</th>
                                        <th class="text-center">绿码</th>
                                        <th class="text-center">蓝码</th>
                                        <th class="text-center">黄码</th>
                                        <th class="text-center">橙码</th>
                                        <th class="text-center">红码</th>
                                        <th class="text-center">绿码占比</th>
                                        <th class="text-center">红码橙码占比</th>
                                    </tr>
                                    </thead>
                                    <tbody id="factorWarningTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script type="text/javascript">
        let initForm = function () {
            // 初始化测评任务列表
            let jsonObj = { "taskKind": 1 };
            initSelectJson("#sr-task", "/measuringroom/task/get_for_select", jsonObj);
            
            // 初始化量表列表
            initSelect("#sr-scaleName", "/measuringroom/scale/get_for_select", "", "", "选择量表");
            
            // 测评任务变化时更新量表列表
            $("#sr-task").change(function () {
                let jsonObj = { "taskId": $("#sr-task").val() };
                initSelectAndSelectedFirst("#sr-scaleName", "/measuringroom/scale/get_for_select_by_taskid", jsonObj);
            });

            // 初始化日期范围选择控件
            let locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " - ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            $('#recordDate').daterangepicker({
                "locale": locale,
                "drops": "down"
            }, function (start, end, label) {
                let startTime = start.format('YYYY-MM-DD HH:mm:ss');
                let endTime = end.format('YYYY-MM-DD HH:mm:ss');
                $("#hidStartTime").val(startTime);
                $("#hidEndTime").val(endTime);
            });
            $('#recordDate').val('');
        };

        let getQueryCondition = function () {
            let param = {};
            param.structId = $("#hidStructParentID").val() || null;
            param.taskId = $("#sr-task").val() || null;
            param.scaleId = $("#sr-scaleName").val() || null;
            if($("#hidStartTime").val() && $("#hidEndTime").val()){
                param.startTimeBegin = $("#hidStartTime").val();
                param.startTimeEnd = $("#hidEndTime").val();
            }
            return param;
        };

        let generateReport = function () {
            let jsonObj = getQueryCondition();
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            $.ajax({
                type: 'POST',
                url: "/measuringroom/stat/group_report",
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (response) {
                    layer.closeAll();
                    if (response.resultCode !== 200) {
                        layer.msg("生成报告失败：" + response.resultMsg, { icon: 2, time: 3000 });
                        $("#report").animate({opacity: 0}, 300, function() { $(this).hide(); });
                        return;
                    }
                    
                    let data = response.data;
                    if (!data || !data.overallSituation || data.overallSituation.totalCount === 0) {
                        layer.msg("在该条件下暂无数据！", { icon: 0, time: 2000 });
                        $("#report").animate({opacity: 0}, 300, function() { $(this).hide(); });
                        return;
                    }

                    // 显示报告区域
                    $("#report").show().css('opacity', '0').animate({opacity: 1}, 500);
                    
                    // 设置报告生成时间
                    let now = new Date();
                    let reportTime = now.getFullYear() + '年' + (now.getMonth() + 1) + '月' + now.getDate() + '日 ' + 
                                   now.getHours().toString().padStart(2, '0') + ':' + 
                                   now.getMinutes().toString().padStart(2, '0');
                    $("#reportDate, #reportDate2").text(reportTime);
                    
                    // 1. 填充量表信息
                    if (data.scaleInfo) {
                        $(".scaleName").html(data.scaleInfo.scaleName || '');
                        $(".scaleIntro").html(data.scaleInfo.scaleIntro || '');
                        $(".scaleGuide").html(data.scaleInfo.scaleGuide || '');
                        $(".needTime").html(data.scaleInfo.needTime || '');
                    }
                    
                    // 2. 填充总体情况
                    let overall = data.overallSituation;
                    $(".totalCount").html(overall.totalCount);
                    $(".completedCount").html(overall.completedCount);
                    $(".uncompletedCount").html(overall.uncompletedCount);
                    
                    let completionRate = overall.totalCount > 0 ? 
                        (overall.completedCount / overall.totalCount * 100).toFixed(1) + '%' : '0%';
                    $(".completionRate").html(completionRate);
                    
                    // 3. 绘制完成情况图表
                    drawCompletionCharts(overall);
                    
                    // 4. 填充完成情况表格
                    fillCompletionTables(overall);
                    
                    // 5. 绘制预警分析图表和表格
                    drawWarningCharts(data.analysis);
                    fillWarningTables(data.analysis);
                    
                    // 滚动到报告区域
                    $('html, body').animate({
                        scrollTop: $("#report").offset().top - 100
                    }, 800);
                },
                error: function () {
                    layer.msg("生成报告失败！", { icon: 2, time: 3000 });
                }
            });
        };

        // 绘制完成情况图表
        let drawCompletionCharts = function(overall) {
            // 饼图配置（用于性别和年龄段）
            let pieChartOptions = {
                chart: { 
                    type: 'pie',
                    backgroundColor: 'transparent'
                },
                title: { text: '' },
                credits: { enabled: false },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: { 
                            enabled: true,
                            format: '<b>{point.name}</b>: {point.percentage:.1f} %',
                            style: {
                                fontSize: '12px'
                            }
                        },
                        showInLegend: false,
                        size: '90%'
                    }
                },
                colors: ['#667eea', '#764ba2', '#56ab2f', '#f093fb', '#4facfe']
            };

            // 柱状图配置（用于部门）
            let barChartOptions = {
                chart: { 
                    type: 'column',
                    backgroundColor: 'transparent'
                },
                title: { text: '' },
                credits: { enabled: false },
                xAxis: {
                    type: 'category',
                    labels: {
                        rotation: -45,
                        style: {
                            fontSize: '11px'
                        }
                    }
                },
                yAxis: {
                    title: { text: '完成人数' },
                    min: 0
                },
                tooltip: {
                    pointFormat: '<b>{point.y}</b> 人'
                },
                plotOptions: {
                    column: {
                        dataLabels: {
                            enabled: true,
                            style: {
                                fontSize: '11px'
                            }
                        },
                        colorByPoint: true
                    }
                },
                colors: ['#667eea', '#764ba2', '#56ab2f', '#f093fb', '#4facfe', '#ff9a9e', '#a8edea', '#fed6e3']
            };

            // 按部门完成情况 - 使用柱状图
            if (overall.departmentStats && overall.departmentStats.length > 0) {
                let departmentData = overall.departmentStats.map(item => [item.categoryName, item.completedCount]);
                Highcharts.chart('departmentChart', {
                    ...barChartOptions,
                    series: [{ 
                        name: '完成人数', 
                        data: departmentData,
                        showInLegend: false
                    }]
                });
            }

            // 按性别完成情况 - 使用饼图
            if (overall.genderStats && overall.genderStats.length > 0) {
                let genderData = overall.genderStats.map(item => [item.categoryName, item.completedCount]);
                Highcharts.chart('genderChart', {
                    ...pieChartOptions,
                    series: [{ name: '完成人数', data: genderData }]
                });
            }

            // 按年龄段完成情况 - 使用饼图
            if (overall.ageStats && overall.ageStats.length > 0) {
                let ageData = overall.ageStats.map(item => [item.categoryName, item.completedCount]);
                Highcharts.chart('ageChart', {
                    ...pieChartOptions,
                    series: [{ name: '完成人数', data: ageData }]
                });
            }
        };

        // 填充完成情况表格
        let fillCompletionTables = function(overall) {
            // 部门统计表格
            $("#departmentStatsTable").html('');
            if (overall.departmentStats) {
                overall.departmentStats.forEach(item => {
                    $("#departmentStatsTable").append(
                        '<tr>' +
                        '<td class="text-center font-weight-semibold">' + item.categoryName + '</td>' +
                        '<td class="text-center">' + item.totalCount + '</td>' +
                        '<td class="text-center text-success">' + item.completedCount + '</td>' +
                        '<td class="text-center"><span class="badge badge-primary">' + (item.completionRate || 0) + '%</span></td>' +
                        '</tr>'
                    );
                });
            }

            // 性别统计表格
            $("#genderStatsTable").html('');
            if (overall.genderStats) {
                overall.genderStats.forEach(item => {
                    $("#genderStatsTable").append(
                        '<tr>' +
                        '<td class="text-center font-weight-semibold">' + item.categoryName + '</td>' +
                        '<td class="text-center">' + item.totalCount + '</td>' +
                        '<td class="text-center text-success">' + item.completedCount + '</td>' +
                        '<td class="text-center"><span class="badge badge-primary">' + (item.completionRate || 0) + '%</span></td>' +
                        '</tr>'
                    );
                });
            }

            // 年龄段统计表格
            $("#ageStatsTable").html('');
            if (overall.ageStats) {
                overall.ageStats.forEach(item => {
                    $("#ageStatsTable").append(
                        '<tr>' +
                        '<td class="text-center font-weight-semibold">' + item.categoryName + '</td>' +
                        '<td class="text-center">' + item.totalCount + '</td>' +
                        '<td class="text-center text-success">' + item.completedCount + '</td>' +
                        '<td class="text-center"><span class="badge badge-primary">' + (item.completionRate || 0) + '%</span></td>' +
                        '</tr>'
                    );
                });
            }
        };

        // 绘制预警分析图表
        let drawWarningCharts = function(analysis) {
            let stackedBarOptions = {
                chart: { 
                    type: 'column',
                    backgroundColor: 'transparent'
                },
                title: { text: '' },
                credits: { enabled: false },
                xAxis: { 
                    categories: [],
                    labels: {
                        style: {
                            fontSize: '11px'
                        }
                    }
                },
                yAxis: { 
                    title: { text: '人数' }, 
                    stackLabels: { enabled: true }
                },
                plotOptions: {
                    column: { 
                        stacking: 'normal',
                        dataLabels: {
                            enabled: true,
                            style: {
                                fontSize: '10px'
                            }
                        }
                    }
                },
                colors: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'], // 绿、黄、橙、红
                series: [
                    { name: '绿码', data: [] },
                    { name: '黄码', data: [] },
                    { name: '橙码', data: [] },
                    { name: '红码', data: [] }
                ]
            };

            // 按组织预警分布
            if (analysis.organizationWarningStats && analysis.organizationWarningStats.length > 0) {
                let orgChart = {...stackedBarOptions};
                orgChart.xAxis.categories = analysis.organizationWarningStats.map(item => item.structName);
                orgChart.series[0].data = analysis.organizationWarningStats.map(item => item.greenCount || 0);
                orgChart.series[1].data = analysis.organizationWarningStats.map(item => item.yellowCount || 0);
                orgChart.series[2].data = analysis.organizationWarningStats.map(item => item.orangeCount || 0);
                orgChart.series[3].data = analysis.organizationWarningStats.map(item => item.redCount || 0);
                Highcharts.chart('orgWarningChart', orgChart);
            }

            // 按性别预警分布
            if (analysis.genderWarningStats && analysis.genderWarningStats.length > 0) {
                let genderChart = {...stackedBarOptions};
                genderChart.xAxis.categories = analysis.genderWarningStats.map(item => item.gender);
                genderChart.series[0].data = analysis.genderWarningStats.map(item => item.greenCount || 0);
                genderChart.series[1].data = analysis.genderWarningStats.map(item => item.yellowCount || 0);
                genderChart.series[2].data = analysis.genderWarningStats.map(item => item.orangeCount || 0);
                genderChart.series[3].data = analysis.genderWarningStats.map(item => item.redCount || 0);
                Highcharts.chart('genderWarningChart', genderChart);
            }

            // 按年龄段预警分布
            if (analysis.ageWarningStats && analysis.ageWarningStats.length > 0) {
                let ageChart = {...stackedBarOptions};
                ageChart.xAxis.categories = analysis.ageWarningStats.map(item => item.ageGroup);
                ageChart.series[0].data = analysis.ageWarningStats.map(item => item.greenCount || 0);
                ageChart.series[1].data = analysis.ageWarningStats.map(item => item.yellowCount || 0);
                ageChart.series[2].data = analysis.ageWarningStats.map(item => item.orangeCount || 0);
                ageChart.series[3].data = analysis.ageWarningStats.map(item => item.redCount || 0);
                Highcharts.chart('ageWarningChart', ageChart);
            }

            // 按因子预警分布
            if (analysis.factorWarningStats && analysis.factorWarningStats.length > 0) {
                let factorChart = {...stackedBarOptions};
                factorChart.xAxis.categories = analysis.factorWarningStats.map(item => item.factorName);
                factorChart.series[0].data = analysis.factorWarningStats.map(item => item.greenCount || 0);
                factorChart.series[1].data = analysis.factorWarningStats.map(item => item.yellowCount || 0);
                factorChart.series[2].data = analysis.factorWarningStats.map(item => item.orangeCount || 0);
                factorChart.series[3].data = analysis.factorWarningStats.map(item => item.redCount || 0);
                Highcharts.chart('factorWarningChart', factorChart);
            }
        };

        // 填充预警分析表格
        let fillWarningTables = function(analysis) {
            // 组织预警统计表格
            $("#orgWarningTable").html('');
            if (analysis.organizationWarningStats) {
                analysis.organizationWarningStats.forEach(item => {
                    let criticalRate = ((item.orangeCount || 0) + (item.redCount || 0)) / (item.totalCount || 1) * 100;
                    $("#orgWarningTable").append(
                        '<tr>' +
                        '<td class="text-center font-weight-semibold">' + item.structName + '</td>' +
                        '<td class="text-center">' + item.totalCount + '</td>' +
                        '<td class="text-center"><span class="badge badge-success">' + (item.greenCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge warning-badge-blue">' + (item.blueCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge badge-warning">' + (item.yellowCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge warning-badge-orange">' + (item.orangeCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge badge-danger">' + (item.redCount || 0) + '</span></td>' +
                        '<td class="text-center">' + (item.greenRate || 0) + '%</td>' +
                        '<td class="text-center"><strong>' + criticalRate.toFixed(1) + '%</strong></td>' +
                        '</tr>'
                    );
                });
            }

            // 重点关注统计表格
            $("#criticalWarningTable").html('');
            if (analysis.criticalWarningStats) {
                analysis.criticalWarningStats.forEach(item => {
                    $("#criticalWarningTable").append(
                        '<tr>' +
                        '<td class="text-center font-weight-semibold">' + item.structName + '</td>' +
                        '<td class="text-center">' + item.totalCount + '</td>' +
                        '<td class="text-center"><span class="badge badge-danger">' + (item.redCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge warning-badge-orange">' + (item.orangeCount || 0) + '</span></td>' +
                        '<td class="text-center"><strong>' + (item.criticalCount || 0) + '</strong></td>' +
                        '<td class="text-center">' + (item.redRate || 0) + '%</td>' +
                        '<td class="text-center">' + (item.orangeRate || 0) + '%</td>' +
                        '<td class="text-center"><strong class="text-danger">' + (item.criticalRate || 0) + '%</strong></td>' +
                        '</tr>'
                    );
                });
            }

            // 因子预警统计表格
            $("#factorWarningTable").html('');
            if (analysis.factorWarningStats) {
                analysis.factorWarningStats.forEach(item => {
                    let criticalRate = ((item.orangeCount || 0) + (item.redCount || 0)) / (item.totalCount || 1) * 100;
                    $("#factorWarningTable").append(
                        '<tr>' +
                        '<td class="text-center font-weight-semibold">' + item.factorName + '</td>' +
                        '<td class="text-center">' + item.totalCount + '</td>' +
                        '<td class="text-center"><span class="badge badge-success">' + (item.greenCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge warning-badge-blue">' + (item.blueCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge badge-warning">' + (item.yellowCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge warning-badge-orange">' + (item.orangeCount || 0) + '</span></td>' +
                        '<td class="text-center"><span class="badge badge-danger">' + (item.redCount || 0) + '</span></td>' +
                        '<td class="text-center">' + (item.greenRate || 0) + '%</td>' +
                        '<td class="text-center"><strong>' + criticalRate.toFixed(1) + '%</strong></td>' +
                        '</tr>'
                    );
                });
            }
        };

        $(function () {
            initForm();
            
            $("#structName").click(function () {
                initTree();
            });
            
            $("#btnGenerate").click(function () {
                let scale = $("#sr-scaleName").val();
                if (!scale) {
                    layer.msg("请选择量表！", { icon: 0, time: 2000 });
                    return;
                }
                
                generateReport();
            });
            
            $("#btnReset").click(function () {
                $("#hidStartTime").val('');
                $("#hidEndTime").val('');
                $("#hidStructParentID").val("");
                $("#structName").val('');
                $("#recordDate").val('');
                // 隐藏报告区域
                $("#report").animate({opacity: 0}, 300, function() { $(this).hide(); });
            });
            
            // 查看测评记录清单
            $("#btnViewRecordList").click(function () {
                let params = getQueryParams();
                let url = "/measuringroom/testing/record_list?" + params;
                window.open(url, '_blank');
            });
            
            // 查看测评筛选清单
            $("#btnViewFilterList").click(function () {
                let params = getQueryParams();
                let url = "/measuringroom/testing/filter_record_list?" + params;
                window.open(url, '_blank');
            });
        });
        
        // 获取当前页面的查询参数
        let getQueryParams = function() {
            let params = [];
            
            // 组织参数
            let structId = $("#hidStructParentID").val();
            if (structId && structId !== "0" && structId !== "") {
                params.push("structId=" + encodeURIComponent(structId));
            }
            
            // 任务参数
            let taskId = $("#sr-task").val();
            if (taskId && taskId !== "") {
                params.push("taskId=" + encodeURIComponent(taskId));
            }
            
            // 量表参数
            let scaleId = $("#sr-scaleName").val();
            if (scaleId && scaleId !== "") {
                params.push("scaleId=" + encodeURIComponent(scaleId));
            }
            
            // 时间范围参数
            let startTime = $("#hidStartTime").val();
            let endTime = $("#hidEndTime").val();
            if (startTime && startTime !== "") {
                params.push("startTime=" + encodeURIComponent(startTime));
            }
            if (endTime && endTime !== "") {
                params.push("endTime=" + encodeURIComponent(endTime));
            }
            
            return params.join("&");
        };
    </script>
</th:block>
</body>
</html>
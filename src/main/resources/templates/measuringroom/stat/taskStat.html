<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测评结果管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">测评统计</a></li>
                    </ol>
                </div>
                <h4 class="page-title"><i class="fa fa-area-chart mr-1"></i>测评统计</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-print-none">
                    <div class="card-title"><h5>统计条件：</h5></div>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="col-form-label">测评任务</label>
                                <select id="sr-task" class="form-control select2" data-toggle="select2" style="width:100%;">
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="col-form-label">所属组织</label>
                                <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择" />
                                <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                    <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                    <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                </div>
                                <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                            </div>
                            <div class="form-group">
                                <label class="col-form-label">选择角色</label>
                                <select id="sr-role" class="form-control select2">
                                    <option value="0">请选择</option>
                                    <option value="3">来访者</option>
                                    <option value="2">咨询师</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="col-form-label">统计字段设置</label>
                                <div class="form-inline">
                                    <div class="custom-control custom-checkbox mr-2">
                                        <input type="checkbox" id="create_condition_sex" name="create_condition" class="custom-control-input" value="condition_sex">
                                        <label class="custom-control-label font-weight-normal" for="create_condition_sex">性别</label>
                                    </div>
                                    <div class="custom-control custom-checkbox mr-2">
                                        <input type="checkbox" id="create_condition_education" name="create_condition" class="custom-control-input" value="condition_education">
                                        <label class="custom-control-label font-weight-normal" for="create_condition_education">文化程度</label>
                                    </div>
                                    <div class="custom-control custom-checkbox mr-2">
                                        <input type="checkbox" id="create_condition_marriage" name="create_condition" class="custom-control-input" value="condition_marriage">
                                        <label class="custom-control-label font-weight-normal" for="create_condition_marriage">婚姻状况</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" id="create_condition_religion" name="create_condition" class="custom-control-input" value="condition_religion">
                                        <label class="custom-control-label font-weight-normal" for="create_condition_religion">宗教信仰</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mt-3">
                                <input type="button" class="btn btn-primary btn-sm mr-1" id="btnStat" value="统计" />
                                <input type="reset" class="btn btn-light btn-sm" id="btnReset" />
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="col-form-label">量表</label>
                                <select id="sr-scaleName" class="form-control select2" data-toggle="select2" style="width:100%;">
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="col-form-label">性别</label>
                                <select id="sr-sex" class="form-control">
                                    <option value="">请选择</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="col-form-label">测评日期</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                    </div>
                                    <input type="text" class="form-control" value="" id="recordDate" name="recordDate" placeholder="请选择">
                                </div>
                                <input type="hidden" id="hidStartTime" value="" />
                                <input type="hidden" id="hidEndTime" value="" />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end card body-->

                <div class="card-body hide" id="report">
                    <div class="card-widgets mr-2 mb-4">
                        <button onclick="javascript:window.print()" class="btn btn-outline-secondary btn-sm d-print-none" title="打印"><i class="fa fa-print" title="打印"></i></button>
                    </div>
                    <div class="clearfix">
                        <div class="text-center mb-3">
                            <h4 class="m-0 letter-spacing-2">《<span class="scaleName"></span>》团体测评报告</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="mb-2">一、测评工具</h4>
                            <p class="text-muted font-14" style="line-height:200%;">
                                &nbsp; &nbsp; &nbsp; 本次测评采用网络调查的形式进行。<br />
                                &nbsp; &nbsp; &nbsp; 测评采用的量表为《<span class="scaleName"></span>》。
                                &nbsp; &nbsp; &nbsp; &nbsp;<span class="scaleIntro" style="text-indent:25px;"></span>
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="mb-2">二、完成情况</h4>
                            <p class="text-muted">
                                本次测评总人次<span class="totalNum"></span>，其中已测<span class="doneNum"></span>人，未测<span class="undoneNum"></span>人，无效<span class="invalidNum"></span>人，异常<span class="abnormalNum"></span>人。
                            </p>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm">
                                    <thead class="bg-light-lighten">
                                    <tr>
                                        <th class="text-center">测评总数</th>
                                        <th class="text-center">已测人数</th>
                                        <th class="text-center">未测人数</th>
                                        <th class="text-center">无效人数</th>
                                        <th class="text-center">异常人数</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td class="text-center totalNum"></td>
                                        <td class="text-center doneNum"></td>
                                        <td class="text-center undoneNum"></td>
                                        <td class="text-center invalidNum"></td>
                                        <td class="text-center abnormalNum"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <p class="text-muted">
                                测评情况图表分析
                            </p>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="card shadow-none border">
                                        <div class="card-header bg-light-lighten"><i class="fa fa-pie-chart mr-1"></i>施测率</div>
                                        <div id="container2" style="height:250px;"></div>
                                    </div>
                                </div>
                                <div class="col-sm-4 pr-3">
                                    <div class="card shadow-none border">
                                        <div class="card-header bg-light-lighten"><i class="fa fa-pie-chart mr-1"></i>有效率</div>
                                        <div id="container3" style="height:250px;"></div>
                                    </div>
                                </div>
                                <div class="col-sm-4 pl-0">
                                    <div class="card shadow-none border">
                                        <div class="card-header bg-light-lighten"><i class="fa fa-pie-chart mr-1"></i>异常率</div>
                                        <div id="container1" style="height:250px;"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6 hide" id="div-sex">
                                    <table class="table table-bordered table-sm table-sex">
                                        <thead class="bg-light-lighten">
                                        <tr>
                                            <th class="text-center">按性别</th>
                                            <th class="text-center">人数（人）</th>
                                            <th class="text-center">百分比</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-6 hide" id="div-education">
                                    <table class="table table-bordered table-sm table-education">
                                        <thead class="bg-light-lighten">
                                        <tr>
                                            <th class="text-center">按学历</th>
                                            <th class="text-center">人数（人）</th>
                                            <th class="text-center">百分比</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-6 hide" id="div-marriage">
                                    <table class="table table-bordered table-sm table-marriage">
                                        <thead class="bg-light-lighten">
                                        <tr>
                                            <th class="text-center">按婚姻状况</th>
                                            <th class="text-center">人数（人）</th>
                                            <th class="text-center">百分比</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-6 hide" id="div-religion">
                                    <table class="table table-bordered table-sm table-religion">
                                        <thead class="bg-light-lighten">
                                        <tr>
                                            <th class="text-center">按宗教信仰</th>
                                            <th class="text-center">人数（人）</th>
                                            <th class="text-center">百分比</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="mb-1">三、问题人员清单</h4>
                            <table class="table table-bordered table-sm table-abnormalusers p-3" id="tbAbnormalUsers">
                                <thead class="bg-light-lighten">
                                <tr>
                                    <th class="text-center">所属组织</th>
                                    <th class="text-center">用户名</th>
                                    <th class="text-center">姓名</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="mb-1">四、各项目选择频度</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead id="freq_head">
                                    </thead>
                                    <tbody>
                                    <tr>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="row distributionWrap">
                        <div class="col-12">
                            <h4 class="mb-1">五、常模比较及方差分析</h4>
                            <div class="table-responsive" id="distribution">

                            </div>
                        </div>
                    </div>
                </div>
                <!-- end card -->
            </div>
            <!-- end col-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script type="text/javascript">
        let initForm = function () {
            let jsonObj = { "taskKind": 1 };
            initSelectJson("#sr-task", "/measuringroom/task/get_for_select", jsonObj);
            //初始化测评任务列表
            initSelect("#sr-scaleName", "/measuringroom/scale/get_for_select", "", "", "选择量表");
            $("#sr-task").change(function () {
                let jsonObj = { "taskId": $("#sr-task").val() };
                initSelectAndSelectedFirst("#sr-scaleName", "/measuringroom/scale/get_for_select_by_taskid", jsonObj);
            });
            //初始化日期范围选择控件
            let locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " - ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            $('#recordDate').daterangepicker({
                "locale": locale,
                "drops": "down"
            }, function (start, end, label) {
                let startTime, endTime;
                if ((start - end) === 0) {
                    let _end = new Date(end);
                    let year = _end.getFullYear();
                    let month = _end.getMonth();
                    let day = _end.getDate();
                    end = new Date(year, month, day);
                    startTime = start.format('YYYY-MM-DD');
                    endTime = end.format('YYYY-MM-dd');
                }
                else {
                    startTime = start.format('YYYY-MM-DD');
                    endTime = end.format('YYYY-MM-DD');
                }
                $('#recordDate').val(startTime + ' - ' + endTime);
                $("#hidStartTime").val(startTime + ' 00:00:00');
                $("#hidEndTime").val(endTime + ' 23:59:59');
            });
            $('#recordDate').val('');
        };
        let getQueryCondition = function () {
            let param = {};
            param.structId = $("#hidStructParentID").val();
            param.roleId = $("#sr-role").val();
            param.sex = $("#sr-sex").val();
            param.taskId = $("#sr-task").val();
            param.scaleName = $("#sr-scaleName option:selected").text();
            if($("#hidStartTime").val()!=''&&$("#hidEndTime").val()!=''){
                param.startTime = $("#hidStartTime").val();
                param.endTime = $("#hidEndTime").val();
            }
            return param;
        };
        let getData = function () {
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            $("#btnStat").attr("Disabled", true);
            let jsonObj = getQueryCondition();
            $.ajax({
                type: 'POST',
                url: "/measuringroom/stat/task_stat",
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (data) {
                    layer.closeAll();
                    var res = JSON.parse(data);
                    $("#btnStat").attr("Disabled", false);
                    if (res.totalCount === 0) {
                        layer.msg("在该统计条件下暂无统计信息！", { icon: 0, time: 2000 });
                        $("#report").removeClass('show').addClass('hide');
                        return;
                    }
                    $("#report").removeClass('hide').addClass('show');
                    $(".scaleName").html(res.scaleName);
                    $(".scaleIntro").html(res.scaleIntro);
                    //测评情况
                    $(".totalNum").html(res.totalCount);
                    $(".doneNum").html(res.doneCount);
                    $(".undoneNum").html(res.unDoCount);
                    $(".invalidNum").html(res.invalidCount);
                    $(".abnormalNum").html(res.abnormalCount);

                    if ($("#create_condition_sex").prop("checked")) {
                        $(".table-sex tbody").html('');
                        $.each(res.listSexFreq, function (i, row) {
                            $(".table-sex tbody").append('<tr><td class= "text-center">' + row.id + '</td><td class="text-center">' + row.cnt + '</td><td class="text-center">' + (row.cnt / res.totalCount * 100).toFixed(1) + '%</td></tr>');
                        });
                        $("#div-sex").removeClass('hide').addClass('show');
                    }
                    else {
                        $("#div-sex").removeClass('show').addClass('hide');
                    }
                    if ($("#create_condition_education").prop("checked")) {
                        $(".table-education tbody").html('');
                        $.each(res.listEducationFreq, function (i, row) {
                            $(".table-education tbody").append('<tr><td class= "text-center">' + row.id + '</td><td class="text-center">' + row.cnt + '</td><td class="text-center">' + (row.cnt / res.totalCount * 100).toFixed(1) + '%</td></tr>');
                        });
                        $("#div-education").removeClass('hide').addClass('show');
                    }
                    else {
                        $("#div-education").removeClass('show').addClass('hide');
                    }
                    if ($("#create_condition_marriage").prop("checked")) {
                        $(".table-marriage tbody").html('');
                        $.each(res.listMarriageFreq, function (i, row) {
                            $(".table-marriage tbody").append('<tr><td class= "text-center">' + row.id + '</td><td class="text-center">' + row.cnt + '</td><td class="text-center">' + (row.cnt / res.totalCount * 100).toFixed(1) + '%</td></tr>');
                        });
                        $("#div-marriage").removeClass('hide').addClass('show');
                    }
                    else {
                        $("#div-marriage").removeClass('show').addClass('hide');
                    }
                    if ($("#create_condition_religion").prop("checked")) {
                        $(".table-religion tbody").html('');
                        $.each(res.listReligionFreq, function (i, row) {
                            $(".table-religion tbody").append('<tr><td class= "text-center">' + row.id + '</td><td class="text-center">' + row.cnt + '</td><td class="text-center">' + (row.cnt / res.totalCount * 100).toFixed(1) + '%</td></tr>');
                        });
                        $("#div-religion").removeClass('hide').addClass('show');
                    }
                    else {
                        $("#div-religion").removeClass('show').addClass('hide');
                    }
                    //问题人员清单
                    $(".table-abnormalusers tbody").html('');
                    if (res.abnormalUsers.length === 0) {
                        $(".table-abnormalusers tbody").html('&nbsp;&nbsp;暂无测评异常人员。');
                    }
                    $.each(res.abnormalUsers, function (i, row) {
                        $(".table-abnormalusers tbody").append('<tr><td class= "text-center">' + row.structFullName + '</td><td class="text-center">' + row.loginName + '</td><td class="text-center">' + row.realName + '</td></tr>');
                    });
                    let chartoptions = {
                        title: "",
                        chart: {
                            renderTo: ''
                        },
                        credits: { enabled: false },
                        colors: ['green', 'red'],
                        tooltip: {
                            headerFormat: '{series.name}<br>',
                            pointFormat: '{point.name}：{point.y} <br/>占比：{point.percentage:.1f}%'
                        },
                        plotOptions: {
                            pie: {
                                allowPointSelect: true,
                                cursor: 'pointer',
                                dataLabels: {
                                    enabled: false
                                },
                                showInLegend: true,
                                size: 160
                            }
                        },
                        series: [{ type: "pie", name: "", data: [] }]
                    };
                    //施测率
                    let dataArray = [];
                    dataArray.push(["已测人数", res.doneCount]);
                    dataArray.push(["未测人数", res.undoCount]);
                    chartoptions.chart.renderTo = "container2";
                    chartoptions.series[0].data = dataArray;
                    chartoptions.series[0].name = "施测率";
                    var testRateChart = new Highcharts.Chart(chartoptions);
                    //异常率
                    let data2 = [];
                    data2.push(["正常人数", res.doneCount - res.abnormalCount]);
                    data2.push(["异常人数", res.abnormalCount]);
                    chartoptions.chart.renderTo = "container1";
                    chartoptions.series[0].data = data2;
                    chartoptions.series[0].name = "异常率";
                    var testRateChart = new Highcharts.Chart(chartoptions);
                    //有效率
                    let data3 = [];
                    data3.push(["有效人数", res.totalCount - res.undoCount]);
                    data3.push(["无效人数", res.invalidCount]);
                    chartoptions.chart.renderTo = "container3";
                    chartoptions.series[0].data = data3;
                    chartoptions.series[0].name = "有效率";
                    var testRateChart = new Highcharts.Chart(chartoptions);
                    //选择频度
                    let htmlstr = "<tr>";
                    for (let i = 1; i <= 3; i++) {
                        htmlstr += '<th class="text-center" rowspan="2">题号</th><th class="text-center" colspan="' + res.aMaxNo + '">选择项目</th>';
                    }
                    htmlstr += "</tr><tr>";
                    for (let i = 1; i <= 3; i++) {
                        for (let j = 1; j <= res.aMaxNo; j++) {
                            htmlstr += '<th class="text-center">' + j + '</th>';
                        }
                    }
                    htmlstr += "</tr>";
                    let row_number = Math.round(res.qCount / 3);
                    for (let i = 1; i <= row_number; i++) {
                        htmlstr += "<tr>";
                        for (let j = 0; j <= 2; j++) {
                            let k = i + j * row_number - 1;
                            let q_no = parseInt(k) + 1;
                            htmlstr += '<td class="text-center">' + q_no + '</td>';
                            for (let n = 0; n <= res.aMaxNo - 1; n++) {
                                if (res.aFreq[k] == undefined) continue;
                                htmlstr += '<td class="text-center">' + res.aFreq[k][n] + '</td>';
                            }
                        }
                        htmlstr += "</tr>";
                    }
                    $("#freq_head").html("");
                    $("#distribution").html("");
                    $("#freq_head").append(htmlstr);
                    if (res.statResultDistribution === "") {
                        $(".distributionWrap").removeClass('show').addClass('hide');
                    }
                    else {
                        $(".distributionWrap").removeClass('hide').addClass('show');
                        $("#distribution_head").append(res.distributionHead);
                        $("#distribution").html(res.statResultDistribution);
                    }
                }
            });
        };
        $(function () {
            initForm();
            $("#structName").click(function () {
                initTree();
            });
            $("#btnStat").click(function () {
                let scale = $("#sr-scaleName").val();
                if (scale === "") {
                    layer.msg("在该统计条件下暂无统计信息！", { icon: 0, time: 2000 });
                    return;
                }
                getData();
            });
            $("#btnReset").click(function () {
                $("#hidStartTime").val('');
                $("#hidEndTime").val('');
                $("#hidStructParentID").val("0");
            });
        });
    </script>
</th:block>
</body>
</html>
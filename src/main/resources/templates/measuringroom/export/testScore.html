<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测评结果管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">数据导出</a></li>
                    </ol>
                </div>
                <h4 class="page-title">数据导出</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card pl-3">
                <div class="card-body">
                    <form class="form-horizontal">
                        <div class="form-group row mb-3">
                            <label class="col-2 col-form-label" for="sr-task">测评任务：</label>
                            <div class="col-4">
                                <select id="sr-task" class="form-control select2" data-toggle="select2">
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-2 col-form-label" for="sr-scaleName">量表名称：</label>
                            <div class="col-4">
                                <select id="sr-scaleName" class="form-control select2" data-toggle="select2">
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-2 col-form-label" for="sr-surveyName">调查问卷：</label>
                            <div class="col-4">
                                <select id="sr-surveyName" class="form-control select2" data-toggle="select2">
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-2 col-form-label">所属组织：</label>
                            <div class="col-4">
                                <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择" />
                                <div id="structContent" class="menuContent" style="display:none; position: absolute;  z-index:100000;">
                                    <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                    <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                </div>
                                <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-2 col-form-label">导出数据类型：</label>
                            <div class="col-4 form-inline">
                                <div class="custom-control custom-radio mr-2">
                                    <input type="radio" id="export_condition_score" name="export_condition" class="custom-control-input" value="test_score" checked>
                                    <label class="custom-control-label" for="export_condition_score">因子得分</label>
                                </div>
                                <div class="custom-control custom-radio mr-2">
                                    <input type="radio" id="export_condition_selected" name="export_condition" class="custom-control-input" value="test_result">
                                    <label class="custom-control-label" for="export_condition_selected">选择项</label>
                                </div>
                                <div class="custom-control custom-radio mr-2">
                                    <input type="radio" id="export_condition_selected_score" name="export_condition" class="custom-control-input" value="test_result_score">
                                    <label class="custom-control-label" for="export_condition_selected_score">选择项分数</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" id="export_condition_selected_survey" name="export_condition" class="custom-control-input" value="survey_result">
                                    <label class="custom-control-label" for="export_condition_selected_survey">调查问卷结果</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-0 row">
                            <div class="col-4 offset-2">
                                <button type="button" id="btnExport" class="btn btn-primary">导出</button>
                            </div>
                        </div>
                    </form>
                </div> <!-- end card-body-->
                <div class="card-footer">
                    <div class="mt-3 alert alert-primary bg-white text-muted">
                        <ul class="list-unstyled">
                            <li class="mb-2"><b>因子得分：</b>导出的数据包含个人基本信息和量表的因子得分。</li>
                            <li class="mb-2"><b>选择项：</b>导出的数据包含个人基本信息和量表题目的选择项。</li>
                            <li><b>选择项分数：</b>导出的数据包含个人基本信息和选择项的分数。</li>
                        </ul>
                    </div>
                </div>
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script type="text/javascript">
        let getQueryCondition = function () {
            let param = {};
            param.structId = $("#hidStructParentID").val();
            param.taskId = $("#sr-task").val();
            param.scaleId = $("#sr-scaleName").val();
            param.surveyId = $("#sr-surveyName").val();
            param.dataType = $('input[name="export_condition"]:checked').val();
            return param;
        };
        $(function () {
            let jsonObj = { "taskKind": 1 };
            initSelectJson("#sr-task", "/measuringroom/task/get_for_select", jsonObj);
            initSelect("#sr-scaleName", "/measuringroom/scale/get_for_select", "");
            initSelect("#sr-surveyName","/survey/survey/get_for_select","");
            $("#sr-task").change(function () {
                let jsonObj = { "taskId": $("#sr-task").val() };
                initSelectAndSelectedFirst("#sr-scaleName", "/measuringroom/scale/get_for_select_by_taskid", jsonObj);
                initSelectAndSelectedFirst("#sr-surveyName", "/survey/survey/get_for_select_by_measuring_taskid", jsonObj);
            });
            $("#structName").click(function () {
                initTree();
            });
            $("#btnExport").click(function () {
                if ($("#sr-scaleName").val() === "") {
                    layer.msg("提示：请选择一个量表后继续操作。");
                    return;
                }
                layer.msg('数据处理中…', {
                    icon: 17, shade: 0.05, time: false
                });
                $("#btnExport").attr("Disabled", true);
                $.ajax({
                    type: 'POST',
                    url: '/export/test_score',
                    data: JSON.stringify(getQueryCondition()),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnExport").attr("Disabled", false);
                        layer.closeAll();
                        if(res.resultCode ===200) {
                            location.href="/static/upload/temp/"+res.resultMsg;
                        }
                        else {
                            layer.msg('导出失败!',{ icon: 2, time: 2000 });
                        }
                    }
                });
            });
        });
    </script>
</th:block>
</body>
</html>
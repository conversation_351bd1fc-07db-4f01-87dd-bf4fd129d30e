<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测评任务管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">调查问卷作答记录管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">调查问卷作答记录管理</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2">
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="btnQuery"><i class="fa fa-search mr-1" title="高级搜索"></i> 高级搜索</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnRefresh" type="button" class="btn btn-light btn-sm mr-1"><i class="fa fa-refresh"></i> 刷新</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger btn-sm mr-1"><i class="fa fa-trash-o"></i> 批量删除</button>
                                <button id="btnExport" type="button" class="btn btn-warning btn-sm" data-toggle="tooltip" data-placement="right" title="" data-original-title="操作提示：先根据条件筛选出所需的数据然后再导出。"><i class="fa fa-file-excel-o mr-1"></i>导出</button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="tbRecordList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th style="width:30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>组织</th>
                                <th>测评任务</th>
                                <th>问卷名称</th>
                                <th>作答日期</th>
                                <th>完成状态</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.高级搜索 start -->
    <div id="search-modal" class="modal fade" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title"><i class="fa fa-search mr-1"></i>高级搜索</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal pl-3 pr-3">
                        <div class="form-group row mb-2">
                            <label for="structName" class="col-3 col-form-label">所属组织：</label>
                            <div class="col-9">
                                <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择" />
                                <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                    <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                    <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                </div>
                                <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                            </div>
                        </div>
                        <div class="form-group row mb-2">
                            <label for="sr-loginName" class="col-3 col-form-label">用户名：</label>
                            <div class="col-9">
                                <input type="text" class="form-control" id="sr-loginName" name="sr-loginName" placeholder="用户名" autocomplete="off">
                            </div>
                        </div>
                        <div class="form-group row mb-2">
                            <label for="sr-realName" class="col-3 col-form-label">姓名：</label>
                            <div class="col-9">
                                <input type="text" class="form-control" id="sr-realName" name="sr-realName" placeholder="姓名" autocomplete="off">
                            </div>
                        </div>
                        <div class="form-group row mb-2">
                            <label class="col-3 col-form-label">作答日期：</label>
                            <div class="input-group col-9">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                </div>
                                <input type="text" class="form-control" value="" id="recordDate" name="recordDate">
                            </div>
                            <input type="hidden" id="hidStartTime" value="" />
                            <input type="hidden" id="hidEndTime" value="" />
                        </div>
                        <div class="form-group row mb-2">
                            <label for="sr-state" class="col-3 col-form-label">完成状态：</label>
                            <div class="col-9">
                                <select id="sr-state" class="form-control">
                                    <option value="">请选择</option>
                                    <option value="0">未完成</option>
                                    <option value="1">已完成</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group mb-0 mt-3 justify-content-end row">
                            <div class="col-9">
                                <input type="button" class="btn btn-primary mr-1" id="btnSearch" value="查询" />
                                <input type="reset" class="btn btn-light mr-1" id="btnReset" />
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.高级搜索 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script type="text/javascript">
        let taskId = getUrlParam('taskId');
        $(function(){
            $("#structName").click(function () {
                initTree();
            });
            $("#btnQuery").click(function () {
                initForm();
                $('#recordDate').val("");
                $("#search-modal").modal();
            });
            $("#btnRefresh").click(function () {
                oTable.draw();
            });
            $("#btnSearch").click(function () {
                $("#search-modal").modal('hide');
                oTable.draw();
            });
            $("#btnReset").click(function () {
                $("#hidStartTime").val('');
                $("#hidEndTime").val('');
                $("#hidStructParentID").val("0");
            });
            //导出
            $("#btnExport").click(function () {
                layer.confirm('确定导出吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        layer.msg('请稍后…', {
                            icon: 17, shade: 0.05, time: false
                        });
                        let param = {};
                        param.structId = $("#hidStructParentID").val();
                        param.loginName = $("#sr-loginName").val();
                        param.realName = $("#sr-realName").val();
                        param.taskId = taskId;
                        param.taskKind = 1;
                        if ($("#hidStartTime").val() !=''){
                            param.startTime = $("#hidStartTime").val();
                        }
                        if($("#hidEndTime").val() != ''){
                            param.endTime = $("#hidEndTime").val();
                        }
                        if($("#sr-state").val() !=''){
                            param.isDone = $("#sr-state").val();
                        }
                        $.ajax({
                            type: 'POST',
                            url: '/export/survey_record',
                            data: JSON.stringify(param),
                            contentType:'application/json',
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll();
                                location.href="/static/upload/temp/"+res;
                            }
                        });
                    }
                });
            });
            //datatables
            $("#tbRecordList").bsDataTables({
                columns: columns,
                url: '/survey/surveyrecord/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel == 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids == "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/survey/surveyrecord/del_record", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });
        let initForm = function () {
            //初始化日期范围选择控件
            let locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " - ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            $('#recordDate').daterangepicker({
                "locale": locale,
                "drops": "up"
            }, function (start, end, label) {
                let startTime, endTime;
                if ((start - end) == 0) {
                    let _end = new Date(end);
                    let year = _end.getFullYear();
                    let month = _end.getMonth();
                    let day = _end.getDate();
                    end = new Date(year, month, day);
                    startTime = start.format('YYYY-MM-DD');
                    endTime = end.format('YYYY-MM-dd');
                }
                else {
                    startTime = start.format('YYYY-MM-DD');
                    endTime = end.format('YYYY-MM-DD');
                }
                $('#recordDate').val(startTime + ' - ' + endTime);
                $("#hidStartTime").val(startTime + ' 00:00:00');
                $("#hidEndTime").val(endTime + ' 23:59:59');
            });
        };
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "loginName", "bSortable": false },
            { "data": "realName", "bSortable": false },
            { "data": "structFullName", "bSortable": false},
            { "data": "taskName", "bSortable": false },
            { "data": "surveyName", "bSortable": false },
            {"data": "recordDate","bSortable": false},
            {"data": "isDone", "bSortable": false,
                render: function (data, type, row, meta) {
                    let lbl = '';
                    switch (data) {
                        case 0:
                            lbl = '<span class="badge badge-secondary-lighten badge-pill">未完成</span>';
                            break;
                        case 1:
                            let lblState = '<span class="badge badge-info badge-pill mr-2">已完成</span>';
                            lbl += lblState;
                            break;
                    }
                    return lbl;
                }
            }];
        let columnDefs = [];
        let getQueryCondition = function (data) {
            let param = {};
            param.structId = $("#hidStructParentID").val();
            param.loginName = $("#sr-loginName").val();
            param.realName = $("#sr-realName").val();
            param.taskId = taskId;
            if ($("#hidStartTime").val() !=''){
                param.startTime = $("#hidStartTime").val();
            }
            if($("#hidEndTime").val() != ''){
                param.endTime = $("#hidEndTime").val();
            }
            if($("#sr-state").val() !=''){
                param.isDone = $("#sr-state").val();
            }
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">个人测评管理</a></li>
                        <li class="breadcrumb-item active">我的测评任务</li>
                    </ol>
                </div>
                <h4 class="page-title">我的测评任务</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2 mr-1">
                                    <select class="form-control" style="width:100%;" name="taskType" id="taskType">
                                        <option value="">选择任务类型</option>
                                        <option value="1">限定</option>
                                        <option value="2">非限定</option>
                                    </select>
                                </div>
                                <div class="form-group mb-2 mr-1">
                                    <input type="text" class="form-control" id="taskName" placeholder="任务名称..." autocomplete="off">
                                </div>
                                <button type="button" class="btn btn-primary mb-2" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-centered table-striped nowrap" id="todo">
                            <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>任务类型</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>状态</th>
                                <th></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div> <!-- end col -->
    </div>
    <!-- modal.测评任务量表 start-->
    <div id="modal-scale" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">测评任务包含的量表</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-centered nowrap" id="tbScale">
                        <thead>
                        <tr>
                            <th>量表名称</th>
                            <th></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.测评任务量表 end-->
    <input type="hidden" id="hidTaskID" value="" />
    <input type="hidden" id="hidTaskType" value="" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        let columns = [
            { "data": "taskName", "bSortable": false },
            { "data": "taskType", "render":
                    function (data, type, full, meta) {
                        let labels = "";
                        if (data === 1) {
                            labels = '<span class="badge badge-danger">限定</span>';
                        }
                        if (data === 2) {
                            labels = '<span class="badge badge-info">非限定</span>';
                        }
                        return labels;
                    }, "bSortable": false
            },
            {"data": "startTime",  "bSortable": false},
            {"data": "endTime", "bSortable": false}
        ];
        let columnDefs_todo = [
            {
                targets: 4,
                render: function (data, type, row, meta) {
                    let labels = "";
                    let startDate = row.startTime;
                    let endDate = row.endTime;
                    if (startDate > getDateNowFormat()) {
                        labels = '<span class="badge badge-light">未开始</span>';
                    }
                    if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                        labels = '<span class="badge badge-success">进行中</span>';
                    }
                    if (getDateNowFormat() >= endDate) {
                        labels = '<span class="badge badge-warning">已结束</span>';
                    }
                    return labels;
                }
            },
            {
                targets: 5,
                render: function (data, type, row, meta) {
                    let labels = "";
                    let startDate = row.startTime;
                    let endDate = row.endTime;
                    if (startDate > getDateNowFormat()) {
                        labels += '<span class="badge badge-danger">测评时间未到</span>';
                    }
                    if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                        labels += '<a href="#"><button class="btn btn-outline-success btn-rounded btn-sm scale" title="参与测评"><i class="fa fa-angle-double-right"></i></button></a>';
                    }
                    return labels;
                }
            }];
        let getQueryCondition = function (data) {
            let param = {};
            param.taskName = $("#taskName").val();
            param.taskType = $("#taskType").val();
            param.taskKind = 1;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let oTableScale = null;
        let initTaskScale = function (scales) {
            if (oTableScale != null) {
                oTableScale.destroy();
            }
            oTableScale = $("#tbScale").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": false,
                "data": scales,
                "columns": [{ "data": "scaleName", "bSortable": false }],
                "columnDefs": [
                    {
                        targets: 1,
                        render: function (data, type, row, meta) {
                            return '<button class="btn btn-outline-success btn-sm start"><i class="fa fa-chevron-circle-right mr-1"></i>开始测评</button>';
                        }
                    }
                ],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": "暂无相关数据",
                    "zeroRecords": "对不起，查询不到任何相关数据",
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        $(function () {
            $("#btnQuery").click(function () {
                $('#todo').DataTable().ajax.reload();
            });
            $("#todo").on('click', '.scale', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidTaskID").val(data.id);
                $("#hidTaskType").val(data.taskType);
                initTaskScale(data.scales);
                $("#modal-scale").modal();
            });
            $("#tbScale").on('click', '.start', function () {
                let data = oTableScale.row($(this).parents('tr')).data();
                $.post('/measuringroom/testing/is_scale_done', { taskId: $("#hidTaskID").val(), scaleId: data.id }, function (res) {
                    if (res.id == '0' || (res.id != '0' && res.state != '1' && res.state != '2')) {
                        location.href = '/measuringroom/testing/guide?taskId=' + $("#hidTaskID").val() + '&type=' + $("#hidTaskType").val() + '&scaleId=' + data.id + '&recordId=' + res.id +'';
                    }
                    else {
                        layer.msg("您已经完成该测试！", { icon: 0, time: 2000 });
                    }
                });
            });
            //datatables
            $("#todo").bsDataTables({
                columns: columns,
                url: '/measuringroom/task/my_tasks',
                columnDefs: columnDefs_todo,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/dual-list-box/bootstrap-duallistbox.min.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测评任务</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">添加测评任务</a></li>
                    </ol>
                </div>
                <h4 class="page-title">添加测评任务</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-sm-8">
                            <h5><i class="fa fa-plus-square mr-1"></i>创建测评任务</h5>
                        </div>
                        <div class="col-sm-4">
                            <div class="text-right">
                                <a th:href="@{/measuringroom/task/list}" class="btn btn-outline-info btn-sm" type="button"><i class="fa fa-tasks mr-1"></i>测评任务列表</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">

                    <form id="frmTask" class="form-wizard" role="form">
                        <input type="hidden" name="hidstep" id="hidstep" value="s1" />
                        <div class="wizard-steps"></div>
                        <div class="step" id="s1">
                            <span data-icon="fa fa-file-text" data-text="任务信息"></span>
                            <div class="card-title">设置任务基本信息</div>
                            <div class="form-group mb-3">
                                <label class="col-form-label" for="taskName">测评任务名称</label>
                                <input type="text" class="form-control" id="taskName" name="taskName">
                            </div>
                            <div class="form-group mb-3">
                                <label class="col-form-label">选择测评日期</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                    </div>
                                    <input type="text" class="form-control" value="" id="taskDate" name="taskDate">
                                </div>
                                <input type="hidden" id="hidStartTime" value="" />
                                <input type="hidden" id="hidEndTime" value="" />
                            </div>
                            <div class="form-group mb-3">
                                <label for="ruleType">结果查看规则</label>
                                <select class="form-control" style="width:100%;" name="ruleType" id="ruleType">
                                    <option value="">请选择</option>
                                    <option value="1">正常允许异常不允许</option>
                                    <option value="2">允许查看</option>
                                    <option value="3">不允许查看</option>
                                </select>
                            </div>
                            <div class="form-group mb-3 form-inline">
                                <label class="mr-1" for="isSurvey">是否启用调查问卷</label>
                                <div class="input-group">
                                    <input type="checkbox" id="isSurvey" data-switch="primary" />
                                    <label for="isSurvey" data-on-label="" data-off-label=""></label>
                                </div>
                                <button id="btnSurveyList" type="button" class="btn btn-outline-secondary btn-rounded btn-sm ml-3 mr-1 hide" data-toggle="modal" data-target="#survey-modal">选择问卷</button>
                                <span class="survey-name ml-2"></span>
                                <input type="hidden" id="hidSurveyId" value="0" />
                            </div>
                            <div class="form-group mb-3 form-inline">
                                <label class="mr-1" for="showBackground">是否显示背景图片</label>
                                <div class="input-group">
                                    <input type="checkbox" id="showBackground" data-switch="primary" />
                                    <label for="showBackground" data-on-label="" data-off-label=""></label>
                                </div>
                                <button id="btnBackgroundSetting" type="button" class="btn btn-outline-secondary btn-rounded btn-sm ml-3 mr-1 hide" data-toggle="modal" data-target="#background-modal">设置背景</button>
                                <span class="background-info ml-2"></span>
                                <input type="hidden" id="hidBackgroundUrl" value="" />
                            </div>
                        </div>
                        <div class="step" id="s2">
                            <span data-icon="fa fa-users" data-text="测评人员"></span>
                            <div class="card-title">
                                <h5><i class="fa fa-list-ol mr-1"></i>选择测评对象</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="structName" class="col-form-label">所属组织</label>
                                    <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择" />
                                    <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                        <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                    </div>
                                    <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                                </div>
                                <div class="form-group">
                                    <label class="col-form-label" for="role">选择身份</label>
                                    <select class="form-control" name="role" id="role" style="width:100%;">
                                        <option value="3">来访者</option>
                                        <option value="4">家属</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="loginName" class="col-form-label">用户名</label>
                                    <input type="text" class="form-control" id="loginName" name="loginName" placeholder="用户名" autocomplete="off">
                                </div>
                                <div class="form-group">
                                    <label for="realName" class="col-form-label">姓名</label>
                                    <input type="text" class="form-control" id="realName" name="realName" placeholder="姓名" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="step submit_step" id="s3">
                            <span data-icon="fa fa-list-alt" data-text="测评量表"></span>
                            <div class="card-title">
                                <h5><i class="fa fa-file-text mr-1"></i>选择量表</h5>
                            </div>
                            <div class="form-group">
                                <select multiple="multiple" name="duallistbox" id="scaleIds" class="duallistbox col-lg-12">
                                </select>
                            </div>
                        </div>
                        <div class="wizard-actions">
                            <button class="btn btn-outline-secondary btn-sm pull-left" type="reset" id="pre"><i class="fa fa-arrow-left"></i> 上一步</button>
                            <button class="btn btn-outline-secondary btn-sm pull-right" type="submit" id="next">下一步 <i class="fa fa-arrow-right"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.选择问卷  -->
    <div id="survey-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="table-responsive">
                        <table id="tbSurvey" class="table table-striped" width="100%">
                            <thead>
                            <tr>
                                <th>问卷名称</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- modal.背景图片设置  -->
    <div id="background-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title text-white">
                        <i class="fa fa-image mr-2"></i>背景图片设置
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 文件上传区域 -->
                    <div class="upload-area mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0 text-primary">
                                    <i class="fa fa-cloud-upload mr-1"></i>选择背景图片
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="custom-file mb-3">
                                    <input type="file" class="custom-file-input" id="backgroundImage" name="backgroundImage" accept="image/*">
                                    <label class="custom-file-label" for="backgroundImage" data-browse="浏览">选择图片文件...</label>
                                </div>
                                <div class="text-center">
                                    <button class="btn btn-primary btn-lg" type="button" id="uploadBackgroundBtn">
                                        <i class="fa fa-upload mr-2"></i>上传背景图片
                                    </button>
                                </div>
                                <div class="mt-3">
                                    <div class="alert alert-info mb-0">
                                        <i class="fa fa-info-circle mr-1"></i>
                                        <strong>上传要求：</strong>支持JPG、PNG格式，建议尺寸1920x1080，文件大小不超过2MB
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 预览区域 -->
                    <div id="backgroundPreview" class="preview-area" style="display: none;">
                        <div class="card border-success">
                            <div class="card-header bg-success-lighten">
                                <h6 class="card-title mb-0 text-success">
                                    <i class="fa fa-check-circle mr-1"></i>背景预览
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <div class="preview-container mb-3">
                                    <img id="previewImg" src="" alt="背景预览" class="img-fluid rounded shadow-sm" style="max-height: 300px; border: 3px solid #e3eaef;">
                                </div>
                                <div class="preview-actions">
                                    <button type="button" class="btn btn-outline-danger" id="removeBackgroundBtn">
                                        <i class="fa fa-trash mr-1"></i>移除背景
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div id="backgroundEmpty" class="empty-state text-center py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px;">
                        <div class="empty-icon mb-3">
                            <i class="fa fa-image text-muted" style="font-size: 4rem; opacity: 0.5;"></i>
                        </div>
                        <h6 class="text-muted mb-2">暂未设置背景图片</h6>
                        <p class="text-muted small mb-0">请选择并上传背景图片</p>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times mr-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dual-list-box/jquery.bootstrap-duallistbox.js}"></script>
    <script th:src="@{/static/js/vendor/jquery-ui-1.10.4.min.js}"></script>
    <script th:src="@{/static/js/plugins/wizard/jquery.form.js}"></script>
    <script th:src="@{/static/js/plugins/wizard/jquery.form.wizard.js}"></script>
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.excheck.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        let locale = {
            "format": 'YYYY-MM-DD HH:mm:ss',
            "separator": " - ",
            "applyLabel": "确定",
            "cancelLabel": "取消",
            "fromLabel": "起始时间",
            "toLabel": "结束时间",
            "customRangeLabel": "自定义",
            "weekLabel": "W",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            "firstDay": 1
        };
        $('#taskDate').daterangepicker({
            "locale": locale,
            "showDropdowns": true,
            "linkedCalendars": false,
            "timePicker": true,
            "timePickerIncrement": 1,
            "timePicker24Hour": true,
            "minDate": moment().subtract(1, "days"),
            "drops": "down"
        }, function (start, end, label) {
            let startTime, endTime;
            if ((start - end) === 0) {
                let _end = new Date(end);
                let year = _end.getFullYear();
                let month = _end.getMonth();
                let day = _end.getDate();
                let hour = _end.getHours();
                let min = _end.getMinutes();
                let s = _end.getSeconds();
                end = new Date(year, month, day, hour + 23, min + 59, s + 59);
                startTime = start.format('YYYY-MM-DD HH:mm:ss');
                endTime = end.format('YYYY-MM-dd hh:mm:ss');
            }
            else {
                startTime = start.format('YYYY-MM-DD HH:mm:ss');
                endTime = end.format('YYYY-MM-DD HH:mm:ss');
            }
            $('#taskDate').val(startTime + ' - ' + endTime);
            $("#hidStartTime").val(startTime);
            $("#hidEndTime").val(endTime);
        });
        /*zTree.所属组织 start */
        let ztreeData;
        let setting = {
            view: {
                dblClickExpand: true
            },
            data: {
                simpleData: {
                    enable: true
                }
            },
            check: {
                enable: true,
                chkboxType: { "Y": "s", "N": "" }
            },
            callback: {
                beforeClick: beforeClick,
                onCheck: zTreeOnCheck
            }
        };
        function showIconForTree(treeId, treeNode) {
            return !treeNode.isParent;
        };

        function beforeClick(treeId, treeNode) {
        }

        function zTreeOnCheck(e, treeId, treeNode) {
            let zTree = $.fn.zTree.getZTreeObj("structTree"), nodes = zTree.getCheckedNodes(), v = "", id = "";
            nodes.sort(function compare(a, b) { return a.id - b.id; });
            for (let i = 0, l = nodes.length; i < l; i++) {
                v += nodes[i].name + ",";
                id += nodes[i].id + ",";
            }
            if (v.length > 0) v = v.substring(0, v.length - 1);
            if (id.length > 0) id = id.substring(0, id.length - 1);
            $("#hidStructParentID").attr("value", id);
        }

        function hideTree() {
            $("#structContent").fadeOut("fast");
            $("body").unbind("mousedown", onfrmBodyDown);
        }
        function onfrmBodyDown(event) {
            if (!(event.target.id === "menuBtn" || event.target.id === "structContent" || $(event.target).parents("#structContent").length > 0)) {
                hideTree();
            }
        }
        async function initTree() {
            $("#structTree").html('<img src="/static/images/loading.gif" height="18" class="mr-1">数据加载中…');
            $("#structContent").slideDown("fast");
            $("body").bind("mousedown", onfrmBodyDown);
            if (ztreeData == undefined) {
                await $.ajax({
                    url: "/anteroom/structs/index",
                    type: 'POST',
                    data: "",
                    dataType: "JSON",
                    async: true,
                    success: function (res) {
                        $("#structTree").html('');
                        ztreeData = JSON.parse(res);
                    }
                });
            }
            $.fn.zTree.init($("#structTree"), setting, ztreeData);
            let zTree = $.fn.zTree.getZTreeObj("structTree");
            let nodes = zTree.getNodes();
            for (let i = 0; i < nodes.length; i++) {
                zTree.expandNode(nodes[i], true, false, true);
            }
        };
        /*zTree.所属组织 end */
        let initDualListBox = function () {
            $.ajax({
                url: "/measuringroom/scale/get_list_for_dual",
                type: 'POST',
                data: "",
                dataType: "JSON",
                async: true,
                success: function (returnData) {
                    $(returnData).each(function () {
                        let o = document.createElement("option");
                        o.value = this['id'];
                        o.text = this['name'];
                        $("#scaleIds")[0].options.add(o);
                    });
                    $("#scaleIds").bootstrapDualListbox({
                        infotext: false, // text when all options are visible / false for no info text
                        infotextfiltered: '<span class="label label-warning">Filtered</span> {0} from {1}', // when not all of the options are visible due to the filter
                        infotextempty: '',      // when there are no options present in the list
                        selectorminimalheight: 200,
                        showfilterinputs: true,
                        filterplaceholder: '搜索...',
                        filtertextclear: '显示所有',
                        iconMove: 'fa fa-arrow-right',
                        iconMoveAll: 'fa fa-angle-double-right',
                        iconRemove: 'fa fa-arrow-left',
                        iconRemoveAll: 'fa fa-angle-double-left'
                    });
                }
            });
        };
        let columns = [
            { "data": "surveyName", "bSortable": false }];
        let columnDefs = [{
            targets: 1, render: function (data, type, row, meta) {
                return'<button type ="button" class="btn btn-outline-warning btn-sm mr-1 select">选择</button>';
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        $(function () {
            $("#structName").click(function () {
                initTree();
            });
            if ($("#isSurvey").prop("checked")) {
                $("#btnSurveyList").removeClass("hide");
            }
            else {
                $("#btnSurveyList").addClass("hide");
            }
            $("#isSurvey").on("change", function () {
                if ($(this).prop("checked")) {
                    $("#btnSurveyList").removeClass("hide");
                }
                else {
                    $("#btnSurveyList").addClass("hide");
                }
            });

            // 背景图片显示开关
            if ($("#showBackground").prop("checked")) {
                $("#btnBackgroundSetting").removeClass("hide");
            }
            else {
                $("#btnBackgroundSetting").addClass("hide");
            }
            $("#showBackground").on("change", function () {
                if ($(this).prop("checked")) {
                    $("#btnBackgroundSetting").removeClass("hide");
                }
                else {
                    $("#btnBackgroundSetting").addClass("hide");
                    // 清空背景设置
                    $("#hidBackgroundUrl").val("");
                    $(".background-info").html("");
                    $("#backgroundPreview").hide();
                    $("#backgroundEmpty").show();
                    $("#backgroundImage").val("");
                }
            });

            // 背景图片模态框显示时更新状态
            $("#background-modal").on('show.bs.modal', function () {
                if ($("#hidBackgroundUrl").val()) {
                    let imageUrl = "/static/upload/task_background/" + $("#hidBackgroundUrl").val();
                    $("#previewImg").attr("src", imageUrl);
                    $("#backgroundPreview").show();
                    $("#backgroundEmpty").hide();
                    $(".upload-area").hide();
                } else {
                    $("#backgroundPreview").hide();
                    $("#backgroundEmpty").show();
                    $(".upload-area").show();
                }
            });

            // Custom file input 文件名显示
            $("#backgroundImage").on('change', function() {
                let fileName = $(this).val().split('\\').pop();
                if (fileName) {
                    $(this).next('.custom-file-label').text(fileName);
                } else {
                    $(this).next('.custom-file-label').text('选择图片文件...');
                }
            });

            // 背景图片上传
            $("#uploadBackgroundBtn").click(function () {
                let fileInput = $("#backgroundImage")[0];
                if (!fileInput.files || !fileInput.files[0]) {
                    layer.msg("请选择图片文件", { icon: 2, time: 2000 });
                    return;
                }

                let file = fileInput.files[0];
                if (file.size > 2 * 1024 * 1024) {
                    layer.msg("文件大小不能超过2MB", { icon: 2, time: 2000 });
                    return;
                }

                let formData = new FormData();
                formData.append('file', file);
                formData.append('fileType', 'task_background');

                layer.msg('上传中...', { icon: 16, shade: 0.2, time: false });

                $.ajax({
                    url: '/fileUpload/general',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (res) {
                        layer.closeAll();
                        if (res.resultCode === 200) {
                            let fileName = res.resultMsg;
                            let imageUrl = "/static/upload/task_background/" + fileName;
                            $("#hidBackgroundUrl").val(fileName);
                            $("#previewImg").attr("src", imageUrl);
                            $("#backgroundPreview").show();
                            $("#backgroundEmpty").hide();
                            $(".upload-area").hide();
                            $(".background-info").html('<span class="text-success"><i class="fa fa-check mr-1"></i>已设置背景图片</span>');
                            // 重置文件输入框
                            $("#backgroundImage").val('');
                            $("#backgroundImage").next('.custom-file-label').text('选择图片文件...');
                            layer.msg("上传成功", { icon: 1, time: 2000 });
                        } else {
                            layer.msg(res.resultMsg || "上传失败", { icon: 2, time: 2000 });
                        }
                    },
                    error: function () {
                        layer.closeAll();
                        layer.msg("上传失败", { icon: 2, time: 2000 });
                    }
                });
            });

            // 移除背景图片
            $("#removeBackgroundBtn").click(function () {
                $("#hidBackgroundUrl").val("");
                $("#backgroundPreview").hide();
                $("#backgroundEmpty").show();
                $(".upload-area").show();
                $("#backgroundImage").val("");
                $("#backgroundImage").next('.custom-file-label').text('选择图片文件...');
                $(".background-info").html("");
                layer.msg("背景图片已移除", { icon: 1, time: 2000 });
            });
            $("#tbSurvey").on('click','.select',function(){
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidSurveyId").val(data.id);
                $(".survey-name").html('已选择问卷：['+data.surveyName+']');
                $("#survey-modal").modal('hide');
            });
            $("#btnSurveyList").click(function () {
                $("#tbSurvey").bsDataTables({
                    columns: columns,
                    url: '/survey/survey/list',
                    columnDefs: columnDefs,
                    paging: true,
                    retrieveData: function (sSource, aoData, fnCallback) {
                        let jsonObj = getQueryCondition(aoData);
                        $.ajax({
                            "type": "post",
                            "url": sSource,
                            "dataType": "json",
                            "contentType": "application/json",
                            "data": JSON.stringify(jsonObj),
                            "success": function (res) {
                                //服务器端返回的对象的returnObject部分是要求的格式
                                fnCallback(res);
                            }
                        });
                    }
                });
                $("#survey-modal").modal();
            });
            $('#taskDate').val("");
            initDualListBox();
            $("#frmTask").formwizard({
                formPluginEnabled: true,
                validationEnabled: true,
                validationOptions: {
                    ignore: '',
                    errorPlacement: function (error, element) {
                        wrap = element.parent();
                        if (wrap.hasClass('input-group')) {
                            error.insertAfter(wrap);
                        } else {
                            error.insertAfter(element);
                        }
                    },
                    rules: {
                        taskName: {
                            required: true,
                            remote: {
                                type: "POST",
                                url: "/measuringroom/task/check_name",
                                dataType: "json",
                                data: {
                                    taskName: function () {
                                        return $("#taskName").val();
                                    },
                                    hidTaskName: function (){
                                        return '';
                                    },
                                    taskKind: function (){
                                        return 1;
                                    }
                                },
                                dataFilter: function (data, type) {
                                    if (data === "0") {
                                        return true;
                                    }
                                    else
                                        return false;
                                }
                            }
                        },
                        taskDate: { required: true },
                        ruleType: { required: true }
                    },
                    messages: {
                        taskName: {
                            required: "请填写任务名称",
                            remote: "该任务名称已存在"
                        },
                        taskDate: { required: "请选择测评时间" },
                        ruleType: { required: "请选择结果查看规则" }
                    }
                },
                formOptions: {
                    success: function (data) {
                        let jsonObj = {
                            "taskName": $("#taskName").val(),
                            "startTime": $("#hidStartTime").val(),
                            "endTime": $("#hidEndTime").val(),
                            "taskType": 1,
                            "structIds": $("#hidStructParentID").val(),
                            "scaleIds": $("#scaleIds").val().join(','),
                            "roleId": $("#role").val(),
                            "loginName": $("#loginName").val(),
                            "realName": $("#realName").val(),
                            "resultViewRule": $("#ruleType").val(),
                            "isSurvey": $("#isSurvey").prop("checked") ? 1 : 0,
                            "surveyId": $("#hidSurveyId").val(),
                            "taskKind": 1,
                            "showBackground": $("#showBackground").prop("checked") ? 1 : 0,
                            "backgroundUrl": $("#hidBackgroundUrl").val()
                        };
                        $("#next").attr("Disabled", true);
                        layer.msg('请稍后…', {
                            icon: 17, shade: 0.2, time: false
                        });
                        $.ajax({
                            type: 'POST',
                            url: '/measuringroom/task/add_task',
                            data: JSON.stringify(jsonObj),
                            contentType:'application/json',
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll();
                                $("#next").attr("Disabled", false);
                                if (res.resultCode === 200) {
                                    layer.alert(res.resultMsg, {
                                        icon: 1, yes: function (index) {
                                            location.href = "/measuringroom/task/list";
                                        }
                                    });
                                }
                                else {
                                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                                }
                            }
                        });
                    },
                    resetForm: false
                },
                disableUIStyles: true,
                showSteps: true,//show the step
                vertical: true
            });
        });
    </script>
</th:block>
</body>
</html>
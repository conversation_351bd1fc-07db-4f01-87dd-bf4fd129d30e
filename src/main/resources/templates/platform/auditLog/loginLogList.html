<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">平台管理</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">审计日志管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">登录日志</a></li>
                    </ol>
                </div>
                <h4 class="page-title">登录日志列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mr-2">
                                    <label class="col-form-label mr-1" for="loginState">登录状态</label>
                                    <select class="form-control" id="loginState">
                                        <option value="">请选择</option>
                                        <option value="1">成功</option>
                                        <option value="0">失败</option>
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <label class="col-form-label mr-1">登录方式</label>
                                    <select class="form-control" id="loginWay">
                                        <option value="">请选择</option>
                                        <option value="0">账号密码登录</option>
                                        <option value="1">手机短信登录</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="col-form-label mr-1">日期：</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                        </div>
                                        <input type="text" class="form-control" value="" id="loginDate" name="loginDate" placeholder="请选择">
                                    </div>
                                    <input type="hidden" id="hidStartTime" value="" />
                                    <input type="hidden" id="hidEndTime" value="" />
                                </div>
                                <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbLog" class="table table-striped nowrap" width="100%">
                            <thead>
                            <tr>
                                <th>用户名</th>
                                <th>登录时间</th>
                                <th>登录方式</th>
                                <th>IP地址</th>
                                <th>设备信息</th>
                                <th>登录状态</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script type="text/javascript">
        let locale = {
            "format": 'YYYY-MM-DD HH:mm:ss',
            "separator": " - ",
            "applyLabel": "确定",
            "cancelLabel": "取消",
            "fromLabel": "起始时间",
            "toLabel": "结束时间",
            "customRangeLabel": "自定义",
            "weekLabel": "W",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            "firstDay": 1
        };
        $('#loginDate').daterangepicker({
            "locale": locale,
            "drops": "down",
            "timePicker": true,
            "timePickerIncrement": 1,
            "timePicker24Hour": true
        }, function (start, end, label) {
            let startTime, endTime;
            if ((start - end) === 0) {
                let _end = new Date(end);
                let year = _end.getFullYear();
                let month = _end.getMonth();
                let day = _end.getDate();
                let hour = _end.getHours();
                let min = _end.getMinutes();
                let s = _end.getSeconds();
                end = new Date(year, month, day, hour + 23, min + 59, s + 59);
                startTime = start.format('YYYY-MM-DD HH:mm:ss');
                endTime = end.format('YYYY-MM-dd hh:mm:ss');
            }
            else {
                startTime = start.format('YYYY-MM-DD HH:mm:ss');
                endTime = end.format('YYYY-MM-DD HH:mm:ss');
            }
            $('#loginDate').val(startTime + ' - ' + endTime);
            $("#hidStartTime").val(startTime);
            $("#hidEndTime").val(endTime);
        });
        $(function () {
            $("#loginDate").val('');
            $("#btnQuery").click(function () {
                $('#tbLog').DataTable().ajax.reload();
            });
            //datatables
            $("#tbLog").bsDataTables({
                columns: columns,
                url: '/platform/auditlog/get_loginloglist',
                columnDefs: '',
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
        });
        let columns = [
            { "data": "loginName", "bSortable": false },
            { "data": "loginDate", "bSortable": false },
            {"data": "loginWay", "bSortable": false,
                render: function (data, type, row, meta) {
                    if(data ===0) return '账号密码登录';
                    if(data ===1) return '手机短信验证登录';
                }
            },
            { "data": "ipAddress", "bSortable": false },
            { "data": "deviceInfo", "bSortable": false },
            {"data": "state", "bSortable": false,
                render: function (data, type, row, meta) {
                    if(data ===1) return '成功';
                    if(data ===0) return '失败';
                }
            }
        ];
        let getQueryCondition = function (data) {
            let param = {};
            param.state = $("#loginState").val();
            param.loginWay = $("#loginWay").val();
            if ($("#hidStartTime").val() !==''){
                param.startTime = $("#hidStartTime").val();
            }
            if($("#hidEndTime").val() !== ''){
                param.endTime = $("#hidEndTime").val();
            }
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
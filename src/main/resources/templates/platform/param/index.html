<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">平台管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">基础参数设置</a></li>
                    </ol>
                </div>
                <h4 class="page-title">基础参数配置</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card pl-3">
                <div class="card-body">
                    <form id="frmParam" class="form-horizontal col-10">
                        <h5 class="mb-1 header-title"><i class="fa fa-desktop mr-1"></i>平台参数</h5>
                        <div class="form-group row">
                            <label for="platformName" class="col-lg-4 col-form-label">平台名称<i class="fa fa-info-circle ml-1" data-toggle="tooltip" data-placement="right" title="" data-original-title="设置此项会显示在标题栏"></i></label>
                            <div class="col-lg-8">
                                <input id="platformName" name="platformName" class="form-control" type="text" aria-describedby="titleHelp" autocomplete="off" th:value="${sysConfig.platformName}">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="orgName" class="col-lg-4 col-form-label">机构名称<i class="fa fa-info-circle ml-1" data-toggle="tooltip" data-placement="right" title="" data-original-title="设置此项会显示在标题栏"></i></label>
                            <div class="col-lg-8">
                                <input id="orgName" name="orgName" class="form-control" type="text" autocomplete="off" th:value="${sysConfig.orgName}">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-lg-4 col-form-label">是否开放注册</label>
                            <div class="input-group col-lg-8 pt-1">
                                <th:block th:if="${sysConfig.isOpenReg == 1}">
                                    <input type="checkbox" id="isOpenReg" data-switch="primary" checked  />
                                </th:block>
                                <th:block th:unless="${sysConfig.isOpenReg == 1}">
                                    <input type="checkbox" id="isOpenReg" data-switch="primary" />
                                </th:block>
                                <label for="isOpenReg" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="form-group row" id="is-reg-checked">
                            <label class="col-lg-4 col-form-label">注册审核</label>
                            <div class="input-group col-lg-8 pt-1">
                                <th:block th:if="${sysConfig.isRegChecked == 1}">
                                    <input type="checkbox" id="isRegChecked" data-switch="primary" checked />
                                </th:block>
                                <th:block th:unless="${sysConfig.isRegChecked == 1}">
                                    <input type="checkbox" id="isRegChecked" data-switch="primary" />
                                </th:block>
                                <label for="isRegChecked" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="divider dotted xlarge mt-2 mb-2"></div>
                        <h5 class="mb-3 header-title"><i class="fa fa-envelope-o mr-1"></i>平台消息配置（站内消息）</h5>
                        <div class="form-group row">
                            <label class="col-lg-4 col-form-label">测评异常通知</label>
                            <div class="input-group col-lg-8 pt-1">
                                <th:block th:if="${sysConfig.isAbnormalNotify ==1}">
                                    <input type="checkbox" id="isAbnormalNotify" data-switch="primary" checked />
                                </th:block>
                                <th:block th:unless="${sysConfig.isAbnormalNotify ==1}">
                                    <input type="checkbox" id="isAbnormalNotify" data-switch="primary" />
                                </th:block>
                                <label for="isAbnormalNotify" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-lg-4 col-form-label">预约咨询通知</label>
                            <div class="input-group col-lg-8 pt-1">
                                <th:block th:if="${sysConfig.isCounselingNotify==1}">
                                    <input type="checkbox" id="isCounselingNotify" data-switch="primary" checked />
                                </th:block>
                                <th:block th:unless="${sysConfig.isCounselingNotify==1}">
                                    <input type="checkbox" id="isCounselingNotify" data-switch="primary" />
                                </th:block>
                                <label for="isCounselingNotify" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="divider divider dotted xlarge mt-2 mb-2"></div>
                        <h5 class="mb-3 header-title"><i class="fa fa-envelope-square mr-1"></i>手机短信参数</h5>
                        <div class="form-group row">
                            <label class="col-lg-4 col-form-label">启用手机短信</label>
                            <div class="input-group col-lg-8 pt-1">
                                <th:block th:if="${sysConfig.isSmsEnabled==1}">
                                    <input type="checkbox" id="isSmsEnabled" data-switch="primary" checked />
                                </th:block>
                                <th:block th:unless="${sysConfig.isSmsEnabled==1}">
                                    <input type="checkbox" id="isSmsEnabled" data-switch="primary" />
                                </th:block>
                                <label for="isSmsEnabled" data-on-label="" data-off-label=""></label>
                            </div>
                            <button id="btnSms" type="button" class="btn btn-outline-secondary btn-rounded btn-sm ml-3 mr-1 hide" data-toggle="modal" data-target="#sms-modal">短信配置</button>
                            <a href="/sms/list" class="btn btn-outline-secondary btn-rounded btn-sm hide" id="btnSmsSendRecord">短信发送记录</a>
                        </div>
                        <div class="divider divider dotted xlarge mt-2 mb-2"></div>
                        <h5 class="mb-3 header-title"><i class="fa fa-comments-o mr-1"></i>咨询</h5>
                        <div class="form-group row">
                            <label for="counselingHotline" class="col-lg-4 col-form-label">服务热线</label>
                            <div class="col-lg-8">
                                <input id="counselingHotline" name="counselingHotline" class="form-control" type="text" autocomplete="off" th:value="${sysConfig.counselingHotline}" placeholder="填写倾诉的电话号码">
                            </div>
                        </div>
                        <div class="divider mt-2 mb-2"></div>
                        <input type="submit" id="btnSave" class="btn btn-primary" value="保存" />
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
    <!-- modal.短信配置 start -->
    <div id="sms-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg modal-right" style="justify-content:left; min-width:350px;">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white" style="border-radius:0;">
                    <h5 class="modal-title">短信配置</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="frmSms" action="#" method="post">
                        <div class="form-group">
                            <label for="serviceName">短信服务商</label>
                            <select class="form-control" id="serviceName" name="serviceName">
                                <option value="阿里云短信服务">阿里云短信服务</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="signName">短信签名</label>
                            <input id="signName" name="signName" class="form-control" th:value="${sysConfig.smsConfig.signName}" placeholder="管理控制台中配置的短信签名（状态必须是验证通过）" />
                        </div>
                        <div class="form-group">
                            <label class="col-form-label">短信选项</label>
                            <div class="custom-control custom-checkbox mb-2">
                                <th:block th:if="${sysConfig.smsConfig.isLogin==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_1" checked />
                                </th:block>
                                <th:block th:unless="${sysConfig.smsConfig.isLogin==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_1" />
                                </th:block>
                                <label class="custom-control-label font-weight-normal" for="option_1">短信登录</label>
                                <input type="text" class="form-control mt-2" id="option_code_1" placeholder="短信登录模板代码" th:value="${sysConfig.smsConfig.loginTemplate}" />
                            </div>
                            <div class="custom-control custom-checkbox mb-2">
                                <th:block th:if="${sysConfig.smsConfig.isRegister==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_2" checked />
                                </th:block>
                                <th:block th:unless="${sysConfig.smsConfig.isRegister==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_2" />
                                </th:block>
                                <label class="custom-control-label font-weight-normal" for="option_2">注册短信验证</label>
                                <input type="text" class="form-control mt-2" id="option_code_2" placeholder="注册短信验证模板代码" th:value="${sysConfig.smsConfig.registerTemplate}" />
                            </div>
                            <div class="custom-control custom-checkbox mb-2">
                                <th:block th:if="${sysConfig.smsConfig.isMobileBind==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_3" checked>
                                </th:block>
                                <th:block th:unless="${sysConfig.smsConfig.isMobileBind==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_3" />
                                </th:block>
                                <label class="custom-control-label font-weight-normal" for="option_3">手机绑定</label>
                                <input type="text" class="form-control mt-2" id="option_code_3" placeholder="手机绑定模板代码" th:value="${sysConfig.smsConfig.mobileBindTemplate}" />
                            </div>
                            <div class="custom-control custom-checkbox mb-2">
                                <th:block th:if="${sysConfig.smsConfig.isModifyPwd==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_4" checked>
                                </th:block>
                                <th:block th:unless="${sysConfig.smsConfig.isModifyPwd==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_4" />
                                </th:block>
                                <label class="custom-control-label font-weight-normal" for="option_4">修改密码</label>
                                <input type="text" class="form-control mt-2" id="option_code_4" placeholder="修改密码模板代码" th:value="${sysConfig.smsConfig.modifyPwdTemplate}" />
                            </div>
                            <div class="custom-control custom-checkbox mb-2">
                                <th:block th:if="${sysConfig.smsConfig.isCounselingOrder==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_6" checked>
                                </th:block>
                                <th:block th:unless="${sysConfig.smsConfig.isCounselingOrder==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_6" />
                                </th:block>
                                <label class="custom-control-label font-weight-normal" for="option_6">预约咨询提醒</label>
                                <div class="form-group form-inline">
                                    <label class="col-form-label font-weight-normal mr-1" for="option_code_6">预约咨询成功提醒咨询师模板代码</label>
                                    <input type="text" class="form-control" id="option_code_6" placeholder="预约咨询成功提醒咨询师模板代码" th:value="${sysConfig.smsConfig.counselorCounselingSuccessTemplate}" />
                                </div>
                                <div class="form-group form-inline">
                                    <label class="col-form-label font-weight-normal mr-1" for="option_code_7">预约咨询成功提醒来访者模板代码</label>
                                    <input type="text" class="form-control" id="option_code_7" placeholder="预约咨询成功提醒来访者模板代码" th:value="${sysConfig.smsConfig.visitorCounselingSuccessTemplate}" />
                                </div>
                                <div class="form-group form-inline">
                                    <label class="col-form-label font-weight-normal mr-1" for="option_code_8">预约咨询状态变化提醒咨询师模板代码</label>
                                    <input type="text" class="form-control" id="option_code_8" placeholder="预约咨询状态变化提醒咨询师模板代码" th:value="${sysConfig.smsConfig.counselorCounselingChangeTemplate}" />
                                </div>
                                <div class="form-group form-inline">
                                    <label class="col-form-label font-weight-normal mr-1" for="option_code_9">预约咨询状态变化提醒来访者模板代码</label>
                                    <input type="text" class="form-control" id="option_code_9" placeholder="预约咨询状态变化提醒来访者模板代码" th:value="${sysConfig.smsConfig.visitorCounselingChangeTemplate}" />
                                </div>
                            </div>
                            <div class="custom-control custom-checkbox mb-2">
                                <th:block th:if="${sysConfig.smsConfig.isTrainingCampStart==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_10" checked>
                                </th:block>
                                <th:block th:unless="${sysConfig.smsConfig.isTrainingCampStart==1}">
                                    <input type="checkbox" class="custom-control-input" id="option_10" />
                                </th:block>
                                <label class="custom-control-label font-weight-normal" for="option_10">心理训练营开营提醒</label>
                                <input type="text" class="form-control mt-2" id="option_code_10" placeholder="心理训练营开营提醒模板代码" th:value="${sysConfig.smsConfig.trainingCampStartTemplate}" />
                            </div>
                        </div>
                        <input type="submit" id="btnSaveSms" class="btn btn-primary mt-3 mr-2" value="保存" />
                        <button type="button" class="btn btn-light mt-3" data-dismiss="modal">关闭</button>
                    </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- /.modal 短信配置end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        $(function () {
            if ($("#isOpenReg").prop("checked")) {
                $("#is-reg-checked").removeClass("hide");
            }
            else {
                $("#is-reg-checked").addClass("hide");
            }
            $("#isOpenReg").on("change", function () {
                if ($(this).prop("checked")) {
                    $("#is-reg-checked").removeClass("hide");
                }
                else {
                    $("#is-reg-checked").addClass("hide");
                }
            });
            //短信配置
            if ($("#isSmsEnabled").prop("checked")) {
                $("#btnSms").removeClass("hide");
                $("#btnSmsSendRecord").removeClass("hide");
            }
            else {
                $("#btnSms").addClass("hide");
                $("#btnSmsSendRecord").addClass("hide");
            }
            $("#isSmsEnabled").on("change", function () {
                if ($(this).prop("checked")) {
                    $("#btnSms").removeClass("hide");
                    $("#btnSmsSendRecord").removeClass("hide");
                }
                else {
                    $("#btnSms").addClass("hide");
                    $("#btnSmsSendRecord").addClass("hide");
                }
            });
            $("#frmParam").validate({
                rules: {
                    platformName: {
                        required: true
                    },
                    orgName: {
                        required: true
                    }
                },
                messages: {
                    platformName: {
                        required: "请输入平台名称"
                    },
                    orgName: {
                        required: "请输入机构名称"
                    }
                },
                submitHandler: function () {
                    $("#btnSave").val("保存中……");
                    $("#btnSave").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.platformName = $.trim($("#platformName").val());
                    jsonObj.orgName = $.trim($("#orgName").val());
                    jsonObj.isOpenReg = $("#isOpenReg").prop("checked") ? 1 : 0;
                    jsonObj.isRegChecked = $("#isRegChecked").prop("checked") ? 1 : 0;
                    jsonObj.isAbnormalNotify = $("#isAbnormalNotify").prop("checked") ? 1 : 0;
                    jsonObj.isCounselingNotify = $("#isCounselingNotify").prop("checked") ? 1 : 0;
                    jsonObj.isSmsEnabled = $("#isSmsEnabled").prop("checked") ? 1 : 0;
                    jsonObj.counselingHotline = $.trim($("#counselingHotline").val());
                    $.ajax({
                        type: 'POST',
                        url: '/platform/param/index',
                        data:JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        },
                        error: function (data) {
                            layer.msg("系统发生错误");
                        }
                    });
                }
            });
            $("#frmSms").validate({
                rules: {
                    signName: { required: true }
                },
                messages: {
                    signName: { required: "请填写短信签名" }
                },
                submitHandler: function () {
                    $("#btnSaveSms").val("保存中……");
                    $("#btnSaveSms").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.serviceName = $("#serviceName").val();
                    jsonObj.signName = $("#signName").val();
                    jsonObj.isLogin = $("#option_1").prop("checked") ? 1 : 0;
                    jsonObj.loginTemplate = $.trim($("#option_code_1").val());
                    jsonObj.isRegister = $("#option_2").prop("checked") ? 1 : 0;
                    jsonObj.registerTemplate = $.trim($("#option_code_2").val());
                    jsonObj.isMobileBind = $("#option_3").prop("checked") ? 1 : 0;
                    jsonObj.mobileBindTemplate = $.trim($("#option_code_3").val());
                    jsonObj.isModifyPwd = $("#option_4").prop("checked") ? 1 : 0;
                    jsonObj.modifyPwdTemplate = $.trim($("#option_code_4").val());
                    jsonObj.isCounselingOrder = $("#option_6").prop("checked") ? 1 : 0;
                    jsonObj.counselorCounselingSuccessTemplate = $.trim($("#option_code_6").val());
                    jsonObj.visitorCounselingSuccessTemplate = $.trim($("#option_code_7").val());
                    jsonObj.counselorCounselingChangeTemplate = $.trim($("#option_code_8").val());
                    jsonObj.visitorCounselingChangeTemplate = $.trim($("#option_code_9").val());
                    jsonObj.isTrainingCampStart = $("#option_10").prop("checked") ? 1 : 0;
                    jsonObj.trainingCampStartTemplate = $.trim($("#option_code_10").val());
                    $.ajax({
                        type: 'POST',
                        url: '/platform/param/save_sms_config',
                        data:JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveSms").val("保存");
                            $("#btnSaveSms").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        },
                        error: function (data) {
                            layer.msg("系统发生错误");
                        }
                    });
                }
            });
            $("#frmPoints").validate({
                submitHandler: function () {
                    $("#btnSavePoints").val("保存中……");
                    $("#btnSavePoints").attr("Disabled", true);
                    var jsonObj = {};
                    jsonObj.isCounselingEnabled = $("#isCounselingEnabled").prop("checked") ? 1 : 0;
                    jsonObj.isMeasuringEnabled = $("#isMeasuringEnabled").prop("checked") ? 1 : 0;
                    $.ajax({
                        type: 'POST',
                        url: '/platform/param/save_points_config',
                        data:JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSavePoints").val("保存");
                            $("#btnSavePoints").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        },
                        error: function (data) {
                            layer.msg("系统发生错误");
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">平台管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">缓存设置</a></li>
                    </ol>
                </div>
                <h4 class="page-title">缓存设置</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <div class="row">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>#</th>
                    <th>数据名称</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>平台参数</td>
                    <td>
                        <th:block th:if="${canSet eq true}">
                            <input type="button" class="btn btn-primary mr-1" id="btnUpdateSysConfigCache" value="更新缓存" />
                            <input type="button" class="btn btn-light" id="btnClearSysConfigCache" value="清除缓存" />
                        </th:block>
                    </td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>平台功能</td>
                    <td>
                        <th:block th:if="${canSet eq true}">
                            <input type="button" class="btn btn-primary mr-1" id="btnUpdateSysFunctionCache" value="更新缓存" />
                            <input type="button" class="btn btn-light" id="btnClearSysFunctionCache" value="清除缓存" />
                        </th:block>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        $(function(){
            $("#btnUpdateSysConfigCache").click(function (){
                $(this).attr('Disabled',true);
                $(this).val('请稍后…');
                $.ajax({
                    type: 'POST',
                    url: '/platform/cache/set_sysconfig_cache',
                    data: '',
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnUpdateSysConfigCache").attr('Disabled',false);
                        $("#btnUpdateSysConfigCache").val('更新缓存');
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }
                });
            });
            $("#btnClearSysConfigCache").click(function (){
                $(this).attr('Disabled',true);
                $(this).val('请稍后…');
                $.ajax({
                    type: 'POST',
                    url: '/platform/cache/clear_sysconfig_cache',
                    data: '',
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnClearSysConfigCache").attr('Disabled',false);
                        $("#btnClearSysConfigCache").val('清除缓存');
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }
                });
            });
            $("#btnUpdateSysFunctionCache").click(function (){
                $(this).attr('Disabled',true);
                $(this).val('请稍后…');
                $.ajax({
                    type: 'POST',
                    url: '/platform/cache/set_sysfunction_cache',
                    data: '',
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnUpdateSysFunctionCache").attr('Disabled',false);
                        $("#btnUpdateSysFunctionCache").val('更新缓存');
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }
                });
            });
            $("#btnClearSysFunctionCache").click(function (){
                $(this).attr('Disabled',true);
                $(this).val('请稍后…');
                $.ajax({
                    type: 'POST',
                    url: '/platform/cache/clear_sysfunction_cache',
                    data: '',
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnClearSysFunctionCache").attr('Disabled',false);
                        $("#btnClearSysFunctionCache").val('清除缓存');
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }
                });
            });
        });
    </script>
</th:block>
</body>
</html>
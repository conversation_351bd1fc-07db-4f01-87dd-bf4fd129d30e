<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">平台管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">平台角色管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">平台角色列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2">
                                    <div class="input-group">
                                        <label for="sr-rolename" class="sr-only">搜索</label>
                                        <input type="text" class="form-control" id="sr-rolename" placeholder="角色名称..." autocomplete="off">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-success mb-2 mr-1" title="新增角色"><i class="fa fa-user-plus mr-1"></i>新增角色</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2" title="删除"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <table id="tbRole" class="table table-striped dt-responsive nowrap" width="100%">
                        <thead>
                        <tr>
                            <th style="width: 30px;">
                                <div class="custom-control custom-checkbox">
                                    <input id="chkall" class="custom-control-input check" type="checkbox">
                                    <label class="custom-control-label" for="chkall"></label>
                                </div>
                            </th>
                            <th>角色名称</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.角色管理 start -->
    <div id="role-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="modal-role-title"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="frmRole" class="pl-3 pr-3" action="#">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="roleName">角色名称</label>
                            <input id="roleName" name="roleName" class="form-control" type="text">
                        </div>
                        <input id="hidID" type="hidden" value="0" />
                        <input type="reset" hidden />
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>&nbsp;&nbsp;
                        <input type="submit" class="btn btn-primary" id="btnSave" value="保存" />
                    </div>
                </form>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.角色管理 end -->
    <!-- modal.分配权限 start-->
    <div id="grant-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <form id="frmGrant" action="" method="post" role="form">
                <div class="modal-content">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title" id="modal-grant-title"></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <ul id="grantTree" class="ztree" style="min-width:300px;"></ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                        <input type="button" class="btn btn-danger" id="btnClear" value="清空" />
                        <input type="button" class="btn btn-primary" id="btnSaveGrant" value="保存" />
                    </div>
                </div>
            </form>
            <!-- /.modal-content -->
        </div>
    </div>
    <!-- modal.分配权限 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.excheck.min.js}"></script>
    <script type="text/javascript">
        let setting = {
            view: { selectedMulti: false},
            check: { enable: true, chkboxType: { "Y": "ps", "N": "s" }},
            data: {simpleData: {enable: true}}
        };
        //初始化zTree
        let InitTree = function () {
            $.ajax({
                type: "GET",
                url: "/platform/role/get_all_functions",
                data: "",
                success: function (res) {
                    let ztreeData = JSON.parse(res);
                    $.fn.zTree.init($("#grantTree"), setting, ztreeData);
                    let zTree = $.fn.zTree.getZTreeObj("grantTree");
                    let nodes = zTree.getNodes();
                    for (let i = 0; i < nodes.length; i++) {
                        zTree.expandNode(nodes[i], true, false, true);
                    }
                }
            });
        }
        //获取选中节点
        let onCheck = function () {
            let treeObj = $.fn.zTree.getZTreeObj("grantTree");
            let nodes = treeObj.getCheckedNodes(true);
            let ids = [];
            for (let i = 0; i < nodes.length; i++) {
                //获取选中节点的值
                ids.push(nodes[i].id);
            }
            return ids;
        }
        $(function () {
            //初始化页面权限
            initPage();
            //初始化权限树
            InitTree();
            //datatables
            $("#tbRole").bsDataTables({
                columns: columns,
                url: '/platform/role/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType":"application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });

            $("#btnQuery").click(function () {
                $('#tbRole').DataTable().ajax.reload();
            });
            //新增
            $("#btnAdd").click(function () {
                resetRoleForm();
                $("#modal-role-title").html('<i class="fa fa-user-plus mr-1"></i>新增角色');
                $("#role-modal").modal();
            });
            //修改
            $("#tbRole").on('click', '.update', function () {
                resetRoleForm();
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidID").val(data.roleId);
                $("#roleName").val(data.roleName);
                $("#modal-role-title").html('<i class="fa fa-pencil-square-o mr-1"></i>修改角色名称');
                $("#role-modal").modal();
            });
            //删除
            $("#tbRole").on('click', '.delete', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.roleId = data.roleId;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/platform/role/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids;
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/platform/role/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //权限设置
            $("#tbRole").on('click', '.grant', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidID").val(data.roleId);
                $("#modal-grant-title").html("分配权限【" + data.roleName + "】");
                let zTree = $.fn.zTree.getZTreeObj("grantTree");
                zTree.checkAllNodes(false);
                zTree.setting.check.chkboxType = { "Y": "", "N": "p" };
                let zNodes;
                let jsonObj = { "roleId": data.roleId };
                layer.msg('请稍后…', {
                    icon: 17, shade: 0.05, time: false
                });
                $.ajax({
                    type: 'POST',
                    url: '/platform/role/get_privilege',
                    data: jsonObj,
                    success: function (res) {
                        layer.closeAll();
                        zNodes = JSON.parse(res);
                        if (zNodes == null) {
                            zNodes = 0;
                        }
                        for (let i = 0; i < zNodes.length; i++) {
                            let node = zTree.getNodeByParam("id", zNodes[i].id, null);
                            if (node == null) {
                                node = 0;
                            } else {
                                zTree.checkNode(node, true, true);
                            }
                            zTree.updateNode(node, true);
                        }
                        zTree.setting.check.chkboxType = { "Y": "ps", "N": "s" };
                    }
                });
                $("#grant-modal").modal();
            });
            //清空ztree
            $("#btnClear").click(function () {
                let zTree = $.fn.zTree.getZTreeObj("grantTree");
                zTree.checkAllNodes(false);
            });
            //保存权限
            $("#btnSaveGrant").click(function () {
                let rid = $("#hidID").val();
                let privileges = onCheck();
                privileges = privileges.join(",");
                $("#btnSaveGrant").val("保存中…");
                $("#btnSaveGrant").attr("Disabled", true);
                let url = privileges.length === 0 ? "/platform/role/clear_privilege" : "/platform/role/add_privilege";
                let data = { "roleId": rid, "privileges": privileges };
                $.post(url, data,
                    function (res) {
                        $("#btnSaveGrant").attr("Disabled", false);
                        $("#btnSaveGrant").val("保存");
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            $("#grant-modal").modal('hide');
                            oTable.draw();
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }, 'json'
                )
            });
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") == true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });

            $("#frmRole").validate({
                rules: {
                    roleName: { required: true }
                },
                messages: {
                    roleName: { required: "请填写角色名称" }
                },
                submitHandler: function () {
                    let jsonObj = {
                        "id": $("#hidID").val(),
                        "roleName": $.trim($("#roleName").val()),
                        "flag": "p"
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    let url = $("#hidID").val() === "0" ? "/platform/role/add" : "/platform/role/update";
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    $("#role-modal").modal('hide');
                                    oTable.draw();
                                    layer.closeAll();
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
        let resetRoleForm = function () {
            $("#frmRole input").removeClass("error");
            $("label.error").hide();
            $("#frmRole input[type='reset']").click();
            $("#hidID").val(0);
        };
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnDel").show() : $("#btnDel").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        let columns = [
            {
                "data": "roleId", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data+ '"><label class="custom-control-label" for="lbl' + data+ '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "roleName", "bSortable": false }];
        let columnDefs = [{
            targets: 2, render: function (data, type, row, meta) {
                let buttons = "";
                if ('[[${canUpdate}]]' === 'true') buttons +='<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update"><i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                if ('[[${canDelete}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-danger btn-sm mr-1 delete"><i class="fa fa-trash-o mr-1"></i>删除</button>';
                if ('[[${canGrant}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-info btn-sm grant"><i class="fa fa-key mr-1"></i>权限设置</button>';
                return buttons;
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            param.roleName = $("#sr-rolename").val();
            param.flag = "p";
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
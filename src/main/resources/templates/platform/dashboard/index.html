<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
    <style>
        /* 自定义样式 */
        .card {
            transition: all 0.3s ease;
        }

        .bg-opacity-20 {
            background-color: rgba(255,255,255,0.2) !important;
        }

        .text-white-50 {
            color: rgba(255,255,255,0.7) !important;
        }

        .btn-loading {
            position: relative;
            pointer-events: none;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .badge {
            font-size: 0.75em;
            padding: 0.375em 0.75em;
        }

        /* 模拟数据提示闪烁动画 */
        @keyframes mockDataBlink {
            0%, 50% { opacity: 1; }
            25%, 75% { opacity: 0.8; }
        }

        .mock-data-blink {
            animation: mockDataBlink 4s infinite;
        }

        /* 模拟数据提示的特殊样式 */
        #mockDataAlert {
            position: relative;
            overflow: hidden;
        }

        #mockDataAlert::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 4s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">平台管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">平台数据看板</a></li>
                    </ol>
                </div>
                <h4 class="page-title">平台数据看板 <small class="text-muted" id="scopeIndicator">（全系统数据）</small></h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form class="form-inline">
                        <div class="form-group mr-3">
                            <label for="structId" class="mr-2 font-weight-semibold">机构筛选：</label>
                            <select class="form-control" name="structId" id="structId" style="width: 200px;"></select>
                        </div>
                        <div class="form-group mr-3">
                            <label for="dateRange" class="mr-2 font-weight-semibold">时间范围：</label>
                            <input type="text" class="form-control" id="dateRange" name="dateRange"
                                   placeholder="选择时间范围" style="width: 250px;">
                        </div>
                        <div class="form-group mr-3">
                            <label for="useMockData" class="mr-2 font-weight-semibold">数据模式：</label>
                            <div class="custom-control custom-switch" style="padding-top: 6px;">
                                <input type="checkbox" class="custom-control-input" id="useMockData">
                                <label class="custom-control-label" for="useMockData">
                                    <span id="mockDataLabel" class="badge badge-success">真实数据</span>
                                </label>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary" id="btnQuery">
                            <i class="fa fa-search"></i> 查询
                        </button>
                        <button type="button" class="btn btn-light ml-2" id="btnReset">
                            <i class="fa fa-refresh"></i> 重置
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- 统计卡片区域 -->
    <div class="row" id="statsCardsRow">
        <div class="col-12">
            <div class="alert alert-info" id="loadingTip" style="display: none;">
                <i class="fa fa-spinner fa-spin"></i> 正在加载系统数据...
            </div>
            <!-- 模拟数据提示 -->
            <div class="alert alert-warning mock-data-blink" id="mockDataAlert" style="display: none;">
                <i class="fa fa-exclamation-triangle mr-2"></i>
                <strong>当前使用模拟数据进行展示</strong> - 如需查看真实数据，请关闭"模拟数据"开关
            </div>
        </div>
    </div>

    <!-- 主要统计卡片 -->
    <div class="row" id="mainStatsRow" style="display: none;">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 text-white" id="totalConsultations">0</h2>
                            <p class="mb-0 text-white-50">总咨询量</p>
                        </div>
                        <div class="icon-container bg-opacity-20 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fa fa-comments fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 text-white" id="totalActivities">0</h2>
                            <p class="mb-0 text-white-50">总活动量</p>
                        </div>
                        <div class="icon-container bg-opacity-20 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fa fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 text-white" id="totalTests">0</h2>
                            <p class="mb-0 text-white-50">总测评量</p>
                        </div>
                        <div class="icon-container bg-opacity-20 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                            <i class="fa fa-list-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 次要统计卡片 -->
    <div class="row" id="secondaryStatsRow" style="display: none;">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="text-muted">风险咨询占比</h5>
                    <h3 class="text-danger" id="riskRate">0%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="text-muted">测评完成率</h5>
                    <h3 class="text-success" id="completionRate">0%</h3>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="text-muted">已完成测评</h5>
                    <h3 class="text-info" id="completedTests">0</h3>
                </div>
            </div>
        </div>

    </div>

    <!-- 咨询模块 -->
    <div class="row" id="consultationSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fa fa-comments mr-2"></i>咨询数据分析</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 咨询形式分布 -->
                        <div class="col-md-4">
                            <h6 class="text-center mb-3">咨询形式分布</h6>
                            <div id="consultationFormChart" style="height: 300px;"></div>
                        </div>
                        <!-- 咨询领域分布 -->
                        <div class="col-md-4">
                            <h6 class="text-center mb-3">咨询领域分布</h6>
                            <div id="consultationFieldChart" style="height: 300px;"></div>
                        </div>
                        <!-- 心理风险分布 -->
                        <div class="col-md-4">
                            <h6 class="text-center mb-3">心理风险分布</h6>
                            <div id="riskChart" style="height: 300px;"></div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <!-- 咨询量趋势 -->
                        <div class="col-12">
                            <h6 class="text-center mb-3">咨询量趋势</h6>
                            <div id="consultationTrendChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 活动模块 -->
    <div class="row mt-4" id="activitySection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fa fa-calendar mr-2"></i>活动数据分析</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 活动类型分布 -->
                        <div class="col-md-6">
                            <h6 class="text-center mb-3">活动类型分布</h6>
                            <div id="activityTypeChart" style="height: 300px;"></div>
                        </div>
                        <!-- 活动量趋势 -->
                        <div class="col-md-6">
                            <h6 class="text-center mb-3">活动量趋势</h6>
                            <div id="activityTrendChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测评模块 -->
    <div class="row mt-4" id="testSection" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fa fa-list-alt mr-2"></i>测评数据分析</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 测评预警等级分布 -->
                        <div class="col-md-6">
                            <h6 class="text-center mb-3">测评预警等级分布</h6>
                            <div id="warningLevelChart" style="height: 300px;"></div>
                        </div>
                        <!-- 测评量趋势 -->
                        <div class="col-md-6">
                            <h6 class="text-center mb-3">测评量趋势</h6>
                            <div id="testTrendChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/pages/main.js}"></script>
    <script type="text/javascript">
        $(function (){
            initSelect('#structId', '/anteroom/structs/get_for_select',{},'','全部机构');
            initDateRangePicker();
            
            // 初始化数据模式开关
            initMockDataSwitch();
            
            // 页面加载完成后自动加载系统数据
            loadDashboardData();

            // 绑定事件
            $('#btnQuery').click(function() {
                // 清除之前的数据和图表
                clearDashboardData();
                loadDashboardData();
            });
            $('#btnReset').click(function() {
                $('#structId').val('').trigger('change');
                $('#dateRange').val('');
                // 清除之前的数据和图表
                clearDashboardData();
                loadDashboardData(); // 重置后重新加载全系统数据
            });
        });

        // 初始化模拟数据开关
        function initMockDataSwitch() {
            // 初始状态显示
            updateMockDataLabel();
            updateMockDataAlert();
            
            // 开关切换事件
            $('#useMockData').change(function() {
                updateMockDataLabel();
                updateMockDataAlert();
                // 清除之前的数据和图表
                clearDashboardData();
                loadDashboardData(); // 切换后重新加载数据
            });
        }

        // 更新模拟数据标签
        function updateMockDataLabel() {
            const isChecked = $('#useMockData').prop('checked');
            const label = $('#mockDataLabel');
            if (isChecked) {
                label.removeClass('badge-success').addClass('badge-warning').text('模拟数据');
            } else {
                label.removeClass('badge-warning').addClass('badge-success').text('真实数据');
            }
        }

        // 更新模拟数据提示
        function updateMockDataAlert() {
            const isChecked = $('#useMockData').prop('checked');
            if (isChecked) {
                $('#mockDataAlert').show();
            } else {
                $('#mockDataAlert').hide();
            }
        }

        // 生成模拟数据
        function generateMockData() {
            return {
                // 基础统计数据
                totalConsultations: Math.floor(Math.random() * 1000) + 500,
                totalActivities: Math.floor(Math.random() * 500) + 200,
                totalTests: Math.floor(Math.random() * 2000) + 800,
                riskConsultationRate: Math.floor(Math.random() * 15) + 8,
                testCompletionRate: Math.floor(Math.random() * 20) + 75,
                completedTestsCount: Math.floor(Math.random() * 800) + 600,

                // 咨询形式分布
                consultationsByForm: [
                    {name: '驻场咨询', value: Math.floor(Math.random() * 200) + 150},
                    {name: '线上咨询', value: Math.floor(Math.random() * 150) + 100},
                    {name: '门店咨询', value: Math.floor(Math.random() * 100) + 50},
                    {name: '其他', value: Math.floor(Math.random() * 50) + 20}
                ],

                // 咨询领域分布
                consultationsByField: [
                    {name: '心理健康', value: Math.floor(Math.random() * 100) + 80},
                    {name: '情绪压力', value: Math.floor(Math.random() * 120) + 90},
                    {name: '人际关系', value: Math.floor(Math.random() * 80) + 60},
                    {name: '恋爱情感', value: Math.floor(Math.random() * 70) + 50},
                    {name: '家庭关系', value: Math.floor(Math.random() * 60) + 40},
                    {name: '亲子教育', value: Math.floor(Math.random() * 50) + 30},
                    {name: '职场发展', value: Math.floor(Math.random() * 90) + 70},
                    {name: '个人成长', value: Math.floor(Math.random() * 80) + 60}
                ],

                // 心理风险分布
                consultationsByRisk: [
                    {name: '无心理风险', value: Math.floor(Math.random() * 400) + 300},
                    {name: '有心理风险', value: Math.floor(Math.random() * 100) + 50},
                    {name: '未评估', value: Math.floor(Math.random() * 50) + 20}
                ],

                // 咨询量趋势（最近12个月）
                consultationsTrend: generateTrendData('consultation'),

                // 活动类型分布
                activitiesByType: [
                    {name: '驻场轻咨询', value: Math.floor(Math.random() * 80) + 60},
                    {name: '驻场咨询50分钟以上', value: Math.floor(Math.random() * 50) + 30},
                    {name: '团体辅导', value: Math.floor(Math.random() * 60) + 40},
                    {name: '心理关爱活动', value: Math.floor(Math.random() * 100) + 70},
                    {name: '其他活动', value: Math.floor(Math.random() * 40) + 20}
                ],

                // 活动量趋势
                activitiesTrend: generateTrendData('activity'),

                // 测评预警等级分布
                testsByWarningLevel: [
                    {name: '绿码', value: Math.floor(Math.random() * 800) + 600},
                    {name: '黄码', value: Math.floor(Math.random() * 200) + 100},
                    {name: '橙码', value: Math.floor(Math.random() * 100) + 50},
                    {name: '红码', value: Math.floor(Math.random() * 50) + 20},
                    {name: '未知', value: Math.floor(Math.random() * 30) + 10}
                ],

                // 测评量趋势
                testsTrend: generateTrendData('test')
            };
        }

        // 生成趋势数据
        function generateTrendData(type) {
            const trendData = [];
            const currentDate = new Date();
            let baseValue = 50;
            
            if (type === 'consultation') baseValue = 60;
            else if (type === 'activity') baseValue = 35;
            else if (type === 'test') baseValue = 120;

            for (let i = 11; i >= 0; i--) {
                const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
                const month = date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0');
                const value = Math.floor(Math.random() * (baseValue * 0.6)) + Math.floor(baseValue * 0.7);
                trendData.push({month: month, value: value});
            }
            return trendData;
        }


        // 初始化日期范围选择器
        function initDateRangePicker() {
            $('#dateRange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    cancelLabel: '清除',
                    applyLabel: '确定',
                    format: 'YYYY-MM-DD',
                    separator: ' 至 ',
                    customRangeLabel: '自定义',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                },
                ranges: {
                    '今天': [moment(), moment()],
                    '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    '最近7天': [moment().subtract(6, 'days'), moment()],
                    '最近30天': [moment().subtract(29, 'days'), moment()],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });

            $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' 至 ' + picker.endDate.format('YYYY-MM-DD'));
            });

            $('#dateRange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        }
        
        // 清除看板数据
        function clearDashboardData() {
            // 清除统计卡片数据
            $('#totalConsultations').text('0');
            $('#totalActivities').text('0');
            $('#totalTests').text('0');
            $('#riskRate').text('0%');
            $('#completionRate').text('0%');
            $('#completedTests').text('0');
            
            // 销毁所有图表实例
            if (window.consultationFormChart) {
                window.consultationFormChart.destroy();
                window.consultationFormChart = null;
            }
            if (window.consultationFieldChart) {
                window.consultationFieldChart.destroy();
                window.consultationFieldChart = null;
            }
            if (window.riskChart) {
                window.riskChart.destroy();
                window.riskChart = null;
            }
            if (window.activityTypeChart) {
                window.activityTypeChart.destroy();
                window.activityTypeChart = null;
            }
            if (window.warningLevelChart) {
                window.warningLevelChart.destroy();
                window.warningLevelChart = null;
            }
            if (window.consultationTrendChart) {
                window.consultationTrendChart.destroy();
                window.consultationTrendChart = null;
            }
            if (window.activityTrendChart) {
                window.activityTrendChart.destroy();
                window.activityTrendChart = null;
            }
            if (window.testTrendChart) {
                window.testTrendChart.destroy();
                window.testTrendChart = null;
            }
            
            // 清空图表容器
            $('#consultationFormChart, #consultationFieldChart, #riskChart, #activityTypeChart, #warningLevelChart, #consultationTrendChart, #activityTrendChart, #testTrendChart').empty();
        }
        
        // 显示加载提示
        function showLoadingTip() {
            $('#loadingTip').show();
            $('#mainStatsRow, #secondaryStatsRow, #consultationSection, #activitySection, #testSection').hide();
        }

        // 隐藏加载提示，显示数据区域
        function hideLoadingTip() {
            $('#loadingTip').hide();
            $('#mainStatsRow, #secondaryStatsRow, #consultationSection, #activitySection, #testSection').show();
        }

        // 加载看板数据
        function loadDashboardData() {
            const useMockData = $('#useMockData').prop('checked');
            
            if (useMockData) {
                // 使用模拟数据
                loadMockData();
            } else {
                // 使用真实API数据
                loadRealData();
            }
        }

        // 加载模拟数据
        function loadMockData() {
            // 显示加载中
            showLoadingTip();
            $('#btnQuery').addClass('btn-loading').prop('disabled', true);

            // 模拟网络延迟
            setTimeout(function() {
                const mockData = generateMockData();
                $('#btnQuery').removeClass('btn-loading').prop('disabled', false);
                updateDashboard(mockData);
            }, 800); // 模拟800ms的网络延迟
        }

        // 加载真实数据
        function loadRealData() {
            let structIdValue = $('#structId').val();
            let dateRange = $('#dateRange').val();
            let startDate = null;
            let endDate = null;

            if (dateRange) {
                let dates = dateRange.split(' 至 ');
                if (dates.length === 2) {
                    startDate = dates[0];
                    endDate = dates[1];
                }
            }

            let requestData = {
                startDate: startDate,
                endDate: endDate
            };
            
            // 如果选择了机构，则添加机构ID
            if (structIdValue && structIdValue !== '') {
                requestData.structId = parseInt(structIdValue);
            }

            // 显示加载中
            showLoadingTip();
            $('#btnQuery').addClass('btn-loading').prop('disabled', true);

            $.ajax({
                url: '/platform/dashboard/get_dashboard_data',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function(response) {
                    $('#btnQuery').removeClass('btn-loading').prop('disabled', false);
                    updateDashboard(response);
                },
                error: function(xhr, status, error) {
                    $('#btnQuery').removeClass('btn-loading').prop('disabled', false);
                    hideLoadingTip();
                    layer.msg('加载数据失败，请重试', {icon: 2});
                }
            });
        }



        // 更新看板数据
        function updateDashboard(data) {
            // 隐藏加载提示，显示数据区域
            hideLoadingTip();
            
            // 更新范围指示器
            updateScopeIndicator();

            // 更新统计卡片
            $('#totalConsultations').text(data.totalConsultations || 0);
            $('#totalActivities').text(data.totalActivities || 0);
            $('#totalTests').text(data.totalTests || 0);
            $('#riskRate').text((data.riskConsultationRate || 0) + '%');
            $('#completionRate').text((data.testCompletionRate || 0) + '%');
            $('#completedTests').text(data.completedTestsCount || 0);

            // 渲染图表
            renderPieChart('consultationFormChart', data.consultationsByForm || [], '咨询形式分布');
            renderPieChart('consultationFieldChart', data.consultationsByField || [], '咨询领域分布');
            renderPieChart('riskChart', data.consultationsByRisk || [], '心理风险分布');
            renderPieChart('activityTypeChart', data.activitiesByType || [], '活动类型分布');
            renderPieChart('warningLevelChart', data.testsByWarningLevel || [], '测评预警等级分布');
            
            // 渲染趋势图
            renderAreaChart('consultationTrendChart', data.consultationsTrend || [], '咨询量', '#667eea');
            renderLineChart('activityTrendChart', data.activitiesTrend || [], '活动量', '#43e97b');
            renderLineChart('testTrendChart', data.testsTrend || [], '测评量', '#f5576c');
        }
        
        // 更新范围指示器
        function updateScopeIndicator() {
            let structIdValue = $('#structId').val();
            let structName = $('#structId option:selected').text();
            let dateRange = $('#dateRange').val();
            
            let scopeText = '';
            if (structIdValue && structIdValue !== '' && structName && structName !== '全部机构') {
                scopeText = '（' + structName + '）';
            } else {
                scopeText = '（全系统数据）';
            }
            
            if (dateRange) {
                scopeText += ' · ' + dateRange;
            }
            
            $('#scopeIndicator').text(scopeText);
        }

        // 渲染饼图
        function renderPieChart(containerId, data, title) {
            if (!data || data.length === 0) {
                document.getElementById(containerId).innerHTML = '<div class="text-center text-muted p-4">暂无数据</div>';
                return;
            }

            // 准备数据和颜色配置
            let seriesData;
            
            if (containerId === 'warningLevelChart') {
                // 测评预警等级分布专用颜色映射
                const warningLevelColors = {
                    '绿码': '#28a745',        // 绿码 - 正常状态
                    '黄码': '#ffc107',    // 黄码 - 轻度预警
                    '橙码': '#fd7e14',    // 橙码 - 中度预警
                    '红码': '#dc3545',    // 红码 - 重度预警
                    '蓝码': '#007bff',         // 蓝码 - 未知状态
                    '未知': '#75777D'
                };
                
                seriesData = data.map(item => ({
                    name: item.name,
                    y: item.value,
                    color: warningLevelColors[item.name]
                }));
            } else {
                // 其他图表使用默认数据，让Highcharts自动分配颜色
                seriesData = data.map(item => ({
                    name: item.name,
                    y: item.value
                }));
            }

            // 创建图表并保存到全局变量
            const chart = Highcharts.chart(containerId, {
                chart: {
                    type: 'pie',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.y}</b> ({point.percentage:.1f}%)'
                },
                accessibility: {
                    point: {
                        valueSuffix: '%'
                    }
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: {
                            enabled: true,
                            format: '<b>{point.name}</b>: {point.y} ({point.percentage:.1f}%)'
                        }
                    }
                },
                colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#43e97b', '#fa709a', '#feb47b'],
                series: [{
                    name: title,
                    colorByPoint: true,
                    data: seriesData
                }],
                credits: {
                    enabled: false
                }
            });
            
            // 根据容器ID保存图表实例到全局变量
            switch(containerId) {
                case 'consultationFormChart':
                    window.consultationFormChart = chart;
                    break;
                case 'consultationFieldChart':
                    window.consultationFieldChart = chart;
                    break;
                case 'riskChart':
                    window.riskChart = chart;
                    break;
                case 'activityTypeChart':
                    window.activityTypeChart = chart;
                    break;
                case 'warningLevelChart':
                    window.warningLevelChart = chart;
                    break;
            }
        }

        // 渲染单条线图
        function renderLineChart(containerId, data, seriesName, color) {
            if (!data || data.length === 0) {
                document.getElementById(containerId).innerHTML = '<div class="text-center text-muted p-4">暂无数据</div>';
                return;
            }

            let categories = data.map(item => item.month);
            let values = data.map(item => item.value);

            // 创建图表并保存到全局变量
            const chart = Highcharts.chart(containerId, {
                chart: {
                    type: 'line',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                xAxis: {
                    categories: categories,
                    title: {
                        text: '月份'
                    }
                },
                yAxis: {
                    title: {
                        text: '数量'
                    }
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.y}</b>'
                },
                plotOptions: {
                    line: {
                        dataLabels: {
                            enabled: false
                        },
                        enableMouseTracking: true,
                        marker: {
                            enabled: true,
                            radius: 4
                        }
                    }
                },
                colors: [color],
                series: [{
                    name: seriesName,
                    data: values
                }],
                credits: {
                    enabled: false
                }
            });
            
            // 根据容器ID保存图表实例到全局变量
            switch(containerId) {
                case 'activityTrendChart':
                    window.activityTrendChart = chart;
                    break;
                case 'testTrendChart':
                    window.testTrendChart = chart;
                    break;
            }
        }

        // 渲染面积图
        function renderAreaChart(containerId, data, seriesName, color) {
            console.log('renderAreaChart 被调用:', containerId, seriesName);
            if (!data || data.length === 0) {
                document.getElementById(containerId).innerHTML = '<div class="text-center text-muted p-4">暂无数据</div>';
                return;
            }

            let categories = data.map(item => item.month);
            let values = data.map(item => item.value);

            // 创建图表并保存到全局变量
            const chart = Highcharts.chart(containerId, {
                chart: {
                    type: 'area',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                xAxis: {
                    categories: categories,
                    title: {
                        text: '月份'
                    }
                },
                yAxis: {
                    title: {
                        text: '数量'
                    }
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.y}</b>'
                },
                plotOptions: {
                    area: {
                        fillOpacity: 0.3,
                        lineWidth: 2,
                        dataLabels: {
                            enabled: false
                        },
                        enableMouseTracking: true,
                        marker: {
                            enabled: true,
                            radius: 4,
                            fillColor: color,
                            lineColor: color,
                            lineWidth: 2
                        }
                    }
                },
                colors: [color],
                series: [{
                    name: seriesName,
                    data: values,
                    fillColor: {
                        linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
                        stops: [
                            [0, Highcharts.Color(color).setOpacity(0.4).get('rgba')],
                            [1, Highcharts.Color(color).setOpacity(0.1).get('rgba')]
                        ]
                    }
                }],
                credits: {
                    enabled: false
                }
            });
            
            // 根据容器ID保存图表实例到全局变量
            switch(containerId) {
                case 'consultationTrendChart':
                    window.consultationTrendChart = chart;
                    break;
            }
        }

    </script>
</th:block>
</body>
</html>
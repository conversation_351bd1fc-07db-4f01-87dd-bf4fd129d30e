<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">档案室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">档案生成</a></li>
                    </ol>
                </div>
                <h4 class="page-title">生成档案</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card pl-3">
                <div class="card-body">
                    <form class="form-horizontal col-8">
                        <div class="form-group row mb-3">
                            <label class="col-4 col-form-label">所属组织：</label>
                            <div class="col-8">
                                <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择" />
                                <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                    <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                    <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                </div>
                                <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                                <input type="hidden" name="hidStructName" id="hidStructName" value="" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-4 col-form-label">用户角色：</label>
                            <div class="col-8">
                                <select id="role" class="form-control">
                                    <option value="">请选择</option>
                                    <option value="3">来访者</option>
                                    <option value="4">家属</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-4 col-form-label">用户名：</label>
                            <div class="col-8">
                                <input id="loginName" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-4 col-form-label">姓名：</label>
                            <div class="col-8">
                                <input id="realName" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-4 col-form-label">档案内容：</label>
                            <div class="col-8 form-inline">
                                <div class="custom-control custom-checkbox mr-2">
                                    <input type="checkbox" id="create_condition_baseinfo" name="create_condition" class="custom-control-input" value="condition_baseinfo" checked disabled>
                                    <label class="custom-control-label" for="create_condition_baseinfo">基本信息</label>
                                </div>
                                <div class="custom-control custom-checkbox mr-2">
                                    <input type="checkbox" id="create_condition_measuring" name="create_condition" class="custom-control-input" value="condition_measuring">
                                    <label class="custom-control-label" for="create_condition_measuring">测评记录</label>
                                </div>
                                <div class="custom-control custom-checkbox mr-2 hide">
                                    <input type="checkbox" id="create_condition_counseling" name="create_condition" class="custom-control-input" value="condition_counseling">
                                    <label class="custom-control-label" for="create_condition_counseling">咨询记录</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" id="create_condition_advice" name="create_condition" class="custom-control-input" value="condition_advice">
                                    <label class="custom-control-label" for="create_condition_advice">评语</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-0 row">
                            <div class="col-12 offset-4">
                                <button id="btnCreate" type="button" class="btn btn-primary">生成</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="card-footer hide">
                    <button id="btnDownload" type="button" class="btn btn-outline-warning mb-2">打包下载</button>
                    <table id="tbUser" class="table table-striped">
                        <thead>
                        <tr>
                            <th>用户名</th>
                            <th>姓名</th>
                            <th>所属组织</th>
                            <th></th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.选择评语 start -->
    <div id="advice-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">选择评语</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table id="tbAdvice" class="table" width="100%">
                            <thead>
                            <tr>
                                <th>选择</th>
                                <th>评语</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="hidZipFolderName" value="">
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script type="text/javascript">
        $(function () {
            if ('[[${canCreate}]]' === 'false') {
                $("#btnCreate").hide();
            }
            $("#structName").click(function () {
                initTree();
            });
            $("#btnCreate").click(function () {
                layer.msg('档案生成中，请稍后…', {
                    icon: 17, shade: 0.05, time: false
                });
                $("#btnCreate").attr("Disabled", true);
                let param = {};
                let userModel = {};
                userModel.structId = $("#hidStructParentID").val();
                userModel.structName = $("#hidStructName").val();
                userModel.roleId = $("#role").val();
                userModel.loginName = $("#loginName").val();
                userModel.realName = $("#realName").val();
                userModel.isChecked = 1;
                param.userDto = userModel;
                param.contentCondition = getCheckBoxSelected("create_condition");
                if ($("#create_condition_advice").prop("checked")) {
                    if ($("input[name='checklist']:checked").length !== 0) {
                        let advice = {};
                        advice.id = $("input[name='checklist']:checked")[0].value;
                        param.advice= advice;
                    }
                }
                $.ajax({
                    type: 'POST',
                    url: '/archiveroom/archive/create',
                    data: JSON.stringify(param),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (data) {
                        $("#btnCreate").attr("Disabled", false);
                        layer.closeAll();
                        let res = JSON.parse(data);
                        $("#hidZipFolderName").val(res.folderName);
                        resData = res.users.data;
                        if (resData.length > 0) {
                            $(".card-footer").removeClass('hide').addClass('show');
                            initTD();
                        }
                    }
                });
            });
            $("#btnDownload").click(function () {
                layer.msg('请稍后…', {
                    icon: 17, shade: 0.05, time: false
                });
                $.getJSON('/archiveroom/archive/batch_download_archive?zipFolderName='+$("#hidZipFolderName").val(),function(res){
                    layer.closeAll();
                    if(res.resultCode ===200) {
                        location.href="/static/upload/archive/"+res.resultMsg;
                    }
                    else {
                        layer.msg('下载失败!',{ icon: 2, time: 2000 });
                    }
                });
            });
            $("#create_condition_advice").change(function () {
                if ($(this).prop("checked")) {
                    $("#tbAdvice").bsDataTables({
                        columns: columns,
                        url: '/archiveroom/archiveadvice/list',
                        columnDefs: columnDefs,
                        paging: true,
                        retrieveData: function (sSource, aoData, fnCallback) {
                            let jsonObj = getQueryCondition(aoData);
                            $.ajax({
                                "type": "post",
                                "url": sSource,
                                "dataType": "json",
                                "contentType": "application/json",
                                "data": JSON.stringify(jsonObj),
                                "success": function (res) {
                                    //服务器端返回的对象的returnObject部分是要求的格式
                                    fnCallback(res);
                                }
                            });
                        }
                    });
                    $("#advice-modal").modal();
                }
            });
        });
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-radio"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="radio" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "adviceContent", "bSortable": false }];
        let columnDefs = [];
        let getQueryCondition = function (data) {
            let param = {};
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let getCheckBoxSelected = function (ctrl) {
            let array = [];
            $('input[name="' + ctrl + '"]:checked').each(function () {
                array.push($(this).val());
            });
            return array.join(',');
        };
        let resData;
        let oTableUser = null;
        let initTD = function () {
            if (oTableUser != null) {
                oTableUser.destroy();
            }
            oTableUser = $("#tbUser").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": true,
                "pageLength": 30,
                "data": resData,
                "columns": [
                    { "data": "loginName" },
                    { "data": "realName" },
                    { "data": "structFullName" },
                ],
                "columnDefs": [{
                    targets: 3, render: function (data, type, row, meta) {
                        let buttons = "";
                        if ('[[${canDownload}]]' === 'true') buttons += '<a href="/static/upload/archive/'+$("#hidZipFolderName").val()+'/'+row.userId+'' + row.realName + '.docx" class="btn btn-outline-info btn-sm mr-1 download"><i class="fa fa-download mr-1"></i>下载</a>';
                        return buttons;
                    }
                }],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": "暂无相关数据",
                    "zeroRecords": "对不起，查询不到任何相关数据",
                    "info": "当前显示 _START_ 到 _END_ 条，共 _TOTAL_ 条记录",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>
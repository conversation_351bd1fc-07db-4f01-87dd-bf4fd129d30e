<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">档案室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">评语管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">评语列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2">
                                    <div class="input-group">
                                        <label for="sr-Advice" class="sr-only">搜索</label>
                                        <input type="text" class="form-control" id="sr-Advice" placeholder="评语关键词..." autocomplete="off">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-success mb-2 mr-1"><i class="fa fa-plus mr-1"></i>新增</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <table id="tbAdvice" class="table table-striped dt-responsive" width="100%">
                        <thead>
                        <tr>
                            <th style="width: 30px;">
                                <div class="custom-control custom-checkbox">
                                    <input id="chkall" class="custom-control-input check" type="checkbox">
                                    <label class="custom-control-label" for="chkall"></label>
                                </div>
                            </th>
                            <th align="left">评语</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.评语管理 start -->
    <div id="advice-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="advice-title"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="frmAdvice" class="pl-3 pr-3" action="#">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="advice">评语内容：</label>
                            <textarea id="advice" name="advice" class="form-control" rows="5" maxlength="200"></textarea>
                            <span class="help-block"><small>请输入最多200字</small></span>
                        </div>
                        <input id="hidID" type="hidden" value="0" />
                        <input type="reset" hidden />
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                        <input type="submit" class="btn btn-primary" id="btnSave" value="保存" />
                    </div>
                </form>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.评语管理 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            //初始化页面权限
            initPage();
            $("#btnQuery").click(function () {
                $('#tbAdvice').DataTable().ajax.reload();
            });
            //datatables
            $("#tbAdvice").bsDataTables({
                columns: columns,
                url: '/archiveroom/archiveadvice/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //新增
            $("#btnAdd").click(function () {
                resetForm();
                $("#advice-title").html('<i class="fa fa-plus mr-1"></i>新增评语');
                $("#advice-modal").modal();
            });
            //修改
            $("#tbAdvice").on('click', '.update', function () {
                resetForm();
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidID").val(data.id);
                $("#advice").val(data.adviceContent);
                $("#advice-title").html('<i class="fa fa-pencil-square-o mr-1"></i>修改评语');
                $("#advice-modal").modal();
            });
            //删除
            $("#tbAdvice").on('click', '.delete', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.id = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/archiveroom/archiveadvice/delete", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids == "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/archiveroom/archiveadvice/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });

            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });

            $("#frmAdvice").validate({
                rules: {
                    advice: { required: true }
                },
                messages: {
                    advice: { required: "请填写评语内容" }
                },
                submitHandler: function () {
                    let jsonObj = {
                        "id": $("#hidID").val(),
                        "adviceContent": $.trim($("#advice").val())
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    let url = $("#hidID").val() == "0" ? "/archiveroom/archiveadvice/add" : "/archiveroom/archiveadvice/update";
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    $("#advice-modal").modal('hide');
                                    oTable.draw();
                                    layer.closeAll();
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
        let resetForm = function () {
            $("#frmAdvice input").removeClass("error");
            $("label.error").hide();
            $("#frmAdvice input[type='reset']").click();
            $("#hidID").val(0);
        };
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnDel").show() : $("#btnDel").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "adviceContent", "bSortable": false }];
        let columnDefs = [{
            targets: 2, render: function (data, type, row, meta) {
                let buttons = "";
                if ('[[${canUpdate}]]' === 'true') buttons +='<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update"><i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                if ('[[${canDelete}]]' === 'true') buttons += '<button type="button" class="btn btn-outline-danger btn-sm mr-1 delete"><i class="fa fa-trash-o mr-1"></i>删除</button>';
                return buttons;
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            param.adviceContent = $.trim($("#sr-Advice").val());
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
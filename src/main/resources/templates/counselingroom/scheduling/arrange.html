<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/fullcalendar/fullcalendar.min.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">咨询师排班</a></li>
                    </ol>
                </div>
                <h4 class="page-title">咨询师排班</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-4">
                            <h4 class="header-title"><i class="fa fa-calendar-check-o mr-1"></i> 排班管理</h4>
                        </div>
                        <div class="col-lg-8">
                            <div class="text-lg-right">

                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-9">
                            <div id="calendar"></div>
                        </div>
                        <div class="col-lg-3 mt-2">
                            <button id="btnAdd" type="button" class="btn btn-lg font-16 btn-primary btn-block" title="创建排班计划"><i class="fa fa-plus mr-1"></i>创建排班计划</button>
                            <h4 class="header-title mb-2 mt-3 font-weight-normal"><i class="fa fa-user-md mr-1"></i>咨询师列表</h4>
                            <div class="counselor-wrapper"></div>
                            <div class="mt-3 d-xl-block">
                                <h4 class="header-title text-left font-weight-normal"><i class="fa fa-info-circle"></i> 操作说明</h4>
                                <ul class="pl-2 list-unstyled">
                                    <li class="text-muted mb-2 mt-2">
                                        具有排班权限的咨询师可以给该组织下的咨询师排班且咨询师默认只能查看负责组织下的咨询师排班信息。
                                    </li>
                                    <li class="text-muted mb-2">
                                        排班表默认显示当天的咨询师排班信息，可以按月、周、天切换查看。
                                    </li>
                                    <li class="text-muted mb-2">
                                        双击排班表中的咨询师排班信息可以修改该次排班。
                                    </li>
                                    <li class="text-muted mb-2">
                                        <ul class="list-unstyled">
                                            <li><i class="fa fa-circle mr-1 text-secondary" aria-hidden="true"></i>表示该次排班时间已经过期不可操作；</li>
                                            <li><i class="fa fa-circle mr-1 text-success" aria-hidden="true"></i>表示排班有效；</li>
                                            <li><i class="fa fa-circle mr-1 text-primary" aria-hidden="true"></i>表示该次排班已经有预约；</li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- modal.排班 start -->
    <div id="scheduling-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-bottom-0 d-block">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h5 class="modal-title" id="modal-scheduling-title"></h5>
                </div>
                <div class="modal-body p-4">
                    <form id="frmScheduling">
                        <div class="form-group">
                            <label class="control-label" for="counselor">咨询师</label>
                            <select class="form-control" name="counselor" id="counselor">
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="col-form-label" for="schedulingDate">排班日期</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                </div>
                                <input type="text" class="form-control" value="" id="schedulingDate" name="schedulingDate" autocomplete="off">
                            </div>
                            <input type="hidden" id="hidStartTime" name="hidStartTime" value="" />
                            <input type="hidden" id="hidEndTime" name="hidEndTime" value="" />
                        </div>
                        <div class="text-right mt-3">
                            <button type="button" class="btn btn-outline-danger btn-sm pull-left" id="btnDel"><i class="fa fa-trash-o"></i> 删除该排班</button>
                            <button type="button" class="btn btn-light btn-sm" data-dismiss="modal">取消</button>
                            <input type="submit" class="btn btn-primary btn-sm" value="保存" id="btnSave">
                            <input type="hidden" id="hidID" value="0" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.排班 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script th:src="@{/static/js/plugins/fullcalendar/fullcalendar.min.js}"></script>
    <script th:src="@{/static/js/plugins/fullcalendar/fullcalendar-zh-cn.js}"></script>
    <script type="text/javascript">
        $(function () {
            init();
            $("#btnAdd").click(function () {
                initSchedulingForm();
                initSelect("#counselor", "/anteroom/user/getCounselorList_for_select", {});
                $("#modal-scheduling-title").html('<i class="fa fa-calendar-plus-o mr-1"></i>添加排班');
                $("#scheduling-modal").modal();
            });
            $("#frmScheduling").validate({
                errorPlacement: function (error, element) {
                    wrap = element.parent();
                    if (wrap.hasClass('input-group')) {
                        error.insertAfter(wrap);
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    counselor: { required: true },
                    schedulingDate: {
                        required: true,
                        remote: {
                            type: "get",
                            url: "/counselingroom/scheduling/check_scheduling",
                            dataType: "json",
                            data: {
                                counselorId: function () {
                                    return $("#counselor").val();
                                },
                                startTime: function () {
                                    return $("#hidStartTime").val();
                                },
                                endTime: function () {
                                    return $("#hidEndTime").val();
                                }
                            },
                            dataFilter: function (data, type) {
                                if (data == "0") {
                                    return true;
                                }
                                else
                                    return false;
                            }
                        }
                    }
                },
                messages: {
                    counselor: { required: "请选择咨询师" },
                    schedulingDate: { required: "请选择排班日期", remote:"该时间段已经有排班，请重新选择。" }
                },
                submitHandler: function () {
                    $("#btnSave").val("保存中……");
                    $("#btnSave").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.counselorId = $("#counselor").val();
                    jsonObj.id = $("#hidID").val();
                    jsonObj.startTime = $("#hidStartTime").val();
                    jsonObj.endTime = $("#hidEndTime").val();
                    let url = $("#hidID").val() == "0" ? "/counselingroom/scheduling/arrange" : "/counselingroom/scheduling/update";
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, {
                                    icon: 1, yes: function (index) {
                                        $('#calendar').fullCalendar('refetchEvents');
                                        $("#scheduling-modal").modal('hide');
                                    }
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            $("#btnDel").click(function () {
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/counselingroom/scheduling/del", { id: $("#hidID").val() }, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                $("#scheduling-modal").modal('hide');
                                initCalendar(0);
                                $('#calendar').fullCalendar('refetchEvents');
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
        });
        let init = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnDel").show() : $("#btnDel").hide();
            initCalendar(0);
            getCounselorList();
        };
        let initCalendar = function (obj) {
            /*------------ 初始化 calendar ------------*/
            $('#calendar').fullCalendar({
                timeFormat: 'HH:mm',
                defaultView: 'listWeek',
                header: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'month,agendaWeek,agendaDay,listWeek'
                },
                navLinks: true,
                editable: false,
                droppable: false,
                timezone: 'local',
                events: function (start, end, timezone, callback) {
                    let jsonObj = {};
                    if ($("#hidID").val() != "0") {
                        jsonObj.id = $("#hidID").val();
                    }
                    layer.msg('数据加载中…', {
                        icon: 17, shade: 0.05, time: false
                    });
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/scheduling/get_for_calendar',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll();
                            let events = res;
                            callback(events);
                        }
                    });
                },
                eventClick: function (eventObj) {
                    if ('[[${canUpdate}]]' === 'false') {
                        layer.msg("提示：您没有该操作权限。");
                        return;
                    }
                    let now = getDateNowFormat();
                    let endTime = $.fullCalendar.formatDate(eventObj.end, "YYYY-MM-DD HH:mm:ss");
                    if (now > endTime) {
                        layer.msg("提示：该排班已经过期，不可操作。");
                        return;
                    }
                    initSchedulingForm();
                    initSelect("#counselor", "/anteroom/user/getCounselorList_for_select", {},eventObj.title);
                    $("#schedulingDate").val($.fullCalendar.formatDate(eventObj.start, "YYYY-MM-DD HH:mm:ss") + ' - ' + $.fullCalendar.formatDate(eventObj.end, "YYYY-MM-DD HH:mm:ss"))
                    $("#modal-scheduling-title").html('<i class="fa fa-pencil-square"></i> 修改排班信息');
                    $("#hidID").val(eventObj.id);
                    $("#scheduling-modal").modal();
                }
            });
        };
        let getCounselorList = function () {
            $.post("/anteroom/user/getRecommendCounselorList", {}, function (res) {
                if (res.length > 0) {
                    let wrapper = $(".counselor-wrapper");
                    let users_list = "";
                    for (let i = 0; i < res.length; i++) {
                        let headPic = res[i].headPic == '' ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + res[i].headPic;
                        users_list += '<div class="inbox-widget"><div class="inbox-item"><div class="inbox-item-img">';
                        users_list += '<img src="' + headPic + '" class="rounded-circle" alt=""></div>';
                        users_list += '<p class="inbox-item-author">' + res[i].realName + '</p>';
                        users_list += '<p class="inbox-item-date"><a href="javascript:" onclick="showScheduling(' + res[i].userId +')" class="btn btn-sm btn-link text-info font-13">排班信息</a></p>'
                        users_list += '</div></div>';
                    }
                    users_list += '<div class="text-center mt-2"><a href="javascript:void(0);" onclick="showScheduling(0)" class="btn btn-outline-danger btn-sm"> 查看全部排班信息 </a></div>';
                    wrapper.empty();
                    wrapper.append(users_list);
                }
            });
        };
        let initSchedulingForm = function () {
            $("#hidID").val("0");
            $("#hidStarttime").val('');
            $("#hidEndtime").val('');
            let locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " - ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            $('#schedulingDate').daterangepicker({
                "locale": locale,
                "showDropdowns": true,
                "linkedCalendars": false,
                "timePicker": true,
                "timePickerIncrement": 1,
                "timePicker24Hour": true,
                "minDate": moment().subtract(0, "days"),//限定不可选当前日期之前的日期
                "drops": "up"
            }, function (start, end, label) {
                let startTime, endTime;
                if ((start - end) == 0) {
                    let _end = new Date(end);
                    let year = _end.getFullYear();
                    let month = _end.getMonth();
                    let day = _end.getDate();
                    let hour = _end.getHours();
                    let min = _end.getMinutes();
                    let s = _end.getSeconds();
                    end = new Date(year, month, day, hour + 23, min + 59, s + 59);
                    startTime = start.format('YYYY-MM-DD HH:mm:ss');
                    endTime = end.format('YYYY-MM-dd hh:mm:ss');
                }
                else {
                    startTime = start.format('YYYY-MM-DD HH:mm:ss');
                    endTime = end.format('YYYY-MM-DD HH:mm:ss');
                }
                $("#schedulingDate").val(startTime + ' - ' + endTime);
                $("#hidStartTime").val(startTime);
                $("#hidEndTime").val(endTime);
            });
            $("#schedulingDate").val('');
        };

        let showScheduling = function (obj) {
            $("#hidID").val(obj);
            initCalendar(obj);
            $('#calendar').fullCalendar('refetchEvents');
        };
    </script>
</th:block>
</body>
</html>
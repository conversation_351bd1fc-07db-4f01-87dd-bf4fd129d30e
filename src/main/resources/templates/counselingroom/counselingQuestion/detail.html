<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .image img { width: 100%;}
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理答疑</a></li>
                    </ol>
                </div>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="card cta-box bg-primary text-white">
        <div class="card-body">
            <div class="media align-items-center">
                <div class="media-body">
                    <h3>在线心理答疑社区</h3>
                    <span>有困惑？请说出来 </span>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a th:href="@{/counselingroom/counselingquestion/post}" title="立即提问" class="btn btn-outline-light  btn-rounded btn-sm"><i class="fa fa-plus"></i> 立即提问</a>
                </div>
                <img class="ml-3" th:src="@{/static/images/email-campaign.svg}" width="120" alt="在线心理答疑社区">
            </div>
        </div>
        <!-- end card-body -->
    </div>
    <div class="row">
        <div class="col-md-12">
            <!-- project card -->
            <div class="card d-block">
                <div class="card-body">
                    <th:block th:if="${question.questioner eq user.userId}">
                        <div class="dropdown float-right">
                            <a href="#" class="dropdown-toggle arrow-none card-drop" data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-ellipsis-v font-14"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right" x-placement="bottom-end" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(-142px, 20px, 0px);">
                                <!-- item-->
                                <a th:href="@{/counselingroom/counselingquestion/update(id=${question.id})}" class="dropdown-item"><i class="fa fa-pencil-square-o mr-1"></i>修改</a>
                                <!-- item-->
                                <a href="#" class="dropdown-item" th:onclick="|del(${question.id})|" ><i class="fa fa-trash-o mr-1"></i>删除</a>
                            </div>
                        </div>
                    </th:block>
                    <!-- project title-->
                    <h4 class="mt-0" th:text="${question.title}"></h4>
                    <p class="text-muted mb-2 q-content" th:utext="${question.content}"></p>
                    <th:block th:if="${question.img eq null or question.img eq ''}"></th:block>
                    <th:block th:unless="${question.img eq null or question.img eq ''}">
                        <img class="img-fluid" th:src="|@{/static/upload/counseling/thumbnail/small/}${question.img}|" />
                    </th:block>
                </div> <!-- end card-body-->

            </div> <!-- end card-->
            <div class="card">
                <div class="card-body">
                    <h4 class="mt-0 mb-3">回复 (<th:block th:text="${question.respondCount}"></th:block>)</h4>
                    <div id="editor_content"></div>
                    <div class="text-right mt-3">
                        <div class="btn-group mb-2 ml-2">
                            <a th:href="@{/counselingroom/counselingquestion/index}" class="btn btn-link">返回</a>
                            <input type="button" class="btn btn-primary btn-sm" id="btnPost" value="发表">
                        </div>
                    </div>
                    <th:block th:if="${not #lists.isEmpty(question.listResponds) and question.listResponds.size() gt 0}">
                        <th:block th:each="respond:${question.listResponds}">
                            <div class="media mt-2 border-bottom-1">
                                <img class="mr-3 avatar-sm rounded-circle" th:if="${respond.headPic eq null or respond.headPic eq ''}" th:src="@{/static/images/user.png}" />
                                <img class="mr-3 avatar-sm rounded-circle" th:unless="${respond.headPic eq null or respond.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${respond.headPic}|" />
                                <div class="media-body">
                                    <h5 class="mt-0"><th:block th:text="${respond.responderLoginName}"></th:block><small class="pull-right" th:text="${#dates.format(respond.addDate, 'yyyy-MM-dd HH:mm:ss')}"></small></h5>
                                    <th:block th:utext="${respond.content}"></th:block>
                                    <th:block th:if="${canDelRespond eq true}">
                                        <br><a href="javascript:" th:onclick="|delRespond(${respond.id})|">删除</a>
                                    </th:block>
                                </div>
                            </div>
                        </th:block>
                    </th:block>
                    <th:block th:unless="${not #lists.isEmpty(question.listResponds) and question.listResponds.size() gt 0}">
                        <div class="text-center">
                            <img th:src="@{/static/images/nocomment.png}" width="120" />
                            <h5 class="font-weight-normal">暂无回复！</h5>
                        </div>
                    </th:block>
                </div> <!-- end card-body-->
            </div>
            <!-- end card-->
        </div> <!-- end col -->
    </div>
    <!-- end row -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script type="text/javascript">
        let editor_content;
        let questionId = getUrlParam('id');
        $(function () {
            ClassicEditor
                .create(document.querySelector('#editor_content'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_content = editor;
                });
            $("#btnPost").click(function () {
                let postContent = editor_content.getData();
                if (postContent == "") {
                    layer.msg("请输入要回复的内容！");
                    return;
                }
                let jsonObj = { "content": postContent, "questionId": questionId };
                $.ajax({
                    type: 'POST',
                    url: '/counselingroom/counselingquestion/add_respond',
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        $("#btnPost").val("保存");
                        $("#btnPost").attr("Disabled", false);
                        if (res.resultCode === 200) {
                            location.reload();
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }
                });
            });
        });
        function del(id) {
            layer.confirm('确定关闭问题吗？', {
                time: 0,
                icon: 7,
                btn: ['确定', '取消'],
                yes: function (index) {
                    $.post("/counselingroom/counselingquestion/delete", 'id=' + questionId, function (res) {
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            location.href = '/counselingroom/counselingquestion/index';
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }, 'json');
                }
            });
        };
        function delRespond(id) {
            layer.confirm('确定删除吗？', {
                time: 0,
                icon: 7,
                btn: ['确定', '取消'],
                yes: function (index) {
                    $.post("/counselingroom/counselingquestion/del_respond", 'id=' + id, function (res) {
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            location.reload();
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }, 'json');
                }
            });
        };
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理答疑</a></li>
                    </ol>
                </div>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="card cta-box bg-primary text-white">
        <div class="card-body">
            <div class="media align-items-center">
                <div class="media-body">
                    <h3>在线心理答疑社区</h3>
                    <span>有困惑？请说出来 </span>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a th:href="@{/counselingroom/counselingquestion/post}" title="立即提问" class="btn btn-outline-light  btn-rounded btn-sm"><i class="fa fa-plus"></i> 立即提问</a>
                </div>
                <img class="ml-3" th:src="@{/static/images/email-campaign.svg}" width="120" alt="在线心理答疑社区">
            </div>
        </div>
        <!-- end card-body -->
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <h4 class="mb-3 card-header bg-light"><i class="fa fa-question-circle-o mr-1"></i>修改提问</h4>
                <div class="card-body">
                    <form id="frmQuestion" class="form-horizontal">
                        <div class="form-group mb-3">
                            <select class="form-control select2" data-toggle="select2" name="counselingType" id="counselingType">
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <input id="title" name="title" class="form-control" type="text" placeholder="一句话完整描述你的问题，限制50字" maxlength="50" autocomplete="off">
                        </div>
                        <div class="form-group mb-3">
                            <label class="font-weight-normal">详细说明问题，以便获得更好的回答。</label>
                            <div id="editor_content"></div>
                        </div>
                        <div class="form-group mb-3">
                            <label>是否匿名</label>
                            <div class="custom-control custom-switch pl-0">
                                <input type="checkbox" id="isAnonymous" data-switch="danger" />
                                <label for="isAnonymous" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="form-group mb-3 hide" id="div-img">
                            <img src="/static/images/nopic.png" class="img-fluid" id="img" />
                        </div>
                        <div class="form-group form-inline">
                            <label>上传图片：</label>
                            <input type="file" name="file" id="txt_file" class="file-loading ml-1" />
                            <input id="hidImg" type="hidden" value="" />
                        </div>
                        <div class="form-group">
                            <input type="submit" id="btnSave" class="btn btn-primary mr-1" value="提问" />
                            <a th:href="@{/counselingroom/counselingquestion/index}" class="btn btn-link">返回</a>
                        </div>
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        let questionId = getUrlParam('id');
        let editor_content;
        let init = function () {
            $.get("/counselingroom/counselingquestion/get?questionId=" + questionId, "", function (res){
                $("#counselingType").val(res.counselingTypeId).trigger('change');
                $("#title").val(res.title);
                editor_content.setData(res.content);
                if (res.isAnonymous === 1) {
                    $("#isAnonymous").attr("checked", true);
                }
                else {
                    $("#isAnonymous").attr("checked", false);
                }
                if(res.img != undefined && res.img != ''){
                    $("#hidImg").val(res.img);
                    $("#div-img").removeClass('hide').addClass('show');
                    $("#img").attr('src','/static/upload/counseling/thumbnail/small/'+res.img);
                }
            });
        };
        let fileInput = function () {
            let oFile = {};
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=counseling',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-primary", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidImg").val(res.resultMsg);
                        layer.open({
                            content: '<img src="/static/images/success.png" width="25" class="mr-1"/>上传成功'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                    else {
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                });
            };
            return oFile;
        };
        $(function () {
            initSelect("#counselingType", "/counselingroom/counselingtype/get_for_select", "","","选择问题类型");
            ClassicEditor
                .create(document.querySelector('#editor_content'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_content = editor;
                });
            let oFileInput = new fileInput();
            oFileInput.Init("txt_file");
            init();
            $("#frmQuestion").validate({
                rules: {
                    counselingType: { required: true },
                    title: { required: true }
                },
                messages: {
                    counselingType: { required: "请选择问题类型" },
                    title: { required: "请完整描述你的问题" }
                },
                submitHandler: function () {
                    let jsonObj = {
                        "id": questionId,
                        "counselingTypeId": $("#counselingType").val(),
                        "title": $.trim($("#title").val()),
                        "content": editor_content.getData(),
                        "isAnonymous": $("#isAnonymous").prop("checked") ? 1 : 0,
                        "img":$("#hidImg").val()
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/counselingquestion/update',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    location.href = "/counselingroom/counselingquestion/detail?id=" + questionId;
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
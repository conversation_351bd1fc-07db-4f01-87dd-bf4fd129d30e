<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/homepage/css/pagination.css}" rel="stylesheet" />
    <style type="text/css">
        .forum-post-container .media {
            margin: 10px 10px 10px 10px;
            padding: 20px 10px 20px 10px;
            border-bottom: 1px solid #f1f1f1;
        }

        .forum-avatar {
            float: left;
            margin-right: 20px;
            text-align: center;
            width: 110px;
        }

        .forum-avatar .img-circle {
            height: 48px;
            width: 48px;
        }

        .author-info {
            color: #676a6c;
            font-size: 11px;
            margin-top: 5px;
            text-align: center;
        }

        .forum-post-info {
            padding: 9px 12px 6px 12px;
            border-bottom: 1px solid #f1f1f1;
        }

        .media-body > .media {
            background: #f9f9f9;
            border-radius: 3px;
            border: 1px solid #f1f1f1;
        }

        .forum-post-container .media-body .photos {
            margin: 10px 0;
        }

        .forum-photo {
            max-width: 140px;
            border-radius: 3px;
        }

        .media-body > .media .forum-avatar {
            width: 70px;
            margin-right: 10px;
        }

        .mid-icon {
            font-size: 66px;
        }

        .forum-item {
            margin: 10px 0;
            padding: 10px 0 20px;
            border-bottom: 1px solid #f1f1f1;
        }

        .forum-container,
        .forum-post-container {
            padding: 30px !important;
        }

        .forum-item small {
            color: #999;
        }

        .forum-item .forum-sub-title {
            color: #999;
            margin-left: 50px;
        }

        .forum-title {
            margin: 15px 0 15px 0;
        }

        .forum-info {
            text-align: center;
        }

        .forum-desc {
            color: #999;
        }

        .forum-icon {
            float: left;
            width: 30px;
            margin-right: 20px;
            text-align: center;
        }

        a.forum-item-title {
            color: inherit;
            display: block;
            font-size: 18px;
            font-weight: 600;
        }

        a.forum-item-title:hover {
            color: inherit;
        }

        .forum-icon .fa {
            font-size: 30px;
            margin-top: 8px;
            color: #9b9b9b;
        }

        .forum-item.active .fa {
            color: #1ab394;
        }

        .forum-item.active a.forum-item-title {
            color: #1ab394;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理答疑</a></li>
                    </ol>
                </div>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="card cta-box bg-primary text-white">
        <div class="card-body">
            <div class="media align-items-center">
                <div class="media-body">
                    <h3>在线心理答疑社区</h3>
                    <span>有困惑？请说出来 </span>&nbsp;&nbsp;&nbsp;&nbsp;
                    <a th:href="@{/counselingroom/counselingquestion/post}" title="立即提问" class="btn btn-outline-light  btn-rounded btn-sm"><i class="fa fa-plus"></i> 立即提问</a>
                </div>
                <img class="ml-3" th:src="@{/static/images/email-campaign.svg}" width="120" alt="在线心理答疑社区">
            </div>
        </div>
        <!-- end card-body -->
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body forum-post-container">
                    <div class="row">
                        <div class="col-lg-8">
                            <form class="form-inline">
                                <div class="form-group mb-1">
                                    <select class="form-control" name="counselingType" id="counselingType">
                                    </select>
                                </div>
                                <div class="form-group mb-1">
                                    <input class="form-control text-left" type="text" name="title" id="title" autocomplete="off" placeholder="问题描述……" />
                                </div>
                                <button class="btn btn-primary mb-1" id="btnQuery" type="button"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <a class="btn btn-outline-info text-info mr-1" th:href="@{/counselingroom/counselingquestion/my_questions}">我的提问</a>
                                <a class="btn btn-outline-info text-info" th:href="@{/counselingroom/counselingquestion/my_responds}">我的回答</a>
                            </div>
                        </div>
                    </div>
                    <div class="text-center norecord hide">
                        <img th:src="@{/static/images/nodata.png}" width="300" />
                        <h4 class="font-weight-normal">没有记录！</h4>
                    </div>
                    <div class="list-question hide">
                        <div class="forum-post-info">

                        </div>
                        <div id="list"></div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="pagination-area mb-30 wow fadeInUp animated pull-right">
                        <nav aria-label="Page navigation example">
                            <ul class="pagination justify-content-start">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/homepage/js/vendor/jquery.pagination.js}"></script>
    <script type="text/javascript">
        let totalCount;
        let pageSize = 10;
        let pageInit = function () {
            $('.pagination').pagination({
                totalData: totalCount,
                showData: pageSize,
                coping: false,
                keepShowPN: true,
                mode: 'fixed',
                count: 4,
                activeCls: 'active',
                callback: function (res) {
                    initList(res.getCurrent() - 1);
                }
            });
        };
        let initList = function (pageIndex) {
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            $.ajax({
                type: 'POST',
                async: false,
                url: '/counselingroom/counselingquestion/list',
                data: JSON.stringify(getQueryCondition(pageIndex)),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    if (res.data.length > 0) {
                        totalCount = res.recordsTotal;
                        loadData(res.data);
                        $(".list-question").removeClass("hide").addClass("show");
                    }
                    else {
                        $(".norecord").removeClass("hide").addClass("show");
                        $(".card-footer").hide();
                    }
                }
            });
        };
        let loadData = function (data) {
            if (data.length > 0) {
                let wrapper = $("#list");
                let str = '';
                for (let i = 0; i < data.length; i++) {
                    str += '<div class="media">';
                    let isAnonymous = data[i].isAnonymous;
                    let headPic;
                    let questioner;
                    if (isAnonymous === 1) {
                        headPic = "/static/images/user.png";
                        questioner = "匿名";
                    }
                    else {
                        headPic = data[i].headPic == '' ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + data[i].headPic;
                        questioner =  data[i].questionerLoginName;
                    }
                    str += '<a class="forum-avatar" href="#"><img src="' + headPic + '" class="rounded-circle img-circle" alt="image">';
                    str += '<div class="author-info">';
                    str += '<strong>' + questioner + '</strong></div></a>';
                    str += '<div class="media-body">';
                    str += '<h5 class="media-heading text-primary font-weight-normal"><a href="/counselingroom/counselingquestion/detail?id=' + data[i].id + '">' + data[i].title + '</a></h5>';
                    str += '<h5 class="font-weight-normal font-13">' + data[i].addDate + '<i class="fa fa-comments-o ml-2 mr-1"></i>' + data[i].respondCount + '</h5>';
                    str += '' + data[i].content.length > 100 ? data[i].content.substring(0, 100) + '…' : data[i].content + '';
                    str += '</div>';
                    str += '</div>';
                }
                wrapper.empty();
                wrapper.append(str);
            }
        };
        let getQueryCondition = function (pageIndex) {
            let jsonObj = {};
            jsonObj.counselingTypeId = $("#counselingType").val();
            jsonObj.title = $("#title").val();
            jsonObj.isMyResponds = 1;
            jsonObj.pageIndex = pageIndex * pageSize;
            jsonObj.pageSize = pageSize;
            return jsonObj;
        };
        $(function () {
            initSelect("#counselingType", "/counselingroom/counselingtype/get_for_select", "", "", "选择问题类型");
            initList(0);
            pageInit();
            $("#btnQuery").click(function () {
                initList(0);
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/fullcalendar/fullcalendar.min.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">预约咨询</a></li>
                    </ol>
                </div>
                <h4 class="page-title">预约咨询</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-4">
                            <h4><i class="fa fa-calendar-check-o mr-1"></i>预约咨询</h4>
                        </div>
                        <div class="col-lg-8">
                            <div class="text-lg-right">
                                <button id="btnMyOrder" class="btn btn-success btn-sm">我的预约记录</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-9">
                            <div id="calendar"></div>
                        </div>
                        <div class="col-lg-3">
                            <h4 class="header-title mb-2 mt-3 font-weight-normal"><i class="fa fa-user-md mr-1"></i>咨询师列表</h4>
                            <div class="counselor-wrapper"></div>

                            <div class="mt-3 d-xl-block">
                                <h4 class="header-title text-left font-weight-normal"><i class="fa fa-info-circle"></i> 操作说明</h4>
                                <ul class="pl-2 list-unstyled">
                                    <li class="text-muted mb-2">
                                        排班表默认显示当天的咨询师排班信息，可以按月、周、天切换查看。
                                    </li>
                                    <li class="text-muted mb-2">
                                        双击排班表中的咨询师排班可以添加预约信息。
                                    </li>
                                    <li class="text-muted mb-2">
                                        <ul class="list-unstyled">
                                            <li><i class="fa fa-circle mr-1 text-secondary" aria-hidden="true"></i>表示该次排班时间已经过期不可预约；</li>
                                            <li><i class="fa fa-circle mr-1 text-success" aria-hidden="true"></i>表示可以预约；</li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- modal.预约咨询 start -->
    <div id="order-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title"><i class="fa fa-calendar-check-o mr-1"></i>预约咨询</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-4">
                    <form id="frmOrder" class="form-horizontal">
                        <div class="form-group row mb-3">
                            <label class="control-label col-md-3">咨询师</label>
                            <div class="col-md-9">
                                <span class="badge badge-info" id="counselor"></span>
                            </div>
                        </div>
                        <div class="form-group row mb-3 visitor">
                            <label class="control-label col-md-3">来访者账号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" id="visitor" name="visitor" autocomplete="off" placeholder="输入来访者账号" />
                                <input id="hidVisitorName" name="hidVisitorName" type="hidden" th:value="${user.loginName}" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="control-label col-md-3">选择咨询时间</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                    </div>
                                    <input type="text" class="form-control" value="" id="schedulingDate" name="schedulingDate" autocomplete="off" readonly>
                                </div>
                                <input type="hidden" id="hidStartTime" name="hidStartTime" value="" />
                                <input type="hidden" id="hidEndTime" name="hidEndTime" value="" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="control-label col-md-3" for="counselingWay">咨询方式</label>
                            <div class="col-md-9">
                                <select class="form-control" id="counselingWay" name="counselingWay">
                                    <option value="">请选择</option>
                                    <option value="1">网上咨询</option>
                                    <option value="2">面询</option>
                                    <option value="3">电话咨询</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="control-label col-md-3" for="counselingType">咨询问题类型</label>
                            <div class="col-md-9">
                                <select class="form-control" name="counselingType" id="counselingType">
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="control-label col-md-3" for="description">咨询问题描述</label>
                            <div class="col-md-9">
                                <textarea class="form-control" name="description" id="description" rows="5" placeholder="您想咨询哪些方面的问题，请尽可能详细的说明。字数限制在100字以内。" maxlength="100"></textarea>
                            </div>
                        </div>
                        <div class="form-group row mb-3 selfComment">
                            <label class="control-label col-md-3" for="selfComment">自我分析</label>
                            <div class="col-md-9">
                                <textarea class="form-control" name="selfComment" id="selfComment" rows="5" placeholder="请列出您对自己现在面临问题的原因是怎么看的，近期是否遇到重大人生事件等。字数限制在100字以内。 " maxlength="100"></textarea>
                            </div>
                        </div>
                        <div class="form-group row mb-3 isPoint">
                            <label class="control-label col-md-3">是否重点个案</label>
                            <div class="col-md-9 form-inline">
                                <div class="custom-control custom-radio mr-2">
                                    <input type="radio" id="isPoint-yes" name="isPoint" class="custom-control-input" value="1">
                                    <label class="custom-control-label" for="isPoint-yes">是</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" id="isPoint-no" name="isPoint" class="custom-control-input" value="0" checked>
                                    <label class="custom-control-label" for="isPoint-no">否</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="control-label col-md-3" for="counselingItem">咨询类型</label>
                            <div class="col-md-9">
                                <select class="form-control" name="counselingItem" id="counselingItem">
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <div class="col-9 offset-3">
                                <input type="submit" class="btn btn-primary btn-sm" value="保存" id="btnSave">
                                <button type="button" class="btn btn-link btn-sm" data-dismiss="modal">取消</button>
                                <input type="hidden" id="hidID" value="0" />
                                <input type="hidden" id="hidUserID" value="0" />
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.预约咨询 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/fullcalendar/fullcalendar.min.js}"></script>
    <script th:src="@{/static/js/plugins/fullcalendar/fullcalendar-zh-cn.js}"></script>
    <script type="text/javascript">
        $(function () {
            init();
            $("#btnMyOrder").click(function () {
                location.href = "/counselingroom/counseling/myorder";
            });
            $("#visitor").on('blur', function () {
                let loginName = $.trim($("#visitor").val());
                if (loginName === "") return;
                let url = "/counselingroom/counseling/check_visitor";
                $.post(url, { loginName: loginName, schedulingId: $("#hidID").val() }, function (res) {
                    if (res.resultCode === 201) {
                        $("#hidVisitorName").val('');
                        layer.msg(res.resultMsg, { icon: 0, time: 2000 });
                    }
                    else {
                        $("#hidUserID").val(res.resultMsg);
                        $("#hidVisitorName").val(loginName);
                        $("#hidVisitorName-error").hide();
                    }
                });
            });
            $("#frmOrder").validate({
                ignore:[],
                rules: {
                    hidVisitorName: { required: true },
                    schedulingDate: { required: true },
                    counselingWay: { required: true },
                    counselingType: { required: true },
                    description: { required: true },
                    counselingItem: { required: true }
                },
                messages: {
                    hidVisitorName: { required: "请输入有效的来访者账号" },
                    schedulingDate: { required: "请选择咨询时间" },
                    counselingWay: { required: "请选择咨询方式" },
                    counselingType: { required: "请选择咨询问题类型" },
                    description: { required: "请填写咨询内容" },
                    counselingItem: { required: "请选择咨询类型" }
                },
                submitHandler: function () {
                    $("#btnSave").val("保存中……");
                    $("#btnSave").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.schedulingId = $("#hidID").val();
                    if ('[[${user.role.roleId}]]'=='3')
                        $("#hidUserID").val('[[${user.userId}]]');
                    jsonObj.visitorId = $("#hidUserID").val();
                    jsonObj.startTime = startTime;
                    jsonObj.endTime = endTime;
                    jsonObj.counselingTypeId = $("#counselingType").val();
                    jsonObj.counselingWay = $("#counselingWay").val();
                    jsonObj.isPoint = $('input[name="isPoint"]:checked').val();
                    jsonObj.selfComment = $("#selfComment").val();
                    jsonObj.description = $("#description").val();
                    jsonObj.counselingItemId = $("#counselingItem").val();
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/counseling/add_order',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, {
                                    icon: 1, yes: function (index) {
                                        $('#calendar').fullCalendar('refetchEvents');
                                        $("#order-modal").modal('hide');
                                    }
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
        let init = function () {
            initCalendar(0);
            getCounselorList();
        };
        let startTime, endTime;
        let initCalendar = function (obj) {
            /*------------ 初始化 calendar ------------*/
            $('#calendar').fullCalendar({
                timeFormat: 'HH:mm',
                defaultView: 'listWeek',
                header: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'month,agendaWeek,agendaDay,listWeek'
                },
                navLinks: true,
                editable: false,
                droppable: false,
                timezone: 'local',
                events: function (start, end, timezone, callback) {
                    let jsonObj = {};
                    if ($("#hidID").val() != "0")
                        jsonObj.counselorId = $("#hidID").val();
                    layer.msg('数据加载中…', {
                        icon: 17, shade: 0.05, time: false
                    });
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/scheduling/get_for_order_calendar',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll();
                            callback(res);
                        }
                    });
                },
                eventClick: function (eventObj) {
                    if (eventObj.color != '#0acf97') {
                        layer.msg("该时间段的排班已经过期或者已经有预约！", { icon: 0, time: 2000 });
                        return;
                    }
                    $("#hidID").val(eventObj.id);
                    $("#counselor").html(eventObj.title);
                    startTime = eventObj.start.format('YYYY-MM-DD HH:mm:ss');
                    endTime = eventObj.end.format('YYYY-MM-DD HH:mm:ss');
                    initDateRangePicker();
                    $("#schedulingDate").val('');
                    initSelect("#counselingType", "/counselingroom/counselingtype/get_for_select", "");
                    initSelect("#counselingItem", "/counselingroom/counselingitem/get_for_select", { schedulingId: eventObj.id });
                    if ('[[${user.role.roleId}]]' === '3') {
                        $(".visitor").hide();
                        $(".isPoint").hide();
                    }
                    else {
                        $(".selfComment").hide();
                    }
                    $("#modal-order-title").html("预约咨询");
                    $("#order-modal").modal();
                }
            });
        };
        let initDateRangePicker = function () {
            let locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " - ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            $('#schedulingDate').daterangepicker({
                "locale": locale,
                "showDropdowns": true,
                "linkedCalendars": false,
                "timePicker": true,
                "timePickerIncrement": 1,
                "timePicker24Hour": true,
                "minDate": startTime,
                "maxDate": endTime,
                "drops": "down"
            }, function (start, end, label) {
                let startTime, endTime;
                if ((start - end) === 0) {
                    let _end = new Date(end);
                    let year = _end.getFullYear();
                    let month = _end.getMonth();
                    let day = _end.getDate();
                    let hour = _end.getHours();
                    let min = _end.getMinutes();
                    let s = _end.getSeconds();
                    end = new Date(year, month, day, hour + 23, min + 59, s + 59);
                    startTime = start.format('YYYY-MM-DD HH:mm:ss');
                    endTime = end.format('YYYY-MM-dd hh:mm:ss');
                }
                else {
                    startTime = start.format('YYYY-MM-DD HH:mm:ss');
                    endTime = end.format('YYYY-MM-DD HH:mm:ss');
                }
                $("#schedulingDate").val(startTime + ' - ' + endTime);
                $("#hidStartTime").val(startTime);
                $("#hidEndTime").val(endTime);
            });
        };
        let getCounselorList = function () {
            $.post("/anteroom/user/getRecommendCounselorList", {}, function (res) {
                if (res.length > 0) {
                    let wrapper = $(".counselor-wrapper");
                    let users_list = "";
                    for (let i = 0; i < res.length; i++) {
                        let headPic = res[i].headPic == '' ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + res[i].headPic;
                        users_list += '<div class="inbox-widget"><div class="inbox-item"><div class="inbox-item-img">';
                        users_list += '<img src="' + headPic + '" class="rounded-circle" alt=""></div>';
                        users_list += '<p class="inbox-item-author">' + res[i].realName + '</p>';
                        users_list += '<p class="inbox-item-date"><a href="javascript:" onclick="showScheduling(' + res[i].UserId + ')" class="btn btn-sm btn-link text-info font-13">排班信息</a></p>'
                        users_list += '</div></div>';
                    }
                    users_list += '<div class="text-center mt-2"><a href="javascript:void(0);" onclick="showScheduling(0)" class="btn btn-outline-danger btn-sm"> 查看全部排班信息 </a></div>';
                    wrapper.empty();
                    wrapper.append(users_list);
                }
            });
        };
        let showScheduling = function (obj) {
            $("#hidID").val(obj);
            initCalendar(obj);
            $('#calendar').fullCalendar('refetchEvents');
        };
    </script>
</th:block>
</body>
</html>
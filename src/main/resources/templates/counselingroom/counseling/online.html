<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">在线咨询</a></li>
                    </ol>
                </div>
                <h4 class="page-title">在线咨询</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-md-12">
            <div class="bg-light-lighten p-2 p-lg-4 border border-light">
                <h5 class="mt-0"><i class="fa fa-comments-o mr-1"></i>咨询室</h5>
                <div id="order-list" class="py-2">
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script type="text/javascript">
        let currentUserRole = [[${user.role.roleId}]];
        let currentUserId = [[${user.userId}]];
        $(function(){
            init();
        });
        let init = function () {
            let jsonObj = {};
            jsonObj.state = 1;
            jsonObj.visitorId = (currentUserRole === 3 || currentUserRole ===4)?currentUserId: 0;
            jsonObj.counselorId = (currentUserRole !== 3 && currentUserRole !==4) ? currentUserId : 0 ;
            $.ajax({
                type: 'POST',
                url: '/counselingroom/counseling/online_order',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    let wrapper = $("#order-list");
                    let orderList = "";
                    if (res.length > 0) {
                        for (let i = 0; i < res.length; i++) {
                            orderList += '<div class="card mb-0 mt-2">';
                            orderList += '<div class="card-body">';
                            orderList += '<div class="media">';
                            let headPic = res[i].counselorHeadPic === "" ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + res[i].counselorHeadPic;
                            orderList += '<img src="' + headPic+'" alt="" class="mr-3 d-sm-block avatar-sm rounded-circle">'
                            orderList += '<div class="media-body">';
                            orderList += '<h5 class="mb-2 mt-0"><span class="font-weight-normal">' + res[i].counselorName + '</span></h5>';
                            orderList += '<p class="text-muted"><i class="fa fa-clock-o mr-1"></i>' +res[i].startTime + ' - ' +res[i].endTime+'</p>';
                            orderList += '<p class="mb-0 text-muted">'
                            orderList += '<span class="font-italic"></span>';
                            orderList += '</p>';
                            orderList += "<button class='btn btn-outline-danger mt-2' type='button' onclick='start(" + JSON.stringify(res[i]) + ")'>进入咨询</button>";
                            orderList += '</div>';
                            orderList += '</div>';
                            orderList += '</div>';
                            orderList += '</div>';
                        }
                    }
                    else {
                        orderList += '<div class="alert text-danger"><i class="fa fa-info-circle mr-1"></i>当前时间内没有任何可以进行咨询的预约。前往<a href="/counselingroom/counseling/order" class="btn btn-outline-primary btn-sm ml-2">预约咨询</a></div >';
                    }
                    wrapper.empty();
                    wrapper.append(orderList);
                }
            });
        };
        let start = function (obj) {
            if (obj.startTime > getDateNowFormat()) {
                layer.msg('咨询开始时间未到，请耐心等待！', { icon: 0, time: 2000 });
                return;
            }
            let isOrder;
            if (currentUserRole === 3) {
                if (currentUserId != obj.visitorId) {
                    isOrder = false;
                }
                else {
                    isOrder = true;
                }
            }
            else {
                if (currentUserId != obj.counselorId) {
                    isOrder = false;
                }
                else {
                    isOrder = true;
                }
            }
            if (!isOrder) {
                layer.msg('信息验证失败，您不是该预约的拥有者！', { icon: 0, time: 2000 });
            }
            else {
                location.href = "/counselingroom/counseling/online_counseling?id=" + obj.id;
            }
        };
    </script>
</th:block>
</body>
</html>
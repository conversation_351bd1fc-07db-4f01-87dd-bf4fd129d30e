<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">咨询记录</a></li>
                    </ol>
                </div>
                <h4 class="page-title">咨询记录</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mr-2">
                                    <i class="fa fa-search mr-1"></i>搜索：
                                </div>
                                <div class="form-group mr-2">
                                    <select id="sr-counselingType" name="sr-counselingType" class="form-control">
                                    </select>
                                </div>
                                <div class="form-group">
                                    <button type="button" class="btn btn-primary mr-1" id="btnSearch"><i class="fa fa-search"></i></button>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" class="btn btn-success" type="button"><i class="fa fa-plus mr-1"></i>添加</button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="tbOrderList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th align="left">咨询者</th>
                                <th align="left">咨询师</th>
                                <th align="left">咨询方式</th>
                                <th align="left">问题类型</th>
                                <th align="left">咨询类型</th>
                                <th align="left">开始时间</th>
                                <th align="left">结束时间</th>
                                <th align="left"></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.添加咨询记录 start -->
    <div id="order-modal" class="modal fade" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title"><i class="fa fa-plus mr-1"></i>添加咨询记录</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="frmOrder" class="form-horizontal">
                    <div class="modal-body p-4">
                        <div class="form-group mb-3">
                            <label class="control-label">咨询师</label>
                            <select class="form-control select2" data-toggle="select2" name="counselor" id="counselor">
                            </select>
                        </div>
                        <div class="form-group mb-3 visitor">
                            <label class="control-label">来访者</label>
                            <input type="text" class="form-control" id="visitor" name="visitor" autocomplete="off" placeholder="输入来访者账号" />
                            <input id="hidVisitorName" name="hidVisitorName" type="hidden" value="" />
                        </div>
                        <div class="form-group mb-3">
                            <label class="control-label">选择咨询时间</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                </div>
                                <input type="text" class="form-control" value="" id="schedulingDate" name="schedulingDate" autocomplete="off" readonly>
                            </div>
                            <input type="hidden" id="hidStartTime" name="hidStartTime" value="" />
                            <input type="hidden" id="hidEndTime" name="hidEndTime" value="" />
                        </div>
                        <div class="form-group mb-3">
                            <label class="control-label">咨询方式</label>
                            <select class="form-control" id="counselingWay" name="counselingWay">
                                <option value="">请选择</option>
                                <option value="1">网上咨询</option>
                                <option value="2">面询</option>
                                <option value="3">电话咨询</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label class="control-label">咨询问题类型</label>
                            <select class="form-control select2" data-toggle="select2" name="counselingType" id="counselingType">
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label class="control-label">咨询问题描述</label>
                            <textarea class="form-control" name="description" id="description" rows="5" placeholder="简单描述来访者的咨询问题，限制100字以内。" maxlength="100"></textarea>
                        </div>
                        <div class="form-group mb-3">
                            <label class="control-label">咨询内容</label>
                            <div id="editor_content" class="ignore"></div>
                        </div>
                        <div class="form-group mb-3">
                            <label class="control-label">是否重点个案</label>
                            <div class="form-inline">
                                <input type="checkbox" id="isPoint" data-switch="danger" />
                                <label for="isPoint" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label class="control-label">咨询类型</label>
                            <select class="form-control" name="counselingItem" id="counselingItem">
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link btn-sm" data-dismiss="modal">取消</button>
                        <input type="submit" class="btn btn-primary btn-sm" value="保存" id="btnSave">
                        <input type="hidden" id="hidID" value="0" />
                        <input type="hidden" id="hidUserID" value="0" />
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- modal.添加咨询记录 end -->
    <!-- modal.查看咨询记录 start-->
    <div id="handle-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title"><i class="fa fa-comments-o mr-1"></i>咨询记录</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <ul class="list-unstyled p-3" id="history-message-list">
                </ul>
            </div>
        </div>
    </div>
    <!-- modal.查看咨询记录 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script type="text/javascript">
        let editor_content;
        let initForm = function () {
            initSelect("#counselor", "/anteroom/user/getCounselorList_for_select", {});
            initSelect("#counselingType", "/counselingroom/counselingtype/get_for_select", "");
            initDateRangePicker();
            $("#schedulingDate").val('');
        };
        $(function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            ClassicEditor
                .create(document.querySelector('#editor_content'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_content = editor;
                });
            $("#schedulingDate").val('');
            initSelect("#sr-counselingType", "/counselingroom/counselingtype/get_for_select", "", "", "选择咨询问题类型");
            $("#btnAdd").click(function () {
                initForm();
                $("#order-modal").modal();
            });
            $("#counselor").change(function () {
                initSelect("#counselingItem", "/counselingroom/counselingitem/get_for_select_by_counselor", { counselorId: $("#counselor").val() });
            });
            $("#visitor").on('blur', function () {
                let loginName = $.trim($("#visitor").val());
                if (loginName === "") return;
                let url = "/counselingroom/counseling/check_visitor";
                $.post(url, { loginName: loginName, schedulingId: $("#hidID").val() }, function (data) {
                    res = JSON.parse(data);
                    if (res.resultCode === 201) {
                        $("#hidVisitorName").val('');
                        $("#visitor").val('');
                        layer.msg(res.resultMsg, { icon: 0, time: 2000 });
                        return;
                    }
                    else {
                        $("#hidUserID").val(res.resultMsg);
                        $("#hidVisitorName").val(loginName);
                        $("#hidVisitorName-error").hide();
                    }
                });
            });
            $("#frmOrder").validate({
                rules: {
                    counselor: { required: true },
                    visitor: { required: true },
                    schedulingDate: { required: true },
                    counselingWay: { required: true },
                    counselingType: { required: true },
                    description: { required: true },
                    counselingItem: { required: true }
                },
                messages: {
                    counselor: { required: "请选择咨询师" },
                    visitor: { required: "请输入有效的来访者账号" },
                    schedulingDate: { required: "请选择咨询时间" },
                    counselingWay: { required: "请选择咨询方式" },
                    counselingType: { required: "请选择咨询问题类型" },
                    description: { required: "请填写问题描述" },
                    counselingItem: { required: "请选择咨询类型" }
                },
                submitHandler: function () {
                    $("#btnSave").val("保存中……");
                    $("#btnSave").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.counselorId = $("#counselor").val();
                    jsonObj.visitorId = $("#hidUserID").val();
                    jsonObj.startTime = $("#hidStartTime").val();
                    jsonObj.endTime = $("#hidEndTime").val();
                    jsonObj.counselingTypeId = $("#counselingType").val();
                    jsonObj.counselingWay = $("#counselingWay").val();
                    jsonObj.isPoint = $("#isPoint").prop("checked") ? 1 : 0;;
                    jsonObj.description = $("#description").val();
                    jsonObj.counselingItemId = $("#counselingItem").val();
                    let counselingContent = {};
                    counselingContent.counselingContent = editor_content.getData();
                    let listCounselingContents = [];
                    listCounselingContents.push(counselingContent);
                    jsonObj.counselingContent = listCounselingContents;
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/counseling/add_counseling_record',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                $("#order-modal").modal('hide');
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            //datatables
            $("#tbOrderList").bsDataTables({
                columns: columns,
                url: '/counselingroom/counseling/counseling_record',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //查看消息记录
            $("#tbOrderList").on('click', '.handle', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let wrapper = $("#history-message-list");
                let msg_str = "";
                if (data.counselingContent.length === 0) {
                    msg_str += '<i class="fa fa-info-circle mr-1 ml-5"></i>暂时查不到咨询记录！';
                }
                else {
                    $.each(data.counselingContent, function (i, item) {
                        msg_str += '<li class="clearfix">';
                        msg_str += '<div class="conversation-text"><div class="ctext-wrap"><div class="text-muted"><span class="text-primary mr-2">' + item.realName + '</span> <span class="font-weight-light">' + item.sendTime+ '</span></div><p class="text-justify font-12">' + item.counselingContent + '</p></div></div>';
                        msg_str += '</li>';
                    });
                }
                wrapper.empty();
                wrapper.append(msg_str);
                $("#handle-modal").modal();
            });
            $("#btnSearch").click(function () {
                oTable.draw();
            });
        });
        let initDateRangePicker = function () {
            let locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " - ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            $('#schedulingDate').daterangepicker({
                "locale": locale,
                "showDropdowns": true,
                "linkedCalendars": false,
                "timePicker": true,
                "timePickerIncrement": 1,
                "timePicker24Hour": true,
                "drops": "right"
            }, function (start, end, label) {
                let startTime, endTime;
                if ((start - end) === 0) {
                    let _end = new Date(end);
                    let year = _end.getFullYear();
                    let month = _end.getMonth();
                    let day = _end.getDate();
                    let hour = _end.getHours();
                    let min = _end.getMinutes();
                    let s = _end.getSeconds();
                    end = new Date(year, month, day, hour + 23, min + 59, s + 59);
                    startTime = start.format('YYYY-MM-DD HH:mm:ss');
                    endTime = end.format('YYYY-MM-dd hh:mm:ss');
                }
                else {
                    startTime = start.format('YYYY-MM-DD HH:mm:ss');
                    endTime = end.format('YYYY-MM-DD HH:mm:ss');
                }
                $("#schedulingDate").val(startTime + ' - ' + endTime);
                $("#hidStartTime").val(startTime);
                $("#hidEndTime").val(endTime);
            });
        };
        let columns = [
            { "data": "visitorName", "bSortable": false },
            { "data": "counselorName", "bSortable": false },
            { "data": "counselingWay", "render":
                    function (data, type, full, meta) {
                        let labels = "";
                        if (data === 1)
                            labels += "网上咨询";
                        if (data === 2)
                            labels += "面询";
                        if (data === 3)
                            labels += "电话咨询";
                        return labels;
                    }, "bSortable": false
            },
            { "data": "counselingType", "bSortable": false },
            { "data": "counselingItemName", "bSortable": false },
            { "data": "startTime", "bSortable": false},
            { "data": "endTime", "bSortable": false},
            { "data": "state", "bSortable": false,
                render: function (data, type, row, meta) {
                    return '<span class="badge badge-info badge-pill cursor-pointer handle">咨询记录</span>';
                }
            }];
        let columnDefs = [];
        let getQueryCondition = function (data) {
            let param = {};
            param.counselingTypeId = $("#sr-counselingType").val();
            param.visitorId = ([[${user.role.roleId}]] === 3 || [[${user.role.roleId}]] === 4) ? [[${user.userId}]]: 0;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
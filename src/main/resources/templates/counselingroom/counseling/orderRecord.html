<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    }
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">预约记录管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">预约记录管理</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-12">
                            <form class="form-inline" action="">
                                <div class="form-group mr-2">
                                    <i class="fa fa-search mr-1"></i>搜索：
                                </div>
                                <div class="form-group mr-2">
                                    <select id="sr-counselingType" name="sr-counselingType" class="form-control">
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <select id="sr-state" class="form-control">
                                        <option value="">选择预约状态</option>
                                        <option value="0">未处理</option>
                                        <option value="1">已接受</option>
                                        <option value="2">取消</option>
                                        <option value="3">已结束</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <button type="button" class="btn btn-primary mr-1" id="btnSearch"><i class="fa fa-search"></i></button>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="tbOrderList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th align="left">咨询者</th>
                                <th align="left">咨询师</th>
                                <th align="left">咨询方式</th>
                                <th align="left">问题类型</th>
                                <th align="left">咨询类型</th>
                                <th align="left">预约开始时间</th>
                                <th align="left">预约结束时间</th>
                                <th align="left">状态</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.查看及处理预约 start-->
    <div id="handle-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title"><i class="fa fa-check-square-o mr-1"></i>查看及处理预约</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="frmHandle" class="pl-2 pr-2 form-inline">
                    <div class="modal-body">
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">咨询师：</label>
                            <div class="col-8">
                                <span class="badge badge-outline-primary" id="counselor"></span>
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">来访者：</label>
                            <div class="col-8">
                                <span class="badge badge-outline-primary" id="visitor"></span>
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">咨询时间段：</label>
                            <div class="col-8">
                                <span id="counselingDate"></span>
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">咨询方式：</label>
                            <div class="col-8">
                                <input type="text" readonly class="form-control-plaintext" id="counselingWay" value="">
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">问题类型：</label>
                            <div class="col-8">
                                <input type="text" readonly class="form-control-plaintext" id="counselingType" value="">
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">咨询问题描述：</label>
                            <textarea class="form-control col-8" name="description" id="description" rows="5" readonly></textarea>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">自我分析：</label>
                            <textarea class="form-control col-8" name="selfComment" id="selfComment" rows="5" readonly></textarea>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">是否重点个案：</label>
                            <div class="form-inline col-8">
                                <div class="custom-control custom-radio mr-2">
                                    <input type="radio" id="isPoint-yes" name="isPoint" class="custom-control-input" value="是" readonly>
                                    <label class="custom-control-label" for="isPoint-yes">是</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" id="isPoint-no" name="isPoint" class="custom-control-input" value="否" checked readonly>
                                    <label class="custom-control-label" for="isPoint-no">否</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">咨询类型：</label>
                            <div class="col-8 p-0">
                                <select class="form-control" name="counselingItem" id="counselingItem" style="width:100%;">
                                </select>
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">处理状态：</label>
                            <div class="col-8 p-0">
                                <select id="state" name="state" class="form-control" style="width:100%;">
                                    <option value="">请选择</option>
                                    <option value="0">未处理</option>
                                    <option value="1">接受预约</option>
                                    <option value="2">取消预约</option>
                                    <option value="3">咨询已结束</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <label class="col-form-label col-4">处理意见：</label>
                            <textarea class="form-control col-8" name="handleInfo" id="handleInfo" rows="5" maxlength="200" placeholder="填写咨询师处理预约意见，限100字以内。"></textarea>
                        </div>
                        <div class="form-group mb-0 justify-content-end row">
                            <div class="col-8 offset-4">
                                <input type="submit" class="btn btn-primary " value="保存" id="btnSave">
                                <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                                <input type="hidden" id="hidID" value="0" />
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- modal.查看及处理预约 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        $(function () {
            initSelect("#sr-counselingType", "/counselingroom/counselingtype/get_for_select", "", "", "选择问题类型");
            //datatables
            $("#tbOrderList").bsDataTables({
                columns: columns,
                url: '/counselingroom/counseling/order_record',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //处理预约
            $("#tbOrderList").on('click', '.handle', function () {
                var data = oTable.row($(this).parents('tr')).data();
                $("#hidID").val(data.id);
                $("#counselor").html(data.counselorName);
                $("#visitor").html(data.visitorName);
                var startTime = data.startTime;
                var endTime = data.endTime;
                $("#counselingDate").html(startTime + ' 至 ' + endTime.substring(11,19));
                if (data.counselingWay ===1)
                    $("#counselingWay").val("网上咨询");
                if (data.counselingWay === 2)
                    $("#counselingWay").val("面询");
                if (data.counselingWay === 3)
                    $("#counselingWay").val("电话咨询");
                $("#counselingType").val(data.counselingType);
                if (data.isPoint === 1) {
                    $("#isPoint-yes").prop("checked", true);
                }
                else {
                    $("#isPoint-no").prop("checked", true);
                }
                $("#selfComment").val(data.selfComment);
                $("#description").val(data.description);
                initSelect("#counselingItem", "/counselingroom/counselingitem/get_for_select", { schedulingId: data.schedulingId }, data.counselingItemName);
                let now = getDateNowFormat();
                if (endTime < now) {
                    $("#isPoint-yes").attr('disabled', true);
                    $("#isPoint-no").attr('disabled', true);
                    $("#selfComment").attr('disabled', true);
                    $("#description").attr('disabled', true);
                    $("#counselingItem").attr('disabled', true);
                    $("#state").attr('disabled', true);
                    $("#handleInfo").attr('disabled', true);
                    $("#btnSave").attr('disabled', true);
                }
                $("#handle-modal").modal();
            });
            $("#frmHandle").validate({
                rules: {
                    state: { required: true }
                },
                messages: {
                    state: { required: "请选择处理状态" }
                },
                submitHandler: function () {
                    let jsonObj = {
                        id: $("#hidID").val(),
                        state: $("#state").val(),
                        handleInfo: $.trim($("#handleInfo").val()),
                        counselingItemId: $("#counselingItem").val()
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/counseling/update_order',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, { icon: 1, closeBtn: 0 }, function () {
                                    $("#handle-modal").modal('hide');
                                    oTable.draw();
                                    layer.closeAll();
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            $("#btnSearch").click(function () {
                oTable.draw();
            });
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") == true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });
        let resetForm = function () {

        };
        let columns = [
            { "data": "visitorName", "bSortable": false },
            { "data": "counselorName", "bSortable": false },
            { "data": "counselingWay", "render":
                    function (data, type, full, meta) {
                        let labels = "";
                        if (data === 1)
                            labels += "网上咨询";
                        if (data === 2)
                            labels += "面询";
                        if (data === 3)
                            labels += "电话咨询";
                        return labels;
                    }, "bSortable": false
            },
            { "data": "counselingType", "bSortable": false },
            { "data": "counselingItemName", "bSortable": false },
            { "data": "startTime",  "bSortable": false},
            {"data": "endTime", "bSortable": false},
            {"data": "state", "bSortable": false,
                render: function (data, type, row, meta) {
                    let label = "";
                    switch (data) {
                        case 0:
                            label += '<span class="badge badge-secondary-lighten badge-pill mr-1">未处理</span><span class="badge badge-info badge-pill cursor-pointer handle">查看及处理</span>';
                            break;
                        case 1:
                            label += '<span class="badge badge-success badge-pill mr-1">已接受</span><span class="badge badge-info badge-pill cursor-pointer handle">查看及处理</span>';
                            break;
                        case 2:
                            label += '<span class="badge badge-light badge-pill mr-1">已取消</span>';
                            break;
                        case 3:
                            label += '<span class="badge badge-danger badge-pill mr-1">已结束</span>';
                            break;
                    }
                    return label;
                }
            }];
        let columnDefs = [];
        let getQueryCondition = function (data) {
            let param = {};
            param.counselingTypeId = $("#sr-counselingType").val();
            param.state = $("#sr-state").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
    </script>
</th:block>
</body>
</html>
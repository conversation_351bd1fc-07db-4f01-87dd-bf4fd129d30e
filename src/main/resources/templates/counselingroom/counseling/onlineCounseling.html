<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">在线咨询</a></li>
                    </ol>
                </div>
                <h4 class="page-title">在线咨询</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-9">
            <!-- Chat-->
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3 font-weight-normal">
                        <i class="fa fa-comments-o mr-1"></i>咨询对话中
                        <span class="badge badge-danger-lighten bg-white pull-right font-weight-normal font-13" id="timer"></span>
                    </h4>
                    <div class="chat-conversation">
                        <ul class="conversation-list slimscroll border border-left p-3" style="min-height:350px; border-radius:5px;">
                        </ul>
                        <form name="chat-form" id="chat-form">
                            <div class="row">
                                <div class="col-12">
                                    <button id="btnHistory" type="button" class="btn btn-light btn-sm pull-right mb-2" data-toggle="modal" data-target="#history-modal"><i class="fa fa-commenting-o mr-1"></i>消息记录</button>
                                </div>
                                <div class="col-12">
                                    <textarea id="message" class="form-control" rows="4"></textarea>
                                </div>
                                <div class="col-12">
                                    <button id="btnClose" type="button" class="btn btn-outline-danger mt-3 pull-left"><i class="fa fa-power-off mr-1"></i>结束咨询</button>
                                    <button id="btnSend" type="button" class="btn btn-primary chat-send waves-effect waves-light mt-3 pull-right"><i class="fa fa-paper-plane-o mr-1"></i>发送（Enter）</button>
                                </div>
                            </div>
                        </form>
                        <input type="hidden" id="hidName" value="" />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title font-14 mb-2 font-weight-normal"><i class="fa fa-info-circle text-muted mr-1"></i>操作说明</h4>
                    <ul class="list-unstyled pl-1">
                        <li class="text-muted mb-2 mt-2">
                            请在咨询设定的时间范围内完成此次咨询。
                        </li>
                        <li class="text-muted mb-2">
                            如果咨询结束时间未到但是咨询工作已经完成，请点击“结束咨询”来结束此次咨询。
                        </li>
                        <li class="text-muted mb-2">
                            如果咨询工作未完成但是咨询时间已到，您只能再重新安排咨询时间。
                        </li>
                        <li class="text-muted mb-2">
                            如果显示离线，请刷新网页重试或者重新进入咨询界面。
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.消息记录 start -->
    <div id="history-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-right" style="justify-content:left;">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white" style="border-radius:0;">
                    <h5 class="modal-title"><i class="fa fa-comments-o mr-1"></i>咨询消息记录</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body">
                    <ul class="list-unstyled" id="history-message-list">
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.消息记录 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script type="text/javascript">
        let currentUserId = [[${user.userId}]];
        let currentUserRoleId = [[${user.role.roleId}]];
        let orderId = getUrlParam("id");
        let order = {};
        let chatMessage = {};
        let endTime = "";
        let t;
        let socket;
        $(function () {
            getOrderInfo();
            $('#btnSend').click(function () {
                if ($.trim($('#message').val()) == "") {
                    layer.msg("说点什么吧！");
                    return;
                }

                chatMessage.message = $.trim($('#message').val());
                send(JSON.stringify(chatMessage));
                $('#message').val('').focus();

            });
            $("#btnClose").click(function () {
                layer.confirm('确定要结束咨询吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        closeConversation();
                    }
                });
            });
            $(window).keydown(function (e) {
                let curKey = e.which;
                if (curKey === 13) {
                    $('#btnSend').click();
                }
            });
            $("#btnHistory").click(function(){
                $.ajax({
                    url: '/counselingroom/counseling/getCounselingRecordContent',
                    type: 'GET',
                    data: { orderId: orderId },
                    dataType: "json",
                    async: true,
                    success: function (res) {
                        let wrapper = $("#history-message-list");
                        let msg_str = "";
                        $.each(res, function (i, item) {
                            msg_str += '<li class="clearfix ml-0 mb-2">';
                            msg_str += '<div class="conversation-text"><div class="ctext-wrap"><div class="text-muted"><span class="text-primary mr-2">' + item.realName + '</span> <span class="font-weight-light">' + item.sendTime + '</span></div><p class="text-justify font-12">' + item.counselingContent+ '</p></div></div>';
                            msg_str += '</li>';
                        });
                        wrapper.empty();
                        wrapper.append(msg_str);
                    }
                })
            });
        });
        //获取预约信息
        let getOrderInfo = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.05, time: false
            });
            $.ajax({
                url: "/counselingroom/counseling/get_order",
                type: 'GET',
                data: { id: orderId },
                dataType: "json",
                async: true,
                success: function (res) {
                    layer.closeAll();
                    order = res;
                    if(currentUserId != order.visitorId && currentUserId != order.counselorId){
                        layer.msg('您不是该预约的拥有者，请确认您的操作！',{ icon: 2, shade: 0.1, time: false});
                        return;
                    }
                    endTime = order.endTime;
                    chatMessage.orderId = orderId;
                    if (currentUserRoleId === 3) {
                        chatMessage.userId = order.visitorId;
                        chatMessage.realName = order.visitorName;
                        chatMessage.headPic = order.visitorHeadPic;
                        chatMessage.toUserId = order.counselorId;
                    }
                    else {
                        chatMessage.userId = order.counselorId;
                        chatMessage.realName = order.counselorName;
                        chatMessage.headPic = order.counselorHeadPic;
                        chatMessage.toUserId = order.visitorId;
                    }
                    //建立websocket连接
                    if(!window.WebSocket){
                        window.WebSocket = window.MozWebSocket;
                    }
                    if(window.WebSocket){
                        let currentUserName;
                        if(currentUserId === order.visitorId){
                            currentUserName = order.visitorName;
                        }
                        if(currentUserId ===order.counselorId){
                            currentUserName = order.counselorName;
                        }
                        socket = new WebSocket("ws://localhost:8889/ws");
                        socket.onmessage = function(event){
                            let res = JSON.parse(event.data);
                            let wrapper = $('.conversation-list');
                            let conversation_str = "";
                            if (currentUserId=== res.userId) {
                                conversation_str += '<li class="clearfix"><div class="chat-avatar">';
                            }
                            else {
                                conversation_str += '<li class="clearfix odd"><div class="chat-avatar">';
                            }
                            res.sendTime = new Date().format('yyyy-MM-dd hh:mm:ss');
                            let headPic = res.headPic === "" ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" +  res.headPic;
                            conversation_str += '<img src="' + headPic + '" alt="' + res.realName + '"></div>';
                            conversation_str += '<div class="conversation-text"><div class="ctext-wrap"><i class="text-muted"><span class="text-primary mr-2">' + res.realName + '</span> <span class="font-weight-light">' + res.sendTime+ '</span></i><p class="text-justify font-14">' + res.message + '</p></div></div></li>';
                            wrapper.append(conversation_str);
                            $(".conversation-list").scrollTop($(".conversation-list").prop('scrollHeight'));
                        };
                        socket.onopen = function(event){
                            $.NotificationApp.send("提醒：", "【" + currentUserName + "】加入了咨询对话！", "bottom-right", "rgba(0,0,0,0.2)", "success");
                            chatMessage.message='【'+currentUserName +'】加入了咨询对话！';
                            send(JSON.stringify(chatMessage));
                        };
                        socket.onclose = function(event){
                            $.NotificationApp.send("提醒：", "【" + currentUserName + "】离开了咨询对话！", "bottom-right", "rgba(0,0,0,0.2)", "warning");
                            chatMessage.message='【'+currentUserName +'】离开了咨询对话！';
                            send(JSON.stringify(chatMessage));
                        };
                    }else{
                        layer.msg('您的浏览器不支持WebSocket协议！', { icon: 2, time: 2000 });
                    }
                    t = setInterval(function () { countDown(endTime) }, 1000);
                }
            });
        };
        let send = function(user){
            if(!window.WebSocket){return;}
            if(socket.readyState === WebSocket.OPEN){
                socket.send(user);
            }else{
                layer.msg('WebSocket 连接没有建立成功！', { icon: 2, time: 2000 });
            }
        }
        //倒计时
        let countDown=function(date) {
            let str = '', s;
            let sec = (new Date(date.replace(/-/ig, '/')).getTime() - new Date().getTime()) / 1000;
            if (sec > 0) {
                s = { '天': sec / 24 / 3600, '小时': sec / 3600 % 24, '分': sec / 60 % 60, '秒': sec % 60 };
                for (i in s) {
                    if (Math.floor(s[i]) > 0) str += Math.floor(s[i]) + i;
                }
                if (Math.floor(sec) === 0) { str = '0秒'; }
                $('#timer').html('<i class="fa fa-clock-o mr-1"></i>咨询剩余时间 ' + str + '</u>');
            }
            else {
                clearInterval(t);
                layer.alert('咨询结束时间已到，如果还有疑问请另行安排咨询时间！', {
                    icon: 0, yes: function (index) {
                        closeConversation();
                        location.href = "/counselingroom/counseling/online";
                    }
                });
            }
        }
        //结束咨询
        let closeConversation = function () {
            $.post("/counselingroom/counseling/update_state", { id: orderId, state: 3 }, function (res) {
                if (res.resultCode === 200) {
                    location.href = "/counselingroom/counseling/online";
                }
                else {
                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                }
            }, 'json');
        };
    </script>
</th:block>
</body>
</html>
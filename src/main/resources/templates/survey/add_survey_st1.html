<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">调查问卷</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">创建问卷</a></li>
                    </ol>
                </div>
                <h4 class="page-title">创建问卷</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header"><i class="fa fa-pencil-square-o mr-1"></i>填写问卷基本信息</div>
                <form id="frmSurvey" class="form-horizontal col-lg-6">
                    <div class="card-body">
                        <div class="form-group row mb-3">
                            <label class="col-form-label col-3" for="surveyName">问卷名称</label>
                            <div class="col-9">
                                <input type="text" class="form-control" id="surveyName" name="surveyName" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-form-label col-3">是否启用</label>
                            <div class="col-9 form-inline">
                                <input type="checkbox" id="isEnabled" data-switch="success" checked />
                                <label for="isEnabled" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="form-group mb-0 justify-content-end row">
                            <div class="col-9">
                                <input type="submit" id="btnSave" class="btn btn-primary" value="下一步" />
                                <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                            </div>
                        </div>
                    </div> <!-- end card-body-->
                </form>
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        $(function(){
            $("#frmSurvey").validate({
                rules: {
                    surveyName: {
                        required: true,
                        remote: {
                            type: "post",
                            url: "/survey/survey/verify_name",
                            dataType: "text",
                            data: {
                                newName: function () {
                                    return $("#surveyName").val();
                                },
                                originalName: function () {
                                    return $("#hidSurveyName").val();
                                }
                            },
                            dataFilter: function (data, type) {
                                if (data === "true") {
                                    return false;
                                }
                                else
                                    return true;
                            }
                        }
                    }
                },
                messages: {
                    surveyName: {
                        required: "请填写问卷名称",
                        remote: "该问卷名称已存在"
                    }
                },
                submitHandler: function (){
                    let jsonObj = {
                        "surveyName": $.trim($("#surveyName").val()),
                        "isEnabled": $("#isEnabled").prop("checked") ? 1 : 0
                    };
                    layer.msg('请稍后…', {
                        icon: 17, shade: 0.2, time: false
                    });
                    $.ajax({
                        type: 'POST',
                        url: '/survey/survey/add',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll();
                            if (res.resultCode === 200) {
                                layer.alert('基本信息保存成功，点击确定进入下一步操作', {
                                    icon: 1, yes: function (index) {
                                        location.replace('/survey/survey/add_survey_st2?surveyId='+res.resultMsg);
                                    }
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">调查问卷管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">问卷调查任务管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">问卷调查任务列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mr-1">
                                    <div class="input-group">
                                        <label for="sr-taskName" class="sr-only">任务名称：</label>
                                        <input type="text" class="form-control" id="sr-taskName" name="sr-loginName" placeholder="任务名称" autocomplete="off" style="width:200px;">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-success mr-1" title="创建问卷调查任务"><i class="fa fa-plus mr-1"></i>创建问卷调查任务</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mr-1" title="删除"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbTaskList" class="table table-striped nowrap">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th>任务名称</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>任务类型</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.问卷调查任务包含问卷 start-->
    <div id="modal-survey" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">任务包含的问卷</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table table-striped" id="tbSurvey">
                        <thead>
                        <tr>
                            <th>调查问卷名称</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.问卷调查任务包含问卷 end-->
    <!-- modal.问卷调查任务包含对象 start-->
    <div id="modal-user" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">测评任务包含的人员</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-centered nowrap" id="tbTaskUser">
                            <thead class="bg-light">
                            <tr>
                                <th>所属组织</th>
                                <th>身份</th>
                                <th>用户名</th>
                                <th>姓名</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.问卷调查任务包含对象 end-->
    <!-- modal.二维码 start -->
    <div id="qrcode-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary text-light">
                    <h5 class="modal-title">问卷调查任务二维码</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <div id="qrcode" style="width: 250px; height: 250px; margin: 0px auto; border: 25px solid rgb(255, 255, 255);">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light mr-2" data-dismiss="modal">关闭</button>
                    <input type="button" class="btn btn-primary" id="download" value="下载" />
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.二维码 end -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/qrcode/qrcode.min.js}"></script>
    <script type="text/javascript">
        let users;
        $(function () {
            //初始化页面权限
            initPage();
            //添加新任务
            $("#btnAdd").click(function () {
                location.href = "/survey/task/choose_type";
            });
            //查询
            $("#btnQuery").click(function () {
                oTable.draw();
            });
            //问卷调查任务列表Datatables
            $("#tbTaskList").bsDataTables({
                columns: columns,
                url: '/measuringroom/task/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //查看问卷调查任务包含的问卷
            $("#tbTaskList").on('click', '.survey', function () {
                let data = oTable.row($(this).parents('tr')).data();
                initTaskSurvey(data.id,data.surveys);
                $("#modal-survey").modal();
            });
            //查看问卷调查任务对象
            $("#tbTaskList").on('click', '.user', function () {
                let data = oTable.row($(this).parents('tr')).data();
                let jsonObj ={};
                jsonObj.taskId = data.id;
                $.ajax({
                    type: 'POST',
                    url: '/measuringroom/task/getUsersByTaskId',
                    data: jsonObj,
                    dataType: "json",
                    success: function (res) {
                        users = res;
                        initTaskUser(users);
                    }
                });
                $("#modal-user").modal();
            });
            //修改
            $("#tbTaskList").on('click', '.edit', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/survey/task/update?taskId=" + data.id;
            });
            let qrcode;
            let qrcodeName;
            //测评二维码
            $("#tbTaskList").on('click', '.qrcode', function () {
                if (qrcode != undefined) {
                    $("#qrcode").empty();
                }
                let data = oTable.row($(this).parents('tr')).data();
                qrcodeName = data.taskName;
                let taskId = data.id;
                let taskType = data.taskType;
                qrcode = new QRCode(document.getElementById("qrcode"), {
                    text: window.location.protocol + "//" + window.location.host + "/app/survey/survey_list?taskId="+taskId+"&type="+taskType,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H
                });
                $("#qrcode-modal").modal();
            });
            $("#download").click(function () {
                let img = $("#qrcode img")[0]; // 获取要下载的图片
                let url = img.src;                            // 获取图片地址
                let a = document.createElement('a');          // 创建一个a节点插入的document
                let event = new MouseEvent('click');          // 模拟鼠标click点击事件
                a.download = qrcodeName;                // 设置a节点的download属性值
                a.href = url;                                 // 将图片的src赋值给a节点的href
                a.dispatchEvent(event)
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/measuringroom/task/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //实现全选
            $("#chkall").change(function () {
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
            $("#btnQueryAddUser").click(function () {
                initAddUserTD();
            });
        });

        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        /*问卷调查任务列表 start*/
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "taskName", "bSortable": false },
            { "data": "startTime",  "bSortable": false },
            { "data": "endTime","bSortable": false},
        ];
        let columnDefs =
            [
                {
                    targets: 4,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        if (row.taskType === 1) {
                            labels = '<span class="badge badge-primary">限定</span>';
                        }
                        if (row.taskType === 2) {
                            labels = '<span class="badge badge-primary">非限定</span>';
                        }
                        return labels;
                    }
                },
                {
                    targets: 5,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        let startDate = row.startTime;
                        let endDate = row.endTime;
                        if (startDate > getDateNowFormat()) {
                            labels = '<span class="badge badge-light">未开始</span>';
                        }
                        if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                            labels = '<span class="badge badge-success">进行中</span>';
                        }
                        if (getDateNowFormat() >= endDate) {
                            labels = '<span class="badge badge-danger">已结束</span>';
                        }
                        return labels;
                    }
                },
                {
                    targets: 6,
                    render: function (data, type, row, meta) {
                        let labels = "";
                        if ('[[${canAdd}]]' === 'true') {
                            labels += '<button class="btn btn-outline-warning btn-sm edit mr-1" title="修改"><i class="fa fa-edit"></i></button>';
                        }
                        labels += '<button class="btn btn-outline-primary btn-sm survey mr-1"><i class="fa fa-file-text mr-1"></i>调查问卷</button>';
                        if (row.taskType === 1){
                            labels += '<button class="btn btn-outline-info btn-sm user mr-1"><i class="fa fa-users mr-1"></i>调查对象</button>';
                        }
                        labels += '<button class="btn btn-outline-success btn-sm qrcode" title="问卷二维码"><i class="fa fa-qrcode"></i></button>';
                        return labels;
                    }
                }
            ];
        let getQueryCondition = function (data) {
            let param = {};
            param.taskName = $.trim($("#sr-taskName").val());
            param.taskKind= 2;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        /*问卷调查任务列表 end*/

        /*问卷调查任务包含的问卷 start*/
        let oTableSurvey = null;
        let initTaskSurvey = function (id,surveys) {
            if (oTableSurvey != null) {
                oTableSurvey.destroy();
            }
            oTableSurvey = $("#tbSurvey").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": false,
                "data": surveys,
                "columns": [
                    { "data": "surveyName", "bSortable": false }
                ],
                "columnDefs":[{
                    targets: 1,
                    render: function (data, type, row, meta) {
                        return '<a href="/survey/task/stat?taskId='+id+'&surveyId='+row.id+'"><button class="btn btn-outline-primary btn-rounded btn-sm" title="结果统计">结果统计</button></a>';
                    }
                }],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": "暂无相关数据",
                    "zeroRecords": "对不起，查询不到任何相关数据",
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        /*问卷调查任务包含的问卷 end */

        /*调查对象 start*/
        let oTableTaskUser = null;
        let initTaskUser = function (users) {
            if (oTableTaskUser != null) {
                oTableTaskUser.destroy();
            }
            oTableTaskUser = $("#tbTaskUser").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": true,
                "pageLength": 20,
                "data": users,
                "columns": [
                    { "data": "structFullName", "bSortable": false},
                    { "data": "role.roleName", "bSortable": false },
                    { "data": "loginName", "bSortable": false },
                    { "data": "realName", "bSortable": false }
                ],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": "暂无相关数据",
                    "zeroRecords": "对不起，查询不到任何相关数据",
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        /*调查对象 end */
    </script>
</th:block>
</body>
</html>
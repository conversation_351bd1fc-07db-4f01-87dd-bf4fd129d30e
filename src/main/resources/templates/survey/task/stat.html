<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">调查问卷管理</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">问卷调查任务管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">结果统计</a></li>
                    </ol>
                </div>
                <h4 class="page-title"><i class="fa fa-area-chart mr-1"></i>问卷结果统计</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-print-none">
                    <div class="card-title"><h5>任务名称：<span id="taskName"></span></h5></div>
                </div>
                <!-- end card body-->
                <div class="card-body" id="report">
                    <div class="card-widgets mr-2 mb-4">
                        <button onclick="javascript:window.print()" class="btn btn-outline-secondary btn-sm d-print-none mr-1" title="打印"><i class="fa fa-print" title="打印"></i></button>
                        <button onclick="download()" class="btn btn-outline-primary btn-sm d-print-none"><i class="fa fa-download mr-2"></i>下载报告[word]</button>
                    </div>
                    <div class="clearfix">
                        <div class="text-center mb-3">
                            <h4 class="m-0 letter-spacing-2">《<span id="surveyName"></span>》结果统计</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="header-title mb-3">总体完成情况</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-centered">
                                    <thead class="thead-light">
                                    <tr>
                                        <th class="text-center">总参与人数</th>
                                        <th class="text-center">完成人数</th>
                                        <th class="text-center">完成率</th>
                                        <th class="text-center">未完成人数</th>
                                        <th class="text-center">未完成率</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td class="text-center totalCount"></td>
                                        <td class="text-center doneCount"></td>
                                        <td class="text-center doneRate"></td>
                                        <td class="text-center unDoneCount"></td>
                                        <td class="text-center unDoneRate"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h4 class="header-title mb-3">各题选择频度统计</h4>
                            <div class="table-responsive" id="survey-container">

                            </div>
                        </div>
                    </div>
                </div>
                <!-- end card -->
            </div>
            <!-- end col-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script type="text/javascript">
        let getQueryCondition = function () {
            let param = {};
            param.taskId = getUrlParam('taskId');
            param.surveyId = getUrlParam('surveyId');
            return param;
        };
        let getData = function () {
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            let jsonObj = getQueryCondition();
            $.ajax({
                type: 'POST',
                url: "/survey/task/get_survey_stat",
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    $("#taskName").html(res.taskName);
                    $("#surveyName").html(res.surveyName);
                    if (res.surveyRecords.length === 0) {
                        layer.msg("未查询到问卷记录数据！", { icon: 0, time: 2000 });
                        $("#report").hide();
                        return;
                    }
                    $(".totalCount").html(res.totalCount);
                    $(".doneCount").html(res.doneCount);
                    $(".doneRate").html(res.doneRate.toFixed(2) + '%');
                    $(".unDoneCount").html(res.unDoneCount);
                    $(".unDoneRate").html(res.unDoneRate.toFixed(2) + '%');

                    getResultStat(res);
                }
            });
        };
        let getResultStat =function (data){
            data.surveyResultStat.forEach(function(result) {
                let tbWrapper = '<div class="question_title font-14 mb-1">第'+result.QNumber+'题：'+result.QContent+'</div>';

                // 为排序题添加说明
                if (result.QType === 4) {
                    tbWrapper += '<div class="alert alert-info mb-3">';
                    tbWrapper += '<small><i class="fa fa-info-circle mr-1"></i>';
                    tbWrapper += '排序结果按平均排名从小到大排列，平均排名越小表示优先级越高';
                    tbWrapper += '</small>';
                    tbWrapper += '</div>';
                }

                tbWrapper += '<table class="table table-centered">';
                tbWrapper += '<thead class="bg-primary text-light">';

                // 根据题型显示不同的表头
                if (result.QType === 4) { // 排序题
                    tbWrapper += '<tr><th>选项</th><th>参与排序人数</th><th>平均排名</th></tr>';
                } else { // 单选题、多选题
                    tbWrapper += '<tr><th>选项</th><th>选择数</th><th>占比</th></tr>';
                }

                tbWrapper += '</thead>';
                tbWrapper += '<tbody>';

                // 对排序题按平均排名从小到大排序
                let sortedResults = result.listResultCounts;
                if (result.QType === 4) {
                    sortedResults = [...result.listResultCounts].sort(function(a, b) {
                        let rankA = parseFloat(a.selRate) || 999; // 如果解析失败，设为很大的数
                        let rankB = parseFloat(b.selRate) || 999;
                        return rankA - rankB; // 从小到大排序
                    });
                }

                sortedResults.forEach(function(countItem, index) {
                    if (result.QType === 4) { // 排序题显示平均排名
                        let rowClass = '';
                        let rankIcon = '';

                        // 为前三名添加特殊样式和图标
                        if (index === 0) {
                            rowClass = 'table-warning'; // 第一名：金色背景
                            rankIcon = '<i class="fa fa-trophy text-warning mr-1"></i>';
                        } else if (index === 1) {
                            rowClass = 'table-info'; // 第二名：蓝色背景
                            rankIcon = '<i class="fa fa-medal text-info mr-1"></i>';
                        } else if (index === 2) {
                            rowClass = 'table-success'; // 第三名：绿色背景
                            rankIcon = '<i class="fa fa-award text-success mr-1"></i>';
                        }

                        tbWrapper += '<tr class="' + rowClass + '">';
                        tbWrapper += '<td>' + rankIcon + countItem.itemContent + '</td>';
                        tbWrapper += '<td>' + countItem.selCount + '</td>';
                        tbWrapper += '<td><strong>' + countItem.selRate + '</strong></td>';
                        tbWrapper += '</tr>';
                    } else { // 单选题、多选题显示占比进度条
                        let progressWrapper = '';
                        progressWrapper += '<div class="progress-w-percent mb-0">';
                        progressWrapper += '<span class="progress-value">'+countItem.selRate+'</span>';
                        progressWrapper += '<div class="progress progress-sm">';
                        progressWrapper += '<div class="progress-bar bg-success" role="progressbar" style="width: '+countItem.selRate+';" aria-valuenow="'+countItem.selCount+'"></div>';
                        progressWrapper += '</div</div>>';
                        tbWrapper += '<tr><td>'+countItem.itemContent+'</td><td>'+countItem.selCount+'</td><td>'+progressWrapper+'</td></tr>';
                    }
                });
                tbWrapper += '</tbody>';
                tbWrapper += '</table>';
                $("#survey-container").append(tbWrapper);
            });
        }
        let download = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            let jsonObj = getQueryCondition();
            $.ajax({
                type: 'POST',
                url: "/survey/task/create_report_word",
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    if(res.resultCode ===200) {
                        location.href="/static/upload/survey/"+res.resultMsg;
                    }
                    else {
                        layer.msg('下载失败!',{ icon: 2, time: 2000 });
                    }
                }
            });
        };
        $(function () {
            getData();
        });
    </script>
</th:block>
</body>
</html>
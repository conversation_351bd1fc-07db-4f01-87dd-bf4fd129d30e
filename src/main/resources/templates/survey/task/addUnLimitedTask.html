<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/dual-list-box/bootstrap-duallistbox.min.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">调查问卷管理</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">问卷调查任务管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">添加问卷调查任务</a></li>
                    </ol>
                </div>
                <h4 class="page-title">添加问卷调查任务</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-sm-8">
                            <h5><i class="fa fa-plus-square mr-1"></i>创建问卷调查任务</h5>
                        </div>
                        <div class="col-sm-4">
                            <div class="text-right">
                                <a th:href="@{/measuringroom/task/list}" class="btn btn-outline-primary btn-sm" type="button"><i class="fa fa-tasks mr-1"></i>问卷调查任务列表</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="frmTask" class="form-wizard" role="form">
                        <input type="hidden" name="hidstep" id="hidstep" value="s1" />
                        <div class="wizard-steps"></div>
                        <div class="step" id="s1">
                            <span data-icon="fa fa-file-text" data-text="任务信息"></span>
                            <div class="card-title">设置任务信息</div>
                            <div class="form-group mb-2">
                                <label class="col-form-label" for="taskName">任务名称</label>
                                <input type="text" class="form-control" id="taskName" name="taskName">
                            </div>
                            <div class="form-group mb-2">
                                <label class="col-form-label">选择调查日期</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                    </div>
                                    <input type="text" class="form-control" value="" id="taskDate" name="taskDate">
                                </div>
                                <input type="hidden" id="hidStartTime" value="" />
                                <input type="hidden" id="hidEndTime" value="" />
                            </div>
                        </div>
                        <div class="step submit_step" id="s2">
                            <span data-icon="fa fa-list-alt" data-text="调查问卷"></span>
                            <div class="card-title">
                                <h5><i class="fa fa-file-text mr-1"></i>选择问卷</h5>
                            </div>
                            <div class="form-group">
                                <select multiple="multiple" name="duallistbox" id="surveyIds" class="duallistbox col-lg-12">
                                </select>
                            </div>
                        </div>
                        <div class="wizard-actions">
                            <button class="btn btn-outline-secondary btn-sm pull-left" type="reset" id="pre"><i class="fa fa-arrow-left"></i> 上一步</button>
                            <button class="btn btn-outline-secondary btn-sm pull-right" type="submit" id="next">下一步 <i class="fa fa-arrow-right"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/vendor/jquery-ui-1.10.4.min.js}"></script>
    <script th:src="@{/static/js/plugins/wizard/jquery.form.js}"></script>
    <script th:src="@{/static/js/plugins/wizard/jquery.form.wizard.js}"></script>
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script th:src="@{/static/js/plugins/dual-list-box/jquery.bootstrap-duallistbox.js}"></script>
    <script type="text/javascript">
        let locale = {
            "format": 'YYYY-MM-DD HH:mm:ss',
            "separator": " - ",
            "applyLabel": "确定",
            "cancelLabel": "取消",
            "fromLabel": "起始时间",
            "toLabel": "结束时间",
            "customRangeLabel": "自定义",
            "weekLabel": "W",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            "firstDay": 1
        };
        $('#taskDate').daterangepicker({
            "locale": locale,
            "showDropdowns": true,
            "linkedCalendars": false,
            "timePicker": true,
            "timePickerIncrement": 1,
            "timePicker24Hour": true,
            "minDate": moment().subtract(1, "days"),
            "drops": "down"
        }, function (start, end, label) {
            let startTime, endTime;
            if ((start - end) === 0) {
                let _end = new Date(end);
                let year = _end.getFullYear();
                let month = _end.getMonth();
                let day = _end.getDate();
                let hour = _end.getHours();
                let min = _end.getMinutes();
                let s = _end.getSeconds();
                end = new Date(year, month, day, hour + 23, min + 59, s + 59);
                startTime = start.format('YYYY-MM-DD HH:mm:ss');
                endTime = end.format('YYYY-MM-dd hh:mm:ss');
            }
            else {
                startTime = start.format('YYYY-MM-DD HH:mm:ss');
                endTime = end.format('YYYY-MM-DD HH:mm:ss');
            }
            $('#taskDate').val(startTime + ' - ' + endTime);
            $("#hidStartTime").val(startTime);
            $("#hidEndTime").val(endTime);
        });
        let initDualListBox = function () {
            $.ajax({
                url: "/survey/survey/get_list_for_dual",
                type: 'POST',
                data: "",
                dataType: "JSON",
                async: true,
                success: function (returnData) {
                    $(returnData).each(function () {
                        let o = document.createElement("option");
                        o.value = this['id'];
                        o.text = this['name'];
                        $("#surveyIds")[0].options.add(o);
                    });
                    $("#surveyIds").bootstrapDualListbox({
                        infotext: false, // text when all options are visible / false for no info text
                        infotextfiltered: '<span class="label label-warning">Filtered</span> {0} from {1}', // when not all of the options are visible due to the filter
                        infotextempty: '',      // when there are no options present in the list
                        selectorminimalheight: 200,
                        showfilterinputs: true,
                        filterplaceholder: '搜索...',
                        filtertextclear: '显示所有',
                        iconMove: 'fa fa-arrow-right',
                        iconMoveAll: 'fa fa-angle-double-right',
                        iconRemove: 'fa fa-arrow-left',
                        iconRemoveAll: 'fa fa-angle-double-left'
                    });
                }
            });
        };

        $(function () {
            $('#taskDate').val("");
            initDualListBox();
            $("#frmTask").formwizard({
                formPluginEnabled: true,
                validationEnabled: true,
                validationOptions: {
                    ignore:'',
                    errorPlacement: function (error, element) {
                        wrap = element.parent();
                        if (wrap.hasClass('input-group')) {
                            error.insertAfter(wrap);
                        } else {
                            error.insertAfter(element);
                        }
                    },
                    rules: {
                        taskName: {
                            required: true,
                            remote: {
                                type: "POST",
                                url: "/measuringroom/task/check_name",
                                dataType: "json",
                                data: {
                                    taskName: function () {
                                        return $("#taskName").val();
                                    },
                                    hidTaskName: function (){
                                        return '';
                                    },
                                    taskKind: function (){
                                        return 2;
                                    }
                                },
                                dataFilter: function (data, type) {
                                    return  data === "0";
                                }
                            }
                        },
                        taskDate: { required: true }
                    },
                    messages: {
                        taskName: {
                            required: "请填写任务名称",
                            remote: "该任务名称已存在"
                        },
                        taskDate: { required: "请选择调查日期" }
                    }
                },
                formOptions: {
                    success: function (data) {
                        let jsonObj = {
                            "taskName": $("#taskName").val(),
                            "startTime": $("#hidStartTime").val(),
                            "endTime": $("#hidEndTime").val(),
                            "taskType": 2,
                            "surveyIds": $("#surveyIds").val().join(','),
                            "taskKind": 2
                        };
                        $("#next").attr("Disabled", true);
                        layer.msg('请稍后…', {
                            icon: 17, shade: 0.2, time: false
                        });
                        $.ajax({
                            type: 'POST',
                            url: '/measuringroom/task/add_task',
                            data: JSON.stringify(jsonObj),
                            contentType:'application/json',
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll();
                                $("#next").attr("Disabled", false);
                                if (res.resultCode === 200) {
                                    layer.alert('您已经成功添加问卷调查任务！', {
                                        icon: 1, yes: function (index) {
                                            location.href = "/survey/task/list";
                                        }
                                    });
                                }
                                else {
                                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                                }
                            }
                        });
                    },
                    resetForm: false
                },
                disableUIStyles: true,
                showSteps: true,//show the step
                vertical: true
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">调查问卷</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">添加问卷调查任务</a></li>
                    </ol>
                </div>
                <h4 class="page-title">添加问卷调查任务</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card pl-3">
                <div class="card-body">
                    <h4 class="mb-3 header-title"></h4>
                    <form class="form-horizonta col-6 mb-2">
                        <div class="form-group row mb-3">
                            <label class="col-4 col-form-label">选择任务类型：</label>
                            <div class="col-8">
                                <select class="form-control" id="taskType">
                                    <option value="">请选择</option>
                                    <option value="1">限定调查对象</option>
                                    <option value="2">不限定调查对象</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group mb-0 justify-content-end row">
                            <div class="col-8">
                                <button class="btn btn-primary" id="btnNext" type="button">下一步</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        $(function () {
            $("#btnNext").click(function () {
                let taskType = $("#taskType").val();
                if (taskType === '') {
                    layer.msg('请选择任务类型', { icon: 2, time: 2000 });
                    return;
                }
                if (taskType == "1") location.href = "/survey/task/add_limited_task";
                if (taskType == "2") location.href = "/survey/task/add_unlimited_task";
            });
        });
    </script>
</th:block>
</body>
</html>
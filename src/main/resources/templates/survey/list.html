<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">调查问卷</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">调查问卷管理</a></li>
                    </ol>
                </div>
                <h4 class="page-title">调查问卷列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mb-2">
                                    <div class="input-group">
                                        <label for="srSurveyName" class="sr-only">搜索</label>
                                        <input type="text" class="form-control" id="srSurveyName" placeholder="问卷名称..." autocomplete="off">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-success mb-2 mr-1"><i class="fa fa-plus mr-1"></i>新增</button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2"><i class="fa fa-trash-o mr-1"></i>删除</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbSurvey" class="table table-striped nowrap" width="100%">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th>问卷名称</th>
                                <th>创建人</th>
                                <th>创建日期</th>
                                <th>是否启用</th>
                                <th>完成状态</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
    <div id="myModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="frmSurvey" class="form-horizontal">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title" id="modal-scale-title">设置问卷基本信息</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                            <div class="form-group row mb-3">
                                <label class="col-form-label col-3" for="surveyName">问卷名称：</label>
                                <div class="col-9">
                                    <input type="text" class="form-control" id="surveyName" name="surveyName" />
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label class="col-form-label col-3">是否启用：</label>
                                <div class="col-9 form-inline">
                                    <input type="checkbox" id="isEnabled" data-switch="success" checked />
                                    <label for="isEnabled" data-on-label="" data-off-label=""></label>
                                </div>
                            </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        <input type="submit" id="btnSaveBaseInfo" class="btn btn-primary btn-sm" value="保存" />
                        <input type="reset" hidden />
                    </div>
                </form>
                <input type="hidden" name="hidId" id="hidId" value="0" />
            </div>
        </div>
    </div>
    <div id="myModalView" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h4 class="modal-title survey-name"></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button></div>
                <form id="surveyForm">
                    <div class="modal-body pt-0">
                        <div id="surveyContent" class="m-2"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        let listQuestions;
        $(function () {
            //初始化页面权限
            initPage();
            $("#btnQuery").click(function () {
                $('#tbSurvey').DataTable().ajax.reload();
            });
            //datatables
            $("#tbSurvey").bsDataTables({
                columns: columns,
                url: '/survey/survey/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //新增
            $("#btnAdd").click(function () {
                location.href = '/survey/survey/add_survey_st1';
            });
            //修改
            $("#tbSurvey").on('click', '.update', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidId").val(data.id);
                $("#surveyName").val(data.surveyName);
                if (data.isEnabled=== 1) {
                    $("#isEnabled").attr("checked", true);
                }
                else {
                    $("#isEnabled").attr("checked", false);
                }
                $("#myModal").modal();
            });
            $("#tbSurvey").on('click','.view',function(){
                let data = oTable.row($(this).parents('tr')).data();
                $(".survey-name").html(data.surveyName);
                getQuestions(data.id);
                viewSurvey(listQuestions);
                $("#myModalView").modal();
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/survey/survey/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            $("#frmSurvey").validate({
                rules: {
                    surveyName: {
                        required: true,
                        remote: {
                            type: "post",
                            url: "/survey/survey/verify_name",
                            dataType: "text",
                            data: {
                                newName: function () {
                                    return $("#surveyName").val();
                                },
                                originalName: function () {
                                    return $("#hidSurveyName").val();
                                }
                            },
                            dataFilter: function (data, type) {
                                if (data === "true") {
                                    return false;
                                }
                                else
                                    return true;
                            }
                        }
                    }
                },
                messages: {
                    surveyName: {
                        required: "请填写问卷名称",
                        remote: "该问卷名称已存在"
                    }
                },
                submitHandler: function (){
                    let jsonObj = {
                        "id":$("#hidId").val(),
                        "surveyName": $.trim($("#surveyName").val()),
                        "isEnabled": $("#isEnabled").prop("checked") ? 1 : 0
                    };
                    layer.msg('请稍后…', {
                        icon: 17, shade: 0.2, time: false
                    });
                    $.ajax({
                        type: 'POST',
                        url: '/survey/survey/update',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll();
                            if (res.resultCode === 200) {
                                layer.msg('基本信息保存成功', { icon: 1, time: 2000 });
                                $("#myModal").modal('hide');
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
        });
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnDel").show() : $("#btnDel").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
        };
        let columns = [
            {
                "data": "id", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + full.id + '" class="custom-control-input checklist" type="checkbox" value="' +  full.id  + '"><label class="custom-control-label" for="lbl' +  full.id  + '"></label></div >';
                    }, "bSortable": false
            },
            { "data": "surveyName", "bSortable": false },
            { "data": "realName", "bSortable": false },
            {"data": "createDate", "bSortable": false},
            {
                "data": "isEnabled", "render":
                    function (data, type, full, meta) {
                        if (full.isEnabled === 1) {
                            return '<span class="badge badge-success badge-pill">已启用</span>';
                        }
                        else {
                            return '<span class="badge badge-light badge-pill">未启用</span>';
                        }
                    }, "bSortable": false
            },
            {
                "data": "isDone", "render":
                    function (data, type, full, meta) {
                        if (full.isDone === 1) {
                            return '<span class="badge badge-success badge-pill"><i class="fa fa-check mr-1"></i>已发布</span>';
                        }
                        else {
                            return '<span class="badge badge-light badge-pill"><i class="fa fa-info-circle mr-1"></i>配置未完成</span>';
                        }
                    }, "bSortable": false
            }
        ];
        let columnDefs = [{
            targets: 6, render: function (data, type, row, meta) {
                let buttons = "";
                if ('[[${canUpdate}]]' === 'true') buttons +='<button type ="button" class="btn btn-outline-primary btn-sm mr-1 update">基本信息</button>';
                if ('[[${canDelete}]]' === 'true') buttons += '<a href="/survey/survey/add_survey_st2?surveyId='+row.id+'" class="btn btn-outline-primary btn-sm mr-1">配置题目</a>';
                if ('[[${canUpdate}]]' === 'true') buttons += '<button type ="button" class="btn btn-outline-primary btn-sm mr-1 view">预览问卷</button>';
                return buttons;
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            param.surveyName = $("#srSurveyName").val();
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let getQuestions = function(id){
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            $.ajax({
                "type": "post",
                "url": "/survey/surveyquestion/get_list_by_surveyId",
                "dataType": "json",
                "async": false,
                "data": { surveyId: id },
                "success": function (data) {
                    layer.closeAll();
                    listQuestions =JSON.parse(data);
                }
            });
        }
        let viewSurvey=function(data){
            let content = '';
            data.forEach(question => {
                content += '<div class="p-2"><div class="mb-2"><span class="font-16 font-weight-bold">'+question.qNumber+'、'+question.qContent.replace(/<.*?>/ig,"")+'</span></div>';
                content += '<div class="input-group">';
                let otherInput = '<div class="form-group" style="display:none;" id="otherInput_' + question.id + '"><input type="text" class="form-control" name="'+question.id+'_other" placeholder="请输入具体内容"></div>';
                if (question.qType === 1) {
                    question.listItems.forEach(option => {
                        content += '<div class="custom-control custom-radio mb-2 mr-2"><input class="custom-control-input" type="radio" name="'+question.id+'" id="option'+option.id+'" value="'+option.itemContent+'" onclick="toggleOtherInput(this,'+option.id+','+option.isOther+','+question.id+')">';
                        content += '<label class="custom-control-label" for="option'+option.id+'"> '+option.itemContent+' </label></div>';
                        if(option.isOther ===1){
                            content += otherInput;
                        }
                    });
                } else if (question.qType ===2) {
                    question.listItems.forEach(option => {
                        content += '<div class="custom-control custom-checkbox mr-2"> <input class="custom-control-input" type="checkbox" name="'+question.id+'" id="option'+option.id+'" value="'+option.itemContent+'" onclick="toggleOtherInput(this,'+option.id+','+option.isOther+','+question.id+')">';
                        content += '<label class="custom-control-label" for="option'+option.id+'">'+option.itemContent+' </label></div>';
                        if(option.isOther ===1){
                            content += otherInput;
                        }
                    });
                } else if (question.qType ===3) {
                    content += '<div class="form-group col-12"> <input type="text" class="form-control" name="'+question.id+'" placeholder="请输入内容" /> </div>';
                }
                else if (question.qType === 4) {
                    content += '<div class="alert alert-info mb-3"><small><i class="fa fa-info-circle mr-1"></i>请拖拽下方选项进行排序，排在前面的表示优先级更高</small></div>';
                    content += '<div class="sortable-list" id="sortable_' + question.id + '">';
                    question.listItems.forEach((item, index) => {
                        content += '<div class="sortable-item" data-value="' + item.itemContent + '">';
                        content += '<div class="sortable-content" style="display: flex; align-items: center; padding: 12px; border-radius: 8px; margin-bottom: 8px; background-color: white; border: 1px solid #dee2e6;">';
                        content += '<div class="sort-number" style="background-color: #2196f3; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px;">' + (index + 1) + '</div>';
                        content += '<i class="fa fa-bars drag-handle" style="margin-right: 12px; cursor: grab; color: #6c757d; font-size: 16px;"></i>';
                        content += '<span style="flex-grow: 1;">' + item.itemContent + '</span>';
                        content += '<i class="fa fa-arrows-v" style="margin-left: 8px; color: #6c757d;"></i>';
                        content += '</div></div>';
                    });
                    content += '</div>';
                }
                else if (question.qType === 5) {
                    content += '<div class="form-group col-12 rating-options-container"><div class="rating-options-row">';
                    question.listItems.forEach(option => {
                        content += '<div class="rating-option-item"><div class="custom-control custom-radio">';
                        content += '<input class="custom-control-input" type="radio" name="'+question.id+'" id="option'+option.id+'" value="'+option.itemContent+'">';
                        content += '<label class="custom-control-label rating-option-label" for="option'+option.id+'">'+option.itemContent+'</label>';
                        content += '</div></div>';
                    });
                    content += '</div></div>';
                }
                content+='</div></div><div class="divider dotted mt-2 mb-2"></div>';
            });
            $('#surveyContent').html(content);
            // 初始化排序功能
            setTimeout(function() {
                $('.sortable-list').each(function() {
                    let questionId = $(this).attr('id').replace('sortable_', '');
                    initSortable(this, {
                        onEnd: function() {
                            updateSortResult(questionId);
                        }
                    });
                });
            }, 100);
        };
        // 排序功能实现
        function initSortable(element, options) {
            let draggedElement = null;
            let placeholder = null;
            let isDragging = false;

            function createPlaceholder() {
                placeholder = document.createElement('div');
                placeholder.className = 'sortable-placeholder';
                placeholder.innerHTML = '<div style="height: 50px; display: flex; align-items: center; justify-content: center; color: #2196f3;"><i class="fa fa-arrows-v"></i> 拖拽到此处</div>';
                return placeholder;
            }

            function getDragAfterElement(container, y) {
                const draggableElements = [...container.querySelectorAll('.sortable-item:not(.dragging)')];
                return draggableElements.reduce((closest, child) => {
                    const box = child.getBoundingClientRect();
                    const offset = y - box.top - box.height / 2;
                    if (offset < 0 && offset > closest.offset) {
                        return { offset: offset, element: child };
                    } else {
                        return closest;
                    }
                }, { offset: Number.NEGATIVE_INFINITY }).element;
            }

            function startDrag(e, item) {
                e.preventDefault();
                e.stopPropagation();
                draggedElement = item;
                isDragging = true;
                draggedElement.classList.add('dragging');
                draggedElement.style.opacity = '0.5';
                createPlaceholder();
                draggedElement.parentNode.insertBefore(placeholder, draggedElement.nextSibling);
            }

            function onDrag(e) {
                if (!isDragging || !draggedElement) return;
                e.preventDefault();
                e.stopPropagation();
                const clientY = e.clientY || (e.touches && e.touches[0].clientY);
                if (!clientY) return;
                const afterElement = getDragAfterElement(element, clientY);
                if (afterElement == null) {
                    element.appendChild(placeholder);
                } else {
                    element.insertBefore(placeholder, afterElement);
                }
            }

            function endDrag(e) {
                if (!isDragging || !draggedElement) return;
                e.preventDefault();
                e.stopPropagation();
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.insertBefore(draggedElement, placeholder);
                    placeholder.parentNode.removeChild(placeholder);
                }
                draggedElement.classList.remove('dragging');
                draggedElement.style.opacity = '';
                draggedElement = null;
                placeholder = null;
                isDragging = false;
                if (options.onEnd) options.onEnd();
            }

            element.querySelectorAll('.sortable-item').forEach(item => {
                const dragHandle = item.querySelector('.drag-handle');
                dragHandle.addEventListener('mousedown', function(e) {
                    startDrag(e, item);
                });
                dragHandle.addEventListener('touchstart', function(e) {
                    startDrag(e, item);
                }, { passive: false });
            });

            document.addEventListener('mousemove', onDrag);
            document.addEventListener('touchmove', onDrag, { passive: false });
            document.addEventListener('mouseup', endDrag);
            document.addEventListener('touchend', endDrag);
        }

        function updateSortResult(questionId) {
            let sortableList = $('#sortable_' + questionId);
            let sortedItems = [];
            sortableList.find('.sortable-item').each(function(index) {
                let value = $(this).attr('data-value');
                if (value) {
                    sortedItems.push((index + 1) + '.' + value);
                    $(this).find('.sort-number').text(index + 1);
                }
            });
        }
        let toggleOtherInput  = function(d,a,b,c) {
            if($(d).is('[type="radio"]')){
                if(b!==0){
                    if ($('#option'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
                else{
                    $("#otherInput_" + c).toggle(false);
                }
            }
            if($(d).is('[type="checkbox"]')){
                if(b!==0){
                    if ($('#option'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
            }
        }
    </script>
    <style>
.rating-options-container {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}
.rating-options-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    padding: 8px 0;
    min-width: max-content;
}
.rating-option-item {
    flex: 0 0 auto;
    min-width: 60px;
    text-align: center;
}
.rating-option-item .custom-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 4px;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}
.rating-option-item .custom-control:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}
.rating-option-item .custom-control-input:checked + .custom-control-label {
    color: #2196f3;
    font-weight: 600;
}
.rating-option-label {
    font-size: 14px;
    line-height: 1.2;
    margin-top: 4px;
    text-align: center;
    word-break: break-word;
    max-width: 100%;
}
@media (max-width: 480px) {
    .rating-options-row {
        gap: 6px;
        padding: 6px 0;
    }
    .rating-option-item {
        min-width: 50px;
    }
    .rating-option-item .custom-control {
        padding: 6px 3px;
    }
    .rating-option-label {
        font-size: 13px;
    }
}
</style>
<style>
/* 排序题样式 */
.sortable-list {
    min-height: 100px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    touch-action: none;
    overflow: visible;
}
.sortable-item {
    cursor: move;
    user-select: none;
    transition: all 0.2s ease;
    touch-action: none;
    position: relative;
    z-index: 1;
}
.sortable-item.dragging {
    z-index: 1000;
    pointer-events: none;
}
.sortable-item:hover .sortable-content {
    background-color: #e3f2fd !important;
    border-color: #2196f3 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}
.sortable-content {
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    touch-action: none;
}
.sortable-placeholder {
    background-color: #e3f2fd;
    border: 2px dashed #2196f3;
    height: 60px;
    margin-bottom: 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2196f3;
    font-weight: 500;
}
.sortable-ghost {
    opacity: 0.3;
}
.drag-handle {
    cursor: grab;
    color: #6c757d;
    font-size: 16px;
    touch-action: none;
    padding: 5px;
}
.drag-handle:active {
    cursor: grabbing;
}
.drag-handle:hover {
    color: #2196f3;
}
.sort-number {
    background-color: #2196f3;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-right: 10px;
}
</style>
</th:block>
</body>
</html>
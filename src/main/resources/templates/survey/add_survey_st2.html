<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">问卷调查</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">创建问卷</a></li>
                    </ol>
                </div>
                <h4 class="page-title">设置问卷题目</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-4">
                            <div class="card-title">
                                <h5><i class="fa fa-list-ol mr-1"></i> 题目列表 </h5>
                            </div>
                        </div>
                        <div class="col-8">
                            <div class="text-lg-right">
                                <button id="btnAddQ" type="button" class="btn btn-warning btn-sm mr-1"><i class="fa fa-plus mr-1"></i>添加题目</button>
                                <button id="btnImportQAndA" type="button" class="btn btn-warning btn-sm mr-1"><i class="fa fa-file-excel-o mr-1"></i>导入题目</button>
                                <button id="btnBatchDelQ" type="button" class="btn btn-danger btn-sm mr-1"><i class="fa fa-trash-o mr-1"></i>删除题目</button>
                                <button id="btnDone" type="button" class="btn btn-success btn-sm mr-1"><i class="fa fa-check mr-1"></i>发布问卷</button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped nowrap" id="tbQ">
                            <thead>
                            <tr>
                                <th style="width:30px;">
                                    <div class="custom-control custom-checkbox"><input id="chkall" class="custom-control-input check" type="checkbox"><label class="custom-control-label" for="chkall"></label></div>
                                </th>
                                <th>排序</th>
                                <th>题目</th>
                                <th>题型</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="card-footer">
                        <a href="/survey/survey/list" class="btn btn-outline-primary btn-sm mt-2"><i class="fa fa-reply mr-1"></i>返回问卷列表</a>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
    <!-- modal.题目 start-->
    <div id="myModalQ" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="frmQ" action="#">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title" id="modal-q-title"></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="qType">条目类型</label>
                            <select id="qType" name="qType" class="form-control">
                                <option value="1">单选题</option>
                                <option value="2">多选题</option>
                                <option value="3">填空题</option>
                                <option value="4">排序题</option>
                                <option value="5">评分单选题</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>条目内容</label>
                            <div id="qContent">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        <input type="submit" id="btnSaveQ" class="btn btn-primary btn-sm" value="保存" />
                        <input type="hidden" id="hidQid" value="0" />
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- modal.题目 end-->
    <!-- modal.导入题目和选项 start-->
    <div id="myModalImportQ" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">导入题目和选项</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group form-inline">
                        <label class="col-form-label col-4">请先下载模板：</label>
                        <div class="col-8">
                            <a th:href="@{/static/template/调查问卷导入模版.xlsx}" class="text-primary pull-left text-decoration-underline"><i class="fa fa-download mr-1"></i>导入模板</a>
                        </div>
                    </div>
                    <div class="form-group form-inline">
                        <label class="col-form-label col-4">选择表格文件：</label>
                        <input type="file" name="file" id="txt_file_import_q" class="file-loading" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-link" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.导入题目和答案 end-->
    <!-- modal.选项 start-->
    <div id="myModalA" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">设置选项</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="frmA" action="#" class="form-horizontal">
                        <input type="hidden" name="hidAID" id="hidAID" value="0" />
                        <div class="form-group">
                            <label>题目：<span id="selQIds"></span></label>
                        </div>
                        <div class="form-group row mb-2">
                            <label class="col-form-label col-4" for="itemContent">选项内容：</label>
                            <div class="col-8">
                                <input type="text" name="itemContent" id="itemContent" class="form-control" autocomplete="off" placeholder="" />
                            </div>
                        </div>
                        <div class="form-group row mb-2">
                            <label class="col-form-label col-4">是否其它选项：</label>
                            <div class="col-8 form-inline">
                                <input type="checkbox" id="isOther" data-switch="danger" />
                                <label for="isOther" data-on-label="" data-off-label=""></label>
                            </div>
                        </div>
                        <div class="form-group mb-2">
                            <input type="submit" name="btnSaveA" id="btnSaveA" value="保存" class="btn btn-sm btn-primary" />
                            <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        </div>
                    </form>
                    <table class="table table-striped" id="tbA">
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>选项内容</th>
                            <th>是否其他选项</th>
                            <th></th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.选项 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        let columns = [
            {
            "data": "id", "render":
                function (data, type, full, meta) {
                    return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                }, "bSortable": false},
            { "data": "qNumber", "bSortable": false },
            { "data": "qContent", "bSortable": false },
            { "data": "qType", "bSortable": false,
                "render": function (data, type, row, meta) {
                    if (data === 1) {
                        return '单选题';
                    }
                    if (data === 2) {
                        return '多选题';
                    }
                    if (data === 3) {
                        return '填空题';
                    }
                    if (data === 4) {
                        return '排序题';
                    }
                    if (data === 5) {
                        return '评分单选题';
                    }
                }
            }
        ];
        let columnDefs = [{
            targets: 4,
            render: function (data, type, row, meta) {
                let buttons = "";
                buttons += '<button class="btn btn-outline-warning btn-sm editQ mr-1" type="button"><i class="fa fa-edit mr-1"></i>修改</button>';
                if(row.qType === 1 || row.qType === 2 || row.qType === 4 || row.qType === 5) {
                    buttons += '<button class="btn btn-outline-info btn-sm answer" type="button"><i class="fa fa-check-square-o mr-1"></i>查看选项</button>';

                    // 检查是否缺少选项，如果缺少则显示提醒文字
                    if (checkQuestionMissingItems(row)) {
                        buttons += '<div class="text-danger mt-1" style="font-size: 12px;"><i class="fa fa-exclamation-triangle mr-1"></i>缺少选项，请设置选项</div>';
                    }
                }
                return buttons;
            }
        }];
        
        // 检查题目是否缺少选项
        function checkQuestionMissingItems(row) {
            // 只检查单选题(1)、多选题(2)、排序题(4)、评分 单选题（5）
            if (row.qType === 1 || row.qType === 2 || row.qType === 4 || row.qType === 5) {
                return !row.listItems || row.listItems.length === 0;
            }
            return false;
        }
        
        let getQueryCondition = function (data) {
            let param = {};
            param.surveyId = getUrlParam('surveyId');
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let oTableA = null;
        let initAList = function () {
            if (oTableA != null) {
                oTableA.destroy();
            }
            oTableA = $("#tbA").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": false,
                "sAjaxSource": "/survey/surveyquestion/get_all_items",
                "fnServerData": retrieveDataA, //执行方法
                //配置列要显示的数据
                "columns": [{ "data": "itemNo", "bSortable": false },
                    { "data": "itemContent", "bSortable": false },
                    {
                        "data": "isOther", "render":
                            function (data, type, full, meta) {
                                if (full.isOther === 1) {
                                    return '<span class="badge badge-danger badge-pill">是</span>';
                                }
                                else {
                                    return '<span class="badge badge-light badge-pill">否</span>';
                                }
                            }, "bSortable": false
                    }
                ],
                "columnDefs":[{
                    targets: 3,
                    render: function (data, type, row, meta) {
                        return '<button class="btn btn-outline-warning btn-sm editA mr-1"><i class="fa fa-pencil-square-o mr-1"></i>编辑</button><button class="btn btn-outline-danger btn-sm delA"><i class="fa fa-trash-o mr-1"></i>删除</button>'
                    }
                }],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "zeroRecords": '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        let getQueryCondition_a = function (data) {
            let param = {};
            param.qId = qIds;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.iDisplayLength = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.iDisplayStart = item.value;
                }
            });
            return param;
        };
        let retrieveDataA = function (sSource, aoData, fnCallback) {
            $.ajax({
                "type": "post",
                "url": sSource,
                "dataType": "json",
                "data": getQueryCondition_a(aoData),
                "success": function (res) {
                    fnCallback(JSON.parse(res));
                }
            });
        };
        let ImportQFileInput = function () {
            let oFile = new Object();
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/survey/surveyquestion/import_question',
                    uploadExtraData: function (previewId, index) {
                        return {
                            surveyId: getUrlParam('surveyId')
                        };
                    },
                    allowedFileExtensions: ['xls', 'xlsx'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-warning btn-sm", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "导入",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file_import_q").on("fileuploaded", function (event, data, previewId, index) {
                    layer.msg('导入完成', { icon: 1, time: 2000 });
                    $("#myModalImportQ").modal('hide');
                    oTable.draw();
                });
            };
            return oFile;
        };
        
        // 检查是否有单选题或多选题没有设置选项
        function checkAllQuestionsHaveItems() {
            return new Promise((resolve, reject) => {
                let surveyId = getUrlParam('surveyId');
                $.ajax({
                    type: 'GET',
                    url: '/survey/surveyquestion/get_list_by_surveyId',
                    data: { surveyId: surveyId },
                    dataType: 'json',
                    success: function(res) {
                        let questions = JSON.parse(res);
                        let missingItems = [];
                        
                        for (let i = 0; i < questions.length; i++) {
                            let question = questions[i];
                            // 检查单选题、多选题和排序题是否有选项
                            if ((question.qType === 1 || question.qType === 2 || question.qType === 4 || question.qType === 5) &&
                                (!question.listItems || question.listItems.length === 0)) {
                                missingItems.push('第' + question.qNumber + '题：' +
                                    question.qContent.replace(/<[^>]*>/g, '').substring(0, 30) +
                                    (question.qContent.replace(/<[^>]*>/g, '').length > 30 ? '...' : ''));
                            }
                        }
                        
                        resolve(missingItems);
                    },
                    error: function() {
                        reject('检查题目选项时发生错误');
                    }
                });
            });
        }
        
        let editor_QContent;
        $(function(){
            ClassicEditor
                .create(document.querySelector('#qContent'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_QContent = editor;
                });
            $("#btnAddQ").click(function () {
                $("#hidQid").val("0");
                $("#qType").val("1");
                editor_QContent.setData("");
                $("#modal-q-title").html("添加题目")
                $("#myModalQ").modal();
            });
            $("#tbQ").on('click', '.editQ', function () {
                $("#qType").val("1");
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidQid").val(data.id);
                $("#qType").val(data.qType);
                editor_QContent.setData(data.qContent);
                $("#modal-q-title").html("修改题目")
                $("#myModalQ").modal();
            });
            //导入题目和答案
            $("#btnImportQAndA").click(function () {
                let oFileInput = new ImportQFileInput();
                oFileInput.Init("txt_file_import_q");
                $("#txt_file_import_q").fileinput('clear');
                $("#myModalImportQ").modal();
            });
            //批量删除题目
            $("#btnBatchDelQ").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/survey/surveyquestion/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //设置选项（单个）
            $("#tbQ").on('click', '.answer', function () {
                $("#itemContent").val("");
                $("#hidAID").val("0");
                $("#isOther").attr("checked", false);
                let data = oTable.row($(this).parents('tr')).data();
                qIds = data.id;
                $("#selQIds").text(data.qContent.replace(/<[^>]*>/g, ''));
                initAList();
                $("#myModalA").modal();
            });
            //编辑选项
            $("#tbA").on('click', '.editA', function () {
                let data = oTableA.row($(this).parents('tr')).data();
                $("#hidAID").val(data.id);
                $("#itemContent").val(data.itemContent);
                if (data.isOther === 1) {
                    $("#isOther").attr("checked", true);
                }
                else {
                    $("#isOther").attr("checked", false);
                }
            });
            //删除选项
            $("#tbA").on('click', '.delA', function () {
                let data = oTableA.row($(this).parents('tr')).data();
                let jsonObj = {};
                jsonObj.itemId = data.id;
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        $.post("/survey/surveyquestion/delete_item", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                initAList();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //保存答案
            $("#frmA").validate({
                rules: {
                    itemContent: {required: true}
                },
                messages: {
                    itemContent: {required: "请填写选项内容"}
                },
                submitHandler: function () {
                    let item = {};
                    item.id = $("#hidAID").val();
                    item.itemContent = $.trim($("#itemContent").val());
                    item.isOther = $("#isOther").prop("checked") ? 1 : 0;
                    item.qId = qIds;
                    $("#btnSaveA").val("请稍后……");
                    $("#btnSaveA").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/survey/surveyquestion/add_item',
                        data: JSON.stringify(item),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveA").val("保存");
                            $("#btnSaveA").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                initAList();
                                oTable.draw();
                                $("#hidAID").val("0");
                                $("#itemContent").val("");
                                $("#isOther").attr("checked", false);
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            $("#tbQ").bsDataTables({
                columns: columns,
                url: '/survey/surveyquestion/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(JSON.parse(res));
                            $("#chkall").change(function () {
                                //实现全选
                                if ($('#chkall').prop("checked") === true) {
                                    $('.checklist').prop("checked", true);
                                }
                                else {
                                    $('.checklist').prop("checked", false);
                                }
                            });
                            
                            // 为没有选项的单选题/多选题行添加高亮显示
                            setTimeout(function() {
                                let table = $("#tbQ").DataTable();
                                table.rows().every(function() {
                                    let rowData = this.data();
                                    let rowNode = this.node();
                                    
                                    if (checkQuestionMissingItems(rowData)) {
                                        $(rowNode).addClass('table-warning');
                                        $(rowNode).attr('title', '该题目缺少选项，请设置选项后再发布问卷');
                                    } else {
                                        $(rowNode).removeClass('table-warning');
                                        $(rowNode).removeAttr('title');
                                    }
                                });
                            }, 100);
                        }
                    });
                }
            });
            //保存条目
            $("#frmQ").validate({
                submitHandler: function () {
                    let jsonObj = {
                        id: $("#hidQid").val(),
                        qContent: editor_QContent.getData(),
                        qType: $("#qType").val(),
                        surveyId: getUrlParam('surveyId')
                    };
                    let url = $("#hidQid").val() === "0" ? "/survey/surveyquestion/add" : "/survey/surveyquestion/update";
                    $("#btnSaveQ").val("请稍后……");
                    $("#btnSaveQ").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveQ").val("保存");
                            $("#btnSaveQ").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                $("#myModalQ").modal('hide');
                                oTable.draw();
                                layer.closeAll();
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            }
                            else {
                                layer.msg(res.resultMsg, {icon: 2, time: 2000});
                            }
                        }
                    });
                }
            });

            $("#btnDone").click(function(){
                // 先检查是否有题目缺少选项
                checkAllQuestionsHaveItems().then(function(missingItems) {
                    if (missingItems.length > 0) {
                        let message = '以下单选题、多选题或排序题还没有设置选项，请先设置选项：<br><br>';
                        message += missingItems.join('<br>');
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + message
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 3000
                        });
                        return;
                    }
                    
                    // 所有题目都有选项，可以发布
                    layer.confirm('确定发布该问卷吗？', {
                        time: 0,
                        icon: 7,
                        btn: ['确定', '取消'],
                        yes: function (index) {
                            let url = "/survey/survey/done";
                            let jsonObj = { surveyId: getUrlParam('surveyId'), state: 1 };
                            $.post(url, jsonObj, function (res) {
                                if (res.resultCode === 200) {
                                    layer.alert(res.resultMsg, {
                                        icon: 1, yes: function (index) {
                                            location.href = "/survey/survey/list";
                                        }
                                    });
                                }
                                else {
                                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                                }
                            });
                        }
                    });
                }).catch(function(error) {
                    layer.msg(error, { icon: 2, time: 2000 });
                });
            });
        });
    </script>
</th:block>
</body>
</html>
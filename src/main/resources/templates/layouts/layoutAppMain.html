<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
<head th:fragment="common_header">
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title th:text="|${ pageData.sysConfigDto.platformName }|"></title>
    <meta name="viewport" content="initial-scale=1, maximum-scale=1, width=device-width">
    <link rel="shortcut icon" th:href="@{/static/images/favicon.ico}">
    <link th:href="@{/static/css/icons.min.css}" rel="stylesheet" />
    <link th:href="@{/static/app/css/style.css}" rel="stylesheet" />
</head>
<body>
    <!-- Page loading -->
    <div class="loading">
        <div class="spinner-grow"></div>
    </div>
    <!-- * Page loading -->
    <th:block layout:fragment="content"></th:block>
    <!-- Sidebar Menu -->
    <div class="sidebarWrapper">
        <div class="overlay toggleSidebar"></div>
        <nav class="sidebar bg-primary">
            <div class="profilebox">
                <th:block th:if="${user ne null }">
                    <a th:href="@{/app/my/upload_avatar}">
                        <img th:if="${user.headPic eq null or user.headPic eq ''}" th:src="@{/static/images/user.png}" class="avatar login" />
                        <img th:unless="${user.headPic eq null or user.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${user.headPic}|" class="avatar login" />
                    </a>
                    <h2 class="title text-light login" th:text="${user.realName}"></h2>
                    <h5 class="lead text-light login" th:text="${user.loginName}"></h5>
                    <div class="button login">
                        <a th:href="@{/app/my/edit_profile}" title="个人设置" class="text-light">
                            <i class="fa fa-cog"></i>
                        </a>
                    </div>
                </th:block>
                <th:block th:if="${user eq null }">
                    <img th:src="@{/static/images/user.png}" class="avatar logout" />
                    <h2 class="title logout"></h2>
                    <h5 class="lead text-light font14"><span>您尚未登录，请 <a id="sideBarLogin" class="text-decoration-underline text-light" href="javascript:">登录</a></span></h5>
                </th:block>
            </div>
            <th:block th:if="${user ne null }">
                <div class="sidebarGroup login">
                    <ul class="sidebarMenu">
                        <li><a th:href="@{/app/my/reset_password}" class="text-light"><i class="fa fa-key mr-1"></i>账号安全</a></li>
                        <li><a th:href="@{/app/measuring/my_tasks}" class="text-light"><i class="fa fa-heartbeat mr-1"></i>我的测评任务</a></li>
                        <li><a th:href="@{/app/measuring/my_records}" class="text-light"><i class="fa fa-list-ul mr-1"></i>我的测评记录</a></li>
                        <li><a th:href="@{/app/activity/my_activities}" class="text-light"><i class="fa fa-gift mr-1"></i>我的活动</a></li>
                        <li><a th:href="@{/app/counseling/my_cases}" class="text-light"><i class="fa fa-folder-open mr-1"></i>我的个案</a></li>
                    </ul>
                </div>
            </th:block>
        </nav>
    </div>
    <script th:src="@{/static/app/js/lib/jquery-3.5.1.min.js}"></script>
    <script th:src="@{/static/app/js/lib/popper.min.js}"></script>
    <script th:src="@{/static/app/js/lib/bootstrap.min.js}"></script>
    <script th:src="@{/static/app/js/plugins/owl.carousel.min.js}"></script>
    <script th:src="@{/static/app/js/plugins/layer_mobile/layer.js}"></script>
    <script th:src="@{/static/app/js/app.js}"></script>
    <script th:src="@{/static/app/js/logout.js}"></script>
    <script th:src="@{/static/js/pages/main.js}"></script>
    <script th:src="@{/static/app/js/adjust_bottom.js}"></script>
    <th:block layout:fragment="common_js" />
</body>
</html>
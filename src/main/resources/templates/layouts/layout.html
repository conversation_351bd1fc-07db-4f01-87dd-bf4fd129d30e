<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
<head th:fragment="common_header">
    <meta charset="utf-8" />
    <title th:text="|${ pageData.sysConfigDto.platformName } - ${ pageData.sysConfigDto.orgName }|"></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" th:href="@{/static/images/favicon.ico}">
    <link th:href="@{/static/css/icons.min.css}" rel="stylesheet"/>
    <link th:href="@{/static/css/app.min-1.0.css}" rel="stylesheet"/>
    <link th:href="@{/static/css/base.css}" rel="stylesheet"/>
</head>
<body>
<!-- Topbar Start -->
<div class="navbar-custom topnav-navbar">
    <div class="container-fluid">
        <!-- LOGO -->
        <a th:href="@{/}" class="topnav-logo text-white font-weight-bold font-16" style="display:flex; align-items:center;">
            <img th:src="@{/static/images/logo-light.png}" alt="" height="28" class="mr-2"><span th:text="${ pageData.sysConfigDto.platformName }"></span>
        </a>
        <ul class="list-unstyled topbar-right-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle arrow-none" href="/" title="平台首页">
                    <i class="fa fa-home noti-icon"></i>
                </a>
            </li>
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle arrow-none" data-toggle="dropdown" href="#" title="访问手机端">
                    <i class="fa fa-qrcode noti-icon"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-menu-animated" aria-labelledby="topbar-notifydrop">
                    <!-- item-->
                    <div class="dropdown-item noti-title">
                        <h5 class="m-0">
                            <img th:src="@{/static/images/qrcode.png}" class="image img-fluid" height="80" />
                        </h5>
                    </div>
                </div>
            </li>
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle nav-user arrow-none mr-0" data-toggle="dropdown" id="topbar-userdrop" href="#" role="button" aria-haspopup="true"
                   aria-expanded="false">
                        <span class="account-user-avatar">
                             <img th:if="${user.headPic eq null or user.headPic eq ''}" th:src="@{/static/images/user.png}" class="rounded-circle"/>
                             <img th:unless="${user.headPic eq null or user.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${user.headPic}|" class="rounded-circle"/>
                        </span>
                        <span class="account-user-name" th:text="${user.realName}"></span>
                        <span class="account-position" th:text="${user.loginName}"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-menu-animated topbar-dropdown-menu profile-dropdown" aria-labelledby="topbar-userdrop">
                    <!-- item-->
                    <div class=" dropdown-header noti-title">
                        <h6 class="text-overflow m-0">欢迎您 !</h6>
                    </div>
                    <!-- item-->
                    <a th:href="@{/anteroom/personal/account_security}" class="dropdown-item notify-item">
                        <i class="fa fa-key mr-2 font-13"></i>
                        <span class="font-13">账号安全</span>
                    </a>
                    <!-- item-->
                    <a id="logout" href="javascript:void(0);" class="dropdown-item notify-item">
                        <i class="fa fa-sign-out mr-2 font-13"></i>
                        <span class="font-13">退出平台</span>
                    </a>
                </div>
            </li>
        </ul>
        <a class="button-menu-mobile">
            <div class="lines">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </a>
    </div>
</div>
<!-- end Topbar -->
<div class="container-fluid">
    <div class="wrapper">
        <!-- ========== Left Sidebar Start ========== -->
        <div class="left-side-menu">
            <div class="leftbar-user">
                <a href="#">
                    <img th:if="${user.headPic eq null or user.headPic eq ''}" th:src="@{/static/images/user.png}" class="rounded-circle shadow-sm" height="42"/>
                    <img th:unless="${user.headPic eq null or user.headPic eq''}" th:src="|@{/static/upload/avatar/thumbnail/}${user.headPic}|" class="rounded-circle shadow-sm" height="42"/>
                    <span class="leftbar-user-name" style="text-align:center;" th:text="${user.realName}"></span>
                </a>
            </div>
            <!-- 功能菜单 -->
            <ul class="metismenu side-nav">
                <th:block th:each="function:${privilegeList}">
                    <li class="side-nav-title side-nav-item font-14 font-weight-normal" th:text="${function.FunctionName}"></li>
                    <th:block  th:each="firstItem:${function.children}">
                        <th:block th:unless="${not #lists.isEmpty(firstItem.children) and firstItem.children.size() gt 0}">
                            <li class="side-nav-item">
                                <a th:href="${firstItem.url}" class="side-nav-link text-black-50"><i th:class="${firstItem.icon}"></i><span th:text="${firstItem.functionName}"></span></a>
                            </li>
                        </th:block>
                        <th:block th:if="${not #lists.isEmpty(firstItem.children) and firstItem.children.size() gt 0}">
                            <li  class="side-nav-item">
                                <a href="javascript:void(0);" class="side-nav-link"><i th:class="${firstItem.Icon}"></i><span th:text="${firstItem.FunctionName}"></span><span class="menu-arrow"></span></a>
                                <ul class="side-nav-second-level" aria-expanded="false">
                                    <th:block th:each="secondItem:${firstItem.children}">
                                        <th:block th:unless="${not #lists.isEmpty(secondItem.children) and secondItem.children.size() gt 0}">
                                            <li><a th:href="${secondItem.url}" th:text="${secondItem.functionName}"></a></li>
                                        </th:block>
                                        <th:block th:if="${not #lists.isEmpty(secondItem.children) and secondItem.children.size() gt 0}">
                                            <li class="side-nav-item">
                                                <a href="javascript: void(0);" aria-expanded="false"><span th:text="${secondItem.functionName}"></span><span class="menu-arrow"></span></a>
                                                <ul class="side-nav-third-level" aria-expanded="false">
                                                    <th:block th:each="thirdItem:${secondItem.children}">
                                                        <li><a th:href="${thirdItem.url}" th:text="${thirdItem.functionName}"></a></li>
                                                    </th:block>
                                                </ul>
                                            </li>
                                        </th:block>
                                    </th:block>
                                </ul>
                            </li>
                        </th:block>
                    </th:block>
                </th:block>
            </ul>
            <div class="help-box text-center">
                <img th:src="@{/static/images/help-icon.png}" width="120" th:alt="心理服务热线" />
                <h5 class="mt-3">壹点灵员工关爱计划</h5>
                <p class="mb-3 font-13">智能化员工心理健康全周期守护者</p>
                <em><span class="text-dark font-weight-500 font-18 text-l" th:text="${pageData.sysConfigDto.counselingHotline}"></span></em>
            </div>
            <!-- End Sidebar -->
            <div class="clearfix"></div>
            <!-- Sidebar -left -->
        </div>
        <!-- Left Sidebar End -->
        <div class="content-page">
            <div class="content">
                <th:block layout:fragment="content"></th:block>
            </div> <!-- content -->
            <!-- Footer Start -->
            <footer class="footer">
                <div class="row">
                    <div class="col-md-8 text-muted font-weight-normal">&copy;壹点灵</div>
                </div>
            </footer>
            <!-- end Footer -->
        </div>
    </div>
</div>
<!-- App js -->
<script th:src="@{/static/js/app.min.js}"></script>
<script th:src="@{/static/js/plugins/layer/layer.js}"></script>
<script th:src="@{/static/js/pages/main.js}"></script>
<th:block layout:fragment="common_js"/>
<script type="text/javascript">
    $(function () {
        //系统注销事件
        $("#logout").click(function () {
            layer.confirm('您确定要退出平台？', {
                time: 0,
                icon: 7,
                btn: ['确定', '取消'],
                yes: function (index) {
                    $.post("/account/logout", "", function (res) {
                        if (res.resultCode === 200) {
                            location.href = '/account/login?returnUrl='+returnUrl;
                        }
                    });
                }
            });
        });
    });
</script>
</body>
</html>


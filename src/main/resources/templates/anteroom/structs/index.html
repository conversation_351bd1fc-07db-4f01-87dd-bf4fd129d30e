<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">组织设置</a></li>
                    </ol>
                </div>
                <h4 class="page-title">组织设置</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-danger hide" id="alert">
                <i class="fa fa-info-circle mr-1"></i> 抱歉，暂时没有任何组织信息。前往
                <button id="btnAddStruct" class="btn btn-outline-danger btn-sm ml-2">添加</button>
            </div>
            <div class="card structs">
                <div class="card-body">
                    <button id="btnSyncStructs" class="btn btn-outline-primary mb-2 hide"><i class="fa fa-refresh mr-1"></i>同步组织</button>
                    <div class="form-group ">
                        <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                    </div>
                    <div class="form-group ">
                        <ul id="structTree" class="ztree pt-3 pl-3 none-border"></ul>
                    </div>
                    <div class="form-group hide">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="toll-free-box text-left">
                                    <span> <i class="fa fa-question font-96"></i></span>
                                    <ul class="list-unstyled pl-3">
                                        <li class="mb-1">
                                            组织相关操作说明：
                                        </li>
                                        <li class="mb-1">
                                            将鼠标移到节点上时会出现操作按钮。
                                        </li>
                                        <li class="mb-1">
                                            添加：点击“添加”时，会弹出新增窗口，输入组织名称。
                                        </li>
                                        <li class="mb-1">
                                            修改：点击“修改”时，会弹出修改窗口，输入想要修改的内容即可。
                                        </li>
                                        <li class="mb-1">
                                            删除：点击“删除”时，会出现确认操作提示框，点击“确定”会完成删除操作。 <span class="text-danger">如果节点下面有子节点，会一起删除。</span>
                                        </li>
                                        <li class="mb-1">
                                            组织转移：鼠标拖动需要转移的组织节点到目标节点上即可。
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.添加/修改组织 start -->
    <div id="struct-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title" id="modal-struct-title"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="frmStruct" class="pl-3 pr-3" action="#">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="structName">组织名称</label>
                            <input id="structName" name="structName" class="form-control" type="text">
                        </div>
                        <input id="hidStructID" type="hidden" value="0" />
                        <input type="reset" hidden />
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>&nbsp;&nbsp;
                        <input type="submit" class="btn btn-primary" id="btnSave" value="保存" />
                    </div>
                </form>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.excheck.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exedit.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script type="text/javascript">
        $(function () {
            initStructs();
            $("#btnAddStruct").click(function () {
                resetForm();
                $("#modal-struct-title").html("添加组织");
                $("#struct-modal").modal();
            });
            $("#frmStruct").validate({
                rules: {
                    structName: { required: true }
                },
                messages: {
                    structName: { required: "请输入组织名称" }
                },
                submitHandler: function () {
                    let url = $("#hidStructID").val() === "0" ? "/anteroom/structs/add" : "/anteroom/structs/update";
                    let jsonObj = {
                        id: $("#hidStructID").val(),
                        structName: $.trim($("#structName").val()),
                        orgCode: "",
                        orgParentCode: "",
                        parentId: newCount
                    };
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                $("#struct-modal").modal('hide');
                                initStructs();
                                $("#alert").removeClass("show").addClass('hide');
                                $(".structs").removeClass("hide").addClass('show');
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            $("#btnSyncStructs").click(function () {
                layer.confirm('提示：执行该操作会先清空现有平台的组织数据，确定开始同步组织架构吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        layer.msg('数据同步中……', {
                            icon: 17, shade: 0.05, time: false
                        });
                        $.ajax({
                            url: "/anteroom/structs/sync_structs",
                            timeout: 240000,
                            type: "post",
                            data: '',
                            dataType: "json",
                            success: function (data) {
                                if (data.resultCode === 200) {
                                    layer.msg(data.resultMsg, { icon: 1, time: 2000 });
                                    initStructs();
                                    $("#alert").removeClass("show").addClass('hide');
                                    $(".structs").show();
                                }
                                else {
                                    layer.msg(data.resultMsg, { icon: 2, time: 2000 });
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                //alert(XMLHttpRequest.status);
                            }
                        });
                    }
                });
            });
        });
        let resetForm = function () {
            $("#frmStruct input").removeClass("error");
            $("label.error").hide();
            $("#frmStruct input[type='reset']").click();
            $("#hidStructID").val(0);
        };
        let newCount = 0;
        let addHoverDom = function (treeId, treeNode) {
            let sObj = $("#" + treeNode.tId + "_span");
            if (treeNode.editNameFlag || $("#addBtn_" + treeNode.tId).length > 0) return;
            let addStr = "<span class='button add' id='addBtn_" + treeNode.tId + "' title='添加组织' onfocus='this.blur();'></span>";
            sObj.after(addStr);
            let btn = $("#addBtn_" + treeNode.tId);
            if (btn) btn.bind("click", function () {
                if ('[[${canAdd}]]' !== 'true') {
                    layer.msg("抱歉，您没有此操作权限。");
                    return false;
                }
                newCount = treeNode.id;
                resetForm();
                $("#modal-struct-title").html("添加组织");
                $("#struct-modal").modal();
                return false;
            });
        };
        let removeHoverDom = function (treeId, treeNode) {
            $("#addBtn_" + treeNode.tId).unbind().remove();
        };
        let beforeEditName = function (treeId, treeNode) {
            if ('[[${canUpdate}]]' !== 'true') {
                layer.msg("抱歉，您没有此操作权限");
                return false;
            }
            else {
                resetForm();
                let zTree = $.fn.zTree.getZTreeObj("structTree");
                zTree.selectNode(treeNode);
                $("#modal-struct-title").html("编辑组织");
                $("#structName").val(treeNode.name);
                $("#hidStructID").val(treeNode.id);
                newCount = treeNode.pId;
                $("#struct-modal").modal();
                return false;
            }
        };
        let beforeRemove = function (treeId, treeNode) {
            let zTree = $.fn.zTree.getZTreeObj("structTree");
            zTree.selectNode(treeNode);
            if ('[[${canDelete}]]' !== 'true') {
                layer.msg("对不起，您没有此操作权限");
                return false;
            }
            if (treeNode.level === 0) {
                layer.msg("不允许删除该组织", { icon: 2, time: 2000 });
                return false;
            }
            let isDeleted = false;
            layer.confirm('确定删除吗？', {
                time: 0,
                icon: 7,
                btn: ['确定', '取消'],
                yes: function (index) {
                    let jsonObj = { id: treeNode.id };
                    $.post("/anteroom/structs/delete", jsonObj, function (res) {
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            initStructs();
                            isDeleted = true;
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }, 'json');
                }
            });
            return isDeleted;
        };
        let beforeRename = function (treeId, treeNode, newName, isCancel) {
        };
        let onDrop = function (event, treeId, treeNodes, targetNode, moveType) {
            let jsonObj = {};
            jsonObj.targetNode = targetNode.id;
            jsonObj.node = treeNodes[0].id;
            $.post('/anteroom/structs/move', jsonObj, function (res) {
                if (res.resultCode === 200) {
                    layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                    initStructs();
                }
                else {
                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                }
            }, "json");
        }
        let setting = {
            view: {
                addHoverDom: addHoverDom,
                removeHoverDom: removeHoverDom,
                dblClickExpand: false,
                selectedMulti: false
            },
            edit: {
                drag: {
                    isCopy: false,
                    isMove: true,
                    prev: true,
                    next: true,
                    inner: true,
                    autoOpenTime: 0,
                    minMoveSize: 10
                },
                enable: true,
                editNameSelectAll: true,
                removeTitle: "删除组织",
                renameTitle: "编辑组织"
            },
            data: {
                simpleData: {
                    enable: true,
                    idKey: "id",
                    pIdKey: "pId",
                    rootPId: 0
                },
                key: {
                    name: "name"
                }
            },
            callback: {
                beforeEditName: beforeEditName,
                beforeRemove: beforeRemove, 	// 用于捕获节点被删除之前的事件回调函数，并且根据返回值确定是否允许删除操作
                beforeRename: beforeRename, 	// 用于捕获节点编辑名称结束（Input 失去焦点 或 按下 Enter 键）之后，更新节点名称数据之前的事件回调函数，并且根据返回值确定是否允许更改名称的操作
                onDrop: onDrop
            }
        };
        let initStructs = function () {
            layer.msg('数据加载中…', {
                icon: 17, shade: 0.05, time: false
            });
            $.post("/anteroom/structs/index", "", function (data) {
                let res = JSON.parse(data);
                layer.closeAll();
                if (res.length === 0) {
                    $("#alert").removeClass("hide");
                    $(".structs").removeClass('show').addClass('hide');
                }
                else {
                    $.fn.zTree.init($("#structTree"), setting, res);
                    fuzzySearch('structTree', '#search', true, true); //初始化模糊搜索方法
                    let zTree = $.fn.zTree.getZTreeObj("structTree");
                    let nodes = zTree.getNodes();
                    for (let i = 0; i < nodes.length; i++) {
                        zTree.expandNode(nodes[i], true, false, true);
                    }
                }
            }, 'json');
        };
    </script>
</th:block>
</body>
</html>
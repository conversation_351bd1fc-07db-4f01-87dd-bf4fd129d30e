<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询师管理</a></li>
                        <li class="breadcrumb-item active">修改咨询师</li>
                    </ol>
                </div>
                <h4 class="page-title">咨询师管理</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card pl-3">
                <div class="card-body">
                    <h4 class="mb-3 header-title"><i class="fa fa-pencil-square-o mr-1"></i>修改咨询师</h4>
                    <form id="frmUser" class="form-horizontal col-8">
                        <div class="form-group row mb-3">
                            <label for="loginName" class="col-form-label col-3">用户名</label>
                            <div class="col-9">
                                <input id="loginName" name="loginName" class="form-control" type="text" readonly>
                                <input id="hidLoginName" name="hidLoginName" type="hidden" value="" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="structName" class="col-form-label col-3">所属组织</label>
                            <div class="col-9">
                                <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择" />
                                <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                    <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                    <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                </div>
                                <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="role" class="col-form-label col-3">咨询师类型</label>
                            <div class="col-9">
                                <select id="role" name="role" class="form-control">
                                    <option></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="realName" class="col-form-label col-3">姓名</label>
                            <div class="col-9">
                                <input id="realName" name="realName" class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="male" class="col-form-label col-3">性别</label>
                            <div class="col-9 form-inline">
                                <div class="custom-control custom-radio mr-2">
                                    <input type="radio" id="male" name="sex" class="custom-control-input" value="男" checked>
                                    <label class="custom-control-label" for="male">男</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" id="female" name="sex" class="custom-control-input" value="女">
                                    <label class="custom-control-label" for="female">女</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-form-label col-3">出生日期</label>
                            <div class="input-group col-9">
                                <input type="text" id="birth" name="birth" class="form-control" readonly="readonly" onClick="WdatePicker({el:this,dateFmt:'yyyy-MM-dd'})" placeholder="点击选择…">
                                <div class="input-group-append">
                                    <button class="btn btn-light" type="button" value="发送短信"><i class="fa fa-calendar"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-form-label col-3">民族</label>
                            <div class="col-9">
                                <select class="form-control select2" data-toggle="select2" name="nation" id="nation">
                                    <option value="">请选择</option>
                                    <option value="汉族">汉族</option>
                                    <option value="满族">满族</option>
                                    <option value="回族">回族</option>
                                    <option value="蒙古族">蒙古族</option>
                                    <option value="壮族">壮族</option>
                                    <option value="藏族">藏族</option>
                                    <option value="阿昌族">阿昌族</option>
                                    <option value="白族">白族</option>
                                    <option value="保安族">保安族</option>
                                    <option value="布朗族">布朗族</option>
                                    <option value="布依族">布依族</option>
                                    <option value="朝鲜族">朝鲜族</option>
                                    <option value="达斡尔族">达斡尔族</option>
                                    <option value="傣族">傣族</option>
                                    <option value="德昂族">德昂族</option>
                                    <option value="东乡族">东乡族</option>
                                    <option value="侗族">侗族</option>
                                    <option value="独龙族">独龙族</option>
                                    <option value="俄罗斯族">俄罗斯族</option>
                                    <option value="鄂伦春族">鄂伦春族</option>
                                    <option value="鄂温克族">鄂温克族</option>
                                    <option value="高山族">高山族</option>
                                    <option value="哈尼族">哈尼族</option>
                                    <option value="哈萨克族">哈萨克族</option>
                                    <option value="赫哲族">赫哲族</option>
                                    <option value="基诺族">基诺族</option>
                                    <option value="京族">京族</option>
                                    <option value="景颇族">景颇族</option>
                                    <option value="柯尔克孜族">柯尔克孜族</option>
                                    <option value="拉祜族">拉祜族</option>
                                    <option value="黎族">黎族</option>
                                    <option value="傈僳族">傈僳族</option>
                                    <option value="毛南族">毛南族</option>
                                    <option value="门巴族">门巴族</option>
                                    <option value="苗族">苗族</option>
                                    <option value="纳西族">纳西族</option>
                                    <option value="怒族">怒族</option>
                                    <option value="普米族">普米族</option>
                                    <option value="羌族">羌族</option>
                                    <option value="撒拉族">撒拉族</option>
                                    <option value="水族">水族</option>
                                    <option value="塔吉克族">塔吉克族</option>
                                    <option value="塔塔尔族">塔塔尔族</option>
                                    <option value="土家族">土家族</option>
                                    <option value="土族">土族</option>
                                    <option value="维吾尔族">维吾尔族</option>
                                    <option value="乌孜别克族">乌孜别克族</option>
                                    <option value="锡伯族">锡伯族</option>
                                    <option value="瑶族">瑶族</option>
                                    <option value="彝族">彝族</option>
                                    <option value="裕固族">裕固族</option>
                                    <option value="仡佬族">仡佬族</option>
                                    <option value="仫佬族">仫佬族</option>
                                    <option value="佤族">佤族</option>
                                    <option value="珞巴族">珞巴族</option>
                                    <option value="畲族">畲族</option>
                                    <option value="其他少数民族">其他少数民族</option>
                                </select>
                            </div>

                        </div>
                        <div class="form-group row mb-3">
                            <label for="mobile" class="col-form-label col-3">联系电话</label>
                            <div class="col-9">
                                <input id="mobile" name="mobile" class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-3">是否推荐</label>
                            <div class="col-9">
                                <div class="custom-control custom-switch pl-0">
                                    <input type="checkbox" id="isRecommend" data-switch="danger" />
                                    <label for="isRecommend" data-on-label="" data-off-label=""></label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-form-label col-3" for="beGoodAt">咨询方向</label>
                            <div class="col-9">
                                <textarea id="beGoodAt" name="beGoodAt" class="form-control" rows="4" maxlength="100" placeholder="填写咨询方向、擅长领域……"></textarea>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-form-label col-3" for="editor_content">咨询师介绍</label>
                            <div class="col-9">
                                <textarea id="editor_content"></textarea>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-form-label col-3">头像</label>
                            <div class="col-9">
                                <div class="ml-3">
                                    <img id="avatar" th:src="@{/static/images/user.png}" class="img-responsive rounded-circle mb-2" style="width:128px;" />
                                </div>
                                <div class="ml-3">
                                    <input type="file" name="file" id="txt_file" class="file-loading" />
                                    <small class="text-warning"><i class="fa fa-info-circle mr-1"></i>建议像素大小：128*128px</small>
                                    <input id="hidAvatar" type="hidden" value="" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-0 justify-content-end row">
                            <div class="col-9">
                                <input type="submit" id="btnSave" class="btn btn-primary" value="保存" />
                                <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                                <input type="hidden" id="hidUserID" value="0" />
                            </div>
                        </div>
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/DatePicker/WdatePicker.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script th:src="@{/static/js/plugins/ckeditor5/ckeditor.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        let userId = getUrlParam("id");
        let editor_content;
        $(function () {
            ClassicEditor
                .create(document.querySelector('#editor_content'), {
                    ckfinder: {
                        uploadUrl: '/fileUpload/ckEditor'
                    }
                })
                .then(editor => {
                    editor_content = editor;
                });
            $("#structName").click(function () {
                initTree();
            });
            initSelect("#role", "/platform/role/get_for_select", { flag: "j" });
            initForm();
            $("#frmUser").validate({
                //ignore: "",
                errorPlacement: function (error, element) {
                    wrap = element.parent();
                    if (wrap.hasClass('input-group')) {
                        error.insertAfter(wrap);
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    hidStructParentID: { required: true },
                    loginName: {
                        required: true,
                        remote: {
                            type: "POST",
                            url: "/anteroom/user/check_loginname",
                            dataType: "json",
                            data: {
                                loginName: function () {
                                    return $.trim($("#loginName").val());
                                },
                                hidLoginName: function () {
                                    return $.trim($("#hidLoginName").val());
                                }
                            },
                            dataFilter: function (data, type) {
                                return data === "0";
                            }
                        }
                    },
                    role: { required: true },
                    realName: { required: true },
                    birth: { required: true }
                },
                messages: {
                    hidStructParentID: { required: "请选择所属组织" },
                    loginName: { required: "请输入登录名", remote: "该用户名已经存在" },
                    role: { required: "请选择咨询师类型" },
                    realName: { required: "请输入姓名" },
                    birth: { required: "请选择出生日期" }
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.userId = $("#hidUserID").val();
                    jsonObj.loginName = $.trim($("#loginName").val());
                    jsonObj.structId = $("#hidStructParentID").val();
                    jsonObj.roleId = $("#role").val();
                    jsonObj.realName = $.trim($("#realName").val());
                    jsonObj.sex = $('input[name="sex"]:checked').val();
                    jsonObj.birth = $("#birth").val();
                    jsonObj.nation = $("#nation").val();
                    jsonObj.mobile = $("#mobile").val();
                    jsonObj.headPic = $("#hidAvatar").val();
                    let counselorInfo = {};
                    counselorInfo.userId = $("#hidUserID").val();
                    counselorInfo.isRecommend = $("#isRecommend").prop("checked") ? 1 : 0;
                    counselorInfo.beGoodAt = $("#beGoodAt").val();
                    counselorInfo.counselorIntro = editor_content.getData();
                    jsonObj.counselorInfo = counselorInfo;
                    $("#btnSave").val("请稍后…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: "post",
                        async: false,
                        url: "/anteroom/user/update",
                        data: JSON.stringify(jsonObj),
                        contentType: 'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, {
                                    icon: 1, yes: function (index) {
                                        location.reload();
                                    }
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            let oFileInput = new FileInput();
            oFileInput.Init("txt_file");
        });
        let FileInput = function () {
            let oFile = {};
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=avatar',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-warning", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidAvatar").val(res.resultMsg);
                        $("#avatar").attr("src", res.resultMsg === "" ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + res.resultMsg);
                        layer.msg("头像上传成功", { icon: 1, time: 2000 });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            };
            return oFile;
        };
        let initForm = function () {
            $.post("/anteroom/user/get?id=" + userId, '', function (data) {
                let res = JSON.parse(data);
                $("#structName").val(res.structName);
                $("#role").val(res.roleId).trigger('change');
                $("#hidStructParentID").val(res.structId);
                $("#loginName").val(res.loginName);
                $("#hidLoginName").val(res.loginName);
                $("#realName").val(res.realName);
                $("#hidUserID").val(userId);
                if (res.sex === "男") {
                    $("#male").attr("checked", true);
                }
                else {
                    $("#female").attr("checked", true);
                }
                $("#birth").val(res.birth);
                $("#nation").val(res.nation).trigger('change');
                $("#mobile").val(res.mobile);
                if(res.counselorInfo !=undefined){
                    if (res.counselorInfo.isRecommend === 1) {
                        $("#isRecommend").attr("checked", true);
                    }
                    else {
                        $("#isRecommend").attr("checked", false);
                    }
                    $("#beGoodAt").val(res.counselorInfo.beGoodAt ==null ? "" : res.counselorInfo.beGoodAt);
                    editor_content.setData(res.counselorInfo.counselorIntro == null ? "" : res.counselorInfo.counselorIntro);
                }
                $("#avatar").attr("src", (res.headPic ==null || res.headPic === "") ? "/static/images/user.png" : "/static/upload/avatar/" + res.headPic);
            });
        };
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">咨询师管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">咨询师列表</a></li>
                    </ol>
                </div>
                <h4 class="page-title">咨询师列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="form-group mr-1">
                                    <select id="sr-role" name="sr-role" class="form-control">
                                    </select>
                                </div>
                                <div class="form-group mr-1">
                                    <label for="sr-loginName" class="sr-only">用户名：</label>
                                    <input type="text" class="form-control" id="sr-loginName" name="sr-loginName" placeholder="用户名" autocomplete="off" style="width:100px;">
                                </div>
                                <div class="form-group mr-1">
                                    <label for="sr-realName" class="sr-only">姓名：</label>
                                    <input type="text" class="form-control" id="sr-realName" name="sr-realName" placeholder="姓名" autocomplete="off" style="width:100px;">
                                </div>
                                <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-primary mb-2" title="新增咨询师"><i class="fa fa-user-plus"></i></button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2" title="删除咨询师"><i class="fa fa-trash-o"></i></button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbCounselor" class="table table-centered table-striped nowrap" width="100%">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th class="text-left">用户名</th>
                                <th class="text-left">姓名</th>
                                <th class="text-left">咨询师类型</th>
                                <th class="text-left">所属机构</th>
                                <th class="text-left">是否推荐</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>
    <!-- modal.密码设置 start-->
    <div id="password-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <form id="frmModifyPwd" action="" method="post" role="form">
                <div class="modal-content">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title">密码设置</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="newPwd" class="col-form-label">密码</label>
                            <input id="newPwd" name="newPwd" class="form-control" type="password" aria-describedby="pwdHelp">
                            <small id="pwdHelp" class="form-text text-muted">长度至少8位以上，由数字、大小写字母和特殊字符组成</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm_newPwd" class="col-form-label">确认新密码</label>
                            <input id="confirm_newPwd" name="confirm_newPwd" class="form-control" type="password">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>&nbsp;&nbsp;
                        <input type="submit" class="btn btn-primary" id="btnSavePwd" value="保存" />
                        <input id="hidUserID" type="hidden" value="0" />
                        <input type="reset" hidden />
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- modal.密码设置 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnDel").show() : $("#btnDel").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
            initSelect("#sr-role", "/platform/role/get_for_select", { flag: "j" }, '', '咨询师类型');
        };
        let columns = [{
            "data": "userId", "render":
                function (data, type, full, meta) {
                    return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                }, "bSortable": false
        },
            {
                "sClass": "text-left", "data": "loginName", "bSortable": false, "render":
                    function (data, type, full, meta) {
                        if (full.headPic ==undefined || full.headPic == '') {
                            return '<img src="/static/images/user.png" class="mr-2 rounded-circle" width="40">' + full.loginName + '';
                        }
                        else {
                            return '<img src="/static/upload/avatar/thumbnail/' + full.headPic + '" class="mr-2 rounded-circle" width="40">' + full.loginName + '';
                        }
                    }
            },
            { "sClass": "text-left", "data": "realName", "bSortable": false },
            { "sClass": "text-left", "data": "role.roleName", "bSortable": false },
            { "sClass": "text-left", "data": "structFullName", "bSortable": false },
            {
                "sClass": "text-left", "data": "counselorInfo.isRecommend", "render":
                    function (data, type, full, meta) {
                        if (full.counselorInfo != undefined && full.counselorInfo.isRecommend === 1) {
                            return '<span class="badge badge-success badge-pill">是</span>';
                        }
                        else {
                            return '<span class="badge badge-light badge-pill">否</span>';
                        }
                    }, "bSortable": false
            }
        ];
        let columnDefs = [{
            targets: 6, render: function (data, type, row, meta) {
                let buttons = "";
                if ('[[${canUpdate}]]' === 'true') buttons += '<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update" title="修改"><i class="fa fa-pencil-square-o"></i></button>';
                if ('[[${canResetPwd}]]' === 'true') buttons += '<button type = "button" class="btn btn-outline-info btn-sm mr-1 password" title="密码设置"><i class="fa fa-key"></i></button>';
                return buttons;
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            param.loginName = $("#sr-loginName").val();
            param.realName = $("#sr-realName").val();
            param.roleId = $("#sr-role").val();
            param.isChecked = 1;
            param.flag = 'j';
            param.isCounselor = 1;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let resetForm = function () {
            $("#frmModifyPwd input").removeClass("error");
            $("label.error").hide();
            $("#frmModifyPwd input[type='reset']").click();
            $("#hidUserID").val(0);
        };
        $(function () {
            //初始化页面权限
            initPage();
            $("#btnQuery").click(function () {
                $('#tbCounselor').DataTable().ajax.reload();
            });
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") == true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
            //datatables
            $("#tbCounselor").bsDataTables({
                columns: columns,
                url: '/anteroom/user/get_counselor_list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType":"application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            //新增
            $("#btnAdd").click(function () {
                location.href = "/anteroom/counselor/add";
            });
            //修改
            $("#tbCounselor").on('click', '.update', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/anteroom/counselor/update?id=" + data.userId;
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        if (array.indexOf("[[${user.role.roleId}]]") > -1) {
                            layer.msg('操作失败，不能删除当前登录账户', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = array.join(',');;
                        $.post("/anteroom/user/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //密码设置
            $("#tbCounselor").on('click', '.password', function () {
                resetForm();
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidUserID").val(data.userId);
                $("#password-modal").modal();
            });
            $.validator.addMethod('check_password', function (value, element, param) {
                let strPwd = $('#newPwd').val();
                let regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[a-zA-Z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,}$/;
                return regex.test(strPwd);
            }, "密码不符合要求");
            $("#frmModifyPwd").validate({
                rules: {
                    newPwd: {
                        required: true,
                        check_password: true
                    },
                    confirm_newPwd: {
                        required: true,
                        equalTo: "#newPwd"
                    }
                },
                messages: {
                    newPwd: {
                        required: "请输入新密码",
                        check_password: "密码不符合要求"
                    },
                    confirm_newPwd: {
                        required: "请输入确认密码", equalTo: "两次输入密码不一致"
                    }
                },
                submitHandler: function () {
                    $("#btnSavePwd").val("保存中……");
                    $("#btnSavePwd").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.userId = $("#hidUserID").val();
                    jsonObj.newPwd = $("#newPwd").val();
                    $.ajax({
                        type: 'POST',
                        url: '/anteroom/user/modify_pwd',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSavePwd").val("保存");
                            $("#btnSavePwd").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                $("#password-modal").modal('hide');
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">个人中心</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">我的消息</a></li>
                    </ol>
                </div>
                <h4 class="page-title"><i class="fa fa-envelope mr-1"></i>我的消息</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="page-aside-right">
                        <div class="btn-group">
                            <button id="btnRefresh" type="button" class="btn btn-light" title="刷新"><i class="fa fa-refresh font-16"></i></button>
                            <button id="btnReadState" type="button" class="btn btn-light" title="标记已读"><i class="fa fa-star-half-full font-16"></i></button>
                            <button id="btnDel" type="button" class="btn btn-light" title="删除"><i class="fa fa-trash-o font-16"></i></button>
                        </div>
                        <div class="mt-3">
                            <table id="mailList" class="table table-striped table-centered">
                                <thead>
                                <tr>
                                    <th style="width: 30px;">
                                        <div class="custom-control custom-checkbox">
                                            <input id="chkall" class="custom-control-input check" type="checkbox">
                                            <label class="custom-control-label" for="chkall"></label>
                                        </div>
                                    </th>
                                    <th class="text-left">状态</th>
                                    <th class="text-left">发件人</th>
                                    <th class="text-left">标题</th>
                                    <th class="text-left">时间</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        let columns = [{
            "data": "id", "render":
                function (data, type, full, meta) {
                    return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                }, "bSortable": false
        },
            {
                "sClass": "text-left", "data": "isRead", "render":
                    function (data, type, full, meta) {
                        let label = "";
                        if (data === 0) {
                            label += '<span class="badge badge-success">未读</span>';
                        }
                        else {
                            label += '<span class="badge badge-danger">已读</span>';
                        }
                        return label;
                    }, "bSortable": false,
            },
            { "sClass": "text-left", "data": "fromName", "render":
                    function (data, type, full, meta) {
                        let label = data;
                        if (full.fromUser === 1) {
                            label = '系统消息';
                        }
                        return label;
                    },"bSortable": false },
            {
                "sClass": "text-left", "data": "msgTitle", "render":
                    function (data, type, full, meta) {
                        return '<a href="/anteroom/mail/read?id=' + full.id + '" target="_blank">' + data + '</a>';
                    }, "bSortable": false
            },
            { "sClass": "text-left", "data": "sendDate", "bSortable": false }
        ];
        let columnDefs = [];
        let getQueryCondition = function (data) {
            let param = {};
            param.toUser = [[${user.userId}]];
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name == "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name == "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        $(function () {
            $("#btnRefresh").click(function () {
                oTable.draw();
            });
            $("#chkall").change(function () {
                if ($('#chkall').prop("checked") === true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
            $("#mailList").bsDataTables({
                columns: columns,
                url: '/anteroom/mail/list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
            $("#btnReadState").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                let ids = "";
                let array = [];
                $("input[name='checklist']:checked").each(function () {
                    array.push($(this).val());
                });
                ids = array.join(',');
                if (ids === "") {
                    layer.msg('操作失败', { icon: 2, time: 2000 });
                    return;
                }
                let jsonObj = {};
                jsonObj.ids = ids;
                $.post("/anteroom/mail/update_read_state", jsonObj, function (res) {
                    if (res.resultCode === 200) {
                        layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                        oTable.draw();
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                }, 'json');
            });
            //批量删除
            $("#btnDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/anteroom/mail/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
        });
    </script>
</th:block>
</body>
</html>
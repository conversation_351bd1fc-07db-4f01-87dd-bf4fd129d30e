<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">个人中心</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">我的消息</a></li>
                    </ol>
                </div>
                <h4 class="page-title"><i class="fa fa-envelope mr-1"></i>我的消息</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="page-aside-right">
                        <div class="mt-1">
                            <h5 class="font-18" th:text="${mail.msgTitle}"></h5>
                            <hr />
                            <div class="media mb-3 mt-1">
                                <div class="media-body">
                                    <small class="float-right font-14" th:text="${#dates.format(mail.sendDate,'yyyy-MM-dd HH:mm:ss')}"></small>
                                    <small class="text-muted font-14">
                                        <th:block th:if="${mail.fromUser eq 1}" th:text="系统消息" />
                                        <th:block th:unless="${mail.fromUser eq 1}"  th:text="${mail.fromName}" />
                                    </small>
                                </div>
                            </div>
                            <p th:utext="${mail.msgContent}"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let id = getUrlParam("id");
        $(function () {
            let jsonObj = {};
            jsonObj.ids = id;
            $.post("/anteroom/mail/update_read_state", jsonObj, function (res) {

            }, 'json');
        });
    </script>
</th:block>
</body>
</html>
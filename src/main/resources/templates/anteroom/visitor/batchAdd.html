<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">来访者管理</a></li>
                        <li class="breadcrumb-item active">批量开通</li>
                    </ol>
                </div>
                <h4 class="page-title">来访者管理</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="card-widgets">
                        <a href="#" data-toggle="remove">&times;</a>
                    </div>
                    <h5 class="card-title mb-0"><i class="fa fa-info-circle mr-1"></i>操作指导</h5>
                    <div class="collapse pt-3 show">
                        <ul>
                            <li class="mb-2">
                                譬如生成1001、1002、1003、 ... 、1010
                            </li>
                            <li class="mb-2">
                                用户名填 10*
                            </li>
                            <li class="mb-2">
                                范围填 1，10
                            </li>
                            <li class="mb-2">
                                通配符长度为2，因为是01至10都是两位数。
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <form id="frmUser">
                        <div class="form-group">
                            <label for="structName" class="col-form-label">所属组织</label>
                            <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择" />
                            <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                            </div>
                            <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                        </div>
                        <div class="form-group">
                            <label for="loginName" class="col-form-label">用户名</label>
                            <input type="text" class="form-control col-sm-6" id="loginName" name="loginName" autocomplete="off" value="*" aria-describedby="nameHelp">
                            <small id="nameHelp" class="form-text text-muted"><i class="fa fa-info-circle mr-1"></i>用户名里的“*”代表替换的部分</small>
                        </div>
                        <div class="form-group">
                            <label class="control-label">范围</label>
                            <div class="form-inline">
                                <input type="number" class="form-control input-sm col-sm-2 mr-1" name="s1" id="s1" value="" autocomplete="off" />到<input type="number" class="form-control input-sm col-sm-2 ml-1" name="s2" id="s2" value="" autocomplete="off" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class=" control-label">通配符长</label>
                            <input type="number" class="form-control col-sm-6" id="len" name="len" autocomplete="off" value="">
                        </div>
                        <div class="form-group">
                            <button id="btnPreview" class="btn btn-outline-warning" type="submit"><i class="fa fa-eye mr-1"></i>预览</button>
                            <button id="btnSave" class="btn btn-primary" type="button"><i class="fa fa-check mr-1"></i>开通</button>
                        </div>
                    </form>
                </div> <!-- end card-body-->
                <div class="card-footer">
                    <table id="tbUser" class="table table-striped">
                        <thead>
                        <tr>
                            <th class="text-left">#</th>
                            <th class="text-left">用户名</th>
                            <th class="text-left">状态</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script type="text/javascript">
        let previewUsers={};
        $(function () {
            $("#tbUser").hide();
            $("#structName").click(function () {
                initTree();
            });

            $("#btnPreview").click(function () {
                $.validator.addMethod('checkLoginName', function (value, element, param) {
                    if (value === '*') {
                        return false;
                    }
                    else {
                        let regex = /^[\u4E00-\u9FA5A-Za-z0-9]{0,16}\*$/;
                        return this.optional(element) || (regex.test(value));
                    }
                }, '请输入正确的用户名');
                $.validator.addMethod('compare', function (value, element, param) {
                    let start = $("#s1").val();
                    let end = $("#s2").val();
                    return (parseInt(end) > parseInt(start));
                });
                $("#frmUser").validate({
                    ignore: "",
                    rules: {
                        hidStructParentID: { required: true },
                        loginName: { required: true, checkLoginName: true },
                        s1: { required: true, compare: true, digits: true },
                        s2: { required: true, compare: true, digits: true },
                        len: { required: true, digits: true }
                    },
                    messages: {
                        hidStructParentID: { required: "请选择所属组织" },
                        loginName: { required: "请填写用户名规则", checkLoginName: "用户名规则不正确" },
                        s1: { required: "请填写范围", compare: "范围不正确", digits: "输入的范围格式不正确" },
                        s2: { required: "请填写范围", compare: "范围不正确", digits: "输入的范围格式不正确" },
                        len: { required: "请填写通配符长度", digits: "输入的通配符长度格式不正确" }
                    },
                    submitHandler: function () {
                        layer.msg('请稍后…', {
                            icon: 17, shade: 0.2, time: false
                        });
                        let jsonObj = getQueryCondition();
                        $.ajax({
                            type: 'POST',
                            async: true,
                            url: '/anteroom/user/preview',
                            data: JSON.stringify(jsonObj),
                            contentType:'application/json',
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll()
                                previewUsers =  res;
                                initTD(previewUsers);
                                $("#tbUser").show();
                            }
                        });

                    }
                });
            });
            $("#btnSave").click(function () {
                layer.msg('请稍后…', {
                    icon: 17, shade: 0.2, time: false
                });
                $.ajax({
                    type: 'POST',
                    url: '/anteroom/user/batch_add',
                    data: JSON.stringify(previewUsers),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        layer.closeAll();
                        if (res.resultCode === 200) {
                            layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                            location.reload();
                        }
                        else {
                            layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                        }
                    }
                });
            });
        });

        let columns = [
            { "sClass": "text-left", "data": "userId", "bSortable": false },
            { "sClass": "text-left", "data": "loginName", "bSortable": false },
            { "sClass": "text-left", "data": "description", "bSortable": false }
        ];
        let columnDefs = [];
        let getQueryCondition = function () {
            let param = {};
            param.structId = $("#hidStructParentID").val();
            param.loginName = $("#loginName").val();
            param.roleId = 3;
            param.start = $.trim($("#s1").val());
            param.end = $.trim($("#s2").val());
            param.len = $.trim($("#len").val());
            return param;
        };
        let oTableUser=null;
        let initTD = function (users) {
            if (oTableUser != null) {
                oTableUser.destroy();
            }
            oTableUser = $("#tbUser").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": true,
                "pageLength":20,
                "data": users,
                "columns":columns,
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": "暂无相关数据",
                    "zeroRecords": "对不起，查询不到任何相关数据",
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">来访者管理</a></li>
                        <li class="breadcrumb-item active">修改来访者信息</li>
                    </ol>
                </div>
                <h4 class="page-title">修改来访者信息</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <form id="frmUser" class="horizontal col-lg-8 col-md-12 col-sm-12 col-xs-12">
                <div class="card pl-3">
                    <div class="card-header font-weight-bold primary-color">基本信息</div>
                    <div class="card-body">
                        <div class="form-group row mb-3">
                            <label for="structName" class="col-form-label col-3">所属组织（必选）</label>
                            <div class="col-9">
                                <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="请选择" />
                                <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                    <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                    <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                </div>
                                <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="loginName" class="col-form-label col-3">用户名（必填）</label>
                            <div class="col-9">
                                <input id="loginName" name="loginName" class="form-control" type="text" aria-describedby="nameHelp">
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="realName" class="col-form-label col-3">姓名（必填）</label>
                            <div class="col-9">
                                <input id="realName" name="realName" class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="male" class="col-form-label col-3">性别</label>
                            <div class="col-9 form-inline">
                                <div class="custom-control custom-radio mr-2">
                                    <input type="radio" id="male" name="sex" class="custom-control-input" value="男" checked>
                                    <label class="custom-control-label" for="male">男</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" id="female" name="sex" class="custom-control-input" value="女">
                                    <label class="custom-control-label" for="female">女</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label class="col-form-label col-3">出生日期（必选）</label>
                            <div class="input-group col-9">
                                <input type="text" id="birth" name="birth" class="form-control" readonly="readonly" onClick="WdatePicker({el:this,dateFmt:'yyyy-MM-dd'})" placeholder="点击选择…">
                                <div class="input-group-append">
                                    <button class="btn btn-light" type="button" value="发送短信"><i class="fa fa-calendar"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="custom-control custom-checkbox mb-2">
                            <input type="checkbox" class="custom-control-input" id="chkDetail">
                            <label class="custom-control-label" for="chkDetail">附加信息</label>
                        </div>
                        <div class="detail hide">
                            <div class="form-group row mb-3">
                                <label for="mobile" class="col-form-label col-3">联系电话</label>
                                <div class="col-9">
                                    <input id="mobile" name="mobile" class="form-control" type="tel" autocomplete="off" maxlength="11">
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="emergency_contact_person" class="col-form-label col-3">紧急联系人</label>
                                <div class="col-9">
                                    <input id="emergency_contact_person" name="emergency_contact_person" class="form-control" type="text" autocomplete="off">
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="emergency_contact_mobile" class="col-form-label col-3">紧急联系电话</label>
                                <div class="col-9">
                                    <input id="emergency_contact_mobile" name="emergency_contact_mobile" class="form-control" type="tel" autocomplete="off" maxlength="11">
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label class="col-form-label col-3">民族</label>
                                <div class="col-9">
                                    <select class="form-control select2" data-toggle="select2" name="nation" id="nation">
                                        <option value="">请选择</option>
                                        <option value="汉族">汉族</option>
                                        <option value="满族">满族</option>
                                        <option value="回族">回族</option>
                                        <option value="蒙古族">蒙古族</option>
                                        <option value="壮族">壮族</option>
                                        <option value="藏族">藏族</option>
                                        <option value="阿昌族">阿昌族</option>
                                        <option value="白族">白族</option>
                                        <option value="保安族">保安族</option>
                                        <option value="布朗族">布朗族</option>
                                        <option value="布依族">布依族</option>
                                        <option value="朝鲜族">朝鲜族</option>
                                        <option value="达斡尔族">达斡尔族</option>
                                        <option value="傣族">傣族</option>
                                        <option value="德昂族">德昂族</option>
                                        <option value="东乡族">东乡族</option>
                                        <option value="侗族">侗族</option>
                                        <option value="独龙族">独龙族</option>
                                        <option value="俄罗斯族">俄罗斯族</option>
                                        <option value="鄂伦春族">鄂伦春族</option>
                                        <option value="鄂温克族">鄂温克族</option>
                                        <option value="高山族">高山族</option>
                                        <option value="哈尼族">哈尼族</option>
                                        <option value="哈萨克族">哈萨克族</option>
                                        <option value="赫哲族">赫哲族</option>
                                        <option value="基诺族">基诺族</option>
                                        <option value="京族">京族</option>
                                        <option value="景颇族">景颇族</option>
                                        <option value="柯尔克孜族">柯尔克孜族</option>
                                        <option value="拉祜族">拉祜族</option>
                                        <option value="黎族">黎族</option>
                                        <option value="傈僳族">傈僳族</option>
                                        <option value="毛南族">毛南族</option>
                                        <option value="门巴族">门巴族</option>
                                        <option value="苗族">苗族</option>
                                        <option value="纳西族">纳西族</option>
                                        <option value="怒族">怒族</option>
                                        <option value="普米族">普米族</option>
                                        <option value="羌族">羌族</option>
                                        <option value="撒拉族">撒拉族</option>
                                        <option value="水族">水族</option>
                                        <option value="塔吉克族">塔吉克族</option>
                                        <option value="塔塔尔族">塔塔尔族</option>
                                        <option value="土家族">土家族</option>
                                        <option value="土族">土族</option>
                                        <option value="维吾尔族">维吾尔族</option>
                                        <option value="乌孜别克族">乌孜别克族</option>
                                        <option value="锡伯族">锡伯族</option>
                                        <option value="瑶族">瑶族</option>
                                        <option value="彝族">彝族</option>
                                        <option value="裕固族">裕固族</option>
                                        <option value="仡佬族">仡佬族</option>
                                        <option value="仫佬族">仫佬族</option>
                                        <option value="佤族">佤族</option>
                                        <option value="珞巴族">珞巴族</option>
                                        <option value="畲族">畲族</option>
                                        <option value="其他少数民族">其他少数民族</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="email" class="col-form-label col-3">邮箱</label>
                                <div class="col-9">
                                    <input id="email" name="email" class="form-control" type="email">
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="idcardNo" class="col-form-label col-3">身份证号</label>
                                <div class="col-9">
                                    <input id="idcardNo" name="idcardNo" class="form-control" type="text" maxlength="18">
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="native" class="col-form-label col-3">籍贯</label>
                                <div class="col-9">
                                    <input id="native" name="native" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label class="col-form-label col-3">地址</label>
                                <div class="col-9">
                                    <div data-toggle="distpicker">
                                        <select class="form-control select2" id="address_province" data-toggle="select2"></select>
                                        <select class="form-control select2" id="address_city" data-toggle="select2"></select>
                                        <select class="form-control select2" id="address_dist" data-toggle="select2"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="address_detail" class="col-form-label col-3">详细地址</label>
                                <div class="col-9">
                                    <input id="address_detail" name="address_detail" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="education" class="col-form-label col-3">文化程度</label>
                                <div class="col-9">
                                    <select class="form-control select2" data-toggle="select2" id="education" name="education">
                                        <option value="">请选择</option>
                                        <option value="研究生及以上">研究生及以上</option>
                                        <option value="大学本科">大学本科</option>
                                        <option value="大学专科">大学专科</option>
                                        <option value="高中/中专">高中/中专</option>
                                        <option value="初中及以下">初中及以下</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="job" class="col-form-label col-3">职业</label>
                                <div class="col-9">
                                    <select class="form-control select2" data-toggle="select2" id="job" name="job">
                                        <option value="">请选择</option>
                                        <option value="市场/销售/商务">市场/销售/商务</option>
                                        <option value="采购">采购</option>
                                        <option value="行政">行政</option>
                                        <option value="人力">人力</option>
                                        <option value="产品/运营人员">产品/运营人员</option>
                                        <option value="个体经营者">个体经营者</option>
                                        <option value="财务/会计/出纳/审计">财务/会计/出纳/审计</option>
                                        <option value="企业管理者">企业管理者</option>
                                        <option value="律师/法务">律师/法务</option>
                                        <option value="设计从业者">设计从业者</option>
                                        <option value="服务业人员">服务业人员</option>
                                        <option value="技术开发/工程师">技术开发/工程师</option>
                                        <option value="农林牧渔劳动者">农林牧渔劳动者</option>
                                        <option value="工人劳动者">工人劳动者</option>
                                        <option value="全职家庭主妇/夫">全职家庭主妇/夫</option>
                                        <option value="自由职业">自由职业</option>
                                        <option value="离休/退休">离休/退休</option>
                                        <option value="学生">学生</option>
                                        <option value="老师">老师</option>
                                        <option value="医护人员">医护人员</option>
                                        <option value="科研人员">科研人员</option>
                                        <option value="党政机关人员">党政机关人员</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="job" class="col-form-label col-3">宗教信仰</label>
                                <div class="col-9">
                                    <select class="form-control" id="religion" name="religion">
                                        <option value="">请选择</option>
                                        <option value="佛教">佛教</option>
                                        <option value="基督教">基督教</option>
                                        <option value="伊斯兰教">伊斯兰教</option>
                                        <option value="天主教">天主教</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row mb-3">
                                <label for="job" class="col-form-label col-3">婚姻状况</label>
                                <div class="col-9">
                                    <select class="form-control" id="marriage" name="marriage">
                                        <option value="">请选择</option>
                                        <option value="未婚">未婚</option>
                                        <option value="已婚">已婚</option>
                                        <option value="丧偶">丧偶</option>
                                        <option value="离婚">离婚</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-0 justify-content-end row">
                            <div class="col-9">
                                <input type="submit" id="btnSave" class="btn btn-primary" value="保存" />
                                <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                                <input id="hidUserID" type="hidden" value="0" />
                            </div>
                        </div>
                    </div> <!-- end card-body-->
                </div> <!-- end card-->
            </form>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/DatePicker/WdatePicker.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script th:src="@{/static/js/plugins/distpicker/distpicker.data.min.js}"></script>
    <script th:src="@{/static/js/plugins/distpicker/distpicker.min.js}"></script>
    <script type="text/javascript">
        let userId = getUrlParam("id");
        $(function () {
            $("#structName").click(function () {
                initTree();
            });
            $("#chkDetail").click(function () {
                if ($("#chkDetail").prop('checked')) {
                    $(".detail").removeClass('hide').addClass('show');
                }
                else {
                    $(".detail").removeClass('show').addClass('hide');
                }
            });
            initForm();
            $("#frmUser").validate({
                ignore: "",
                errorPlacement: function (error, element) {
                    wrap = element.parent();
                    if (wrap.hasClass('input-group')) {
                        error.insertAfter(wrap);
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    hidStructParentID: { required: true },
                    realName: { required: true },
                    birth: { required: true }
                },
                messages: {
                    hidStructParentID: { required: "请选择所属组织" },
                    realName: { required: "请输入姓名" },
                    birth: { required: "请选择出生日期" }
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.userId = $("#hidUserID").val();
                    jsonObj.loginName = $.trim($("#loginName").val());
                    jsonObj.structId = $("#hidStructParentID").val();
                    jsonObj.roleId = 3;
                    jsonObj.realName = $.trim($("#realName").val());
                    jsonObj.sex = $('input[name="sex"]:checked').val();
                    jsonObj.birth = $("#birth").val();
                    jsonObj.nation = $("#nation").val();
                    jsonObj.mobile = $("#mobile").val();
                    jsonObj.emergencyContactPerson = $("#emergency_contact_person").val();
                    jsonObj.emergencyContactMobile = $("#emergency_contact_mobile").val();
                    jsonObj.email = $("#email").val();
                    jsonObj.iDCardNo = $("#idcardNo").val();
                    jsonObj.nativePlace = $("#native").val();
                    jsonObj.addressProvince = $("#address_province").val();
                    jsonObj.addressCity = $("#address_city").val();
                    jsonObj.addressDist = $("#address_dist").val();
                    jsonObj.addressDetail = $("#address_detail").val();
                    jsonObj.education = $("#education").val();
                    jsonObj.marriage = $("#marriage").val();
                    jsonObj.job = $("#job").val();
                    jsonObj.religion = $("#religion").val();
                    $("#btnSave").val("请稍后…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/anteroom/user/update',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, {
                                    icon: 1, yes: function (index) {
                                        location.reload();
                                    }
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
        });
        let initForm = function () {
            $.post("/anteroom/user/get?id=" + userId, '', function (data) {
                let res = JSON.parse(data);
                $("#structName").val(res.structName);
                $("#hidStructParentID").val(res.structId);
                $("#loginName").val(res.loginName);
                $("#hidLoginName").val(res.loginName);
                $("#realName").val(res.realName);
                $("#hidUserID").val(userId);
                if (res.sex === "男") {
                    $("#male").attr("checked", true);
                }
                else {
                    $("#female").attr("checked", true);
                }
                $("#birth").val(res.birth);
                $("#nation").val(res.nation).trigger('change');
                $("#mobile").val(res.mobile);
                $("#emergency_contact_person").val(res.emergencyContactPerson);
                $("#emergency_contact_mobile").val(res.emergencyContactMobile);
                $("#email").val(res.email);
                $("#idcardNo").val(res.iDCardNo);
                $("#native").val(res.nativePlace);
                $("#address_province").val(res.addressProvince).trigger('change');
                $("#address_city").val(res.addressCity).trigger('change');
                $("#address_dist").val(res.addressDist);
                $("#address_detail").val(res.addressDetail);
                $("#education").val(res.education).trigger('change');
                $("#marriage").val(res.marriage);
                $("#job").val(res.job).trigger('change');
                $("#religion").val(res.religion);
            });
        };
    </script>
</th:block>
</body>
</html>
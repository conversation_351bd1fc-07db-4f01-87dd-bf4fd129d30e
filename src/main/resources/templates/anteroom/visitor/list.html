<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/datatables/dataTables.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/css/vendor/datatables/responsive.bootstrap4.css}" rel="stylesheet" type="text/css" />
    <link th:href="@{/static/js/plugins/zTree/metroStyle.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">来访者管理</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">来访者查询与维护</a></li>
                    </ol>
                </div>
                <h4 class="page-title">来访者列表</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-lg-8">
                            <form class="form-inline" action="">
                                <div class="mr-1">
                                    <label for="structName" class="sr-only">所属组织：</label>
                                    <input type="text" id="structName" name="structName" readonly value="" class="form-control" placeholder="选择所属组织" />
                                    <div id="structContent" class="menuContent" style="display:none; position: absolute; z-index:100000;">
                                        <input type="text" class="form-control" id="search" placeholder="根据组织关键词搜索…" autocomplete="off" />
                                        <ul id="structTree" class="ztree mt-0" style="width:100%; height:200px;"></ul>
                                    </div>
                                    <input type="hidden" name="hidStructParentID" id="hidStructParentID" class="form-control" value="" />
                                </div>
                                <div class="form-group mr-1">
                                    <label for="sr-loginName" class="sr-only">用户名：</label>
                                    <input type="text" class="form-control" id="sr-loginName" name="sr-loginName" placeholder="用户名" autocomplete="off" style="width:100px;">
                                </div>
                                <div class="form-group mr-1">
                                    <label for="sr-realName" class="sr-only">姓名：</label>
                                    <input type="text" class="form-control" id="sr-realName" name="sr-realName" placeholder="姓名" autocomplete="off" style="width:100px;">
                                </div>
                                <button type="button" class="btn btn-primary" id="btnQuery"><i class="fa fa-search"></i></button>
                            </form>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-right">
                                <button id="btnAdd" type="button" class="btn btn-primary mb-2" title="来访者建档"><i class="fa fa-user-plus"></i></button>
                                <button id="btnBatchDel" type="button" class="btn btn-danger mb-2" title="删除来访者"><i class="fa fa-trash-o"></i></button>
                                <button id="btnChangeRole" type="button" class="btn btn-success mb-2" title="更改角色"><i class="fa fa-users"></i></button>
                                <button id="btnSyncUsers" class="btn btn-primary mb-2" title="同步用户"><i class="fa fa-refresh"></i></button>
                            </div>
                        </div><!-- end col-->
                    </div>
                    <div class="table-responsive">
                        <table id="tbVisitor" class="table table-striped table-centered nowrap" width="100%">
                            <thead>
                            <tr>
                                <th style="width: 30px;">
                                    <div class="custom-control custom-checkbox">
                                        <input id="chkall" class="custom-control-input check" type="checkbox">
                                        <label class="custom-control-label" for="chkall"></label>
                                    </div>
                                </th>
                                <th class="text-left">用户名</th>
                                <th class="text-left">姓名</th>
                                <th class="text-left">性别</th>
                                <th class="text-left">年龄</th>
                                <th class="text-left">所属组织</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- end card body-->
            </div>
            <!-- end card -->
        </div>
        <!-- end col-->
    </div>

    <!-- modal.密码设置 start-->
    <div id="password-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <form id="frmModifyPwd" action="" method="post" role="form">
                <div class="modal-content">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title">密码设置</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="newPwd" class="col-form-label">密码</label>
                            <input id="newPwd" name="newPwd" class="form-control" type="password" aria-describedby="pwdHelp">
                            <small id="pwdHelp" class="form-text text-muted">长度至少8位以上，由数字、大小写字母和特殊字符组成</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm_newPwd" class="col-form-label">确认新密码</label>
                            <input id="confirm_newPwd" name="confirm_newPwd" class="form-control" type="password">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>&nbsp;&nbsp;
                        <input type="submit" class="btn btn-primary" id="btnSavePwd" value="保存" />
                        <input id="hidUserID" type="hidden" value="0" />
                        <input type="reset" hidden />
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- modal.密码设置 end-->
    <!-- modal.更改角色 start-->
    <div id="role-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <form id="frmRole" action="" method="post" role="form">
                <div class="modal-content">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title">更改平台角色</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group form-inline">
                            <label for="role" class="col-form-label mr-2">角色</label>
                            <select id="role" name="role" class="form-control">
                                <option></option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>&nbsp;&nbsp;
                        <input type="submit" class="btn btn-primary" id="btnSaveRole" value="保存" />
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- modal.更改角色 end-->
    
    <!-- modal.查看档案 start-->
    <div id="archive-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">来访者档案</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="card font-14">
                                <div class="card-header bg-light font-weight-bold">
                                    <i class="fa fa-newspaper-o mr-1"></i>基本信息
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>用户名：</strong><span id="archive-loginName"></span></p>
                                            <p><strong>姓名：</strong><span id="archive-realName"></span></p>
                                            <p><strong>所属组织：</strong><span id="archive-structName"></span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>年龄：</strong><span id="archive-age"></span></p>
                                            <p><strong>性别：</strong><span id="archive-sex"></span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-1">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light font-weight-bold">
                                    <i class="fa fa-heartbeat mr-1"></i>测评记录
                                </div>
                                <div class="card-body">
                                    <div id="archive-measuring-records">
                                        <!-- 测评记录将通过JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-1">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light font-weight-bold">
                                    <i class="fa fa-line-chart mr-1"></i>绩效数据
                                </div>
                                <div class="card-body">
                                    <div id="archive-performance-data">
                                        <!-- 绩效数据将通过JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 新增个人活动点评部分 -->
                    <div class="row mt-1">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light font-weight-bold">
                                    <i class="fa fa-comments-o mr-1"></i>个人活动点评
                                </div>
                                <div class="card-body">
                                    <div id="archive-self-evaluations">
                                        <!-- 个人活动点评将通过JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 新增咨询个案部分 -->
                    <div class="row mt-1">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light font-weight-bold">
                                    <i class="fa fa-user-md mr-1"></i>心理咨询个案
                                </div>
                                <div class="card-body">
                                    <div id="archive-consultation-cases">
                                        <!-- 心理咨询个案将通过JavaScript动态填充 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.查看档案 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.core.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/jquery.ztree.exhide.min.js}"></script>
    <script th:src="@{/static/js/plugins/zTree/fuzzysearch.js}"></script>
    <script th:src="@{/static/js/pages/ztree.js}"></script>
    <script type="text/javascript">
        let initPage = function () {
            '[[${canAdd}]]' === 'true' ? $("#btnAdd").show() : $("#btnAdd").hide();
            '[[${canAdd}]]' === 'true' ? $("#btnSyncUsers").show() : $("#btnSyncUsers").hide();
            '[[${canDelete}]]' === 'true' ? $("#btnBatchDel").show() : $("#btnBatchDel").hide();
            '[[${canUpdateRole}]]' === 'true' ? $("#btnChangeRole").show() : $("#btnChangeRole").hide();
        };
        let columns = [
            {
                "data": "userId", "render":
                    function (data, type, full, meta) {
                        return '<div class="custom-control custom-checkbox"><input name="checklist" id="lbl' + data + '" class="custom-control-input checklist" type="checkbox" value="' + data + '"><label class="custom-control-label" for="lbl' + data + '"></label></div >';
                    }, "bSortable": false
            },
            { "sClass": "text-left", "data": "loginName", "bSortable": false },
            { "sClass": "text-left", "data": "realName", "bSortable": false },
            { "sClass": "text-left", "data": "sex", "bSortable": false },
            { "sClass": "text-left", "data": "age","render":
                    function (data, type, full, meta) {
                        return getAge(full.birth);
                    }, "bSortable": false },
            { "sClass": "text-left", "data": "structFullName", "bSortable": false }
        ];
        let columnDefs = [{
            targets: 6, render: function (data, type, row, meta) {
                let buttons = "";
                if ('[[${canUpdate}]]' === 'true') buttons += '<button type = "button" class="btn btn-outline-warning btn-sm mr-1 update"><i class="fa fa-pencil-square-o mr-1"></i>修改</button>';
                if ('[[${canResetPwd}]]' === 'true') buttons += '<button type = "button" class="btn btn-outline-primary btn-sm mr-1 password"><i class="fa fa-key mr-1"></i>密码设置</button>';
                buttons += '<button type = "button" class="btn btn-outline-primary btn-sm mr-1 view-archive"><i class="fa fa-file-text-o mr-1"></i>查看档案</button>';
                return buttons;
            }
        }];
        let getQueryCondition = function (data) {
            let param = {};
            param.structId = $("#hidStructParentID").val();
            param.loginName = $("#sr-loginName").val();
            param.realName = $("#sr-realName").val();
            param.roleId = 3;
            param.isChecked = 1;
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let resetForm = function () {
            $("#frmModifyPwd input").removeClass("error");
            $("label.error").hide();
            $("#frmModifyPwd input[type='reset']").click();
            $("#hidUserID").val(0);
        };
        $(function () {
            //初始化页面权限
            initPage();
            $("#structName").click(function () {
                initTree();
            });
            $("#btnQuery").click(function () {
                $('#tbVisitor').DataTable().ajax.reload();
            });
            $("#chkall").change(function () {
                //实现全选
                if ($('#chkall').prop("checked") == true) {
                    $('.checklist').prop("checked", true);
                }
                else {
                    $('.checklist').prop("checked", false);
                }
            });
            $("#tbVisitor").bsDataTables({
                columns: columns,
                url: '/anteroom/user/get_visitor_list',
                columnDefs: columnDefs,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });

            //新增
            $("#btnAdd").click(function () {
                location.href = "/anteroom/visitor/add";
            });
            //修改
            $("#tbVisitor").on('click', '.update', function () {
                let data = oTable.row($(this).parents('tr')).data();
                location.href = "/anteroom/visitor/update?id=" + data.userId;
            });
            //更改角色
            $("#btnChangeRole").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                initSelect("#role", "/platform/role/get_for_select_all", '');
                $("#role-modal").modal();
            });
            //批量删除
            $("#btnBatchDel").click(function () {
                let sel = $("input[name='checklist']:checked").length;
                if (sel === 0) {
                    layer.msg("没有选择任何数据");
                    return;
                }
                layer.confirm('确定删除吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        let ids = "";
                        let array = [];
                        $("input[name='checklist']:checked").each(function () {
                            array.push($(this).val());
                        });
                        ids = array.join(',');
                        if (ids === "") {
                            layer.msg('操作失败', { icon: 2, time: 2000 });
                            return;
                        }
                        let jsonObj = {};
                        jsonObj.ids = ids;
                        $.post("/anteroom/user/batch_del", jsonObj, function (res) {
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json');
                    }
                });
            });
            //密码设置
            $("#tbVisitor").on('click', '.password', function () {
                resetForm();
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidUserID").val(data.userId);
                $("#password-modal").modal();
            });
            $.validator.addMethod('check_password', function (value, element, param) {
                let strPwd = $('#newPwd').val();
                let regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[a-zA-Z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,}$/;
                return regex.test(strPwd);
            }, "密码不符合要求");
            $("#frmModifyPwd").validate({
                rules: {
                    newPwd: {
                        required: true,
                        check_password: true
                    },
                    confirm_newPwd: {
                        required: true,
                        equalTo: "#newPwd"
                    }
                },
                messages: {
                    newPwd: {
                        required: "请输入新密码",
                        check_password: "密码不符合要求"
                    },
                    confirm_newPwd: {
                        required: "请输入确认密码", equalTo: "两次输入密码不一致"
                    }
                },
                submitHandler: function () {
                    $("#btnSavePwd").val("保存中……");
                    $("#btnSavePwd").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.userId = $("#hidUserID").val();
                    jsonObj.newPwd = $("#newPwd").val();
                    $.ajax({
                        type: 'POST',
                        url: '/anteroom/user/modify_pwd',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSavePwd").val("保存");
                            $("#btnSavePwd").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                $("#password-modal").modal('hide');
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            $("#frmRole").validate({
                rules: {
                    role: { required: true }
                },
                messages: {
                    role: { required: "请选择角色" }
                },
                submitHandler: function () {
                    $("#btnSaveRole").val("保存中……");
                    $("#btnSaveRole").attr("Disabled", true);

                    let userIds = "";
                    let array = [];
                    $("input[name='checklist']:checked").each(function () {
                        array.push($(this).val());
                    });
                    userIds = array.join(',');
                    if (userIds === "") {
                        layer.msg('操作失败', { icon: 2, time: 2000 });
                        return;
                    }

                    let jsonObj = {};
                    jsonObj.roleId = $("#role").val();
                    jsonObj.userIds = userIds;
                    $.post("/anteroom/user/batch_update_role", jsonObj,
                        function (res) {
                            $("#btnSaveRole").val("保存");
                            $("#btnSaveRole").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                                $("#role-modal").modal('hide');
                                oTable.draw();
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }, 'json'
                    );
                }
            });
            $("#btnSyncUsers").click(function () {
                layer.confirm('提示：同步过程中请勿关闭网页，确定要开始同步吗？', {
                    time: 0,
                    icon: 7,
                    btn: ['确定', '取消'],
                    yes: function (index) {
                        layer.msg('数据同步中……', {
                            icon: 17, shade: 0.05, time: false
                        });
                        $.ajax({
                            url: "/anteroom/user/sync_users",
                            timeout: 240000,
                            type: "post",
                            data: '',
                            dataType: "json",
                            success: function (data) {
                                if (data.resultCode === 200) {
                                    layer.msg(data.resultMsg, { icon: 1, time: 2000 });
                                    oTable.draw();
                                }
                                else {
                                    layer.msg(data.resultMsg, { icon: 2, time: 2000 });
                                }
                            },
                            error: function (XMLHttpRequest, textStatus, errorThrown) {
                                //alert(XMLHttpRequest.status);
                            }
                        });
                    }
                });
            });

            // 查看档案
            $("#tbVisitor").on('click', '.view-archive', function () {
                layer.msg('数据加载中……', {
                    icon: 17, shade: 0.05, time: false
                });
                let data = oTable.row($(this).parents('tr')).data();
                $.ajax({
                    url: "/archiveroom/archive/get?userId=" + data.userId,
                    type: "GET",
                    data: '',
                    contentType: "application/json",
                    dataType: "json",
                    success: function(res) {
                        layer.closeAll();
                        let archiveData = res;
                        // 填充基本信息
                        let user = archiveData.user;
                        $("#archive-loginName").text(user.loginName || '');
                        $("#archive-realName").text(user.realName || '');
                        $("#archive-structName").text(user.structName || '');
                        $("#archive-age").text(getAge(user.birth) || '');
                        $("#archive-sex").text(user.sex || '');

                        // 填充测评记录
                        let measuringHtml = '';
                        if (archiveData.measuringRecords && archiveData.measuringRecords.length > 0) {
                            archiveData.measuringRecords.forEach(function(record) {
                                measuringHtml += '<div class="measuring-record mb-3">';
                                measuringHtml += '<div class="row">';
                                measuringHtml += '<div class="col-12"><strong>量表名称：</strong>' + (record.scaleName || '') + '</div>';
                                measuringHtml += '</div>';
                                measuringHtml += '<div class="row mt-2">';
                                measuringHtml += '<div class="col-4"><strong>开始时间：</strong>' + (record.startTime ? new Date(record.startTime).toLocaleString() : '') + '</div>';
                                measuringHtml += '<div class="col-4"><strong>结束时间：</strong>' + (record.endTime ? new Date(record.endTime).toLocaleString() : '') + '</div>';
                                measuringHtml += '<div class="col-4"><strong>测评耗时：</strong>' + (record.timeInterval ? record.timeInterval + '秒' : '') + '</div>';
                                measuringHtml += '</div>';
                                measuringHtml += '<div class="row mt-2">';
                                measuringHtml += '<div class="col-12"><strong>结果解释：</strong>' + (record.interpretation || '') + '</div>';
                                measuringHtml += '</div>';
                                measuringHtml += '<hr class="my-3">';
                                measuringHtml += '</div>';
                            });
                        } else {
                            measuringHtml = '<p class="text-muted">暂无测评记录</p>';
                        }
                        $("#archive-measuring-records").html(measuringHtml);

                        // 填充绩效数据
                        let performanceHtml = '';
                        if (archiveData.userPerformance && archiveData.userPerformance.length > 0) {
                            performanceHtml = '<div class="table-responsive"><table class="table table-bordered table-sm">';
                            performanceHtml += '<thead><tr>';
                            performanceHtml += '<th>年度</th>';
                            performanceHtml += '<th>一月</th>';
                            performanceHtml += '<th>二月</th>';
                            performanceHtml += '<th>三月</th>';
                            performanceHtml += '<th>四月</th>';
                            performanceHtml += '<th>五月</th>';
                            performanceHtml += '<th>六月</th>';
                            performanceHtml += '<th>七月</th>';
                            performanceHtml += '<th>八月</th>';
                            performanceHtml += '<th>九月</th>';
                            performanceHtml += '<th>十月</th>';
                            performanceHtml += '<th>十一月</th>';
                            performanceHtml += '<th>十二月</th>';
                            performanceHtml += '</tr></thead>';
                            performanceHtml += '<tbody>';
                            archiveData.userPerformance.forEach(function(data) {
                                performanceHtml += '<tr>';
                                performanceHtml += '<td>' + (data.yearly || '') + '</td>';
                                performanceHtml += '<td>' + (data.month01 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month02 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month03 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month04 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month05 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month06 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month07 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month08 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month09 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month10 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month11 || '') + '</td>';
                                performanceHtml += '<td>' + (data.month12 || '') + '</td>';
                                performanceHtml += '</tr>';
                            });
                            performanceHtml += '</tbody></table></div>';
                        } else {
                            performanceHtml = '<p class="text-muted">暂无绩效数据</p>';
                        }
                        $("#archive-performance-data").html(performanceHtml);

                        // 填充个人活动点评
                        let selfEvaluationsHtml = '';
                        if (archiveData.selfEvaluations && archiveData.selfEvaluations.length > 0) {
                            archiveData.selfEvaluations.forEach(function(evaluation) {
                                selfEvaluationsHtml += '<div class="evaluation-item mb-3">';
                                selfEvaluationsHtml += '<div class="row">';
                                selfEvaluationsHtml += '<div class="col-12"><strong>活动主题：</strong>' + (evaluation.activityName || '') + '</div>';
                                selfEvaluationsHtml += '</div>';
                                selfEvaluationsHtml += '<div class="row mt-2">';
                                selfEvaluationsHtml += '<div class="col-6"><strong>活动开始时间：</strong>' + (evaluation.startTime ? new Date(evaluation.startTime).toLocaleString() : '') + '</div>';
                                selfEvaluationsHtml += '<div class="col-6"><strong>活动结束时间：</strong>' + (evaluation.endTime ? new Date(evaluation.endTime).toLocaleString() : '') + '</div>';
                                selfEvaluationsHtml += '</div>';
                                selfEvaluationsHtml += '<div class="row mt-2">';
                                selfEvaluationsHtml += '<div class="col-12"><strong>心理咨询师：</strong>' + (evaluation.counselorName || '') + '</div>';
                                selfEvaluationsHtml += '</div>';
                                selfEvaluationsHtml += '<div class="row mt-2">';
                                selfEvaluationsHtml += '<div class="col-12"><strong>点评内容：</strong>' + (evaluation.evaluationContent || '') + '</div>';
                                selfEvaluationsHtml += '</div>';
                                selfEvaluationsHtml += '<div class="row mt-2">';
                                selfEvaluationsHtml += '<div class="col-12"><strong>点评标签：</strong>' + (evaluation.tags || '') + '</div>';
                                selfEvaluationsHtml += '</div>';
                                selfEvaluationsHtml += '<hr class="my-2">';
                                selfEvaluationsHtml += '</div>';
                            });
                        } else {
                            selfEvaluationsHtml = '<p class="text-muted">暂无活动评价记录</p>';
                        }
                        $("#archive-self-evaluations").html(selfEvaluationsHtml);

                        // 填充心理咨询个案
                        let casesHtml = '';
                        if (archiveData.consultationCases && archiveData.consultationCases.length > 0) {
                            archiveData.consultationCases.forEach(function(caseItem) {
                                casesHtml += '<div class="case-item mb-3">';
                                casesHtml += '<div class="row">';
                                casesHtml += '<div class="col-4"><strong>咨询日期：</strong>' + (caseItem.consultationDate ? new Date(caseItem.consultationDate).toLocaleDateString() : '') + '</div>';
                                casesHtml += '<div class="col-4"><strong>咨询师：</strong>' + (caseItem.counselorName || '') + '</div>';
                                casesHtml += '<div class="col-4"></div>';
                                casesHtml += '</div>';
                                
                                casesHtml += '<div class="row mt-2">';
                                casesHtml += '<div class="col-4"><strong>咨询形式：</strong>' + getConsultationForm(caseItem.consultationForm) + '</div>';
                                casesHtml += '<div class="col-4"><strong>咨询时长：</strong>' + (caseItem.consultationDuration || '') + '分钟</div>';
                                casesHtml += '<div class="col-4"><strong>咨询类型：</strong>' + getConsultationType(caseItem.consultationType) + '</div>';
                                casesHtml += '</div>';
                                
                                casesHtml += '<div class="row mt-2">';
                                casesHtml += '<div class="col-4"><strong>婚姻状态：</strong>' + getMaritalStatus(caseItem.maritalStatus) + '</div>';
                                casesHtml += '<div class="col-4"><strong>有无子女：</strong>' + getHasChildren(caseItem.hasChildren) + '</div>';
                                casesHtml += '<div class="col-4"><strong>咨询领域：</strong>' + getConsultationField(caseItem.consultationField) + '</div>';
                                casesHtml += '</div>';
                                
                                casesHtml += '<div class="row mt-2">';
                                casesHtml += '<div class="col-12"><strong>问题概述：</strong>' + (caseItem.problemSummary || '') + '</div>';
                                casesHtml += '</div>';
                                
                                if (caseItem.consultationKeywords) {
                                    casesHtml += '<div class="row mt-2">';
                                    casesHtml += '<div class="col-12"><strong>咨询关键词：</strong>' + caseItem.consultationKeywords + '</div>';
                                    casesHtml += '</div>';
                                }
                                
                                casesHtml += '<div class="row mt-2">';
                                casesHtml += '<div class="col-4"><strong>是否职场问题：</strong>' + (caseItem.isWorkplaceIssue == 1 ? '是' : '否') + '</div>';
                                if (caseItem.isWorkplaceIssue == 1 && caseItem.workplaceDescription) {
                                    casesHtml += '<div class="col-8"><strong>职场问题描述：</strong>' + caseItem.workplaceDescription + '</div>';
                                }
                                casesHtml += '</div>';
                                
                                casesHtml += '<div class="row mt-2">';
                                casesHtml += '<div class="col-4"><strong>来访是否有心理风险：</strong>' + (caseItem.hasPsychologicalRisk == 1 ? '是' : '否') + '</div>';
                                if (caseItem.hasPsychologicalRisk == 1 && caseItem.riskDescription) {
                                    casesHtml += '<div class="col-8"><strong>风险程度简要描述：</strong>' + caseItem.riskDescription + '</div>';
                                }
                                casesHtml += '</div>';
                                
                                casesHtml += '<div class="row mt-2">';
                                casesHtml += '<div class="col-12"><strong>后续建议：</strong>' + getFollowUpSuggestion(caseItem.followUpSuggestion) +
                                    (caseItem.followUpSuggestion == 4 && caseItem.otherSuggestion ? ' - ' + caseItem.otherSuggestion : '') + '</div>';
                                casesHtml += '</div>';
                                
                                casesHtml += '<div class="divider dashed small mt-2 mb-2"></div>';
                                casesHtml += '</div>';
                            });
                        } else {
                            casesHtml = '<p class="text-muted">暂无心理咨询个案记录</p>';
                        }
                        $("#archive-consultation-cases").html(casesHtml);
                        
                        // 辅助函数：获取咨询形式文本
                        function getConsultationForm(formCode) {
                            switch(formCode) {
                                case 1: return '驻场咨询';
                                case 2: return '线上咨询';
                                case 3: return '门店咨询';
                                default: return '未知';
                            }
                        }
                        
                        // 辅助函数：获取咨询类型文本
                        function getConsultationType(typeCode) {
                            switch(typeCode) {
                                case 1: return '首次咨询';
                                case 2: return '第二次咨询';
                                case 3: return '第三次咨询';
                                case 4: return '第四次咨询';
                                case 5: return '第五次咨询';
                                case 6: return '第六次及以上咨询';
                                default: return '未知';
                            }
                        }
                        
                        // 辅助函数：获取婚姻状态文本
                        function getMaritalStatus(statusCode) {
                            switch(statusCode) {
                                case 1: return '未婚';
                                case 2: return '已婚';
                                default: return '未知';
                            }
                        }
                        
                        // 辅助函数：获取有无子女文本
                        function getHasChildren(hasChildrenCode) {
                            switch(hasChildrenCode) {
                                case 1: return '有';
                                case 2: return '无';
                                default: return '未知';
                            }
                        }
                        
                        // 辅助函数：获取咨询领域文本
                        function getConsultationField(fieldCode) {
                            switch(fieldCode) {
                                case 1: return '心理健康';
                                case 2: return '情绪压力';
                                case 3: return '人际关系';
                                case 4: return '恋爱情感';
                                case 5: return '家庭关系';
                                case 6: return '亲子教育';
                                case 7: return '职场发展';
                                case 8: return '个人成长';
                                default: return '未知';
                            }
                        }
                        
                        // 辅助函数：获取后续建议文本
                        function getFollowUpSuggestion(suggestionCode) {
                            switch(suggestionCode) {
                                case 1: return '无需跟进';
                                case 2: return '定期咨询';
                                case 3: return '转介就医';
                                case 4: return '其他';
                                default: return '未知';
                            }
                        }

                        $("#archive-modal").modal('show');
                    },
                    error: function() {
                        layer.msg('获取档案数据失败', { icon: 2, time: 2000 });
                    }
                });
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">个人中心</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">账号安全</a></li>
                        <li class="breadcrumb-item active">密码管理</li>
                    </ol>
                </div>
                <h4 class="page-title">个人中心</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <h6 class="mb-3 card-header bg-light"><i class="fa fa-lock mr-1"></i>密码设置</h6>
                <div class="card-body">
                    <form id="frmModifyPwd" class="form-horizontal col-lg-6">
                        <div class="form-group row mb-3">
                            <label for="originalPwd" class="col-3 col-form-label">当前密码</label>
                            <div class="col-9">
                                <input id="originalPwd" name="originalPwd" class="form-control" type="password">
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="newPwd" class="col-3 col-form-label">新密码</label>
                            <div class="col-9">
                                <input id="newPwd" name="newPwd" class="form-control" type="password" aria-describedby="pwdHelp">
                                <small id="pwdHelp" class="form-text text-muted">长度至少8位以上，由数字、大小写字母和特殊字符组成</small>
                            </div>
                        </div>
                        <div class="form-group row mb-3">
                            <label for="confirm_newPwd" class="col-3 col-form-label">确认新密码</label>
                            <div class="col-9">
                                <input id="confirm_newPwd" name="confirm_newPwd" class="form-control" type="password">
                            </div>
                        </div>
                        <div class="form-group mb-0 justify-content-end row">
                            <div class="col-9">
                                <input type="submit" id="btnSave" class="btn btn-primary mr-1" value="保存" />
                                <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                            </div>
                        </div>
                        <input id="hidMobile" type="hidden" th:value="${user.mobile}" />
                        <input id="hidSignName" type="hidden" th:value="${pageData.sysConfigDto.smsConfig.signName}" />
                        <input id="hidTemplateCode" type="hidden" th:value= "${pageData.sysConfigDto.smsConfig.modifyPwdTemplate}" />
                    </form>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/pages/sms.js}"></script>
    <script type="text/javascript">
        $(function () {
            let mobile = "[[${user.mobile}]]";
            $("#mobile").val(mobile.substring(0, 3) + "****" + mobile.substring(7, 11));
            $.validator.addMethod('check_password', function (value, element, param) {
                let strPwd = $('#newPwd').val();
                let regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[a-zA-Z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,}$/;
                return regex.test(strPwd) ? true : false;
            }, "密码不符合要求");
            $("#frmModifyPwd").validate({
                rules: {
                    originalPwd: {
                        required: true,
                        remote: {
                            type: "POST",
                            url: "/anteroom/user/verifypwd",
                            dataType: "json",
                            data: {
                                originalPwd: function () {
                                    return $("#originalPwd").val();
                                }
                            },
                            dataFilter: function (data, type) {
                                data = JSON.parse(data);
                                if (data.resultCode === 200){
                                    return true;
                                }
                                else
                                    return false;
                            }
                        }
                    },
                    newPwd: {
                        required: true,
                        check_password: true
                    },
                    confirm_newPwd: {
                        required: true,
                        equalTo: "#newPwd"
                    }
                },
                messages: {
                    originalPwd: {
                        required: "请输入当前密码",
                        remote: "当前密码输入错误"
                    },
                    newPwd: {
                        required: "请输入新密码",
                        check_password: "密码不符合要求"
                    },
                    confirm_newPwd: {
                        required: "请输入确认密码", equalTo: "两次输入密码不一致"
                    }
                },
                submitHandler: function () {
                    //先验证系统是否启用短信功能和是否启用修改密码需要短信验证
                    if ('[[${pageData.sysConfigDto.isSmsEnabled}]]' === '1' && '[[${pageData.sysConfigDto.smsConfig.isMeasuringNotify}]]' === '1') {
                        //验证密保手机
                        if ('[[${user.mobile}]]' === '') {
                            layer.msg('您还没有设置密保手机号码！<a href="/platform/personal/account_security" class="text-primary">前往设置</a>', { icon: 0 });
                        }
                        else {
                            $("#mobile").attr("disabled", true);
                            $("#sms-modal").modal();
                        }
                    }
                    else {
                        save();
                    }
                }
            });
        });
        let save = function () {
            $("#btnSave").val("保存中……");
            $("#btnSave").attr("Disabled", true);
            let jsonObj = {};
            jsonObj.newPwd = $("#newPwd").val();
            $.post("/anteroom/user/modifypwd", jsonObj,
                function (res) {
                    $("#btnSave").val("保存");
                    $("#btnSave").attr("Disabled", false);
                    if (res.resultCode === 200) {
                        layer.alert(res.resultMsg, {
                            icon: 1, yes: function (index) {
                                location.reload();
                            }
                        });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                }, 'json'
            );
        };
    </script>
</th:block>
</body>
</html>
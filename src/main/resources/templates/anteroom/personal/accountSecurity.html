<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">个人中心</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">账号设置</a></li>
                    </ol>
                </div>
                <h4 class="page-title">账号设置</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-3"><i class="fa fa-shield mr-1"></i>账号安全</h5>
                    <div class="card mb-2 shadow-none border">
                        <div class="p-2">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <img th:src="@{/static/images/app/password.png}" class="avatar-sm rounded" th:alt="设置密码" />
                                </div>
                                <div class="col pl-0">
                                    <a href="javascript:void(0);" class="text-secondary font-weight-bold">设置登录密码</a>
                                    <p class="mb-0 mt-1">如果您的密码还是默认初始密码，强烈建议您修改为安全性强的密码。</p>
                                </div>
                                <div class="col-auto">
                                    <!-- Button -->
                                    <a th:href="@{/anteroom/personal/modifypwd}" class="btn btn-outline-primary btn-sm btn-rounded">
                                        立即修改
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <th:block th:if="${pageData.sysConfigDto.isSmsEnabled eq 1}">
                        <div class="card mb-1 shadow-none border">
                            <div class="p-2">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <img th:src="@{/static/images/app/mobile.png}" class="avatar-sm rounded" th:alt="绑定手机号码" />
                                    </div>
                                    <div class="col pl-0">
                                        <a href="javascript:void(0);" class="text-secondary font-weight-bold">手机绑定</a>
                                        <p class="mb-0 mt-1">
                                            <th:block th:if="${(not #strings.isEmpty(user.mobile)) and user.isMobileBind eq 1}" th:text="|${#strings.substring(user.mobile,0,3)}****${#strings.substring(user.mobile,7,11)}|" />
                                            <th:block th:unless="${(not #strings.isEmpty(user.mobile)) and user.isMobileBind eq 1}">
                                                您还没有绑定密保手机号码。
                                            </th:block>
                                        </p>
                                    </div>
                                    <div class="col-auto">
                                        <button id="btnSmsBind" class="btn btn-outline-primary btn-sm btn-rounded">
                                            <th:block th:if="${not #strings.isEmpty(user.mobile) and user.isMobileBind eq 1}">
                                                更改绑定
                                            </th:block>
                                            <th:block th:unless="${not #strings.isEmpty(user.mobile) and user.isMobileBind eq 1}">
                                                立即绑定
                                            </th:block>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </th:block>
                    <!--<th:block th:if="${pageData.sysConfigDto.isWeChatEnabled eq 1}">
                        <div class="card mb-1 shadow-none border">
                            <div class="p-2">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <img th:src="/static/images/app/wechat.png" class="avatar-sm rounded" th:alt="绑定微信" />
                                    </div>
                                    <div class="col pl-0">
                                        <a href="javascript:void(0);" class="text-dark font-weight-bold font-14">绑定微信</a>
                                        <p class="mb-0 mt-1 font-13 text-muted">
                                            <th:block th:if="${not #strings.isEmpty(user.wechatUser.unionid)}" th:text="|已绑定微信:${user.wechatUser.nickname}|"></th:block>
                                            <th:block th:unless="${not #strings.isEmpty(user.wechatUser.unionid)}">您还没有绑定微信账号。</th:block>
                                        </p>
                                    </div>
                                    <div class="col-auto">
                                        <button id="btnWechatBind" class="btn btn-outline-danger btn-sm btn-rounded">
                                            <th:block th:if="${not #strings.isEmpty(user.wechatUser.unionid)}" th:text="|已绑定微信:${user.wechatUser.nickname}|">解除绑定</th:block>
                                            <th:block th:unless="${not #strings.isEmpty(user.wechatUser.unionid)}" th:text="|已绑定微信:${user.wechatUser.nickname}|">立即绑定</th:block>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </th:block>-->
                </div>
            </div>
        </div>
    </div>
    <!-- modal.短信验证码 start -->
    <div id="sms-verify-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">
                <div class="modal-body">
                    <form id="frmSendVerifySms" action="#" method="post">
                        <div class="form-group mb-3">
                            <span>请输入绑定手机号码的短信验证码</span>
                        </div>
                        <div class="form-group mb-3">
                            <div class="input-group mb-3">
                                <input id="verifySms" name="verifySms" type="text" class="form-control" placeholder="输入短信验证码" autocomplete="off">
                                <div class="input-group-append">
                                    <input id="btnSendVerifySms" class="btn btn-light" type="button" value="发送短信" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <button id="btnVerifySms" class="btn btn-primary btn-block" type="button">确定</button>
                            <button type="button" class="btn btn-block btn-light" data-dismiss="modal">取消</button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.短信验证码 end -->
    <!-- modal.手机短信验证 start -->
    <div id="sms-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title"><i class="fa fa-envelope-square mr-1"></i> <span id="sms-title">手机短信验证</span></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="frmSendSms" action="#" method="post">
                        <div class="form-group mb-3">
                            <input id="mobile" name="mobile" class="form-control" type="text" placeholder="输入手机号码" value="" autocomplete="off">
                        </div>
                        <div class="form-group mb-3">
                            <div class="input-group mb-3">
                                <input id="verifyCode" name="verifyCode" type="text" class="form-control" placeholder="输入短信验证码" autocomplete="off">
                                <div class="input-group-append">
                                    <input id="btnSendSms" class="btn btn-light" type="button" value="发送短信" />
                                    <input id="hidVerifyCode" type="hidden" value="" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <button class="btn btn-primary btn-block" type="submit">确定</button>
                            <button type="button" class="btn btn-block btn-light" data-dismiss="modal">取消</button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    <!-- modal.手机短信验证 end -->
    <input id="hidMobile" type="hidden" th:value="${user.mobile}" />
    <input id="hidSignName" type="hidden" th:value="${pageData.sysConfigDto.smsConfig.signName}" />
    <input id="hidTemplateCode" type="hidden" th:value="${pageData.sysConfigDto.smsConfig.mobileBindTemplate}" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/pages/sms.js}"></script>
    <script type="text/javascript">
        $(function () {
            $("#btnSmsBind").click(function () {
                if ("[[${user.mobile}]]" !== "" && '[[${user.isMobileBind}]]' === '1') {     //已绑定手机号码，进行短信验证
                    $("#sms-verify-modal").modal();
                }
                else {
                    $("#sms-modal").modal(); //直接输入新的手机号码进行短信验证
                }
            });
            $("#btnSendVerifySms").click(function () {
                let jsonObj = {};
                jsonObj.phoneNumber = $("#hidMobile").val();
                jsonObj.signName = $("#hidSignName").val();
                jsonObj.templateCode = $("#hidTemplateCode").val();
                jsonObj.smsType = 1;
                obj = $("#btnSendVerifySms");
                sendSms(jsonObj);
            });
            $("#btnVerifySms").click(function () {
                let verifySms = $("#verifySms").val();
                if (verifySms === '') {
                    layer.msg("请填写短信验证码");
                    return;
                }
                let jsonObj = {};
                jsonObj.phoneNumber = $("#hidMobile").val();
                jsonObj.templateCode = $("#hidTemplateCode").val();
                jsonObj.smsCode = verifySms;
                $.post("/sms/verify",{"phoneNumber":$("#hidMobile").val(), "templateCode": $("#hidTemplateCode").val(), "smsCode": verifySms},function(res){
                    if (res.resultCode === 200) {
                        $("#sms-verify-modal").modal('hide');
                        $("#sms-title").html("绑定手机号码");
                        $("#sms-modal").modal();
                    }
                    else{
                        layer.msg('短信验证码错误', { icon: 2, time: 2000 });
                    }
                });
            });
        });
        $.validator.addMethod('checkMobile', function (value, element, param) {
            let phomeNumber = $(element).val();
            let mobileReg = /^(((13[0-9]{1})|(14[0-9]{1})|(17[0]{1})|(15[0-3]{1})|(15[5-9]{1})|(18[0-9]{1}))+\d{8})$/;
            if (mobileReg.test(phomeNumber)) {
                return true;
            }
            else {
                return false;
            }
        }, "请输入正确格式的手机号码");
        $("#frmSendSms").validate({
            errorPlacement: function (error, element) {
                wrap = element.parent();
                if (wrap.hasClass('input-group')) {
                    error.insertAfter(wrap);
                } else {
                    error.insertAfter(element);
                }
            },
            rules: {
                mobile: {
                    required: true,
                    checkMobile: true,
                    remote: {
                        type: "POST",
                        url: "/account/check_mobile",
                        dataType: "json",
                        data: {
                            mobile: function () {
                                return $("#mobile").val();
                            }
                        },
                        dataFilter: function (data, type) {
                            let res = JSON.parse(data);
                            if (res.resultCode === 200) {
                                return true;
                            }
                            else
                                return false;
                        }
                    }
                },
                verifyCode: {
                    required: true,
                    remote:{
                        type: "POST",
                        url: "/sms/verify",
                        dataType: "json",
                        data: {
                            phoneNumber: function () {
                                return $("#mobile").val();
                            },
                            templateCode: function () {
                                return $("#hidTemplateCode").val();
                            },
                            smsCode: function () {
                                return $("#verifyCode").val();
                            }
                        },
                        dataFilter: function (data, type) {
                            let res = JSON.parse(data);
                            if (res.resultCode === 200) {
                                return true;
                            }
                            else
                                return true;
                        }
                    }
                }
            },
            messages: {
                mobile: { required: "请填写手机号码", checkMobile: "请输入正确格式的手机号码",remote:"该手机号已经被绑定"},
                verifyCode: { required: "请填写短信验证码",remote:"短信验证码错误"}
            },
            submitHandler: function () {
                save();
            }
        });
        let save = function () {
            let mobile = $.trim($("#mobile").val());
            $.post("/anteroom/user/bind_mobile?mobile=" + mobile, '', function (res) {
                if (res.resultCode === 200) {
                    layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                    $("#sms-modal").modal('hide');
                    location.reload();
                }
                else {
                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                }
            });
        };
    </script>
</th:block>
</body>
</html>
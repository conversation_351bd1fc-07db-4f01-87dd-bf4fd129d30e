<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">个人中心</a></li>
                    </ol>
                </div>
                <h4 class="page-title">个人中心</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-sm-12">
            <!-- Profile -->
            <div class="card bg-primary">
                <div class="card-body profile-user-box">
                    <div class="row">
                        <div class="col-sm-8">
                            <div class="media">
                            <span class="float-left m-2 mr-4">
                                <img th:if="${user.headPic eq null or user.headPic eq ''}" th:src="@{/static/images/user.png}" style="height: 100px;" alt="" class="rounded-circle img-thumbnail"/>
                                <img th:unless="${user.headPic eq null or user.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${user.headPic}|" style="height: 100px;" alt="" class="rounded-circle img-thumbnail"/>
                            </span>
                                <div class="media-body pt-2">
                                    <h4 class="mt-1 mb-1 text-white" th:text="${user.loginName}"></h4>
                                    <p class="font-13 text-white-50" th:text="${user.realName}"></p>
                                    <ul class="mb-0 list-inline text-light">
                                        <li class="list-inline-item mr-3">
                                            <p class="mb-0 font-14 text-white"><i class="fa fa-lock mr-1"></i><a th:href="@{/anteroom/personal/account_security}" class="text-white font-13">账号安全</a></p>
                                        </li>
                                        <li class="list-inline-item mr-3">
                                            <p class="mb-0 font-14 text-white"><i class="fa fa-envelope mr-1"></i><a th:href="@{/anteroom/mail/list}" class="text-white font-13">消息中心</a></p>
                                        </li>
                                        <li class="list-inline-item mr-3">
                                            <p class="mb-0 font-14 text-white"><i class="fa fa-database mr-1"></i><a th:href="@{/anteroom/points/my_points}" class="text-white font-13">我的积分</a></p>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div> <!-- end col-->
                        <div class="col-sm-4">
                            <div class="text-center mt-sm-0 mt-3 text-sm-right">
                                <button type="button" class="btn btn-outline-light" id="editProfile">
                                    <i class="fa fa-edit mr-1"></i> 编辑资料
                                </button>
                            </div>
                        </div> <!-- end col-->
                    </div> <!-- end row -->

                </div> <!-- end card-body/ profile-user-box-->
            </div><!--end profile/ card -->
        </div> <!-- end col-->
    </div>
    <!-- end row -->
    <div class="row">
        <div class="col-4">
            <!-- Personal-Information -->
            <div class="card">
                <div class="card-body">
                    <div class="card-widgets">
                        <a th:href="@{/anteroom/personal/edit_profile(id=${user.userId})}" th:title="修改资料"><i class="fa fa-edit"></i></a>
                    </div>
                    <h4 class="header-title mt-0 mb-3">个人信息</h4>
                    <p class="text-muted font-13" id="personalIntro" th:text="${user.description}"></p>
                    <hr />
                    <div class="text-left pl-2">
                        <p class="text-muted"><strong>所属组织：</strong><span class="ml-2" th:text="${user.structName}"></span></p>
                        <p class="text-muted"><strong>登录账号：</strong> <span class="ml-2" th:text="${user.loginName}"></span></p>
                        <p class="text-muted"><strong>姓名：</strong><span class="ml-2" th:text="${user.realName}"></span></p>
                        <p class="text-muted"><strong>性别：</strong> <span class="ml-2" th:text="${user.sex}"></span></p>
                        <p class="text-muted"><strong>年龄：</strong> <span class="ml-2" th:text="${user.age}"></span></p>
                        <p class="text-muted"><strong>民族：</strong> <span class="ml-2" th:text="${user.nation}"></span></p>
                        <p class="text-muted"><strong>联系电话：</strong> <span class="ml-2" th:text="${user.mobile}"></span></p>
                        <p class="text-muted"><strong>邮箱：</strong> <span class="ml-2" th:text="${user.email}"></span></p>
                        <p class="text-muted"><strong>身份证号码：</strong> <span class="ml-2" th:text="${user.iDCardNo}"></span></p>
                        <p class="text-muted"><strong>籍贯：</strong> <span class="ml-2" th:text="${user.nativePlace}"></span></p>
                        <p class="text-muted"><strong>地址：</strong> <span class="ml-2" th:text="|${user.addressProvince}${user.addressCity}${user.addressDist}|"></span></p>
                        <p class="text-muted"><strong>详细地址：</strong> <span class="ml-2" th:text="${user.addressDetail}"></span></p>
                        <p class="text-muted"><strong>学历：</strong><span class="ml-2" th:text="${user.education}"></span></p>
                        <p class="text-muted"><strong>职业：</strong><span class="ml-2" th:text="${user.job}"></span></p>
                        <p class="text-muted"><strong>婚姻状况：</strong><span class="ml-2" th:text="${user.marriage}"></span></p>
                        <p class="text-muted"><strong>宗教信仰：</strong><span class="ml-2" th:text="${user.religion}"></span></p>
                        <p class="text-muted"><strong>最后登录时间：</strong> <span class="ml-2" th:text="${#dates.format(user.lastLoginDateTime,'yyyy-MM-dd HH:mm:ss')}"></span></p>
                    </div>
                </div>
            </div>
            <!-- Personal-Information -->
        </div> <!-- end col-->
        <div class="col-8">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">我的测评任务</h4>
                    <div class="myTaskList hide">
                        <table id="myTaskList" class="table table-striped table-centered mb-0 ">
                            <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>任务类型</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>状态</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="myTaskList2 hide">
                        <table id="myTaskList2" class="table table-striped table-centered mb-0">
                            <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>状态</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div> <!-- end col-->
            </div> <!-- end row-->
        </div>
        <!-- end col -->
    </div>
    <!-- modal.测评任务量表 start-->
    <div id="modal-scale" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title">测评任务包含的量表</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table table-striped table-centered nowrap" id="tbScale">
                        <thead>
                        <tr>
                            <th>量表名称</th>
                            <th></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.测评任务量表 end-->
    <input type="hidden" id="hidTaskID" value="" />
    <input type="hidden" id="hidTaskType" value="" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/dataTables/jquery.dataTables.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.bootstrap4.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/dataTables.responsive.min.js}"></script>
    <script th:src="@{/static/js/plugins/dataTables/responsive.bootstrap4.min.js}"></script>
    <script th:src="@{/static/js/pages/bsDatatable.js}"></script>
    <script type="text/javascript">
        let columns = [
            { "data": "taskName", "bSortable": false },
            { "data": "taskType", "render":
                    function (data, type, full, meta) {
                        let labels = "";
                        if (data === 1) {
                            labels = '<span class="badge badge-primary">限定</span>';
                        }
                        if (data === 2) {
                            labels = '<span class="badge badge-primary">非限定</span>';
                        }
                        return labels;
                    }, "bSortable": false
            },
            { "data": "startTime", "bSortable": false},
            { "data": "endTime", "bSortable": false}
        ];
        let columnDefs_todo = [
            { targets: 4,
                render: function (data, type, row, meta) {
                    let labels = "";
                    let startDate = row.startTime;
                    let endDate = row.endTime;
                    if (startDate > getDateNowFormat()) {
                        labels = '<span class="badge badge-light">未开始</span>';
                    }
                    if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                        labels = '<span class="badge badge-success">进行中</span>';
                    }
                    if (getDateNowFormat() >= endDate) {
                        labels = '<span class="badge badge-warning">已结束</span>';
                    }
                    return labels;
                }
            },
            { targets:5,
                render: function (data, type, row, meta) {
                    let labels = "";
                    let startDate = row.startTime;
                    let endDate = row.endTime;
                    if (startDate > getDateNowFormat()) {
                        labels += '<span class="badge badge-danger">测评时间未到</span>';
                    }
                    if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                        labels += '<a href="#"><button class="btn btn-outline-success btn-rounded btn-sm scale" title="参与测评"><i class="fa fa-angle-double-right"></i></button></a>';
                    }
                    return labels;
                }
            }];
        let getQueryCondition = function (data) {
            let param = {};
            param.taskName = "";
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let oTableScale = null;
        let initTaskScale = function (scales) {
            if (oTableScale != null) {
                oTableScale.destroy();
            }
            oTableScale = $("#tbScale").DataTable({
                "ordering": false,
                "processing": false,
                "searching": false,
                "autoWidth": false,
                "deferRender": true,
                "lengthChange": false,
                "paging": false,
                "data": scales,
                "columns": [{ "data": "scaleName", "bSortable": false }],
                "columnDefs": [{
                        targets: 1,
                        render: function (data, type, row, meta) {
                            return '<button class="btn btn-outline-success btn-sm start"><i class="fa fa-chevron-circle-right mr-1"></i>开始测评</button>';
                        }
                    }
                ],
                //语言配置--页面显示的文字
                "language": {
                    "sLengthMenu": "每页显示 _MENU_ 条记录",
                    "emptyTable": "暂无相关数据",
                    "zeroRecords": "对不起，查询不到任何相关数据",
                    "info": "_START_ - _END_ 条，共 _TOTAL_ 条",
                    "infoEmpty": "",
                    "sInfoFiltered": "数据表中共为 _MAX_ 条记录)",
                    "sSearch": "搜索",
                    "sUrl": "",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "前一页",
                        "sNext": "后一页",
                        "sLast": "尾页"
                    }
                }
            });
        };
        $(function () {
            $("#editProfile").click(function () {
                location.href = "/anteroom/personal/edit_profile?id=[[${user.userId}]]";
            });
            if ([[${user.roleId}]] === 3) {
                initVisitorTaskDT();
                $(".myTaskList").removeClass("hide").addClass("show");
            }
            else {
                initCounselorTaskDT();
                $(".myTaskList2").removeClass("hide").addClass("show");
            }
            $("#myTaskList").on('click', '.scale', function () {
                let data = oTable.row($(this).parents('tr')).data();
                $("#hidTaskID").val(data.taskId);
                $("#hidTaskType").val(data.taskType);
                initTaskScale(data.scales);
                $("#modal-scale").modal();
            });
            $("#tbScale").on('click', '.start', function () {
                let data = oTableScale.row($(this).parents('tr')).data();
                $.post('/measuringroom/testing/is_scale_done', { taskId: $("#hidTaskID").val(), scaleId: data.id }, function (res) {
                    if (res.id === 0 || (res.id !== 0 && res.state !== 1 && res.state !== 2)) {
                        location.href = '/measuringroom/testing/guide?taskId=' + $("#hidTaskID").val() + '&type=' + $("#hidTaskType").val() + '&scaleId=' + data.id + '&recordId=' + res.id + '';
                    }
                    else {
                        layer.msg("您已经完成该测试！", { icon: 0, time: 2000 });
                    }
                });
            });
        });
        let initVisitorTaskDT = function () {
            $("#myTaskList").bsDataTables({
                columns: columns,
                url: '/measuringroom/task/my_tasks',
                columnDefs: columnDefs_todo,
                paging: true,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                            $(".dataTables_info").hide();
                        }
                    });
                }
            });
        };
        let columns2 = [
            { "data": "taskName", "bSortable": false },
            { "data": "startTime", "bSortable": false},
            { "data": "endTime", "bSortable": false}
        ];
        let columnDefs2 = [
            {targets: 3,
                render: function (data, type, row, meta) {
                    let labels = "";
                    let startDate = row.startTime;
                    let endDate =  row.endTime;
                    if (startDate > getDateNowFormat()) {
                        labels = '<span class="badge badge-light">未开始</span>';
                    }
                    if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                        labels = '<span class="badge badge-success">进行中</span>';
                    }
                    if (getDateNowFormat() >= endDate) {
                        labels = '<span class="badge badge-warning">已结束</span>';
                    }
                    return labels;
                }
            }
        ];
        let getQueryCondition2 = function (data) {
            let param = {};
            //组装分页参数
            $.each(data, function (i, item) {
                if (item.name === "iDisplayLength") {
                    param.pageSize = item.value;
                }
                if (item.name === "iDisplayStart") {
                    param.pageIndex = item.value;
                }
            });
            return param;
        };
        let initCounselorTaskDT = function () {
            $("#myTaskList2").bsDataTables({
                columns: columns2,
                url: '/measuringroom/task/list',
                columnDefs: columnDefs2,
                paging: true,
                pageLength:20,
                retrieveData: function (sSource, aoData, fnCallback) {
                    let jsonObj = getQueryCondition2(aoData);
                    $.ajax({
                        "type": "post",
                        "url": sSource,
                        "dataType": "json",
                        "contentType": "application/json",
                        "data": JSON.stringify(jsonObj),
                        "success": function (res) {
                            //服务器端返回的对象的returnObject部分是要求的格式
                            fnCallback(res);
                        }
                    });
                }
            });
        };
    </script>
</th:block>
</body>
</html>
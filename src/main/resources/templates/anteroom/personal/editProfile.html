    <!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">接待室</a></li>
                        <li class="breadcrumb-item"><a href="javascript: void(0);">个人中心</a></li>
                        <li class="breadcrumb-item active">编辑个人资料</li>
                    </ol>
                </div>
                <h4 class="page-title">个人中心</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3"><i class="fa fa-pencil-square-o mr-1"></i>编辑个人资料</h4>
                    <ul class="nav nav-tabs mb-3 nav-bordered">
                        <li class="nav-item">
                            <a href="#profile-b1" data-toggle="tab" aria-expanded="false" class="nav-link active">
                                <span class="d-lg-block">个人信息</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#profile-b2" data-toggle="tab" aria-expanded="true" class="nav-link">
                                <span class="d-lg-block">个人头像</span>
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane active" id="profile-b1">
                            <form id="frmProfile" class="horizontal col-8 m-4">
                                <div class="form-group row mb-3">
                                    <label for="loginName" class="col-form-label col-3">用户名</label>
                                    <div class="col-9">
                                        <input id="loginName" name="loginName" class="form-control" type="text" aria-describedby="nameHelp" readonly>
                                        <input id="hidLoginName" name="hidLoginName" type="hidden" value="" />
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="realName" class="col-form-label col-3">姓名（必填）</label>
                                    <div class="col-9">
                                        <input id="realName" name="realName" class="form-control" type="text">
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="male" class="col-form-label col-3">性别</label>
                                    <div class="col-9 form-inline">
                                        <div class="custom-control custom-radio mr-2">
                                            <input type="radio" id="male" name="sex" class="custom-control-input" value="男" checked>
                                            <label class="custom-control-label" for="male">男</label>
                                        </div>
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="female" name="sex" class="custom-control-input" value="女">
                                            <label class="custom-control-label" for="female">女</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label class="col-form-label col-3">出生日期（必选）</label>
                                    <div class="input-group col-9">
                                        <input type="text" id="birth" name="birth" class="form-control" readonly="readonly" onClick="WdatePicker({el:this,dateFmt:'yyyy-MM-dd'})" placeholder="点击选择…">
                                        <div class="input-group-append">
                                            <button class="btn btn-light" type="button" value="发送短信"><i class="fa fa-calendar"></i></button>
                                        </div>
                                    </div>
                                </div>
                                <th:block th:if="${pageData.sysConfigDto.isSmsEnabled eq 0 and pageData.sysConfigDto.smsConfig.isMobileBind eq 0}">
                                    <div class="form-group row mb-3">
                                        <label for="mobile" class="col-form-label col-3">联系电话</label>
                                        <div class="col-9">
                                            <input id="mobile" name="mobile" class="form-control" type="text" autocomplete="off" maxlength="11">
                                        </div>
                                    </div>
                                </th:block>
                                <div class="form-group row mb-3">
                                    <label for="emergency_contact_person" class="col-form-label col-3">紧急联系人</label>
                                    <div class="col-9">
                                        <input id="emergency_contact_person" name="emergency_contact_person" class="form-control" type="text" autocomplete="off">
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="emergency_contact_mobile" class="col-form-label col-3">紧急联系电话</label>
                                    <div class="col-9">
                                        <input id="emergency_contact_mobile" name="emergency_contact_mobile" class="form-control" type="text" autocomplete="off" maxlength="11">
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label class="col-form-label col-3">民族</label>
                                    <div class="col-9">
                                        <select class="form-control select2" data-toggle="select2" name="nation" id="nation">
                                            <option value="">请选择</option>
                                            <option value="汉族">汉族</option>
                                            <option value="满族">满族</option>
                                            <option value="回族">回族</option>
                                            <option value="蒙古族">蒙古族</option>
                                            <option value="壮族">壮族</option>
                                            <option value="藏族">藏族</option>
                                            <option value="阿昌族">阿昌族</option>
                                            <option value="白族">白族</option>
                                            <option value="保安族">保安族</option>
                                            <option value="布朗族">布朗族</option>
                                            <option value="布依族">布依族</option>
                                            <option value="朝鲜族">朝鲜族</option>
                                            <option value="达斡尔族">达斡尔族</option>
                                            <option value="傣族">傣族</option>
                                            <option value="德昂族">德昂族</option>
                                            <option value="东乡族">东乡族</option>
                                            <option value="侗族">侗族</option>
                                            <option value="独龙族">独龙族</option>
                                            <option value="俄罗斯族">俄罗斯族</option>
                                            <option value="鄂伦春族">鄂伦春族</option>
                                            <option value="鄂温克族">鄂温克族</option>
                                            <option value="高山族">高山族</option>
                                            <option value="哈尼族">哈尼族</option>
                                            <option value="哈萨克族">哈萨克族</option>
                                            <option value="赫哲族">赫哲族</option>
                                            <option value="基诺族">基诺族</option>
                                            <option value="京族">京族</option>
                                            <option value="景颇族">景颇族</option>
                                            <option value="柯尔克孜族">柯尔克孜族</option>
                                            <option value="拉祜族">拉祜族</option>
                                            <option value="黎族">黎族</option>
                                            <option value="傈僳族">傈僳族</option>
                                            <option value="毛南族">毛南族</option>
                                            <option value="门巴族">门巴族</option>
                                            <option value="苗族">苗族</option>
                                            <option value="纳西族">纳西族</option>
                                            <option value="怒族">怒族</option>
                                            <option value="普米族">普米族</option>
                                            <option value="羌族">羌族</option>
                                            <option value="撒拉族">撒拉族</option>
                                            <option value="水族">水族</option>
                                            <option value="塔吉克族">塔吉克族</option>
                                            <option value="塔塔尔族">塔塔尔族</option>
                                            <option value="土家族">土家族</option>
                                            <option value="土族">土族</option>
                                            <option value="维吾尔族">维吾尔族</option>
                                            <option value="乌孜别克族">乌孜别克族</option>
                                            <option value="锡伯族">锡伯族</option>
                                            <option value="瑶族">瑶族</option>
                                            <option value="彝族">彝族</option>
                                            <option value="裕固族">裕固族</option>
                                            <option value="仡佬族">仡佬族</option>
                                            <option value="仫佬族">仫佬族</option>
                                            <option value="佤族">佤族</option>
                                            <option value="珞巴族">珞巴族</option>
                                            <option value="畲族">畲族</option>
                                            <option value="其他少数民族">其他少数民族</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="email" class="col-form-label col-3">邮箱</label>
                                    <div class="col-9">
                                        <input id="email" name="email" class="form-control" type="email">
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="idcardNo" class="col-form-label col-3">身份证号</label>
                                    <div class="col-9">
                                        <input id="idcardNo" name="idcardNo" class="form-control" type="text" maxlength="18">
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="native" class="col-form-label col-3">籍贯</label>
                                    <div class="col-9">
                                        <input id="native" name="native" class="form-control" type="text">
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label class="col-form-label col-3">地址</label>
                                    <div class="col-9">
                                        <div data-toggle="distpicker">
                                            <select class="form-control select2" id="address_province" data-toggle="select2"></select>
                                            <select class="form-control select2" id="address_city" data-toggle="select2"></select>
                                            <select class="form-control select2" id="address_dist" data-toggle="select2"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="address_detail" class="col-form-label col-3">详细地址</label>
                                    <div class="col-9">
                                        <input id="address_detail" name="address_detail" class="form-control" type="text">
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="education" class="col-form-label col-3">文化程度</label>
                                    <div class="col-9">
                                        <select class="form-control select2" data-toggle="select2" id="education" name="education">
                                            <option value="">请选择</option>
                                            <option value="研究生及以上">研究生及以上</option>
                                            <option value="大学本科">大学本科</option>
                                            <option value="大学专科">大学专科</option>
                                            <option value="高中/中专">高中/中专</option>
                                            <option value="初中及以下">初中及以下</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="job" class="col-form-label col-3">职业</label>
                                    <div class="col-9">
                                        <select class="form-control select2" data-toggle="select2" id="job" name="job">
                                            <option value="">请选择</option>
                                            <option value="市场/销售/商务">市场/销售/商务</option>
                                            <option value="采购">采购</option>
                                            <option value="行政">行政</option>
                                            <option value="人力">人力</option>
                                            <option value="产品/运营人员">产品/运营人员</option>
                                            <option value="个体经营者">个体经营者</option>
                                            <option value="财务/会计/出纳/审计">财务/会计/出纳/审计</option>
                                            <option value="企业管理者">企业管理者</option>
                                            <option value="律师/法务">律师/法务</option>
                                            <option value="设计从业者">设计从业者</option>
                                            <option value="服务业人员">服务业人员</option>
                                            <option value="技术开发/工程师">技术开发/工程师</option>
                                            <option value="农林牧渔劳动者">农林牧渔劳动者</option>
                                            <option value="工人劳动者">工人劳动者</option>
                                            <option value="全职家庭主妇/夫">全职家庭主妇/夫</option>
                                            <option value="自由职业">自由职业</option>
                                            <option value="离休/退休">离休/退休</option>
                                            <option value="学生">学生</option>
                                            <option value="老师">老师</option>
                                            <option value="医护人员">医护人员</option>
                                            <option value="科研人员">科研人员</option>
                                            <option value="党政机关人员">党政机关人员</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="job" class="col-form-label col-3">宗教信仰</label>
                                    <div class="col-9">
                                        <select class="form-control" id="religion" name="religion">
                                            <option value="">请选择</option>
                                            <option value="佛教">佛教</option>
                                            <option value="基督教">基督教</option>
                                            <option value="伊斯兰教">伊斯兰教</option>
                                            <option value="天主教">天主教</option>
                                            <option value="其他">其他</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label for="job" class="col-form-label col-3">婚姻状况</label>
                                    <div class="col-9">
                                        <select class="form-control" id="marriage" name="marriage">
                                            <option value="">请选择</option>
                                            <option value="未婚">未婚</option>
                                            <option value="已婚">已婚</option>
                                            <option value="丧偶">丧偶</option>
                                            <option value="离婚">离婚</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row mb-3">
                                    <label class="col-form-label col-3">个人说明</label>
                                    <div class="col-9">
                                        <textarea id="description" data-toggle="maxlength" class="form-control" rows="3" maxlength="100" placeholder="限制100字以内"></textarea>
                                    </div>
                                </div>
                                <div class="form-group row mb-3 be-good-at">
                                    <label class="col-form-label col-3" for="beGoodAt">咨询方向</label>
                                    <div class="col-9">
                                        <textarea id="beGoodAt" name="beGoodAt" class="form-control" rows="3" maxlength="50" placeholder="填写咨询方向、擅长领域……"></textarea>
                                    </div>
                                </div>
                                <div class="form-group mb-0 justify-content-end row">
                                    <div class="col-9">
                                        <input type="submit" id="btnSave" class="btn btn-primary" value="保存" />
                                        <a href="javascript:history.go(-1)" class="btn btn-link">返回</a>
                                    </div>
                                </div>
                                <input id="hidStructParentID" name="hidStructParentID" value="0" type="hidden" />
                                <input id="hidIsRecommend" type="hidden" value="0" />
                            </form>
                        </div>
                        <div class="tab-pane" id="profile-b2">
                            <div class="form-group ml-3">
                                <img id="avatar" src="" class="img-responsive rounded-circle" style="width:128px; height: 128px;" />
                            </div>
                            <div class="form-group ml-3">
                                <label>更换头像</label>
                                <input type="file" name="file" id="txt_file" class="file-loading" />
                                <small class="text-warning"><i class="fa fa-info-circle mr-1"></i>建议像素大小：128*128px</small>
                                <input id="hidAvatar" type="hidden" value="" />
                            </div>
                            <div class="form-group ml-3">
                                <button id="btnAvatar" class="btn btn-primary mt-2" type="button">保存</button>
                            </div>
                        </div>
                    </div>

                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div> <!-- end col -->
    </div>
    <input id="hidMobile" type="hidden" th:value="${user.mobile}" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/DatePicker/WdatePicker.js}"></script>
    <script th:src="@{/static/js/plugins/distpicker/distpicker.data.min.js}"></script>
    <script th:src="@{/static/js/plugins/distpicker/distpicker.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        let userId = getUrlParam("id");
        let initForm = function () {
            $.post("/anteroom/user/get?id=" + userId, '', function (data) {
                let res = JSON.parse(data);
                $("#hidStructParentID").val(res.structId);
                $("#loginName").val(res.loginName);
                $("#hidLoginName").val(res.loginName);
                $("#realName").val(res.realName);
                $("#hidUserID").val(userId);
                if (res.sex === "男") {
                    $("#male").attr("checked", true);
                }
                else {
                    $("#female").attr("checked", true);
                }
                $("#birth").val(res.birth.substring(0, 10));
                $("#nation").val(res.nation).trigger('change');
                if ([[${pageData.sysConfigDto.isSmsEnabled}]] === 0) {
                    $("#mobile").val(res.mobile);
                }
                $("#emergency_contact_person").val(res.emergencyContactPerson);
                $("#emergency_contact_mobile").val(res.emergencyContactMobile);
                $("#email").val(res.email);
                $("#idcardNo").val(res.iDCardNo);
                $("#native").val(res.nativePlace);
                $("#education").val(res.education).trigger('change');
                $("#marriage").val(res.marriage);
                $("#job").val(res.job).trigger('change');
                $("#religion").val(res.religion);
                $("#description").val(res.description);
                $("#address_province").val(res.addressProvince).trigger('change');
                $("#address_city").val(res.addressCity).trigger('change');
                $("#address_dist").val(res.addressDist);
                $("#address_detail").val(res.addressDetail);
                $("#avatar").attr("src", res.headPic === "" ? "/static/images/user.png" : "/static/upload/avatar/" + res.headPic);
                var currentUserRoleId = [[${user.role.roleId}]];
                if (currentUserRoleId !== 1 && currentUserRoleId !== 3 && currentUserRoleId !== 4) {
                    $("#hidIsRecommend").val(res.counselorInfo.isRecommend);
                    $("#beGoodAt").val(res.counselorInfo.beGoodAt);
                }
                else {
                    $(".be-good-at").hide();
                }
            });
        };
        let fileInput = function () {
            let oFile = new Object();
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=avatar',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-warning", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    var res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidAvatar").val(res.resultMsg);
                        $("#avatar").attr("src", res.resultMsg === "" ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + res.resultMsg);
                        layer.msg("头像上传成功", { icon: 1, time: 2000 });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                });
            };
            return oFile;
        };
        $(function () {
            initForm();
            $("#frmProfile").validate({
                ignore: "",
                errorPlacement: function (error, element) {
                    wrap = element.parent();
                    if (wrap.hasClass('input-group')) {
                        error.insertAfter(wrap);
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    loginName: { required: true },
                    realName: { required: true },
                    birth: { required: true }
                },
                messages: {
                    loginName: { required: "请输入登录名"},
                    realName: { required: "请输入姓名" },
                    birth: { required: "请选择出生日期" }
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.userId = userId;
                    jsonObj.loginName = $.trim($("#loginName").val());
                    jsonObj.structId = $("#hidStructParentID").val();
                    jsonObj.roleId = [[${user.role.roleId}]];
                    jsonObj.realName = $.trim($("#realName").val());
                    jsonObj.sex = $('input[name="sex"]:checked').val();
                    jsonObj.birth = $("#birth").val();
                    jsonObj.nation = $("#nation").val();
                    jsonObj.mobile = [[${pageData.sysConfigDto.isSmsEnabled}]] === 0 ? $("#mobile").val() : $("#hidMobile").val();
                    jsonObj.emergencyContactPerson = $("#emergency_contact_person").val();
                    jsonObj.emergencyContactMobile = $("#emergency_contact_mobile").val();
                    jsonObj.email = $("#email").val();
                    jsonObj.iDCardNo = $("#idcardNo").val();
                    jsonObj.nativePlace = $("#native").val();
                    jsonObj.education = $("#education").val();
                    jsonObj.marriage = $("#marriage").val();
                    jsonObj.job = $("#job").val();
                    jsonObj.religion = $("#religion").val();
                    jsonObj.addressProvince = $("#address_province").val();
                    jsonObj.addressCity = $("#address_city").val();
                    jsonObj.addressDist = $("#address_dist").val();
                    jsonObj.addressDetail = $("#address_detail").val();
                    jsonObj.description = $("#description").val();
                    let counselorInfo = {};
                    counselorInfo.userId = userId;
                    counselorInfo.isRecommend = $("#hidIsRecommend").val();
                    counselorInfo.beGoodAt = $("#beGoodAt").val();
                    jsonObj.counselorInfo = counselorInfo;
                    $("#btnSave").val("请稍后…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: "post",
                        async: false,
                        url: "/anteroom/user/update",
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.alert(res.resultMsg, {
                                    icon: 1, yes: function (index) {
                                        location.reload();
                                    }
                                });
                            }
                            else {
                                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                            }
                        }
                    });
                }
            });
            let oFileInput = new fileInput();
            oFileInput.Init("txt_file");

            $("#btnAvatar").click(function () {
                let headPic = $("#hidAvatar").val();
                if (headPic === "") {
                    layer.msg("请先上传头像", { icon: 2, time: 2000 });
                    return;
                }
                $.post("/anteroom/user/avatar", { userId: userId, headPic: $("#hidAvatar").val() }, function (res) {
                    if (res.resultCode === 200) {
                        layer.msg(res.resultMsg, { icon: 1, time: 2000 });
                    }
                    else {
                        layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                    }
                }, 'json');
            });
        });
    </script>
</th:block>
</body>
</html>
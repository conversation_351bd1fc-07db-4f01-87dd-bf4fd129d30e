<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            我的问卷记录
        </div>
        <div class="right">
            <label for="surveyName" class="mb-0 toggleSearchbox">
                <i class="fa fa-search font14"></i>
            </label>
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div class="searchBox">
        <form>
        <span class="inputIcon" id="btnQuery">
            <i class="fa fa-search font14"></i>
        </span>
            <input type="text" class="form-control" id="surveyName" placeholder="问卷名称...">
            <a href="javascript:" class="toggleSearchbox closeButton">
                <i class="fa fa-times-circle font14"></i>
            </a>
        </form>
    </div>
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <div class="list-content hide">
                <div class="mt-2 mb-3">
                    <img th:src="@{/static/images/app_testrecord_banner.png}" alt="image" class="imageBlock img-fluid rounded">
                </div>
                <div class="detail font14">
                </div>
                <div class="mt-2 mb-2 load-more hide"><a href="javascript:" class="btn btn-primary btn-sm btn-block" id="loadMore">查看更多</a></div>
            </div>
            <div class="splashBlock hide">
                <div class="mb-3 mt-3">
                    <img class="nodata-img" th:src="@{/static/images/nodata.png}" alt="">
                </div>
                <div class="sectionTitle text-center">
                    <div class="title">
                    </div>
                    <div class="lead">
                        暂无问卷记录！
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let currentPage = 0;
        let pageSize = 8;
        $(function () {
            init(currentPage);
            $("#btnQuery").click(function () {
                $(".detail").empty();
                init(0);
            });
            $(".load-more").on('click', '#loadMore', function () {
                currentPage = currentPage + 1;
                init(currentPage);
            });
        });
        let getQueryCondition = function (currentPage) {
            let param = {};
            param.surveyName = $("#surveyName").val();
            param.userId = '[[${user.userId}]]';
            param.pageSize = pageSize;
            param.pageIndex = currentPage * pageSize;
            return param;
        };
        let init = function (currentPage) {
            layer.open({
                type: 2
                , content: '加载中…'
            });
            $.ajax({
                type: 'POST',
                url: '/survey/surveyrecord/get_my_records',
                data: JSON.stringify(getQueryCondition(currentPage)),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    if (res.data.length === 0) {
                        if (currentPage === 0) {
                            $(".list-content").removeClass('show').addClass('hide');
                            $(".splashBlock").removeClass('hide').addClass('show');
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                    }
                    else {
                        let str = "";
                        for (let i = 0; i < res.data.length; i++) {
                            let surveyRecord = res.data[i];
                            str += '<ul class="list-unstyled mt-2 pl-2">';
                            str += '<li class="pb15">问卷：' + surveyRecord.surveyName + '</li>';
                            str += '<li class="pb15">日期：' + surveyRecord.recordDate + '</li>';
                            let strState = "";
                            let lblReport = "";
                            let state = surveyRecord.isDone;
                            if (state === 0) strState = '<span class="badge badge-secondary mr-1 mb-1">未完成</span>';
                            if (state === 1) {
                                strState = '<span class="badge badge-success mr-1 mb-1">已完成</span>';
                            }
                            str += '<li class="pb15">' + strState + '</li></ul>';
                            str += '<div class="divider dashed large mt-2 mb-2"></div>';

                        }
                        $(".detail").append(str);
                        $(".list-content").removeClass('hide').addClass('show');
                        if (res.data.length < pageSize) {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('hide').addClass('show');
                        }
                        $(".splashBlock").removeClass('show').addClass('hide');
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .sortable-list {
            min-height: 100px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            touch-action: none; /* 防止触摸滚动 */
            overflow: visible;
        }
        .sortable-item {
            cursor: move;
            user-select: none;
            transition: all 0.2s ease;
            touch-action: none; /* 防止触摸滚动 */
            position: relative;
            z-index: 1;
        }
        .sortable-item.dragging {
            z-index: 1000;
            pointer-events: none;
        }
        .sortable-item:hover .sortable-content {
            background-color: #e3f2fd !important;
            border-color: #2196f3 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }
        .sortable-content {
            transition: all 0.2s ease;
            border: 1px solid #dee2e6;
            touch-action: none; /* 防止触摸滚动 */
        }
        .sortable-placeholder {
            background-color: #e3f2fd;
            border: 2px dashed #2196f3;
            height: 60px;
            margin-bottom: 8px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2196f3;
            font-weight: 500;
        }
        .sortable-ghost {
            opacity: 0.3;
        }
        .drag-handle {
            cursor: grab;
            color: #6c757d;
            font-size: 16px;
            touch-action: none; /* 防止触摸滚动 */
            padding: 5px;
        }
        .drag-handle:active {
            cursor: grabbing;
        }
        .drag-handle:hover {
            color: #2196f3;
        }
        .sort-number {
            background-color: #2196f3;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        /* 评分单选题样式 */
        .rating-options-container {
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .rating-options-row {
            display: flex;
            flex-wrap: nowrap;
            gap: 8px;
            padding: 8px 0;
            min-width: max-content;
        }
        
        .rating-option-item {
            flex: 0 0 auto;
            min-width: 60px;
            text-align: center;
        }
        
        .rating-option-item .custom-control {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 4px;
            border-radius: 8px;
            transition: all 0.2s ease;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        
        .rating-option-item .custom-control:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
        }
        
        .rating-option-item .custom-control-input:checked + .custom-control-label {
            color: #2196f3;
            font-weight: 600;
        }
        
        .rating-option-item .custom-control-input:checked ~ .custom-control {
            background: #e3f2fd;
            border-color: #2196f3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }
        
        .rating-option-label {
            font-size: 14px;
            line-height: 1.2;
            margin-top: 4px;
            text-align: center;
            word-break: break-word;
            max-width: 100%;
        }
        
        /* 移动端优化 */
        @media (max-width: 480px) {
            .rating-options-row {
                gap: 6px;
                padding: 6px 0;
            }
            
            .rating-option-item {
                min-width: 50px;
            }
            
            .rating-option-item .custom-control {
                padding: 6px 3px;
            }
            
            .rating-option-label {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle" th:text="${survey.surveyName}">
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent p-3 mb-5">
            <form id="frmSurvey" class="font14">
                <th:block th:each="question:${listQuestions}">
                    <div class="font-weight-bold font16 mb-1">
                        <th:block th:utext="${question.qNumber}"/>、<th:block th:utext="${#strings.replace(question.qContent,'<p>','')}"/>
                    </div>
                    <div class="form-group" th:if="${question.qType eq 1}">
                        <th:block th:each="item:${question.listItems}">
                            <div class="custom-control custom-radio mb-1">
                                <input class="custom-control-input" type="radio"   th:with="prefix1='option_',prefix2='question_'"
                                       th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}"  th:value="${item.itemContent}"
                                       th:onclick="toggleOtherInput(this,[[${item.id}]],[[${item.isOther}]],[[${question.id}]])"  />
                                <label class="custom-control-label" th:with="prefix='option_'"  th:attr="for=${prefix}+${item.id}" th:utext="${item.itemContent}"></label>
                            </div>
                            <th:block th:if="${item.isOther eq 1}">
                                <div class="form-group" style="display:none;" th:with="prefix='otherInput_'" th:attr="id=${prefix}+${question.id}">
                                    <input type="text" class="form-control" th:with="prefix='_other'" th:attr="name=${question.id}+${prefix}" placeholder="请输入具体内容" />
                                </div>
                            </th:block>
                        </th:block>
                    </div>
                    <div class="form-group" th:if="${question.qType eq 2}">
                        <th:block th:each="item:${question.listItems}">
                            <div class="custom-control custom-checkbox mb-1">
                                <input class="custom-control-input" type="checkbox"   th:with="prefix1='option_',prefix2='question_'"
                                       th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}"  th:value="${item.itemContent}"
                                       th:onclick="toggleOtherInput(this,[[${item.id}]],[[${item.isOther}]],[[${question.id}]])" />
                                <label class="custom-control-label" th:with="prefix='option_'"  th:attr="for=${prefix}+${item.id}" th:text="${item.itemContent}"></label>
                            </div>
                            <th:block th:if="${item.isOther eq 1}">
                                <div class="form-group" style="display:none;" th:with="prefix='otherInput_'" th:attr="id=${prefix}+${question.id}">
                                    <input type="text" class="form-control" th:with="prefix='_other'" th:attr="name=${question.id}+${prefix}"  placeholder="请输入具体内容" />
                                </div>
                            </th:block>
                        </th:block>
                    </div>
                    <div class="form-group" th:if="${question.qType eq 3}">
                        <input type="text" class="form-control" th:with="prefix='question_'"  th:attr="name=${prefix}+${question.id}" placeholder="请输入内容" />
                    </div>
                    <div class="form-group" th:if="${question.qType eq 4}">
                        <div class="alert alert-info mb-3">
                            <small><i class="fa fa-info-circle mr-1"></i>请拖拽下方选项进行排序，排在前面的表示优先级更高</small>
                        </div>
                        <!-- 调试信息，可以在生产环境中删除 -->
                        <div class="small text-muted mb-2" style="display: none;">
                            调试信息 - 题目ID: <span th:text="${question.id}"></span>,
                            题目类型: <span th:text="${question.qType}"></span>,
                            选项数量: <span th:text="${#lists.size(question.listItems)}"></span>
                        </div>
                        <div class="sortable-list" th:with="prefix='sortable_'" th:attr="id=${prefix}+${question.id}">
                            <th:block th:each="item,iterStat:${question.listItems}">
                                <div class="sortable-item" th:attr="data-value=${item.itemContent}">
                                    <div class="sortable-content" style="display: flex; align-items: center; padding: 12px; border-radius: 8px; margin-bottom: 8px; background-color: white;">
                                        <div class="sort-number" th:text="${iterStat.count}"></div>
                                        <i class="fa fa-bars drag-handle" style="margin-right: 12px;"></i>
                                        <span th:text="${item.itemContent}" style="flex-grow: 1;"></span>
                                        <i class="fa fa-arrows-v" style="margin-left: 8px; color: #6c757d;"></i>
                                    </div>
                                </div>
                            </th:block>
                        </div>
                        <input type="hidden" th:with="prefix='question_'" th:attr="name=${prefix}+${question.id}" class="sort-result" />
                    </div>
                    <!-- 评分单选题 -->
                    <div class="form-group" th:if="${question.qType eq 5}">
                        <div class="rating-options-container">
                            <div class="rating-options-row">
                                <th:block th:each="item:${question.listItems}">
                                    <div class="rating-option-item">
                                        <div class="custom-control custom-radio">
                                            <input class="custom-control-input" type="radio" th:with="prefix1='option_',prefix2='question_'"
                                                   th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}" th:value="${item.itemContent}" />
                                            <label class="custom-control-label rating-option-label" th:with="prefix='option_'" th:attr="for=${prefix}+${item.id}" th:utext="${item.itemContent}"></label>
                                        </div>
                                    </div>
                                </th:block>
                            </div>
                        </div>
                    </div>
                    <div class="divider dotted large mt-2 mb-2"></div>
                </th:block>
                <div class="modal-footer border-0">
                    <input type="submit" id="btnSave" class="btn btn-primary btn-block" value="提交" />
                </div>
            </form>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script>
        // 排序功能实现 - 优化版，支持触摸和鼠标，避免页面滚动冲突
        function initSortable(element, options) {
            let draggedElement = null;
            let placeholder = null;
            let isDragging = false;

            // 创建占位符
            function createPlaceholder() {
                placeholder = document.createElement('div');
                placeholder.className = 'sortable-placeholder';
                placeholder.innerHTML = '<div style="height: 50px; display: flex; align-items: center; justify-content: center; color: #2196f3;"><i class="fa fa-arrows-v"></i> 拖拽到此处</div>';
                return placeholder;
            }

            // 获取拖拽后的位置
            function getDragAfterElement(container, y) {
                const draggableElements = [...container.querySelectorAll('.sortable-item:not(.dragging)')];

                return draggableElements.reduce((closest, child) => {
                    const box = child.getBoundingClientRect();
                    const offset = y - box.top - box.height / 2;

                    if (offset < 0 && offset > closest.offset) {
                        return { offset: offset, element: child };
                    } else {
                        return closest;
                    }
                }, { offset: Number.NEGATIVE_INFINITY }).element;
            }

            // 开始拖拽
            function startDrag(e, item) {
                e.preventDefault();
                e.stopPropagation();

                draggedElement = item;
                isDragging = true;

                // 添加拖拽样式
                draggedElement.classList.add('dragging');
                draggedElement.style.opacity = '0.5';

                // 创建并插入占位符
                createPlaceholder();
                draggedElement.parentNode.insertBefore(placeholder, draggedElement.nextSibling);

                console.log('开始拖拽');
            }

            // 拖拽中
            function onDrag(e) {
                if (!isDragging || !draggedElement) return;

                e.preventDefault();
                e.stopPropagation();

                const clientY = e.clientY || (e.touches && e.touches[0].clientY);
                if (!clientY) return;

                const afterElement = getDragAfterElement(element, clientY);

                if (afterElement == null) {
                    element.appendChild(placeholder);
                } else {
                    element.insertBefore(placeholder, afterElement);
                }
            }

            // 结束拖拽
            function endDrag(e) {
                if (!isDragging || !draggedElement) return;

                e.preventDefault();
                e.stopPropagation();

                // 移动元素到占位符位置
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.insertBefore(draggedElement, placeholder);
                    placeholder.parentNode.removeChild(placeholder);
                }

                // 恢复样式
                draggedElement.classList.remove('dragging');
                draggedElement.style.opacity = '';

                // 清理状态
                draggedElement = null;
                placeholder = null;
                isDragging = false;

                console.log('拖拽结束');
                if (options.onEnd) options.onEnd();
            }

            // 为每个排序项添加事件监听
            element.querySelectorAll('.sortable-item').forEach(item => {
                const dragHandle = item.querySelector('.drag-handle');

                // 鼠标事件
                dragHandle.addEventListener('mousedown', function(e) {
                    startDrag(e, item);
                });

                // 触摸事件
                dragHandle.addEventListener('touchstart', function(e) {
                    startDrag(e, item);
                }, { passive: false });
            });

            // 全局事件监听
            document.addEventListener('mousemove', onDrag);
            document.addEventListener('touchmove', onDrag, { passive: false });
            document.addEventListener('mouseup', endDrag);
            document.addEventListener('touchend', endDrag);
        }
    </script>
    <script type="text/javascript">
        let taskId = getUrlParam('taskId');
        let taskType = getUrlParam('type');
        let surveyId = getUrlParam('surveyId');
        let recordId = getUrlParam('recordId');
        $(function(){
            // 等待页面完全加载后初始化排序功能
            setTimeout(function() {
                $('.sortable-list').each(function() {
                    let questionId = $(this).attr('id').replace('sortable_', '');
                    console.log('初始化排序题:', questionId); // 调试信息

                    // 使用自定义排序功能
                    initSortable(this, {
                        onEnd: function() {
                            console.log('拖拽结束');
                            updateSortResult(questionId);
                        }
                    });

                    // 初始化排序结果
                    updateSortResult(questionId);
                });
            }, 100);

            // 动态生成验证规则
            let rules = {};
            let messages = {};

            // 获取所有的问题
            $('.form-group').each(function() {
                let questionId = $(this).find('input, text').first().attr('name');
                if (questionId) {
                    questionId = questionId.replace('question_', '');
                    let questionType = $(this).find('input, text').first().attr('type');

                    if (questionType === 'radio' || questionType === 'checkbox') {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请选择一个选项";
                    } else if (questionType === 'text') {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请填写内容";
                    } else if (questionType === 'hidden' && $(this).find('.sortable-list').length > 0) {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请完成排序";
                    }
                }
            });
            $("#frmSurvey").validate({
                errorPlacement: function (error, element) {
                    if (element.attr("name").indexOf("_other_input") !== -1) {
                        // 如果是其他选项的填空题，将错误信息放置在其下方
                        error.insertAfter(element);
                    } else {
                        // 将错误信息放置在 form-group 的后面
                        error.insertAfter(element.closest('.form-group'));
                    }
                },
                rules:rules,
                messages:messages,
                submitHandler: function (form){
                    let answers = [];
                    // 处理单选按钮
                    $('.custom-control-input[type="radio"]').each(function() {
                        if (this.checked) {
                            let questionId = this.name.replace('question_', '');
                            answers.push({
                                qId: questionId,
                                itemId: this.value,
                                otherAnswer: ''
                            });
                        }
                    });
                    // 处理复选按钮
                    $('.custom-control-input[type="checkbox"]').each(function() {
                        if (this.checked && !this.name.endsWith('_other')) {
                            let questionId = this.name.replace('question_', '');
                            let optionId = this.id.replace('option_','');
                            let existingAnswer = answers.find(answer => answer.qId === questionId);
                            if (existingAnswer) {
                                if (!existingAnswer.itemId) {
                                    existingAnswer.itemId = '';
                                }
                                existingAnswer.itemId += (existingAnswer.itemId ? '|' : '') + this.value;
                            } else {
                                answers.push({
                                    qId: questionId,
                                    itemId: this.value,
                                    otherAnswer: ''
                                });
                            }
                        }
                    });

                    // 处理文本输入
                    $('.form-control[type="text"]').each(function() {
                        let questionId = this.name.replace('question_', '');
                        if (this.value.trim() !== '' ) {
                            if(questionId.indexOf('_other') !== -1){
                                let answer = answers.find(answer=> answer.qId === questionId.replace('_other',''));
                                if(answer){
                                    answer.itemId += '|其他：'+this.value.trim();
                                }
                            }
                            else{
                                let existingAnswer = answers.find(answer => answer.qId === questionId);
                                if (existingAnswer) {
                                    existingAnswer.itemId = this.value.trim();
                                } else {
                                    answers.push({
                                        qId: questionId,
                                        itemId: this.value.trim(),
                                        otherAnswer: ''
                                    });
                                }
                            }
                        }
                    });

                    // 处理排序题
                    $('.form-group input[type="hidden"].sort-result').each(function() {
                        if (this.name.indexOf('question_') === 0 && this.value.trim() !== '') {
                            let questionId = this.name.replace('question_', '');
                            answers.push({
                                qId: questionId,
                                itemId: this.value.trim(),
                                otherAnswer: ''
                            });
                        }
                    });

                    // 转换为JSON字符串
                    let jsonObj = {};
                    jsonObj.surveyId = surveyId;
                    jsonObj.id = recordId;
                    jsonObj.taskId = taskId;
                    jsonObj.listAnswers = answers;
                    $("#btnSave").attr('Disabled',true);
                    $("#btnSave").val('请稍后……');
                    $.ajax({
                        type: 'POST',
                        url: '/survey/surveyrecord/add_for_task_survey',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />提交成功，感谢参与！' ,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function (index) {
                                        location.href = "/app/survey/survey_list?taskId="+taskId+"&type="+taskType;
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });

        // 更新排序结果
        function updateSortResult(questionId) {
            let sortableList = $('#sortable_' + questionId);
            let sortedItems = [];

            console.log('更新排序结果，题目ID:', questionId); // 调试信息

            sortableList.find('.sortable-item').each(function(index) {
                let value = $(this).attr('data-value');
                if (value) {
                    sortedItems.push((index + 1) + '.' + value);
                    // 更新序号显示
                    $(this).find('.sort-number').text(index + 1);
                    console.log('排序项:', (index + 1), value); // 调试信息
                }
            });

            let result = sortedItems.join('|');
            $('input[name="question_' + questionId + '"]').val(result);
            console.log('排序结果:', result); // 调试信息
        }

        let toggleOtherInput  = function(d,a,b,c) {
            if($(d).is('[type="radio"]')){
                if(b!==0){
                    if ($('#option_'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
                else{
                    $("#otherInput_" + c).toggle(false);
                }
            }
            if($(d).is('[type="checkbox"]')){
                if(b!==0){
                    if ($('#option_'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
            }
        }
    </script>
</th:block>
</body>
</html>
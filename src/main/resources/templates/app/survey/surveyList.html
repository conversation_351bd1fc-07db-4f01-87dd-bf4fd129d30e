<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle" th:text="${myTask.taskName}">
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <th:block th:if="${not #lists.isEmpty(myTask.surveys) and myTask.surveys.size() gt 0}">
                <th:block th:each="survey:${myTask.surveys}">
                    <div class="sectionTitle mt-2">
                        <div class="title">
                            <div class="font16 text-dark" th:text="${survey.surveyName}"></div>
                            <a href="javascript:void(0)" th:onclick="check([[${myTask.id}]],[[${survey.Id}]], [[${myTask.taskType}]])" class="badge badge-outline-danger text-danger">开始调查</a>
                        </div>
                    </div>
                    <div class="divider mt-2 mb-2"></div>
                </th:block>
            </th:block>
            <th:block th:unless="${not #lists.isEmpty(myTask.surveys) and myTask.surveys.size() gt 0}">
                <div class="splashBlock hide">
                    <div class="mb-3 mt-3">
                        <img th:src="@{/static/app/img/sample/draw-2.png}" alt="draw" class="img-fluid">
                    </div>
                    <div class="sectionTitle text-center">
                        <div class="title">
                        </div>
                        <div class="lead">
                            <i class="fa fa-info-circle mr-1"></i>没有数据
                        </div>
                    </div>
                </div>
            </th:block>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        let taskType = getUrlParam('type');
        let taskId = getUrlParam('taskId');
        $(function(){
            isTaskValid();
        });
        let isTaskValid = function (){
            layer.open({
                type: 2
                , content: '请稍后…',
                shadeClose: false
            });
            //判断任务的有效性
            $.post("/measuringroom/task/is_task_valid", {taskId: taskId},function(res){
                layer.closeAll();
                if(res.resultCode === 106){
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>问卷调查任务无效！'
                        , style: 'background-color:#ffffff; border:none;'
                        , shadeClose: false
                    });
                }
                if(res.resultCode === 105){
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>问卷调查任务已结束！'
                        , style: 'background-color:#ffffff; border:none;'
                        , shadeClose: false
                    });
                }
            })
        }
        function check(a, b, c) {
            layer.open({
                type: 2
                , content: '请稍后…',
                shadeClose: false
            });
            $.post('/survey/task/is_survey_done', { taskId: a, surveyId: b }, function (res) {
                layer.closeAll();
                if (res === ''|| res.id === 0 || (res.id!==0 && res.isDone !== 1)) {
                    location.href = '/app/survey/answer?taskId=' + a + '&surveyId=' + b + '&type=' + c + '&recordId=' + res.id + '';
                }
                else {
                    layer.open({
                        content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>您已经完成该调查问卷！'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                }
            });
        }
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            我的调查问卷
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <div class="splashBlock hide">
                <div class="mb-3 mt-3">
                    <img class="nodata-img" th:src="@{/static/images/nodata.png}" alt="">
                </div>
                <div class="sectionTitle text-center">
                    <div class="title">
                    </div>
                    <div class="lead">
                        当前没有问卷！
                    </div>
                </div>
            </div>
            <div class="list hide">
                <div class="mt-2 mb-3">
                    <img th:src="@{/static/images/app_testrecord_banner.jpg}" alt="image" class="imageBlock img-fluid rounded">
                </div>
                <div class="detail font16">
                </div>
                <div class="mt-2 mb-2 load-more hide"><a href="javascript:" class="btn btn-primary btn-block" id="loadMore">查看更多</a></div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let currentPage = 0;
        let pageSize = 5;
        let getQueryCondition = function (currentPage) {
            let param = {};
            param.taskName = $("#taskName").val();
            param.pageSize = pageSize;
            param.pageIndex = currentPage;
            param.taskKind = 2;
            return param;
        };
        $(function () {
            init(currentPage);
            $("#btnQuery").click(function () {
                $(".detail").empty();
                init(0);
            });
            $(".load-more").on('click', '#loadMore', function () {
                currentPage = currentPage + 1;
                init(currentPage);
            });
        });
        let init = function (currentPage) {
            layer.open({type: 2, content: '加载中'});
            $.ajax({
                type: "post",
                url: '/measuringroom/task/my_tasks',
                dataType: "json",
                contentType:'application/json',
                data: JSON.stringify(getQueryCondition(currentPage)),
                success: function (res) {
                    layer.closeAll();
                    if (res.data.length === 0) {
                        if (currentPage === 0) {
                            $(".list").removeClass('show').addClass('hide');
                            $(".splashBlock").removeClass('hide').addClass('show');
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                    }
                    else {
                        let str = "";
                        for (let i = 0; i < res.data.length; i++) {
                            let myTask = res.data[i];
                            let startDate =myTask.startTime;
                            let endDate = myTask.endTime;
                            let state = "";
                            let labels = "";
                            if (startDate > getDateNowFormat()) {
                                state = 1;
                                labels = '<span class="badge badge-outline-secondary pull-right">未开始</span>';
                            }
                            if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                                state = 2;
                                labels = '<span class="badge badge-outline-success pull-right">进行中</span>';
                            }
                            if (getDateNowFormat() >= endDate) {
                                state = 3;
                                labels = '<span class="badge badge-outline-danger pull-right">已结束</span>';
                            }
                            str += '<ul class="list-unstyled mt-2 pl-2 bg-light text-dark p-3" style="border-radius:10px; border:1px #efeef4 solid;">';
                            str += '<li class="pb-1 font-weight-bold">' + myTask.taskName + '' + labels + '</li>';
                            str += '<li class="pb-1">开始时间：' + startDate + '</li>';
                            str += '<li class="pb-1">结束时间：' + endDate + '</li>';
                            let taskType = "";
                            if (myTask.taskType === 1) taskType = "限定";
                            if (myTask.taskType === 2) taskType = "非限定";
                            str += '<li class="pb-1">任务类型：' + taskType+'</li>';
                            let entry = '<a href="/app/survey/survey_list?taskId=' +myTask.id + '&type='+myTask.taskType+'" class="btn btn-primary rounded btn-block font14">参与调查</a>';
                            if (state === 2)
                                str += ' <li class="pb15 text-center"> ' + entry + '</li>';
                            str += '</ul>';
                            if (i < res.data.length - 1) {
                                str += '<div class="divider dashed large mt-2 mb-2"></div>';
                            }
                        }
                        $(".detail").append(str);
                        $(".list").removeClass('hide').addClass('show');
                        if (res.data.length < pageSize) {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('hide').addClass('show');
                        }
                        $(".splashBlock").removeClass('show').addClass('hide');
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>
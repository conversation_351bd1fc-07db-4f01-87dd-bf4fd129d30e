<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            账号安全
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <img th:src="@{/static/images/app_pwd_banner.jpg}" alt="密码管理" class="img-fluid mb-3 mt-3">
            <form id="frmModifyPwd">
                <div class="form-group">
                    <label for="originalPwd" class="col-form-label">当前密码</label>
                    <input id="originalPwd" name="originalPwd" class="form-control col-sm-3" type="password">
                </div>
                <div class="form-group">
                    <label for="newPwd" class="col-form-label">新密码</label>
                    <input id="newPwd" name="newPwd" class="form-control col-sm-3" type="password" aria-describedby="pwdHelp">
                    <small id="pwdHelp" class="form-text text-muted">长度至少8位以上，由数字、大小写字母和特殊字符组成</small>
                </div>
                <div class="form-group">
                    <label for="confirm_newPwd" class="col-form-label">确认新密码</label>
                    <input id="confirm_newPwd" name="confirm_newPwd" class="form-control col-sm-3" type="password">
                </div>
                <input type="submit" id="btnSave" class="btn btn-primary btn-block" value="保存" />
                <a href="javascript:history.go(-1)" class="btn btn-outline-secondary btn-block text-muted">返回</a>
            </form>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMy}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        $(function () {
            $.validator.addMethod('check_password', function (value, element, param) {
                let strPwd = $('#newPwd').val();
                let regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[a-zA-Z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,}$/;
                return regex.test(strPwd);
            }, "密码不符合要求");
            $("#frmModifyPwd").validate({
                rules: {
                    originalPwd: {
                        required: true,
                        remote: {
                            type: "POST",
                            url: "/anteroom/user/verifypwd",
                            dataType: "json",
                            data: {
                                originalPwd: function () {
                                    return $("#originalPwd").val();
                                }
                            },
                            dataFilter: function (data, type) {
                                data = JSON.parse(data);
                                return data.resultCode === 200;
                            }
                        }
                    },
                    newPwd: {
                        required: true,
                        check_password: true
                    },
                    confirm_newPwd: {
                        required: true,
                        equalTo: "#newPwd"
                    }
                },
                messages: {
                    originalPwd: {
                        required: "请输入当前密码",
                        remote: "当前密码输入错误"
                    },
                    newPwd: {
                        required: "请输入新密码",
                        check_password: "密码不符合要求"
                    },
                    confirm_newPwd: {
                        required: "请输入确认密码", equalTo: "两次输入密码不一致"
                    }
                },
                submitHandler: function () {
                    $("#btnSave").val("保存中……");
                    $("#btnSave").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.newPwd = $("#newPwd").val();
                    $.post("/anteroom/user/modifypwd", jsonObj,
                        function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />设置成功，请妥善保管登录密码！' ,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function () {
                                        location.reload();
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }, 'json'
                    );
                }
            });
        });
    </script>
</th:block>
</body>
</html>
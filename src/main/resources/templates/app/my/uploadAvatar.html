<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/bootstrap-fileinput/css/fileinput.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            头像
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule">
        <div class="appContent">
            <div class="form-group mt-3 mb-3 text-center">
                <img id="avatar" src="" class="img-responsive rounded-circle" style="width:128px;" />
            </div>
            <div class="form-group form-inline mb-3">
                <label>选择头像文件：</label>
                <input type="file" name="file" id="txt_file" class="file-loading ml-1" />
                <input id="hidAvatar" type="hidden" value="" />
            </div>
            <div class="form-group mb-3">
                <button id="btnAvatar" class="btn btn-primary btn-block rounded" type="button">保存</button>
                <a href="javascript:history.go(-1)" class="btn btn-outline-secondary btn-block rounded text-muted">返回</a>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMy}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput.min.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/theme.js}"></script>
    <script th:src="@{/static/js/plugins/bootstrap-fileinput/js/fileinput-zh.js}"></script>
    <script type="text/javascript">
        let userId = '[[${user.userId}]]';
        let fileInput = function () {
            let oFile = {};
            oFile.Init = function (ctrlName) {
                let control = $('#' + ctrlName);
                //初始化上传控件的样式
                control.fileinput({
                    theme: "fa",
                    language: 'zh', //设置语言
                    uploadUrl: '/fileUpload/general?fileType=avatar',
                    allowedFileExtensions: ['png', 'jpg', 'jpeg', 'bmp', 'gif'], //接收的文件后缀
                    showCaption: false, //是否显示标题
                    showUpload: true,
                    browseClass: "btn btn-outline-warning", //按钮样式
                    dropZoneEnabled: false, //是否显示拖拽区域
                    maxFileCount: 1,
                    enctype: 'multipart/form-data',
                    validateInitialCount: true,
                    previewFileIcon: "<i class='fa fa-archive'></i>",
                    uploadLabel: "上传",
                    msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！"
                });
                //导入文件上传完成之后的事件
                $("#txt_file").on("fileuploaded", function (event, data, previewId, index) {
                    let res = data.response;
                    if (res.resultCode === 200) {
                        $("#hidAvatar").val(res.resultMsg);
                        $("#avatar").attr("src", res.resultMsg === "" ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + res.resultMsg);
                        layer.open({
                            content: '<img src="/static/images/success.png" width="25" class="mr-1"/>上传成功'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                    else {
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                });
            };
            return oFile;
        };
        $(function () {
            let headPic ='[[${user.headPic}]]' === '' ? '/static/images/user.png' : '/static/upload/avatar/thumbnail/[[${user.headPic}]]';
            $("#avatar").attr("src",headPic);
            let oFileInput = new fileInput();
            oFileInput.Init("txt_file");
            $("#btnAvatar").click(function () {
                let headPic = $("#hidAvatar").val();
                if (headPic === "") {
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>请先上传头像文件'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                    return;
                }
                $.post("/anteroom/user/avatar", { userId: userId, headPic: $("#hidAvatar").val() }, function (res) {
                    if (res.resultCode === 200) {
                        layer.open({
                            content: '<img src="/static/images/success.png" width="25" class="mr-1" />' + res.resultMsg,
                            btn: '确定',
                            shadeClose: false,
                            yes: function () {
                                location.reload();
                            }
                        });
                    }
                    else {
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                }, 'json');
            });
        });
    </script>
</th:block>
</body>
</html>
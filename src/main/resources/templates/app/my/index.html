<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="#" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">个人中心</div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule">
        <div class="appContent">
            <div class="card mt-1 mb-3">
                <div class="card-body bg-primary rounded">
                    <div class="itemList">
                        <div class="item">
                            <div class="image">
                                <a th:href="@{/app/my/upload_avatar}">
                                    <img th:if="${user.headPic eq null or user.headPic eq ''}" th:src="@{/static/images/user.png}" class="rounded-circle" style="width:70px;margin-top:7px" />
                                    <img th:unless="${user.headPic eq null or user.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${user.headPic}|" class="rounded-circle" style="width:70px;margin-top:7px" />
                                </a>
                            </div>
                            <div class="text pt-2">
                                <div class="font14 text-white">
                                    账号：<th:block th:text="${user.loginName}" /><br>
                                    姓名：<th:block th:text="${user.realName}" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="contentBox long mt-3 mb-3">
                <h5 class="title font16">个人信息</h5>
                <div class="contentBox-body">
                    <!-- listview -->
                    <div class="listView pl-1">
                        <a th:href="@{/app/my/edit_profile}" class="listItem">
                            <div class="image">
                                <div class="iconBox bg-info">
                                    <i class="fa fa-cog"></i>
                                </div>
                            </div>
                            <div class="text">
                                <div>
                                    <strong class="font16">修改资料</strong>
                                </div>
                            </div>
                        </a>
                        <a th:href="@{/app/my/reset_password}" class="listItem">
                            <div class="image">
                                <div class="iconBox bg-info">
                                    <i class="fa fa-key"></i>
                                </div>
                            </div>
                            <div class="text">
                                <div>
                                    <strong class="font16">密码管理</strong>
                                </div>
                            </div>
                        </a>
                    </div>
                    <!-- * listview -->
                </div>
            </div>
            <div class="contentBox long mb-3">
                <h5 class="title font16">我的测评</h5>
                <div class="contentBox-body">
                    <!-- listview -->
                    <div class="listView pl-1">
                        <a th:href="@{/app/measuring/my_tasks}" class="listItem">
                            <div class="image">
                                <div class="iconBox bg-warning">
                                    <i class="fa fa-list"></i>
                                </div>
                            </div>
                            <div class="text">
                                <div>
                                    <strong class="font16">测评任务</strong>
                                </div>
                            </div>
                        </a>
                        <a th:href="@{/app/measuring/my_records}" class="listItem">
                            <div class="image">
                                <div class="iconBox bg-warning">
                                    <i class="fa fa-file-text-o"></i>
                                </div>
                            </div>
                            <div class="text">
                                <div>
                                    <strong class="font16">测评记录</strong>
                                </div>
                            </div>
                        </a>
                    </div>
                    <!-- * listview -->
                </div>
            </div>
            <div class="contentBox long mb-3 hide">
                <h5 class="title font16">我的活动</h5>
                <div class="contentBox-body">
                    <div class="listView pl-1">
                        <a th:href="@{/app/activity/my_activities}" class="listItem">
                            <div class="image">
                                <div class="iconBox bg-danger">
                                    <i class="fa fa-list"></i>
                                </div>
                            </div>
                            <div class="text">
                                <div>
                                    <strong class="font16">活动记录</strong>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            <div class="contentBox long mb-3">
                <h5 class="title font16">我的咨询</h5>
                <div class="contentBox-body">
                    <!-- listview -->
                    <div class="listView pl-1">
                        <a th:href="@{/app/counseling/my_cases}" class="listItem">
                            <div class="image">
                                <div class="iconBox bg-success">
                                    <i class="fa fa-comment-o"></i>
                                </div>
                            </div>
                            <div class="text">
                                <div>
                                    <strong class="font16">我的个案</strong>
                                </div>
                            </div>
                        </a>
                    </div>
                    <!-- * listview -->
                </div>
            </div>
            <a href="#" id="logout" class="btn btn-primary btn-block rounded logout">退出平台</a>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMy}"/>
</th:block>
<th:block layout:fragment="common_js" />
</body>
</html>
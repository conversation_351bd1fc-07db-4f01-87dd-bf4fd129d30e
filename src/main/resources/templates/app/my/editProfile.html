<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            修改个人信息
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-2">
            <img th:src="@{/static/app/img/sample/draw-1.png}" alt="修改个人信息" class="img-fluid mb-3 mt-3">
            <form id="frmProfile" class="pl-lg-4 mb-5">
                <div class="form-group">
                    <label for="loginName" class="col-form-label">用户名</label>
                    <input id="loginName" name="loginName" class="form-control col-sm-6" type="text" aria-describedby="nameHelp" readonly>
                    <input id="hidLoginName" name="hidLoginName" type="hidden" value="" />
                </div>
                <div class="form-group">
                    <label for="realName" class="col-form-label">姓名</label>
                    <input id="realName" name="realName" class="form-control col-sm-6" type="text">
                </div>
                <div class="form-group form-inline">
                    <label for="male" class="col-form-label mr-2">性别</label>
                    <div class="custom-control custom-radio mr-2">
                        <input type="radio" id="male" name="sex" class="custom-control-input" value="男" checked>
                        <label class="custom-control-label" for="male">男</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="female" name="sex" class="custom-control-input" value="女">
                        <label class="custom-control-label" for="female">女</label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-form-label">出生日期</label>
                    <div class="input-group">
                        <input type="date" id="birth" name="birth" class="form-control col-sm-6" >
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-form-label">民族</label>
                    <div class="col-sm-6 p-0">
                        <select class="form-control select2" data-toggle="select2" name="nation" id="nation">
                            <option value="">请选择</option>
                            <option value="汉族">汉族</option>
                            <option value="满族">满族</option>
                            <option value="回族">回族</option>
                            <option value="蒙古族">蒙古族</option>
                            <option value="壮族">壮族</option>
                            <option value="藏族">藏族</option>
                            <option value="阿昌族">阿昌族</option>
                            <option value="白族">白族</option>
                            <option value="保安族">保安族</option>
                            <option value="布朗族">布朗族</option>
                            <option value="布依族">布依族</option>
                            <option value="朝鲜族">朝鲜族</option>
                            <option value="达斡尔族">达斡尔族</option>
                            <option value="傣族">傣族</option>
                            <option value="德昂族">德昂族</option>
                            <option value="东乡族">东乡族</option>
                            <option value="侗族">侗族</option>
                            <option value="独龙族">独龙族</option>
                            <option value="俄罗斯族">俄罗斯族</option>
                            <option value="鄂伦春族">鄂伦春族</option>
                            <option value="鄂温克族">鄂温克族</option>
                            <option value="高山族">高山族</option>
                            <option value="哈尼族">哈尼族</option>
                            <option value="哈萨克族">哈萨克族</option>
                            <option value="赫哲族">赫哲族</option>
                            <option value="基诺族">基诺族</option>
                            <option value="京族">京族</option>
                            <option value="景颇族">景颇族</option>
                            <option value="柯尔克孜族">柯尔克孜族</option>
                            <option value="拉祜族">拉祜族</option>
                            <option value="黎族">黎族</option>
                            <option value="傈僳族">傈僳族</option>
                            <option value="毛南族">毛南族</option>
                            <option value="门巴族">门巴族</option>
                            <option value="苗族">苗族</option>
                            <option value="纳西族">纳西族</option>
                            <option value="怒族">怒族</option>
                            <option value="普米族">普米族</option>
                            <option value="羌族">羌族</option>
                            <option value="撒拉族">撒拉族</option>
                            <option value="水族">水族</option>
                            <option value="塔吉克族">塔吉克族</option>
                            <option value="塔塔尔族">塔塔尔族</option>
                            <option value="土家族">土家族</option>
                            <option value="土族">土族</option>
                            <option value="维吾尔族">维吾尔族</option>
                            <option value="乌孜别克族">乌孜别克族</option>
                            <option value="锡伯族">锡伯族</option>
                            <option value="瑶族">瑶族</option>
                            <option value="彝族">彝族</option>
                            <option value="裕固族">裕固族</option>
                            <option value="仡佬族">仡佬族</option>
                            <option value="仫佬族">仫佬族</option>
                            <option value="佤族">佤族</option>
                            <option value="珞巴族">珞巴族</option>
                            <option value="畲族">畲族</option>
                            <option value="其他少数民族">其他少数民族</option>
                        </select>
                    </div>

                </div>
                <th:block th:if="${pageData.sysConfigDto.isSmsEnabled eq 0 and pageData.sysConfigDto.smsConfig.isMobileBind eq 0}">
                    <div class="form-group">
                        <label for="mobile" class="col-form-label">联系电话</label>
                        <input id="mobile" name="mobile" class="form-control col-sm-6" type="text">
                    </div>
                </th:block>
                <div class="form-group">
                    <label for="emergency_contact_person" class="col-form-label">紧急联系人</label>
                    <input id="emergency_contact_person" name="emergency_contact_person" class="form-control col-sm-6" type="text" autocomplete="off">
                </div>
                <div class="form-group">
                    <label for="emergency_contact_mobile" class="col-form-label">紧急联系电话</label>
                    <input id="emergency_contact_mobile" name="emergency_contact_mobile" class="form-control col-sm-6" type="text" autocomplete="off" maxlength="11">
                </div>
                <div class="form-group">
                    <label for="email" class="col-form-label">邮箱</label>
                    <input id="email" name="email" class="form-control col-sm-6" type="email">
                </div>
                <div class="form-group">
                    <label for="idcardNo" class="col-form-label">身份证号</label>
                    <input id="idcardNo" name="idcardNo" class="form-control col-sm-6" type="text" maxlength="18">
                </div>
                <div class="form-group">
                    <label for="native" class="col-form-label">籍贯</label>
                    <input id="native" name="native" class="form-control col-sm-6" type="text">
                </div>
                <div class="form-group">
                    <label class="col-form-label">地址</label>
                    <div class="col-sm-6 p-0">
                        <div data-toggle="distpicker">
                            <select class="form-control select2 mb-1" id="address_province" data-toggle="select2"></select>
                            <select class="form-control select2 mb-1" id="address_city" data-toggle="select2"></select>
                            <select class="form-control select2 mb-1" id="address_dist" data-toggle="select2"></select>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="address_detail" class="col-form-label">详细地址</label>
                    <input id="address_detail" name="address_detail" class="form-control col-sm-6" type="text">
                </div>
                <div class="form-group">
                    <label for="education" class="col-form-label">文化程度</label>
                    <div class="col-sm-6 p-0">
                        <select class="form-control select2" data-toggle="select2" id="education" name="education">
                            <option value="">请选择</option>
                            <option value="研究生及以上">研究生及以上</option>
                            <option value="大学本科">大学本科</option>
                            <option value="大学专科">大学专科</option>
                            <option value="高中/中专">高中/中专</option>
                            <option value="初中及以下">初中及以下</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="job" class="col-form-label">职业</label>
                    <div class="col-sm-6 p-0">
                        <select class="form-control select2" data-toggle="select2" id="job" name="job">
                            <option value="">请选择</option>
                            <option value="市场/销售/商务">市场/销售/商务</option>
                            <option value="采购">采购</option>
                            <option value="行政">行政</option>
                            <option value="人力">人力</option>
                            <option value="产品/运营人员">产品/运营人员</option>
                            <option value="个体经营者">个体经营者</option>
                            <option value="财务/会计/出纳/审计">财务/会计/出纳/审计</option>
                            <option value="企业管理者">企业管理者</option>
                            <option value="律师/法务">律师/法务</option>
                            <option value="设计从业者">设计从业者</option>
                            <option value="服务业人员">服务业人员</option>
                            <option value="技术开发/工程师">技术开发/工程师</option>
                            <option value="农林牧渔劳动者">农林牧渔劳动者</option>
                            <option value="工人劳动者">工人劳动者</option>
                            <option value="全职家庭主妇/夫">全职家庭主妇/夫</option>
                            <option value="自由职业">自由职业</option>
                            <option value="离休/退休">离休/退休</option>
                            <option value="学生">学生</option>
                            <option value="老师">老师</option>
                            <option value="医护人员">医护人员</option>
                            <option value="科研人员">科研人员</option>
                            <option value="党政机关人员">党政机关人员</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="job" class="col-form-label">宗教信仰</label>
                    <div class="col-sm-6 p-0">
                        <select class="form-control" id="religion" name="religion">
                            <option value="">请选择</option>
                            <option value="佛教">佛教</option>
                            <option value="基督教">基督教</option>
                            <option value="伊斯兰教">伊斯兰教</option>
                            <option value="天主教">天主教</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="job" class="col-form-label">婚姻状况</label>
                    <div class="col-sm-6 p-0">
                        <select class="form-control" id="marriage" name="marriage">
                            <option value="">请选择</option>
                            <option value="未婚">未婚</option>
                            <option value="已婚">已婚</option>
                            <option value="丧偶">丧偶</option>
                            <option value="离婚">离婚</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-form-label">个人说明</label>
                    <textarea id="description" data-toggle="maxlength" class="form-control col-sm-6" rows="3" maxlength="100" placeholder="限制100字以内"></textarea>
                </div>
                <div class="form-group be-good-at hide">
                    <label class="col-form-label">咨询方向</label>
                    <textarea id="beGoodAt" name="beGoodAt" class="form-control col-sm-6" rows="2" maxlength="50" placeholder="填写咨询方向、擅长领域……"></textarea>
                </div>
                <input type="submit" id="btnSave" class="btn btn-primary btn-block rounded" value="保存" />
                <a href="javascript:history.go(-1)" class="btn btn-outline-secondary btn-block text-muted rounded">返回</a>
                <input id="hidStructParentID" name="hidStructParentID" value="0" type="hidden" />
                <input id="hidIsRecommend" type="hidden" value="0" />
            </form>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMy}" />
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/DatePicker/WdatePicker.js}"></script>
    <script th:src="@{/static/js/plugins/distpicker/distpicker.data.min.js}"></script>
    <script th:src="@{/static/js/plugins/distpicker/distpicker.min.js}"></script>
    <script type="text/javascript">
        let userId = '[[${user.userId}]]';
        let initForm = function () {
            $.post("/anteroom/user/get?id=" + userId, '', function (data) {
                let res = JSON.parse(data);
                $("#hidStructParentID").val(res.structId);
                $("#loginName").val(res.loginName);
                $("#hidLoginName").val(res.loginName);
                $("#realName").val(res.realName);
                $("#hidUserID").val(userId);
                if (res.sex === "男") {
                    $("#male").attr("checked", true);
                }
                else {
                    $("#female").attr("checked", true);
                }
                $("#birth").val(res.birth.substring(0, 10));
                $("#nation").val(res.nation).trigger('change');
                if ([[${pageData.sysConfigDto.isSmsEnabled}]] === 0) {
                    $("#mobile").val(res.mobile);
                }
                $("#emergency_contact_person").val(res.emergencyContactPerson);
                $("#emergency_contact_mobile").val(res.emergencyContactMobile);
                $("#email").val(res.email);
                $("#idcardNo").val(res.iDCardNo);
                $("#native").val(res.nativePlace);
                $("#education").val(res.education);
                $("#marriage").val(res.marriage);
                $("#job").val(res.job);
                $("#religion").val(res.religion);
                $("#description").val(res.description);
                $("#address_province").val(res.addressProvince).trigger('change');
                $("#address_city").val(res.addressCity).trigger('change');
                $("#address_dist").val(res.addressDist);
                $("#address_detail").val(res.addressDetail);
                $("#avatar").attr("src", res.headPic === "" ? "/static/images/user.png" : "/static/upload/avatar/" + res.headPic);
                let currentUserRoleId = [[${user.role.roleId}]];
                if (currentUserRoleId !== 1 && currentUserRoleId !== 3 && currentUserRoleId !== 4) {
                    $("#hidIsRecommend").val(res.counselorInfo.isRecommend);
                    $("#beGoodAt").val(res.counselorInfo.beGoodAt);
                }
                else {
                    $(".be-good-at").hide();
                }
            });
        };
        $(function () {
            initForm();
            $("#frmProfile").validate({
                ignore: "",
                errorPlacement: function (error, element) {
                    wrap = element.parent();
                    if (wrap.hasClass('input-group')) {
                        error.insertAfter(wrap);
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    loginName: {required: true},
                    realName: { required: true },
                    birth: { required: true }
                },
                messages: {
                    loginName: { required: "请输入登录名"},
                    realName: { required: "请输入姓名" },
                    birth: { required: "请选择出生日期" }
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.userId = userId;
                    jsonObj.loginName = $.trim($("#loginName").val());
                    jsonObj.structId = $("#hidStructParentID").val();
                    jsonObj.roleId = [[${user.role.roleId}]];
                    jsonObj.realName = $.trim($("#realName").val());
                    jsonObj.sex = $('input[name="sex"]:checked').val();
                    jsonObj.birth = $("#birth").val();
                    jsonObj.nation = $("#nation").val();
                    jsonObj.mobile = [[${pageData.sysConfigDto.isSmsEnabled}]] === 0 ? $("#mobile").val() : $("#hidMobile").val();
                    jsonObj.emergencyContactPerson = $("#emergency_contact_person").val();
                    jsonObj.emergencyContactMobile = $("#emergency_contact_mobile").val();
                    jsonObj.email = $("#email").val();
                    jsonObj.iDCardNo = $("#idcardNo").val();
                    jsonObj.nativePlace = $("#native").val();
                    jsonObj.education = $("#education").val();
                    jsonObj.marriage = $("#marriage").val();
                    jsonObj.job = $("#job").val();
                    jsonObj.religion = $("#religion").val();
                    jsonObj.addressProvince = $("#address_province").val();
                    jsonObj.addressCity = $("#address_city").val();
                    jsonObj.addressDist = $("#address_dist").val();
                    jsonObj.addressDetail = $("#address_detail").val();
                    jsonObj.description = $("#description").val();
                    let counselorInfo = {};
                    counselorInfo.userId = userId;
                    counselorInfo.isRecommend = $("#hidIsRecommend").val();
                    counselorInfo.beGoodAt = $("#beGoodAt").val();
                    jsonObj.counselorInfo = counselorInfo;
                    $("#btnSave").val("请稍后…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: "post",
                        async: false,
                        url: "/anteroom/user/update",
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />' + res.resultMsg,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function () {
                                        location.reload();
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
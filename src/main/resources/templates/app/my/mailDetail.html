<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            消息中心
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-2">
            <div class="appContent">
                <!-- title -->
                <h5 class="mt-2 mb-2" th:text="${mail.msgTitle}">
                </h5>
                <!-- * title -->
                <!-- post header -->
                <div class="postHeader mb-2">
                    <div>
                        <th:block th:if="${mail.fromUser eq 1}" th:text="系统消息" />
                        <th:block th:unless="${mail.fromUser eq 1}" th:text="${mail.fromName}" />
                    </div>
                    <div th:text="${#dates.format(mail.sendDate,'yyyy-MM-dd')}">
                    </div>
                </div>
                <!-- * post header-->
                <!-- post body -->
                <div class="postBody">
                    <p class="font14" th:utext="${mail.msgContent}">
                    </p>
                </div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMy}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let id = getUrlParam("id");
        $(function () {
            let jsonObj = {};
            jsonObj.ids = id;
            $.post("/anteroom/mail/update_read_state", jsonObj, function (res) {

            }, 'json');
        });
    </script>
</th:block>
</body>
</html>
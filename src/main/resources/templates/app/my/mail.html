<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            消息中心
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <th:block th:if="${not #lists.isEmpty(mails) and mails.size() gt 0}">
                <div class="mt-2 mb-3">
                    <img th:src="@{/static/images/app_message_banner.jpg}" alt="image" class="imageBlock img-fluid rounded">
                </div>
                <th:block th:each="mail:${mails}">
                    <a th:href="@{/app/my/mail_detail(id=${mail.id})}">
                        <div class="sectionTitle pl-2 pr-1 pt-2">
                            <div class="title">
                                <th:block th:if="${mail.isRead ==0}">
                                    <span class="text-dark" style="font-size:16px;"><i class="fa fa-envelope-o mr-1 text-warning"></i><th:block th:text="${mail.msgTitle}" /></span>
                                </th:block>
                                <th:block th:unless="${mail.isRead ==0}">
                                    <span class="text-muted" style="font-size:16px;"><i class="fa fa-envelope-open-o mr-1"></i><th:block th:text="${mail.msgTitle}" /></span>
                                </th:block>
                                <i class="fa fa-angle-right text-muted"></i>
                            </div>
                        </div>
                    </a>
                    <div class="divider mt-2 mb-1"></div>
                </th:block>
            </th:block>
            <th:block th:unless="${not #lists.isEmpty(mails) and mails.size() gt 0}">
                <img th:src="@{/static/images/nodata.png}" alt="" class="img-fluid mb-3 mt-3">
                <div class="text-center"><i class="fa fa-info-circle mr-1"></i>暂无消息</div>
            </th:block>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMy}"/>
</th:block>
<th:block layout:fragment="common_js" />
</body>
</html>
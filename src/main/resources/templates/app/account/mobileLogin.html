<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutApp.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle" th:text="${pageData.sysConfigDto.platformName}"></div>
    </div>
    <div id="appCapsule">
        <div class="appContent">
            <div class="sectionTitle text-center mt-3">
                <div class="title mb-2">
                    <div class="lead font18 mb-2 mt-2">请登录</div>
                </div>
            </div>
            <form id="frmSmsLogin">
                <div class="form-group mb-3">
                    <input id="mobile" name="mobile" class="form-control" type="text" placeholder="输入手机号码" value="" autocomplete="off" maxlength="11">
                </div>
                <div class="form-group mb-3">
                    <div class="input-group">
                        <input id="verifyCode" name="verifyCode" type="text" class="form-control" placeholder="输入短信验证码" autocomplete="off">
                        <div class="input-group-append">
                            <input id="btnSendSms" class="btn btn-light" type="button" value="获取验证码" style="height: calc(1.5em + .75rem + 4px);border-top-right-radius: 5px; border-bottom-right-radius: 5px;" />
                            <input id="hidVerifyCode" type="hidden" value="0" />
                        </div>
                    </div>
                </div>
                <div>
                    <input id="signIn" class="btn btn-primary btn-block" type="submit" value="登录">
                </div>
            </form>
            <div class="mt-3 text-center mb-2"><a href="#" onclick="accountLogin()" class="text-muted text-decoration-underline">账号密码登录</a></div>
            <div class="appBottomMenu text-muted text-center border-top-0">&copy; Psy-Cloud</div>
        </div>
    </div>
    <input id="hidSignName" type="hidden" th:value="${pageData.sysConfigDto.smsConfig.signName}" />
    <input id="hidTemplateCode" type="hidden" th:value="${pageData.sysConfigDto.smsConfig.loginTemplate}" />
    <input id="hidMobileFlag" type="hidden" th:value="0">
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/jsencrypt.min.js}"></script>
    <script type="text/javascript">
        let interValObj; //timer变量，控制时间
        let count = 120; //间隔函数，1秒执行
        let curCount;//当前剩余秒数
        let obj = $("#btnSendSms");
        let sendSms = function (sendObj) {
            curCount = count;
            //设置button效果，开始计时
            obj.attr("disabled", "true");
            obj.val("重新发送（" + curCount + "）秒");
            interValObj = window.setInterval(countDown, 1000); //启动计时器，1秒执行一次
            $.ajax({
                type: 'POST',
                url: '/sms/send',
                data: JSON.stringify(sendObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    if (res.resultCode === 200) {
                        layer.open({
                            content: '<img src="/static/images/success.png" width="25" class="mr-1"/>'+res.resultMsg+''
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 3
                        });
                    }
                    else{
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>短信发送失败！'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 3
                        });
                    }
                }
            });
        }
        let accountLogin = function () {
            let returnUrl = getUrlParam('returnUrl');
            location.href = returnUrl == undefined ? "/app/account/login" : "/app/account/login?returnUrl=" + encodeURIComponent(returnUrl);
        };
        //timer处理函数
        let countDown = function () {
            if (curCount === 0) {
                window.clearInterval(interValObj);//停止计时器
                obj.removeAttr("disabled");//启用按钮
                obj.val("重新发送");
            }
            else {
                curCount--;
                obj.val("重新发送（" + curCount + "）秒");
            }
        }
        /**
         * 对密码进行加密处理
         * @param password 原始密码
         * @returns 加密后的密码
         */
        let encryptData = function(password) {
            const publicKey = getPublicKey();
            return RSAEncrypt(password, publicKey);
        }
        /**
         * 从服务器获取RSA公钥
         * @returns 公钥字符串
         */
        let getPublicKey = function() {
            let publicKey = '';
            $.ajax({
                url: '/account/getPublicKey',
                type: 'GET',
                contentType:'application/json',
                dataType: "json",
                async: false,
                success: function(res) {
                    if(res.resultCode === 200){
                        publicKey = res.data;
                    }
                    else{
                        layer.msg('登录失败，请重试！', { icon: 2, time: 3000 });
                    }
                }
            });
            return publicKey;
        }
        /**
         * 使用RSA公钥加密数据
         * @param password 要加密的密码
         * @param publicKey RSA公钥
         * @returns 加密后的密文
         */
        let RSAEncrypt = function(password, publicKey) {
            let encrypt = new JSEncrypt();
            encrypt.setPublicKey(publicKey);
            return encrypt.encrypt(password);
        }
        $(function () {
            $("#btnSendSms").click(function () {
                if ($("#mobile").val() === '') {
                    layer.open({
                        content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>请填写手机号码！'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 3
                    });
                    return;
                }
                if($("#hidMobileFlag").val() ==='0'){
                    return;
                }
                let jsonObj = {};
                jsonObj.phoneNumber = encryptData($("#mobile").val());
                jsonObj.signName = $("#hidSignName").val();
                jsonObj.templateCode = $("#hidTemplateCode").val();
                jsonObj.smsType = 1;
                obj = $("#btnSendSms");
                sendSms(jsonObj);
            });
            $.validator.addMethod('checkMobile', function (value, element, param) {
                let phomeNumber = $(element).val();
                let mobileReg =/^(((13[0-9]{1})|(14[0-9]{1})|(17[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(19[0-9]{1})|(18[0-9]{1}))+\d{8})$/;
                return mobileReg.test(phomeNumber);
            }, "请输入正确格式的手机号码");
            $("#frmSmsLogin").validate({
                ignore: [],
                errorPlacement: function (error, element) {
                    wrap = element.parent();
                    if (wrap.hasClass('input-group')) {
                        error.insertAfter(wrap);
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    mobile: {
                        required: true,
                        checkMobile: true,
                        remote: {
                            type: "POST",
                            url: "/account/check_mobile",
                            dataType: "json",
                            data: {
                                mobile: function () {
                                    return $.trim($("#mobile").val());
                                }
                            },
                            dataFilter: function (data, type) {
                                var res = JSON.parse(data);
                                $("#hidMobileFlag").val(res.resultCode ===200 ? 0 : 1);
                                return res.resultCode !== 200;
                            }
                        }
                    },
                    verifyCode: {
                        required: true,
                        remote:{
                            type: "POST",
                            url: "/sms/verify",
                            dataType: "json",
                            data: {
                                phoneNumber: function () {
                                    return $.trim($("#mobile").val());
                                },
                                templateCode: function () {
                                    return $("#hidTemplateCode").val();
                                },
                                smsCode: function () {
                                    return $("#verifyCode").val();
                                }
                            },
                            dataFilter: function (data, type) {
                                var res = JSON.parse(data);
                                return res.resultCode === 200;
                            }
                        }
                    }
                },
                messages: {
                    mobile: {
                        required: "请填写手机号码",
                        checkMobile: "请输入正确格式的手机号码",
                        remote: "手机号码未绑定"
                    },
                    verifyCode: {
                        required: "请填写短信验证码",
                        remote: "短信验证码错误" }
                },
                submitHandler: function () {
                    smsLogin();
                }
            });
        });
        function smsLogin() {
            let jsonObj = {};
            jsonObj.mobile = $.trim($("#mobile").val());
            layer.open({type: 2, content: '请稍后…', shadeClose: false});
            $.ajax({
                type: 'POST',
                url: '/account/mobileLogin',
                data: jsonObj,
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    if (res.resultCode === 100) {
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                    else {
                        let returnUrl = getUrlParam('returnUrl');
                        location.href = returnUrl == undefined ? "/app/home/<USER>" : decodeURIComponent(returnUrl);
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>
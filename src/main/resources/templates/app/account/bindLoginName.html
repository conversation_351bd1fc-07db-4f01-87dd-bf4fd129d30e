<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutApp.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="pageTitle">
            绑定账号
        </div>
    </div>
    <!-- * App Header -->

    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent">
            <div class="sectionTitle text-center mt-3">
                <div class="title mb-2">
                    <div class="lead font18 mb-2 mt-2">绑定系统账号信息</div>
                </div>
            </div>
            <form id="frmBind">
                <div class="form-group mb-2">
                    <input id="loginName" class="form-control" name="loginName" type="text" autocomplete="off" placeholder="填写工号（用户名）" />
                </div>
                <div class="form-group mb-2">
                    <input id="realName" class="form-control" name="realName" type="text" placeholder="填写姓名" />
                </div>
                <div>
                    <button id="btnBind" class="btn btn-primary btn-block" type="submit">绑定</button>
                </div>
            </form>
        </div>
    </div>
    <!-- * App Capsule -->
</th:block>

<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        let redirectUrl = getUrlParam("returnUrl");
        $(function () {
            $("#frmBind").validate({
                rules: {
                    loginName: { required: true },
                    realName: { required: true }
                },
                messages: {
                    loginName: { required: "请输入工号" },
                    realName: { required: "请输入姓名"}
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.loginName = $("#loginName").val();
                    jsonObj.realName = $("#realName").val();
                    
                    $.post("/account/bind_loginname", jsonObj, function (res) {
                        if (res.resultCode === 200) {
                            layer.open({
                                content: '<img src="/static/images/success.png" width="25" class="mr-1"/>绑定成功！'
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2000
                            });
                            setTimeout(function() {
                                window.location.href = decodeURIComponent(redirectUrl);
                            }, 2000);
                        } else {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2000
                            });
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html> 
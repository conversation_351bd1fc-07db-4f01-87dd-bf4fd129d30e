<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutApp.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        /* 登录页面专用样式 */
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #6f42c1 0%, #5a359a 50%, #4c2a85 100%);
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .login-header {
            position: relative;
            z-index: 2;
            padding: 2rem 1.5rem 1rem;
            text-align: center;
        }
        
        .login-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
        }
        
        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .login-logo i {
            font-size: 2.5rem;
            color: white;
        }
        
        .login-title {
            color: white;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .login-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            font-weight: 400;
        }
        
        .login-form-container {
            position: relative;
            z-index: 2;
            padding: 0 1.5rem 2rem;
        }
        
        .login-form-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: formSlideUp 0.8s ease-out;
        }
        
        @keyframes formSlideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(111, 66, 193, 0.2);
            border-radius: 12px;
            padding: 1rem 1rem 1rem 3rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .form-control:focus {
            background: white;
            border-color: #6f42c1;
            box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
            transform: translateY(-2px);
        }
        
        .form-control::placeholder {
            color: #9ca3af;
        }
        
        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6f42c1;
            font-size: 1.2rem;
            z-index: 3;
        }
        
        .remember-forgot-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .custom-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .custom-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #6f42c1;
        }
        
        .custom-checkbox label {
            color: #6b7280;
            font-size: 0.9rem;
            cursor: pointer;
        }
        
        .login-btn {
            background: linear-gradient(135deg, #6f42c1 0%, #5a359a 100%);
            border: none;
            border-radius: 12px;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .login-btn:hover::before {
            left: 100%;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(111, 66, 193, 0.3);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .divider {
            margin: 2rem 0;
            text-align: center;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(0, 0, 0, 0.1);
        }
        
        .divider-text {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 1rem;
            color: #6b7280;
            font-size: 0.9rem;
            position: relative;
            z-index: 1;
        }
        
        .social-login {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .social-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid rgba(111, 66, 193, 0.2);
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .social-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(111, 66, 193, 0.2);
            border-color: #6f42c1;
        }
        
        .social-btn img {
            width: 24px;
            height: 24px;
            transition: transform 0.3s ease;
        }
        
        .social-btn:hover img {
            transform: scale(1.1);
        }
        
        .footer-text {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-top: 1rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-header {
                padding: 1.5rem 1rem 1rem;
            }
            
            .login-logo {
                width: 70px;
                height: 70px;
            }
            
            .login-logo i {
                font-size: 2rem;
            }
            
            .login-title {
                font-size: 1.6rem;
            }
            
            .login-form-container {
                padding: 0 1rem 1.5rem;
            }
            
            .login-form-card {
                padding: 1.5rem 1rem;
            }
            
            .form-control {
                padding: 0.875rem 0.875rem 0.875rem 2.5rem;
            }
            
            .input-icon {
                left: 0.875rem;
                font-size: 1.1rem;
            }
        }
        
        /* 加载动画 */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }
        
        .btn-loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 浮动动画元素 */
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 40%;
            left: 80%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <div class="login-container">
        <!-- 浮动背景元素 -->
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <!-- 登录头部 -->
        <div class="login-header">
            <div class="login-logo">
                <i class="fa fa-user-circle"></i>
            </div>
            <h1 class="login-title" th:text="${pageData.sysConfigDto.platformName}">心理平台</h1>
            <p class="login-subtitle">欢迎回来，请登录您的账户</p>
        </div>
        
        <!-- 登录表单 -->
        <div class="login-form-container">
            <div class="login-form-card">
                <form>
                    <div class="form-group">
                        <i class="fa fa-user input-icon"></i>
                        <input id="loginName" class="form-control" name="loginName" type="text" autocomplete="off" placeholder="请输入账号">
                    </div>
                    <div class="form-group">
                        <i class="fa fa-lock input-icon"></i>
                        <input id="password" class="form-control" name="password" type="password" placeholder="请输入密码">
                    </div>
                    
                    <div class="remember-forgot-row">
                        <div class="custom-checkbox">
                            <input type="checkbox" id="isRemember" />
                            <label for="isRemember">下次自动登录</label>
                        </div>
                    </div>
                    
                    <button id="signIn" class="login-btn" type="button">
                        登录
                    </button>
                </form>
                
                <div class="divider">
                    <span class="divider-text">— 使用其它方式登录 —</span>
                </div>
                
                <div class="social-login">
                    <th:block th:if="${pageData.sysConfigDto.isSmsEnabled eq 1 and pageData.sysConfigDto.smsConfig.isLogin eq 1}">
                        <div class="social-btn" onclick="mobileLogin()">
                            <img th:src="@{/static/images/app/mobile.png}" alt="手机登录">
                        </div>
                    </th:block>
                    <a href="/app/account/wechat_login" class="social-btn">
                        <img th:src="@{/static/images/app/wechat.png}" alt="微信登录">
                    </a>
                </div>
            </div>
        </div>
        
        <div class="footer-text">&copy;壹点灵</div>
    </div>
    
    <!-- Modal -->
    <div id="modalPwd" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index: 99999;">
        <div class="modal-dialog modal-dialog-scrollable modal-sm modal-dialog-centered text-dark">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title font16 font-weight-bold"><i class="fa fa-key mr-1"></i>密码修改</div>
                </div>
                <div class="modal-body p-3">
                    <form id="frmModifyPwd">
                        <div class="form-group">
                            <label for="originalPwd" class="col-form-label">当前密码</label>
                            <input id="originalPwd" name="originalPwd" class="form-control col-sm-3" type="password">
                        </div>
                        <div class="form-group">
                            <label for="newPwd" class="col-form-label">新密码</label>
                            <input id="newPwd" name="newPwd" class="form-control col-sm-3" type="password" aria-describedby="pwdHelp">
                            <small id="pwdHelp" class="form-text text-muted">长度至少8位以上，由数字、大小写字母和特殊字符组成</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm_newPwd" class="col-form-label">确认新密码</label>
                            <input id="confirm_newPwd" name="confirm_newPwd" class="form-control col-sm-3" type="password">
                        </div>
                        <input type="submit" id="btnSave" class="btn btn-primary btn-block" value="确定" />
                        <input type="hidden" id="hidUid" value="0">
                    </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/jsencrypt.min.js}"></script>
    <script type="text/javascript">
        $(function () {
            $("#signIn").click(function () {
                signIn();
            });
            $.validator.addMethod('check_password', function (value, element, param) {
                let strPwd = $('#newPwd').val();
                let regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[a-zA-Z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,}$/;
                return regex.test(strPwd);
            }, "密码不符合要求");
            $("#frmModifyPwd").validate({
                rules: {
                    originalPwd: {
                        required: true,
                        remote: {
                            type: "POST",
                            url: "/account/verify_pwd_by_userId",
                            dataType: "json",
                            data: {
                                originalPwd: function () {
                                    return $("#originalPwd").val();
                                },
                                userId:function(){
                                    return $("#hidUid").val()
                                }
                            },
                            dataFilter: function (data, type) {
                                data = JSON.parse(data);
                                return data.resultCode === 200;
                            }
                        }
                    },
                    newPwd: {
                        required: true,
                        check_password: true
                    },
                    confirm_newPwd: {
                        required: true,
                        equalTo: "#newPwd"
                    }
                },
                messages: {
                    originalPwd: {
                        required: "请输入当前密码",
                        remote: "当前密码输入错误"
                    },
                    newPwd: {
                        required: "请输入新密码",
                        check_password: "密码不符合要求"
                    },
                    confirm_newPwd: {
                        required: "请输入确认密码", equalTo: "两次输入密码不一致"
                    }
                },
                submitHandler: function () {
                    $("#btnSave").val("保存中……");
                    $("#btnSave").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.originalPwd = $("#originalPwd").val();
                    jsonObj.newPwd = $("#newPwd").val();
                    jsonObj.userId= $("#hidUid").val();
                    $.post("/account/modifypwd", jsonObj,
                        function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />设置成功，请妥善保管登录密码！' ,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function () {
                                        location.reload();
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }, 'json'
                    );
                }
            });
        });
        function mobileLogin() {
            let redirectUri = getUrlParam('returnUrl') == undefined ? '/app/home/<USER>' : getUrlParam('returnUrl');
            window.location.href = "/app/account/mobile_login?returnUrl=" + encodeURIComponent(redirectUri);
        };
        function checkForm() {
            let loginName = $("#loginName").val();
            let password = $("#password").val();
            if (loginName === "") {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>请输入用户名！'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                });
                return false;
            }
            else if (password === "") {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>请输入密码！'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                });
                return false;
            }
            else
                return true;
        };
        function signIn() {
            if (checkForm()) {
                let jsonObj = {};
                jsonObj.loginName = $("#loginName").val();
                jsonObj.pwd = encryptPassword($("#password").val());
                jsonObj.isRemember = $("#isRemember").prop("checked") ? 1 : 0;
                layer.open({
                    type: 2
                    , content: '登录中…',
                    shadeClose: false
                });
                $.ajax({
                    type: 'POST',
                    url: '/account/login',
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        layer.closeAll();
                        if (res.resultCode === 100) {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                        }
                        else if(res.resultCode === 101){ //默认密码
                            layer.open({
                                content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>' + res.resultMsg +''
                                ,btn:['前往修改','暂不修改']
                                , style: 'background-color:#ffffff; border:none;'
                                , shadeClose: false
                                , time: 0
                                ,no:function(index){
                                    let returnUrl = getUrlParam('returnUrl');
                                    location.href = returnUrl == undefined ? "/app/home/<USER>" : decodeURIComponent(returnUrl);
                                }
                                ,yes:function(index){
                                    location.href = '/app/my/reset_password';
                                }
                            });
                        }
                        else if(res.resultCode === 108){
                            layer.open({
                                content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>' + res.resultMsg +''
                                ,btn:['前往修改']
                                , style: 'background-color:#ffffff; border:none;'
                                , shadeClose: false
                                , time: 0
                                ,yes:function(index){
                                    layer.close(index);
                                    $("#hidUid").val(res.data);
                                    $("#modalPwd").modal();
                                }
                            });
                        }
                        else if(res.resultCode === 110){
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>登录失败，还剩余'+res.data+'次登录机会。'
                                ,btn:['确定']
                                , style: 'background-color:#ffffff; border:none;'
                                , shadeClose: false
                                , time: 0
                                ,yes:function(index){
                                    location.reload();
                                }
                            });
                        }
                        else if(res.resultCode === 109){
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1" />'+res.resultMsg
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 3
                            });
                        }
                        else {
                            let returnUrl = getUrlParam('returnUrl');
                            location.href = returnUrl == undefined ? "/app/home/<USER>" : decodeURIComponent(returnUrl);
                        }
                    }
                });
            }
        }
        /**
         * 对密码进行加密处理
         * @param password 原始密码
         * @returns 加密后的密码
         */
        function encryptPassword(password) {
            const publicKey = getPublicKey();
            return RSAEncrypt(password, publicKey);
        }
        /**
         * 从服务器获取RSA公钥
         * @returns 公钥字符串
         */
        function getPublicKey() {
            let publicKey = '';
            $.ajax({
                url: '/account/getPublicKey',
                type: 'GET',
                contentType:'application/json',
                dataType: "json",
                async: false,
                success: function(res) {
                    if(res.resultCode === 200){
                        publicKey = res.data;
                    }
                    else{
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1" />登录失败，请重试！'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 3
                        });
                    }
                }
            });
            return publicKey;
        }
        /**
         * 使用RSA公钥加密数据
         * @param password 要加密的密码
         * @param publicKey RSA公钥
         * @returns 加密后的密文
         */
        function RSAEncrypt(password, publicKey) {
            let encrypt = new JSEncrypt();
            encrypt.setPublicKey(publicKey);
            return encrypt.encrypt(password);
        }
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title>修改个案</title>
    <style>
        .form-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 246, 252, 0.95) 100%);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid rgba(111, 66, 193, 0.1);
            backdrop-filter: blur(15px);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 8px;
            color: #6f42c1;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #495057;
            margin-bottom: 8px;
        }

        .form-control {
            width: 100%;
            padding: 8px 16px;
            border: 1px solid rgba(111, 66, 193, 0.2);
            border-radius: 8px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #6f42c1;
            box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
            background: white;
        }

        .form-control[readonly] {
            background: #f8f9fa;
            color: #6c757d;
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }

        .required {
            color: #dc3545;
        }

        .action-buttons {
            margin-top: 20px;
            padding: 16px 0;
            display: flex;
            gap: 12px;
        }

        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #6f42c1 0%, #8b5fbf 100%);
            color: white;
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
        }

        .content-wrapper {
            padding-bottom: 80px;
        }

        .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            background: rgba(111, 66, 193, 0.1);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
        }

        .radio-item input[type="radio"] {
            margin-right: 6px;
        }

        .risk-level-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .risk-level-item {
            flex: 1;
            min-width: 80px;
        }

        .risk-level-item input[type="radio"] {
            margin-right: 4px;
        }

        .risk-level-item label {
            font-size: 14px;
            margin-bottom: 0;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            text-align: center;
            color: #6f42c1;
        }

        .loading-content i {
            font-size: 48px;
            margin-bottom: 16px;
        }

        /* 关键词样式 */
        .keyword-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
        }

        .tag-item {
            padding: 4px 12px;
            border-radius: 12px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
            font-size: 12px;
        }

        .tag-item:hover {
            background-color: #e9ecef;
        }

        .tag-item.selected {
            background-color: #6f42c1;
            color: white;
            border-color: #6f42c1;
        }

        .selected-keywords {
            min-height: 40px;
            border: 1px dashed #dee2e6;
            border-radius: 8px;
            padding: 8px;
            background-color: #f8f9fa;
        }

        .selected-tag {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            background-color: #6f42c1;
            color: white;
            border-radius: 12px;
            font-size: 12px;
            position: relative;
        }

        .selected-tag.custom {
            background-color: #28a745;
        }

        .selected-tag .remove-btn {
            margin-left: 6px;
            cursor: pointer;
            font-weight: bold;
        }

        .selected-tag .remove-btn:hover {
            color: #ffebee;
        }

        .empty-keywords {
            color: #6c757d;
            font-style: italic;
            font-size: 12px;
        }

        .input-group {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .input-group .form-control {
            flex: 1;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .input-group-append {
            display: flex;
        }

        .input-group-append .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
            white-space: nowrap;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            修改个案
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <div class="content-wrapper">
                <form id="caseForm">
                    <input type="hidden" id="id" name="id">
                    <input type="hidden" id="userId" name="userId">
                    
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fa fa-info-circle"></i>基本信息
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属机构 <span class="required">*</span></label>
                            <select class="form-control" id="structId" name="structId" required>
                                <option value="">请选择机构</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">咨询日期 <span class="required">*</span></label>
                            <input type="date" class="form-control" id="consultationDate" name="consultationDate" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">员工工号 <span class="required">*</span></label>
                            <input type="text" class="form-control" id="loginName" name="loginName" placeholder="请输入工号" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">咨询时长(分钟) <span class="required">*</span></label>
                            <input type="number" class="form-control" id="consultationDuration" name="consultationDuration" placeholder="请输入时长" min="1" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">咨询形式 <span class="required">*</span></label>
                            <select class="form-control" id="consultationForm" name="consultationForm" required>
                                <option value="">请选择咨询形式</option>
                                <option value="1">驻场咨询</option>
                                <option value="2">线上咨询</option>
                                <option value="3">门店咨询</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">咨询类型 <span class="required">*</span></label>
                            <select class="form-control" id="consultationType" name="consultationType" required>
                                <option value="">请选择咨询类型</option>
                                <option value="1">首次咨询</option>
                                <option value="2">第二次咨询</option>
                                <option value="3">第三次咨询</option>
                                <option value="4">第四次咨询</option>
                                <option value="5">第五次咨询</option>
                                <option value="6">第六次及以上咨询</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">咨询领域 <span class="required">*</span></label>
                            <select class="form-control" id="consultationField" name="consultationField" required>
                                <option value="">请选择咨询领域</option>
                                <option value="1">心理健康</option>
                                <option value="2">情绪压力</option>
                                <option value="3">人际关系</option>
                                <option value="4">恋爱情感</option>
                                <option value="5">家庭关系</option>
                                <option value="6">亲子教育</option>
                                <option value="7">职场发展</option>
                                <option value="8">个人成长</option>
                            </select>
                        </div>
                    </div>

                    <!-- 来访者信息 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fa fa-user"></i>来访者信息
                        </div>
                        <div class="form-group">
                            <label class="form-label">来访年龄 <span class="required">*</span></label>
                            <select class="form-control" id="visitorAge" name="visitorAge" required>
                                <option value="">请选择年龄</option>
                                <option value="1">20岁及以下</option>
                                <option value="2">21-25</option>
                                <option value="3">26-30</option>
                                <option value="4">31-35</option>
                                <option value="5">36-40</option>
                                <option value="6">41-45</option>
                                <option value="7">46-50</option>
                                <option value="8">50岁以上</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">来访性别 <span class="required">*</span></label>
                            <select class="form-control" id="visitorGender" name="visitorGender" required>
                                <option value="">请选择性别</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">婚姻状态 <span class="required">*</span></label>
                            <select class="form-control" id="maritalStatus" name="maritalStatus" required>
                                <option value="">请选择婚姻状态</option>
                                <option value="1">未婚</option>
                                <option value="2">已婚</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">有无子女 <span class="required">*</span></label>
                            <select class="form-control" id="hasChildren" name="hasChildren" required>
                                <option value="">请选择</option>
                                <option value="1">有</option>
                                <option value="2">无</option>
                            </select>
                        </div>
                    </div>

                    <!-- 问题描述 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fa fa-comment"></i>问题描述
                        </div>
                        <div class="form-group">
                            <label class="form-label">问题概述 <span class="required">*</span></label>
                            <textarea class="form-control" id="problemSummary" name="problemSummary" placeholder="请详细描述问题，至少100字" required minlength="100" maxlength="500"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">咨询关键词 <span class="required">*</span></label>
                            
                            <!-- 关键词选择区域 -->
                            <div class="mb-2">
                                <small class="text-muted">关键词选择：</small>
                                <div class="keyword-tags" id="databaseKeywords">
                                    <!-- 从数据库动态加载的关键词会显示在这里 -->
                                    <div class="text-muted">加载中...</div>
                                </div>
                            </div>
                            
                            <!-- 自定义关键词输入区域 -->
                            <div class="input-group mb-2">
                                <input type="text" id="keywordInput" class="form-control" placeholder="输入自定义关键词" maxlength="50">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-primary" onclick="addCustomKeyword()">添加</button>
                                </div>
                            </div>
                            
                            <!-- 已选择关键词显示区域 -->
                            <div class="mb-2">
                                <small class="text-muted">已选择的关键词：</small>
                                <div class="selected-keywords" id="selectedKeywords">
                                    <span class="empty-keywords">暂无选择关键词</span>
                                </div>
                            </div>
                            
                            <input type="hidden" id="consultationKeywords" name="consultationKeywords" value="">
                        </div>
                    </div>

                    <!-- 特殊情况 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fa fa-exclamation-triangle"></i>特殊情况
                        </div>
                        <div class="form-group">
                            <label class="form-label">是否职场问题</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="isWorkplaceIssue1" name="isWorkplaceIssue" value="1" onchange="toggleWorkplaceDesc()">
                                    <label for="isWorkplaceIssue1">是</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="isWorkplaceIssue0" name="isWorkplaceIssue" value="0" onchange="toggleWorkplaceDesc()" checked>
                                    <label for="isWorkplaceIssue0">否</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="workplaceDescriptionGroup" style="display: none;">
                            <label class="form-label">职场问题描述</label>
                            <textarea class="form-control" id="workplaceDescription" name="workplaceDescription" placeholder="请描述具体的职场问题"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">是否有心理风险</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="hasPsychologicalRisk1" name="hasPsychologicalRisk" value="1" onchange="toggleRiskDesc()">
                                    <label for="hasPsychologicalRisk1">是</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="hasPsychologicalRisk0" name="hasPsychologicalRisk" value="0" onchange="toggleRiskDesc()" checked>
                                    <label for="hasPsychologicalRisk0">否</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="riskLevelGroup" style="display: none;">
                            <label class="form-label">风险等级</label>
                            <div class="risk-level-group">
                                <div class="risk-level-item">
                                    <input type="radio" id="riskLevel0" name="riskLevel" value="0">
                                    <label for="riskLevel0">无风险</label>
                                </div>
                                <div class="risk-level-item">
                                    <input type="radio" id="riskLevel1" name="riskLevel" value="1">
                                    <label for="riskLevel1">低风险</label>
                                </div>
                                <div class="risk-level-item">
                                    <input type="radio" id="riskLevel2" name="riskLevel" value="2">
                                    <label for="riskLevel2">中风险</label>
                                </div>
                                <div class="risk-level-item">
                                    <input type="radio" id="riskLevel3" name="riskLevel" value="3">
                                    <label for="riskLevel3">高风险</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="riskDescriptionGroup" style="display: none;">
                            <label class="form-label">风险描述</label>
                            <textarea class="form-control" id="riskDescription" name="riskDescription" placeholder="请描述具体的风险情况"></textarea>
                        </div>
                    </div>

                    <!-- 后续建议 -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fa fa-lightbulb-o"></i>后续建议
                        </div>
                        <div class="form-group">
                            <label class="form-label">建议类型 <span class="required">*</span></label>
                            <select class="form-control" id="followUpSuggestion" name="followUpSuggestion" required>
                                <option value="">请选择建议类型</option>
                                <option value="1">无需跟进</option>
                                <option value="2">定期咨询</option>
                                <option value="3">转介就医</option>
                                <option value="4">其他</option>
                            </select>
                        </div>
                        <div class="form-group" id="otherSuggestionGroup" style="display: none;">
                            <label class="form-label">其他建议</label>
                            <textarea class="form-control" id="otherSuggestion" name="otherSuggestion" placeholder="请描述其他建议"></textarea>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="action-buttons">
                        <button type="button" class="action-btn secondary" onclick="goBack()">取消</button>
                        <button type="submit" class="action-btn primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- * appCapsule -->

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <i class="fa fa-spinner fa-spin"></i>
            <p>加载中...</p>
        </div>
    </div>

    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        // 全局变量
        let allKeywords = [];
        let selectedKeywords = [];
        let caseId = getUrlParam('id');

        $(function () {
            if (!caseId) {
                layer.open({
                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>缺少个案ID参数'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                    , end: function() {
                        goBack();
                    }
                });
                return;
            }

            // 加载关键词（先加载关键词，再加载个案数据）
            loadKeywords(function() {
                // 关键词加载完成后，再加载个案数据
                loadCaseData();
            });

            // 职场问题单选框事件已在HTML中通过onchange处理
            // 心理风险单选框事件已在HTML中通过onchange处理

            // 后续建议选择事件
            $("#followUpSuggestion").change(function () {
                $("#otherSuggestionGroup").toggle($(this).val() === "4");
            });
            
            // 初始化关键词验证
            setTimeout(function() {
                if ($("#caseForm").validate()) {
                    $("#caseForm").validate().element("#consultationKeywords");
                }
            }, 1000);
            
            // 添加自定义验证方法
            $.validator.addMethod("keywordsRequired", function(value, element) {
                return selectedKeywords.length > 0;
            }, "请至少选择一个关键词");

            // 表单验证
            $("#caseForm").validate({
                rules: {
                    structId: { required: true },
                    loginName: { 
                        required: true,
                        remote: {
                            type: "GET",
                            url: "/anteroom/user/getByLoginName",
                            dataType: "json",
                            contentType: "application/json",
                            data: {
                                loginName: function() {
                                    return $.trim($("#loginName").val());
                                }
                            },
                            dataFilter: function (data, type) {
                                if (data === null || data === "") {
                                    return false;
                                }
                                else{
                                    let res = JSON.parse(data);
                                    $("#userId").val(res);
                                    return true;
                                }
                            }
                        }
                    },
                    consultationDate: { required: true },
                    consultationForm: { required: true },
                    consultationDuration: { required: true, number: true, min: 1 },
                    consultationType: { required: true },
                    visitorGender: { required: true },
                    visitorAge: { required: true },
                    maritalStatus: { required: true },
                    hasChildren: { required: true },
                    consultationField: { required: true },
                    problemSummary: { required: true, minlength: 100, maxlength: 500 },
                    consultationKeywords: { 
                        keywordsRequired: true
                    },
                    riskLevel: {
                        required: function() {
                            return $("input[name='hasPsychologicalRisk']:checked").val() === '1';
                        }
                    },
                    riskDescription: {
                        required: function() {
                            return $("input[name='hasPsychologicalRisk']:checked").val() === '1';
                        }
                    },
                    followUpSuggestion: { required: true }
                },
                messages: {
                    structId: { required: "请选择所属机构" },
                    loginName: { 
                        required: "请输入来访者工号",
                        remote: "该来访者工号不存在"
                    },
                    consultationDate: { required: "请选择咨询日期" },
                    consultationForm: { required: "请选择咨询形式" },
                    consultationDuration: { 
                        required: "请输入咨询时长",
                        number: "请输入有效的数字",
                        min: "咨询时长必须大于0"
                    },
                    consultationType: { required: "请选择咨询类型" },
                    visitorGender: { required: "请选择来访性别" },
                    visitorAge: { required: "请选择来访年龄" },
                    maritalStatus: { required: "请选择婚姻状态" },
                    hasChildren: { required: "请选择是否有子女" },
                    consultationField: { required: "请选择咨询领域" },
                    problemSummary: {
                        required: "请填写问题概述",
                        minlength: "问题概述至少需要100字",
                        maxlength: "问题概述不能超过500字"
                    },
                    consultationKeywords: {
                        keywordsRequired: "请至少选择一个关键词"
                    },
                    riskLevel: { required: "请选择风险等级" },
                    riskDescription: { required: "请填写风险程度简要描述" },
                    followUpSuggestion: { required: "请选择后续建议" }
                },
                errorPlacement: function(error, element) {
                    // 特殊处理关键词验证错误
                    if (element.attr('name') === 'consultationKeywords') {
                        error.insertAfter('#selectedKeywords');
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function () {
                    // 更新关键词字段
                    updateKeywordsInput();
                    updateCase();
                }
            });
        });

        // 加载个案数据
        function loadCaseData() {
            $.ajax({
                type: "GET",
                url: "/counselingroom/consultationcase/get?id=" + caseId,
                data: '',
                success: function (res) {
                    $("#loadingOverlay").hide();
                    // 先初始化机构选择器，再填充表单数据
                    initSelect('#structId', '/anteroom/structs/get_for_select',{},res.structName,'请选择机构');
                    fillFormData(res);
                },
                error: function () {
                    $("#loadingOverlay").hide();
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>数据加载失败'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                        , end: function() {
                            goBack();
                        }
                    });
                }
            });
        }

        // 填充表单数据
        function fillFormData(data) {
            $("#id").val(data.id);
            $("#userId").val(data.userId);
            $("#consultationDate").val(data.consultationDate);
            $("#loginName").val(data.loginName);
            $("#consultationDuration").val(data.consultationDuration);
            $("#consultationForm").val(data.consultationForm);
            $("#consultationType").val(data.consultationType);
            $("#consultationField").val(data.consultationField);
            $("#visitorAge").val(data.visitorAge);
            $("#visitorGender").val(data.visitorGender);
            $("#maritalStatus").val(data.maritalStatus);
            $("#hasChildren").val(data.hasChildren);
            $("#problemSummary").val(data.problemSummary);
            
            // 处理关键词
            if (data.consultationKeywords) {
                const keywords = data.consultationKeywords.split(',').map(k => k.trim());
                keywords.forEach(keyword => {
                    // 检查关键词是否在数据库中存在，如果存在则标记为database，否则标记为custom
                    const existsInDatabase = allKeywords.some(item => item.keyword === keyword);
                    addKeywordToArray(keyword, existsInDatabase ? 'database' : 'custom');
                });
                updateSelectedKeywordsDisplay();
                updateKeywordsInput();
                // 重新渲染关键词以显示选中状态
                renderKeywords();
                // 触发关键词验证
                if ($("#caseForm").validate()) {
                    $("#caseForm").validate().element("#consultationKeywords");
                }
            }
            
            // 职场问题
            if (data.isWorkplaceIssue === 1) {
                $("#isWorkplaceIssue1").prop("checked", true);
                $("#workplaceDescription").val(data.workplaceDescription);
                toggleWorkplaceDesc();
            }
            
            // 心理风险
            if (data.hasPsychologicalRisk === 1) {
                $("#hasPsychologicalRisk1").prop("checked", true);
                $("input[name='riskLevel'][value='" + data.riskLevel + "']").prop("checked", true);
                $("#riskDescription").val(data.riskDescription);
                toggleRiskDesc();
            }
            
            // 后续建议
            $("#followUpSuggestion").val(data.followUpSuggestion);
            if (data.followUpSuggestion === 4) {
                $("#otherSuggestionGroup").show();
                $("#otherSuggestion").val(data.otherSuggestion);
            }
        }

        // 更新个案
        function updateCase() {
            let formData = getFormData();
            
            layer.open({
                type: 2,
                content: '保存中…',
                shadeClose: false
            });

            $.ajax({
                type: "POST",
                url: "/counselingroom/consultationcase/update",
                data: JSON.stringify(formData),
                contentType: "application/json",
                success: function (res) {
                    layer.closeAll();
                    if (res.resultCode === 200) {
                        layer.open({
                            content: '<img src="/static/images/success.png" width="25" class="mr-1"/>保存成功'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                            , end: function() {
                                goBack();
                            }
                        });
                    } else {
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + (res.resultMsg || '保存失败')
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                },
                error: function () {
                    layer.closeAll();
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>网络错误，请重试'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                }
            });
        }



        // 获取表单数据
        function getFormData() {
            let data = {
                id: $("#id").val(),
                structId: $("#structId").val(),
                userId: $("#userId").val(),
                consultationDate: $("#consultationDate").val(),
                loginName: $("#loginName").val(),
                consultationDuration: $("#consultationDuration").val(),
                consultationForm: $("#consultationForm").val(),
                consultationType: $("#consultationType").val(),
                consultationField: $("#consultationField").val(),
                visitorAge: $("#visitorAge").val(),
                visitorGender: $("#visitorGender").val(),
                maritalStatus: $("#maritalStatus").val(),
                hasChildren: $("#hasChildren").val(),
                problemSummary: $("#problemSummary").val(),
                consultationKeywords: getCheckedKeywords(),
                isWorkplaceIssue: $("input[name='isWorkplaceIssue']:checked").val(),
                workplaceDescription: $("#workplaceDescription").val(),
                hasPsychologicalRisk: $("input[name='hasPsychologicalRisk']:checked").val(),
                riskLevel: $("input[name='riskLevel']:checked").val() || 0,
                riskDescription: $("#riskDescription").val(),
                followUpSuggestion: $("#followUpSuggestion").val(),
                otherSuggestion: $("#otherSuggestion").val()
            };

            return data;
        }

        // 返回上一页
        function goBack() {
            history.back();
        }

        // 从数据库加载关键词
        function loadKeywords(callback) {
            $.ajax({
                type: 'GET',
                url: '/counselingroom/keywords/all',
                success: function(response) {
                    if (response.resultCode === 200 && response.data) {
                        allKeywords = response.data.map(keyword => ({ keyword: keyword }));
                        renderKeywords();
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        console.error('加载关键词失败:', response.resultMsg);
                        $('#databaseKeywords').html('<div class="text-muted">加载失败</div>');
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                    }
                },
                error: function() {
                    console.error('加载关键词请求失败');
                    $('#databaseKeywords').html('<div class="text-muted">加载失败</div>');
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                }
            });
        }

        // 渲染关键词
        function renderKeywords() {
            const container = $('#databaseKeywords');
            let html = '';
            
            if (allKeywords.length === 0) {
                html = '<div class="text-muted">暂无关键词</div>';
            } else {
                allKeywords.forEach(keyword => {
                    const escapedKeyword = keyword.keyword.replace(/"/g, '&quot;');
                    const isSelected = selectedKeywords.some(item => item.keyword === keyword.keyword);
                    const selectedClass = isSelected ? ' selected' : '';
                    html += `<div class="tag-item${selectedClass}" data-value="${escapedKeyword}">
                                ${keyword.keyword}
                             </div>`;
                });
            }
            
            container.html(html);
            
            // 绑定点击事件
            $('.keyword-tags .tag-item').on('click', function() {
                toggleKeyword($(this));
            });
        }

        // 切换关键词选择状态
        function toggleKeyword($tag) {
            const keyword = $tag.data('value');
            
            if ($tag.hasClass('selected')) {
                // 取消选择
                $tag.removeClass('selected');
                removeKeywordFromArray(keyword);
            } else {
                // 选择
                $tag.addClass('selected');
                addKeywordToArray(keyword, 'database');
            }
            
            updateSelectedKeywordsDisplay();
            updateKeywordsInput();
            
            // 触发验证
            if ($("#caseForm").validate()) {
                $("#caseForm").validate().element("#consultationKeywords");
            }
        }

        // 添加自定义关键词
        function addCustomKeyword() {
            const input = $('#keywordInput');
            const keyword = input.val().trim();
            
            if (!keyword) {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>请输入关键词'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                });
                return;
            }
            
            if (keyword.length > 50) {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>关键词长度不能超过50个字符'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                });
                input.focus();
                return;
            }
            
            // 检查本地是否重复
            if (isKeywordExists(keyword)) {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>该关键词已存在'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                });
                input.focus();
                return;
            }
            
            // 先添加到本地
            addKeywordToArray(keyword, 'custom');
            input.val('');
            updateSelectedKeywordsDisplay();
            updateKeywordsInput();
            
            // 触发验证
            if ($("#caseForm").validate()) {
                $("#caseForm").validate().element("#consultationKeywords");
            }
            
            // 异步保存到数据库
            saveKeywordToDatabase(keyword);
        }

        // 保存关键词到数据库
        function saveKeywordToDatabase(keyword) {
            $.ajax({
                type: 'POST',
                url: '/counselingroom/keywords/add',
                data: JSON.stringify({ keyword: keyword }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.resultCode === 200) {
                        console.log('关键词已保存到数据库:', keyword);
                        // 重新加载关键词库
                        loadKeywords();
                    } else {
                        console.log('关键词已存在于数据库:', keyword);
                    }
                },
                error: function() {
                    console.error('保存关键词到数据库失败:', keyword);
                }
            });
        }

        // 检查关键词是否已存在
        function isKeywordExists(keyword) {
            // 检查已选择的关键词
            if (selectedKeywords.some(item => item.keyword === keyword)) {
                return true;
            }
            
            // 检查所有数据库中的关键词
            return allKeywords.some(item => item.keyword === keyword);
        }

        // 添加关键词到数组
        function addKeywordToArray(keyword, type) {
            if (!isKeywordExistsInSelected(keyword)) {
                selectedKeywords.push({
                    keyword: keyword,
                    type: type
                });
            }
        }

        // 从数组中移除关键词
        function removeKeywordFromArray(keyword) {
            selectedKeywords = selectedKeywords.filter(item => item.keyword !== keyword);
        }

        // 移除关键词
        function removeKeyword(keyword) {
            // 如果是数据库关键词，需要同时更新标签的选择状态
            const keywordObj = selectedKeywords.find(item => item.keyword === keyword);
            if (keywordObj && keywordObj.type === 'database') {
                $(`.keyword-tags .tag-item[data-value="${keyword.replace(/"/g, '&quot;')}"]`).removeClass('selected');
            }
            
            removeKeywordFromArray(keyword);
            updateSelectedKeywordsDisplay();
            updateKeywordsInput();
            
            // 触发验证
            if ($("#caseForm").validate()) {
                $("#caseForm").validate().element("#consultationKeywords");
            }
        }

        // 更新已选择关键词的显示
        function updateSelectedKeywordsDisplay() {
            const container = $('#selectedKeywords');
            
            if (selectedKeywords.length === 0) {
                container.html('<span class="empty-keywords">暂无选择关键词</span>');
                return;
            }
            
            let html = '';
            selectedKeywords.forEach(item => {
                const cssClass = item.type === 'custom' ? 'selected-tag custom' : 'selected-tag';
                const escapedKeyword = item.keyword.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
                html += `<span class="${cssClass}">
                            ${item.keyword}
                            <span class="remove-btn" onclick="removeKeyword('${escapedKeyword}')">&times;</span>
                         </span>`;
            });
            
            container.html(html);
        }

        // 更新隐藏字段的值
        function updateKeywordsInput() {
            const keywords = selectedKeywords.map(item => item.keyword).join(',');
            $('#consultationKeywords').val(keywords);
        }

        // 获取选中的关键词（用于验证）
        function getCheckedKeywords() {
            return selectedKeywords.map(item => item.keyword).join(',');
        }

        // 检查关键词是否已在选中列表中
        function isKeywordExistsInSelected(keyword) {
            return selectedKeywords.some(item => item.keyword === keyword);
        }

        // 回车键添加关键词
        $('#keywordInput').on('keypress', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                addCustomKeyword();
            }
        });

        // 切换职场问题描述显示
        function toggleWorkplaceDesc() {
            $("#workplaceDescriptionGroup").toggle($("input[name='isWorkplaceIssue']:checked").val() === '1');
        }

        // 切换风险描述显示
        function toggleRiskDesc() {
            $("#riskLevelGroup, #riskDescriptionGroup").toggle($("input[name='hasPsychologicalRisk']:checked").val() === '1');
        }
    </script>
</th:block>
</body>
</html> 
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            我的预约
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <div class="list font14 hide">
                <div class="mt-2 mb-3">
                    <img th:src="@{/static/images/app_testrecord_banner.png}" alt="image" class="imageBlock img-fluid rounded">
                </div>
                <div class="detail font14">
                </div>
                <div class="mt-2 mb-2 load-more hide"><a href="javascript:" class="btn btn-primary btn-sm btn-block" id="loadMore">查看更多</a></div>
            </div>
            <div class="splashBlock hide">
                <div class="mb-3 mt-3">
                    <img class="nodata-img" th:src="@{/static/images/nodata.png}" alt="">
                </div>
                <div class="sectionTitle text-center">
                    <div class="title">
                    </div>
                    <div class="lead">
                        暂无预约记录！
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let currentPage = 0;
        let pageSize = 8;
        $(function () {
            init(currentPage);
            $(".load-more").on('click', '#loadMore', function () {
                currentPage = currentPage + 1;
                init(currentPage);
            });
        });
        let getQueryCondition = function (currentPage) {
            let param = {};
            let roleId = '[[${user.role.roleId}]]';
            let userId = '[[${user.userId}]]';
            param.visitorId = (roleId == '3' || roleId == '4') ? userId: 0;
            param.counselorId = (roleId != '3' && roleId != '4') ? -1: 0;
            param.pageSize = pageSize;
            param.pageIndex = currentPage * pageSize;
            return param;
        };
        let cancel = function (id) {
            layer.open({
                content: '<i class="fa fa-info-circle mr-1"></i>确定要取消预约吗？'
                , btn: ['确定', '取消']
                , shadeClose: false
                , yes: function (index) {
                    $.post("/counselingroom/counseling/update_state", { id :id, state:2 }, function (res) {
                        if (res.resultCode === 200) {
                            $(".detail").empty();
                            init(0);
                            layer.closeAll();
                        }
                    });
                }
            });
        };
        let init = function (currentPage) {
            layer.open({
                type: 2
                , content: '加载中'
            });
            $.ajax({
                type: "post",
                url: '/counselingroom/counseling/order_record',
                dataType: "json",
                contentType:'application/json',
                data: JSON.stringify(getQueryCondition(currentPage)),
                success: function (res) {
                    layer.closeAll();
                    if (res.data.length === 0) {
                        if (currentPage === 0) {
                            $(".list").removeClass('show').addClass('hide');
                            $(".splashBlock").removeClass('hide').addClass('show');
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                    }
                    else {
                        let str = "";
                        for (let i = 0; i < res.data.length; i++) {
                            str += '<ul class="list-unstyled p-2 mt-2 bg-light border" style="border-radius:6px;">';
                            str += '<li class="pb15">咨询师：<span class="ml-1">' + res.data[i].counselorName + '</span></li>';
                            str += '<li class="pb15">来访者：<span class="ml-1">' + res.data[i].visitorName + '</span></li>';
                            let counselingWay = "";
                            if (res.data[i].counselingWay === 1)
                                counselingWay = "网上咨询";
                            if (res.data[i].counselingWay === 2)
                                counselingWay = "面询";
                            if (res.data[i].counselingWay === 3)
                                counselingWay = "电话咨询";
                            str += '<li class="pb15">咨询方式：<span class="ml-1">' + counselingWay + '</span></li>';
                            str += '<li class="pb15">咨询类型：<span class="ml-1">' + res.data[i].counselingItemName + '</span></li>';
                            str += '<li class="pb15">问题类型：<span class="ml-1">' + res.data[i].counselingType + '</span></li>';
                            str += '<li class="pb15">咨询时间段：<span class="ml-1">' + res.data[i].startTime.substring(5, 16) + ' - ' + res.data[i].endTime.substring(5, 16) + '</li>';
                            let strState = "";
                            let state = res.data[i].state;
                            let handleFlag = ('[[${user.role.roleId}]]' != '3') ? '<span class="badge badge-primary mr-1"><a href="/app/counseling/handle?id=' + res.data[i].id + '" class="text-white">查看及处理</a></span>' : '<span class="badge badge-danger mr-1" onclick="cancel(' + res.data[i].id + ')">取消预约</span>';
                            if (state === 0) {
                                if(res.data[i].endTime < getDateNowFormat()) {
                                    strState = '<span class="badge badge-light mr-1 mb-1">已过期</span>';
                                }
                                else{
                                    strState = '<span class="badge badge-outline-secondary mr-1 mb-1">未处理</span>' + handleFlag + '';
                                }
                            }
                            if (state === 1) {
                                if (res.data[i].startTime > getDateNowFormat()) {
                                    strState = '<span class="badge badge-outline-success mr-1 mb-1">已接受</span>' + handleFlag + '<span class="badge badge-outline-warning mr-1 mb-1">咨询时间未开始</span>';
                                }
                                else if (res.data[i].startTime <= getDateNowFormat() && getDateNowFormat() <= res.data[i].endTime) {
                                    strState = '<span class="badge badge-outline-success mr-1 mb-1">已接受</span><span class="badge badge-outline-info mr-1 mb-1"><a href="/app/counseling/online">进入咨询</a></span>';
                                }
                                else {
                                    strState = '<span class="badge badge-light mr-1 mb-1">已过期</span>';
                                }
                            }
                            if (state === 2) {
                                strState = '<span class="badge badge-outline-secondary mr-1 mb-1">已取消</span>';
                            }
                            if (state === 3) {
                                strState = '<span class="badge badge-outline-danger mr-1 mb-1">已结束</span>';
                            }
                            str += '<li class="pb15">状态：' + strState + '</li>';
                            str += '</ul>';
                            str += '<div class="divider dashed large mt-2 mb-2"></div>';
                        }
                        $(".detail").append(str);
                        $(".list").removeClass('hide').addClass('show');
                        if (res.data.length < pageSize) {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('hide').addClass('show');
                        }
                        $(".splashBlock").removeClass('show').addClass('hide');
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>
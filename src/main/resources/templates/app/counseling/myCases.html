<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title>我的个案</title>
    <style>
        /* 个案卡片样式 */
        .case-card {
            background: #fff;
            border-radius: 12px;
            margin-bottom: 12px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .case-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(111, 66, 193, 0.15);
            border-color: #6f42c1;
        }

        .case-card-body {
            padding: 16px;
        }

        .case-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .case-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
        }

        .case-date {
            font-size: 12px;
            color: #6c757d;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 6px;
        }

        .case-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 12px;
        }

        .info-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #495057;
        }

        .info-item i {
            width: 16px;
            margin-right: 8px;
            color: #6f42c1;
            font-size: 12px;
        }

        .case-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-start;
        }

        .case-btn {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .case-btn.view {
            background: #6f42c1;
            color: white;
        }

        .case-btn.view:hover {
            background: #5a359a;
            transform: translateY(-1px);
        }

        .case-btn.edit {
            background: #ffc107;
            color: #212529;
        }

        .case-btn.edit:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }

        .case-btn.delete {
            background: #dc3545;
            color: white;
        }

        .case-btn.delete:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        /* 搜索区域样式 */
        .search-section {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .search-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .search-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .search-toggle {
            background: #6f42c1;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-toggle:hover {
            background: #5a359a;
            transform: translateY(-1px);
        }

        .search-form {
            display: none;
            animation: slideDown 0.3s ease;
        }

        .search-form.show {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-label {
            display: block;
            font-size: 13px;
            font-weight: 500;
            color: #495057;
            margin-bottom: 6px;
        }

        .search-buttons {
            display: flex;
            gap: 8px;
            margin-top: 16px;
        }

        .search-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn.primary {
            background: #6f42c1;
            color: white;
        }

        .search-btn.primary:hover {
            background: #5a359a;
        }

        .search-btn.secondary {
            background: #6c757d;
            color: white;
        }

        .search-btn.secondary:hover {
            background: #5a6268;
        }

        .search-btn:hover {
            transform: translateY(-1px);
        }

        /* 加载更多按钮 */
        .load-more {
            text-align: center;
            margin-top: 20px;
        }

        .load-more .btn {
            background: #f8f9fa;
            border: 1px solid #6f42c1;
            color: #6f42c1;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .load-more .btn:hover {
            background: #6f42c1;
            color: white;
            transform: translateY(-1px);
        }

        /* 模态框样式 */
        .modal-header {
            background: #6f42c1;
            color: white !important;
            border: none;
            padding: 20px;
        }

        .modal-header .modal-title {
            color: white !important;
        }

        .modal-header .close {
            color: white !important;
        }

        .modal-body {
            max-height: 70vh;
            overflow-y: auto;
        }

        .detail-section {
            margin-bottom: 20px;
        }

        .detail-section:last-child {
            margin-bottom: 0;
        }

        .detail-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .detail-title i {
            margin-right: 8px;
            color: #6f42c1;
        }

        .detail-content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .detail-item:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            font-weight: 500;
            color: #495057;
        }

        .detail-value {
            color: #2c3e50;
            text-align: right;
            flex: 1;
            margin-left: 12px;
        }

        .keyword-badge {
            display: inline-block;
            background: #6f42c1;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            margin: 2px;
        }

        .risk-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .risk-badge.low {
            background: #d4edda;
            color: #155724;
        }

        .risk-badge.medium {
            background: #fff3cd;
            color: #856404;
        }

        .risk-badge.high {
            background: #f8d7da;
            color: #721c24;
        }

        .risk-badge.none {
            background: #e2e3e5;
            color: #383d41;
        }

        /* 加载状态样式 */
        .loading-spinner {
            color: #6f42c1;
        }

        .loading-text {
            color: #6c757d;
            font-size: 14px;
        }

        .error-icon {
            color: #ffc107;
        }

        .error-text {
            color: #6c757d;
            font-size: 14px;
        }

        .error-detail {
            color: #6c757d;
            font-size: 12px;
        }

        /* 响应式优化 */
        @media (max-width: 576px) {
            .case-header {
                margin-bottom: 10px;
            }

            .case-card-body {
                padding: 14px;
            }

            .case-title {
                font-size: 14px;
                flex: 1;
                margin-right: 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .case-actions {
                flex-shrink: 0;
            }

            .search-section {
                padding: 14px;
                margin-bottom: 14px;
            }

            .search-buttons {
                flex-direction: column;
            }

            .search-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            我的个案
        </div>
        <div class="right">
            <th:block th:if="${canAdd}">
                <a href="javascript:" class="icon" onclick="addCase()">
                    <i class="fa fa-plus"></i>
                </a>
            </th:block>
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-header">
                    <div class="search-title">筛选条件</div>
                    <button type="button" class="search-toggle" id="toggleSearch">
                        <i class="fa fa-search"></i> 搜索
                    </button>
                </div>
                <form class="search-form" id="searchForm">
                    <div class="form-group">
                        <label class="form-label">所属机构</label>
                        <select class="form-control" id="structId" name="structId">
                            <option value="">选择机构</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">咨询日期</label>
                        <input type="date" class="form-control" id="consultationDate" name="consultationDate">
                    </div>
                    <div class="form-group">
                        <label class="form-label">员工工号</label>
                        <input type="text" class="form-control" id="loginName" name="loginName" placeholder="请输入工号">
                    </div>
                    <div class="form-group">
                        <label class="form-label">员工姓名</label>
                        <input type="text" class="form-control" id="realName" name="realName" placeholder="请输入姓名">
                    </div>
                    <div class="form-group">
                        <label class="form-label">性别</label>
                        <select class="form-control" id="visitorGender" name="visitorGender">
                            <option value="">全部</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">咨询形式</label>
                        <select class="form-control" id="consultationForm" name="consultationForm">
                            <option value="">全部</option>
                            <option value="1">驻场咨询</option>
                            <option value="2">线上咨询</option>
                            <option value="3">门店咨询</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">咨询领域</label>
                        <select class="form-control" id="consultationField" name="consultationField">
                            <option value="">全部</option>
                            <option value="1">心理健康</option>
                            <option value="2">情绪压力</option>
                            <option value="3">人际关系</option>
                            <option value="4">恋爱情感</option>
                            <option value="5">家庭关系</option>
                            <option value="6">亲子教育</option>
                            <option value="7">职场发展</option>
                            <option value="8">个人成长</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">咨询类型</label>
                        <select class="form-control" id="consultationType" name="consultationType">
                            <option value="">全部</option>
                            <option value="1">首次咨询</option>
                            <option value="2">第二次咨询</option>
                            <option value="3">第三次咨询</option>
                            <option value="4">第四次咨询</option>
                            <option value="5">第五次咨询</option>
                            <option value="6">第六次及以上咨询</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">来访年龄</label>
                        <select class="form-control" id="visitorAge" name="visitorAge">
                            <option value="">全部</option>
                            <option value="1">20岁及以下</option>
                            <option value="2">21-25</option>
                            <option value="3">26-30</option>
                            <option value="4">31-35</option>
                            <option value="5">36-40</option>
                            <option value="6">41-45</option>
                            <option value="7">46-50</option>
                            <option value="8">50岁以上</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">婚姻状态</label>
                        <select class="form-control" id="maritalStatus" name="maritalStatus">
                            <option value="">全部</option>
                            <option value="1">未婚</option>
                            <option value="2">已婚</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">有无子女</label>
                        <select class="form-control" id="hasChildren" name="hasChildren">
                            <option value="">全部</option>
                            <option value="1">有</option>
                            <option value="2">无</option>
                        </select>
                    </div>
                    <div class="search-buttons">
                        <button type="button" class="btn btn-primary rounded" id="submitSearch">查询</button>
                        <button type="button" class="btn btn-secondary rounded" id="resetSearch">重置</button>
                    </div>
                </form>
            </div>

            <!-- 个案列表 -->
            <div id="caseList">
                <!-- 个案卡片将通过JavaScript动态生成 -->
            </div>

            <!-- 加载更多 -->
            <div class="load-more" id="loadMore" style="display: none;">
                <button type="button" class="btn" id="loadMoreBtn">
                    <i class="fa fa-refresh"></i> 加载更多
                </button>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="fa fa-folder-open"></i>
                </div>
                <div class="empty-title">暂无个案记录</div>
                <div class="empty-text">您还没有任何咨询个案记录</div>
            </div>
        </div>
    </div>
    <!-- * appCapsule -->

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel">
                        <i class="fa fa-eye mr-2"></i>个案详情
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="detailModalBody">
                    <!-- 详情内容将通过JavaScript动态生成 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        // 枚举值转换函数
        const EnumConverter = {
            consultationForm: {
                1: '驻场咨询',
                2: '线上咨询',
                3: '门店咨询'
            },
            consultationType: {
                1: '首次咨询',
                2: '第二次咨询',
                3: '第三次咨询',
                4: '第四次咨询',
                5: '第五次咨询',
                6: '第六次及以上咨询'
            },
            consultationField: {
                1: '心理健康',
                2: '情绪压力',
                3: '人际关系',
                4: '恋爱情感',
                5: '家庭关系',
                6: '亲子教育',
                7: '职场发展',
                8: '个人成长'
            },
            visitorAge: {
                1: '20岁及以下',
                2: '21-25',
                3: '26-30',
                4: '31-35',
                5: '36-40',
                6: '41-45',
                7: '46-50',
                8: '50岁以上'
            },
            maritalStatus: {
                1: '未婚',
                2: '已婚'
            },
            hasChildren: {
                1: '有',
                2: '无'
            },
            followUpSuggestion: {
                1: '无需跟进',
                2: '定期咨询',
                3: '转介就医',
                4: '其他'
            },
            riskLevel: {
                0: '无风险',
                1: '低风险',
                2: '中风险',
                3: '高风险'
            }
        };

        // 获取枚举值对应的文本
        function getEnumText(enumObj, value) {
            return enumObj[value] || '';
        }

        // 分页参数
        let currentPage = 1;
        let pageSize = 10;
        let hasMore = true;

        $(function () {
            if ('[[${canView}]]' === 'false') {
                layer.open({
                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>您没有查看个案的权限'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2000
                    , end: function() {
                        window.history.go(-1);
                    }
                });
                return;
            }
            // 初始化机构选择
            initSelect('#structId', '/anteroom/structs/get_for_select',{},'','选择机构');
            
            // 加载个案列表
            loadCaseList();

            // 搜索切换
            $("#toggleSearch").click(function () {
                $("#searchForm").toggleClass("show");
            });

            // 提交搜索
            $("#submitSearch").click(function () {
                currentPage = 1;
                loadCaseList();
                // 搜索后自动收缩面板
                $("#searchForm").removeClass("show");
            });

            // 重置搜索
            $("#resetSearch").click(function () {
                $("#searchForm")[0].reset();
                currentPage = 1;
                loadCaseList();
            });

            // 加载更多
            $("#loadMoreBtn").click(function () {
                if (hasMore) {
                    currentPage++;
                    loadCaseList(true);
                }
            });
        });

        // 加载个案列表
        function loadCaseList(append = false) {
            // 显示加载状态
            if (!append) {
                $("#caseList").html('<div class="text-center py-4"><i class="fa fa-spinner fa-spin fa-2x loading-spinner"></i><p class="mt-2 loading-text">加载中...</p></div>');
                $("#emptyState").hide();
                $("#loadMore").hide();
            }

            let params = {
                pageIndex: currentPage - 1,
                pageSize: pageSize
            };

            // 添加搜索条件
            const consultationDate = $("#consultationDate").val();
            if (consultationDate) {
                params.consultationDate = consultationDate;
            }
            params.structId = $("#structId").val();
            params.consultationType = $("#consultationType").val();
            params.consultationForm = $("#consultationForm").val();
            params.consultationField = $("#consultationField").val();
            params.visitorGender = $("#visitorGender").val();
            params.visitorAge = $("#visitorAge").val();
            params.maritalStatus = $("#maritalStatus").val();
            params.hasChildren = $("#hasChildren").val();
            params.loginName = $("#loginName").val();
            params.realName = $("#realName").val();

            $.ajax({
                type: "POST",
                url: "/counselingroom/consultationcase/get_my_cases",
                data: JSON.stringify(params),
                contentType: "application/json",
                success: function (res) {
                    if (append) {
                        appendCaseList(res.data);
                    } else {
                        renderCaseList(res.data);
                    }

                    // 检查是否还有更多数据
                    hasMore = res.data && res.data.length === pageSize;
                    $("#loadMore").toggle(hasMore);
                }
            });
        }

        // 渲染个案列表
        function renderCaseList(data) {
            if (!data || data.length === 0) {
                $("#caseList").hide();
                $("#emptyState").show();
                $("#loadMore").hide();
                return;
            }

            $("#emptyState").hide();
            $("#caseList").show();

            let html = '';
            data.forEach(function (item) {
                html += generateCaseCard(item);
            });
            $("#caseList").html(html);
        }

        // 追加个案列表
        function appendCaseList(data) {
            if (!data || data.length === 0) {
                return;
            }

            let html = '';
            data.forEach(function (item) {
                html += generateCaseCard(item);
            });
            $("#caseList").append(html);
        }

        // 生成个案卡片
        function generateCaseCard(item) {
            return `
                <div class="case-card" data-id="${item.id}">
                    <div class="case-card-body">
                        <div class="case-header">
                            <div class="case-title">${item.realName || '未知'} - ${item.structName || '未知机构'}</div>
                            <div class="case-date">${item.consultationDate || ''}</div>
                        </div>
                        <div class="case-content">
                            <div class="case-info">
                                <div class="info-item">
                                    <i class="fa fa-user"></i>
                                    <span>工号：${item.loginName || '未知'}</span>
                                </div>
                                <div class="info-item">
                                    <i class="fa fa-clock-o"></i>
                                    <span>时长：${item.consultationDuration || 0}分钟</span>
                                </div>
                                <div class="info-item">
                                    <i class="fa fa-tag"></i>
                                    <span>形式：${getEnumText(EnumConverter.consultationForm, item.consultationForm)}</span>
                                </div>
                                <div class="info-item">
                                    <i class="fa fa-map-marker"></i>
                                    <span>领域：${getEnumText(EnumConverter.consultationField, item.consultationField)}</span>
                                </div>
                            </div>
                        </div>
                        <div class="case-actions">
                            <button type="button" class="case-btn view" onclick="viewCaseDetail(${item.id})">
                                <i class="fa fa-eye mr-1"></i>查看
                            </button>
                            <button type="button" class="case-btn edit" onclick="editCase(${item.id})">
                                <i class="fa fa-edit mr-1"></i>编辑
                            </button>
                            <button type="button" class="case-btn delete" onclick="deleteCase(${item.id}, '${item.realName || '未知'}')">
                                <i class="fa fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 查看个案详情
        function viewCaseDetail(id) {
            // 显示加载状态
            $("#detailModalBody").html('<div class="text-center py-4"><i class="fa fa-spinner fa-spin fa-2x loading-spinner"></i><p class="mt-2 loading-text">加载中...</p></div>');
            $("#detailModal").modal('show');
            
            $.ajax({
                type: "GET",
                url: "/counselingroom/consultationcase/get?id=" + id,
                data: '',
                success: function (res) {
                    let html = generateDetailContent(res);
                    $("#detailModalBody").html(html);
                },
                error: function () {
                    $("#detailModalBody").html('<div class="text-center py-4"><i class="fa fa-exclamation-triangle fa-2x error-icon"></i><p class="mt-2 error-text">网络错误，请重试</p></div>');
                }
            });
        }

        // 生成详情内容
        function generateDetailContent(data) {
            return `
                <div class="detail-section">
                    <div class="detail-title">
                        <i class="fa fa-info-circle"></i>基本信息
                    </div>
                    <div class="detail-content">
                        <div class="detail-item">
                            <span class="detail-label">所属机构：</span>
                            <span class="detail-value">${data.structName || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">咨询日期：</span>
                            <span class="detail-value">${data.consultationDate || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">咨询形式：</span>
                            <span class="detail-value">${getEnumText(EnumConverter.consultationForm, data.consultationForm)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">咨询时长：</span>
                            <span class="detail-value">${data.consultationDuration || 0}分钟</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">咨询类型：</span>
                            <span class="detail-value">${getEnumText(EnumConverter.consultationType, data.consultationType)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">咨询领域：</span>
                            <span class="detail-value">${getEnumText(EnumConverter.consultationField, data.consultationField)}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">
                        <i class="fa fa-user"></i>来访者信息
                    </div>
                    <div class="detail-content">
                        <div class="detail-item">
                            <span class="detail-label">来访年龄：</span>
                            <span class="detail-value">${getEnumText(EnumConverter.visitorAge, data.visitorAge)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">来访性别：</span>
                            <span class="detail-value">${data.visitorGender || ''}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">婚姻状态：</span>
                            <span class="detail-value">${getEnumText(EnumConverter.maritalStatus, data.maritalStatus)}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">有无子女：</span>
                            <span class="detail-value">${getEnumText(EnumConverter.hasChildren, data.hasChildren)}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">
                        <i class="fa fa-comment"></i>问题描述
                    </div>
                    <div class="detail-content">
                        <div class="detail-item" style="flex-direction: column; align-items: flex-start;">
                            <span class="detail-label" style="margin-bottom: 8px;">问题摘要：</span>
                            <span class="detail-value" style="white-space: pre-wrap; word-break: break-word; text-align: left; margin-left: 0; width: 100%;">${data.problemSummary || '暂无描述'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">咨询关键词：</span>
                            <span class="detail-value">
                                ${data.consultationKeywords ? data.consultationKeywords.split(',').map(keyword => 
                                    `<span class="keyword-badge">${keyword.trim()}</span>`
                                ).join('') : '暂无关键词'}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">
                        <i class="fa fa-exclamation-triangle"></i>特殊情况
                    </div>
                    <div class="detail-content">
                        <div class="detail-item">
                            <span class="detail-label">是否职场问题：</span>
                            <span class="detail-value">${data.isWorkplaceIssue === 1 ? '是' : '否'}</span>
                        </div>
                        ${data.isWorkplaceIssue === 1 ? `
                        <div class="detail-item">
                            <span class="detail-label">职场问题描述：</span>
                            <span class="detail-value">${data.workplaceDescription || ''}</span>
                        </div>
                        ` : ''}
                        <div class="detail-item">
                            <span class="detail-label">是否有心理风险：</span>
                            <span class="detail-value">${data.hasPsychologicalRisk === 1 ? '有' : '无'}</span>
                        </div>
                        ${data.hasPsychologicalRisk === 1 ? `
                        <div class="detail-item">
                            <span class="detail-label">风险等级：</span>
                            <span class="detail-value">
                                <span class="risk-badge ${getRiskClass(data.riskLevel)}">${getEnumText(EnumConverter.riskLevel, data.riskLevel)}</span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">风险描述：</span>
                            <span class="detail-value">${data.riskDescription || ''}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">
                        <i class="fa fa-lightbulb-o"></i>后续建议
                    </div>
                    <div class="detail-content">
                        <div class="detail-item">
                            <span class="detail-label">建议类型：</span>
                            <span class="detail-value">${getEnumText(EnumConverter.followUpSuggestion, data.followUpSuggestion)}</span>
                        </div>
                        ${data.followUpSuggestion === 4 ? `
                        <div class="detail-item">
                            <span class="detail-label">其他建议：</span>
                            <span class="detail-value">${data.otherSuggestion || ''}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>

                <div class="detail-section">
                    <div class="detail-title">
                        <i class="fa fa-clock-o"></i>时间信息
                    </div>
                    <div class="detail-content">
                        <div class="detail-item">
                            <span class="detail-label">创建时间：</span>
                            <span class="detail-value">${data.createTime || ''}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取风险等级样式类
        function getRiskClass(riskLevel) {
            switch (riskLevel) {
                case 0: return 'none';
                case 1: return 'low';
                case 2: return 'medium';
                case 3: return 'high';
                default: return 'none';
            }
        }

        // 新增个案
        function addCase() {
            location.href = "/app/counseling/add";
        }

        // 编辑个案
        function editCase(id) {
            location.href = "/app/counseling/update?id=" + id;
        }

        // 删除个案
        function deleteCase(id, name) {
            layer.open({
                content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>确定要删除个案"' + name + '"吗？'
                , btn: ['确定', '取消']
                , shadeClose: false
                , yes: function (index) {
                    layer.close(index);
                    $.post("/counselingroom/consultationcase/batch_del", {ids: id}, function (res) {
                        if (res.resultCode === 200) {
                            layer.open({
                                content: '<img src="/static/images/success.png" width="25" class="mr-1"/>删除成功'
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                            // 重新加载列表
                            currentPage = 1;
                            loadCaseList();
                        } else {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + (res.resultMsg || '删除失败')
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                        }
                    });
                }
            });
        }
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            预约咨询
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent mt-3">
            <!-- item list -->
            <div class="itemList">
                <th:block th:if="${not #lists.isEmpty(counselors) and counselors.size() gt 0}">
                    <th:block th:each="counselor:${counselors}">
                        <div class="item mt-3 mb-3">
                            <div class="image">
                                <a th:href="@{/app/counseling/counselorinfo(userId=${counselor.userId})}">
                                    <img th:if="${counselor.headPic eq null or counselor.headPic eq ''}" th:src="@{/static/images/user.png}" class="rounded-circle" />
                                    <img th:unless="${counselor.headPic eq null or counselor.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${counselor.headPic}|" class="rounded-circle" />
                                 </a>
                            </div>
                            <div class="text">
                                <div class="pb-1 font15"><a th:href="@{/app/counseling/counselorinfo(userId=${counselor.userId})}" class="text-dark" th:text="${counselor.realName}"></a></div>
                                <div class="text-muted font12">
                                    擅长方向：
                                    <th:block th:if="${counselor.counselorInfo.beGoodAt.length >40}" th:text="${#strings.substring(counselor.counselorInfo.beGoodAt,0,40)}" />
                                    <th:block th:unless="${counselor.counselorInfo.beGoodAt.length >40}" th:text="${counselor.counselorInfo.beGoodAt}" />
                                </div>
                            </div>
                        </div>
                        <div class="divider dashed"></div>
                    </th:block>
                </th:block>
                <th:block th:unless="${not #lists.isEmpty(counselors) and counselors.size() gt 0}">
                    <div class="splashBlock">
                        <div class="mb-3 mt-3">
                            <img class="nodata-img" th:src="@{/static/images/nodata.png}" alt="">
                        </div>
                        <div class="sectionTitle text-center">
                            <div class="title">
                            </div>
                            <div class="lead">
                                没有咨询师数据！
                            </div>
                        </div>
                    </div>
                </th:block>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js" />
</body>
</html>
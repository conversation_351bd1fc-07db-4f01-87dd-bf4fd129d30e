<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/css/vendor/fullcalendar/fullcalendar.min.css}" rel="stylesheet" type="text/css" />
    <style type="text/css">
        .fc button, .fc table, body .fc {
            font-size: 0.7em
        }

        .fc-center h2 {
            font-size: 13px;
            font-weight: normal;
            margin-top: 20px;
        }

        .fc-view {
            margin-top: 30px;
            font-size: 12px;
        }

        .none-border .modal-footer {
            border-top: none
        }

        .fc-toolbar {
            margin: 15px 0 5px 0
        }

        .fc-day-grid-event .fc-time {
            font-weight: 700
        }

        .fc-day {
            background: 0 0
        }

        .fc-toolbar .fc-state-active, .fc-toolbar .ui-state-active, .fc-toolbar .ui-state-hover, .fc-toolbar button:focus, .fc-toolbar button:hover {
            z-index: 0
        }

        .fc th.fc-widget-header {
            background: #edf1f5;
            font-size: 13px;
            line-height: 20px;
            padding: 10px 0;
            text-transform: uppercase
        }

        .fc-unthemed .fc-divider, .fc-unthemed .fc-popover, .fc-unthemed .fc-row, .fc-unthemed tbody, .fc-unthemed td, .fc-unthemed th, .fc-unthemed thead {
            border-color: #f1f3fa
        }

        .fc-button {
            background: #edf1f5;
            border: none;
            color: #adb5bd;
            text-shadow: none;
            text-transform: capitalize;
            -webkit-box-shadow: none;
            box-shadow: none;
            border-radius: 3px;
            margin: 0 3px;
            padding: 6px 12px;
            height: auto
        }

        .fc-text-arrow {
            font-family: inherit;
            font-size: 1rem
        }

        .fc-state-hover {
            background: #e3eaef
        }

        .fc-state-highlight {
            background: #dee2e6
        }

        .fc-state-active, .fc-state-disabled, .fc-state-down {
            background-color: #727cf5;
            color: #fff;
            text-shadow: none
        }

        .fc-cell-overlay {
            background: #dee2e6
        }

        .fc-unthemed td.fc-today {
            background: #f3f6f8
        }

        .fc-unthemed .fc-divider, .fc-unthemed .fc-list-heading td, .fc-unthemed .fc-popover .fc-header {
            background: #f1f3fa
        }

        .fc-event {
            border-radius: 2px;
            border: none;
            cursor: move;
            font-size: .8125rem;
            margin: 5px 7px;
            padding: 5px 5px;
            text-align: center
        }

        .external-event {
            cursor: move;
            margin: 10px 0;
            padding: 8px 10px;
            color: #fff
        }

        .fc-basic-view td.fc-week-number span {
            padding-right: 8px
        }

        .fc-basic-view td.fc-day-number {
            padding-right: 8px
        }

        .fc-basic-view .fc-content {
            color: #fff
        }

        .fc-time-grid-event .fc-content {
            color: #fff
        }

        .fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
            float: right;
            height: 20px;
            width: 20px;
            text-align: center;
            line-height: 20px;
            background-color: #f1f3fa;
            border-radius: 50%;
            margin: 5px;
            font-size: 11px
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            预约咨询
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule">
        <div class="appContent text-center pl-1 pr-1">
            <div class="sectionTitle mb-1">
                <div class="text-muted"></div>
                <div class="title">
                    <h3 class="font14"><i class="fa fa-calendar mr-1"></i>预约时间</h3>
                </div>
            </div>
            <div id="calendar" class="mb-1"></div>
            <div class="alert border border-primary mt-3 bg-light">
                <ul class="list-unstyled text-left">
                    <li class="text-muted mb-1 font12">
                        排班表默认显示当天的咨询师排班信息，可以按月、周、天切换查看。
                    </li>
                    <li class="text-muted mb-1 font12">
                        双击排班表中的咨询师排班信息可以在线预约。
                    </li>
                    <li class="text-muted mb-1 font12">
                        <ul class="list-unstyled">
                            <li><i class="fa fa-circle mr-1 text-secondary" aria-hidden="true"></i>表示该次排班时间已经过期不可操作；</li>
                            <li><i class="fa fa-circle mr-1 text-success" aria-hidden="true"></i>表示排班有效；</li>
                            <li><i class="fa fa-circle mr-1 text-primary" aria-hidden="true"></i>表示该次排班已经有预约；</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/fullcalendar/fullcalendar.min.js}"></script>
    <script th:src="@{/static/js/plugins/fullcalendar/fullcalendar-zh-cn.js}"></script>
    <script type="text/javascript">
        let counselorId = getUrlParam('counselorId');
        $(function () {
            /*初始化 calendar*/
            $('#calendar').fullCalendar({
                timeFormat: 'HH:mm',
                defaultView: 'listWeek',
                header: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'month,agendaWeek,agendaDay,listWeek'
                },
                navLinks: true,
                editable: false,
                droppable: false,
                timezone: 'local',
                events: function (start, end, timezone, callback) {
                    let jsonObj = {};
                    jsonObj.counselorId = counselorId;
                    layer.open({type: 2, content: '请稍后…', shadeClose: false});
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/scheduling/get_for_order_calendar',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll();
                            callback(res);
                        }
                    });
                },
                eventClick: function (eventObj) {
                    layer.open({
                        content: '<div class="text-left">您的预约信息如下：<br>预约的咨询师：'+eventObj.title+'。<br>预约时间段：'+ eventObj.start.format('YYYY-MM-DD HH:mm:ss')+' - '+ eventObj.end.format('YYYY-MM-DD HH:mm:ss')+'。<br>确定预约吗？点击确定继续操作。</div>'
                        , btn: ['确定', '取消']
                        , shadeClose: false
                        , yes: function (index) {
                            layer.close(index);
                            location.href = "/app/counseling/order?" + encodeURIComponent("id=" + eventObj.id + "&counselor=" + eventObj.title + "&start=" + eventObj.start.format('YYYY-MM-DD HH:mm:ss') + "&end=" + eventObj.end.format('YYYY-MM-DD HH:mm:ss') + "");
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            在线咨询
        </div>
        <div class="right">
            <a href="#" id="btnClose" class="icon">
                <i class="fa fa-power-off"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <div class="conversation-list">

            </div>
            <div class="chatFooter mb-5">
                <div class="leftButton">
                    <button id="btnHistory" type="button" class="btn btn-secondary btn-icon">
                        <i class="fa fa-commenting-o"></i>
                    </button>
                </div>
                <form class="formArea">
                    <input id="message" type="text" class="form-control">
                    <button id="btnSend" type="button" class="btn btn-primary btn-icon">
                        <i class="fa fa-paper-plane-o"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
    <!-- appCapsule -->
    <div id="historyMessageModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header modal-colored-header bg-primary">
                    <h5 class="modal-title font16 text-white">历史咨询聊天记录</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="history-message-list">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-link" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script type="text/javascript">
        let currentUserId = [[${user.userId}]];
        let currentUserRoleId = [[${user.role.roleId}]];
        let orderId = getUrlParam("id");
        let order = {};
        let chatMessage = {};
        let endTime = "";
        let t;
        let socket;
        $(function () {
            getOrderInfo();
            $('#btnSend').click(function () {
                if ($.trim($('#message').val()) === "") {
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>说点什么吧！'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                    return;
                }
                chatMessage.message = $.trim($('#message').val());
                send(JSON.stringify(chatMessage));
                $('#message').val('').focus();
            });
            $("#btnClose").click(function () {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>确定要结束咨询吗？'
                    , btn: ['确定', '取消']
                    , shadeClose: false
                    , yes: function (index) {
                        closeConversation();
                    }
                });
            });
            $("#btnHistory").click(function(){
                $.ajax({
                    url: '/counselingroom/counseling/getCounselingRecordContent',
                    type: 'GET',
                    data: { orderId: orderId },
                    dataType: "json",
                    async: true,
                    success: function (res) {
                        let wrapper = $("#history-message-list");
                        let msg_str = "";
                        if(res.length === 0){
                            msg_str += '<span><i class="fa fa-info-circle mr-1 ml-2 mt-2"></i>还没有咨询消息</span>';
                        }
                        else{
                            $.each(res, function (i, item) {
                                msg_str += '<li class="clearfix ml-0 mb-2 list-unstyled">';
                                msg_str += '<div class="conversation-text"><div class="ctext-wrap"><div class="text-muted"><span class="text-primary mr-2">' + item.realName + '</span> <span class="font-weight-light">' + item.sendTime + '</span></div><p class="text-justify font-12">' + item.counselingContent+ '</p></div></div>';
                                msg_str += '</li>';
                            });
                        }
                        wrapper.empty();
                        wrapper.append(msg_str);
                        $("#historyMessageModal").modal();
                    }
                })
            });
        });
        //获取预约信息
        let getOrderInfo = function () {
            layer.open({type: 2, content: '请稍后…', shadeClose: false});
            $.ajax({
                url: "/counselingroom/counseling/get_order",
                type: 'GET',
                data: { id: orderId },
                dataType: "json",
                async: true,
                success: function (res) {
                    layer.closeAll();
                    order = res;
                    if(currentUserId != order.visitorId && currentUserId != order.counselorId){
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>您不是该预约的拥有者，请确认您的操作！'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: false
                        });
                        return;
                    }
                    endTime = order.endTime;
                    chatMessage.orderId = orderId;
                    if (currentUserRoleId === 3) {
                        chatMessage.userId = order.visitorId;
                        chatMessage.realName = order.visitorName;
                        chatMessage.headPic = order.visitorHeadPic;
                        chatMessage.toUserId = order.counselorId;
                    }
                    else {
                        chatMessage.userId = order.counselorId;
                        chatMessage.realName = order.counselorName;
                        chatMessage.headPic = order.counselorHeadPic;
                        chatMessage.toUserId = order.visitorId;
                    }
                    //建立websocket连接
                    if(!window.WebSocket){
                        window.WebSocket = window.MozWebSocket;
                    }
                    if(window.WebSocket){
                        let currentUserName;
                        if(currentUserId === order.visitorId){
                            currentUserName = order.visitorName;
                        }
                        if(currentUserId ===order.counselorId){
                            currentUserName = order.counselorName;
                        }
                        socket = new WebSocket("ws://localhost:8889/ws");
                        socket.onmessage = function(event){
                            let res = JSON.parse(event.data);
                            let wrapper = $('.conversation-list');
                            let conversation_str = "";
                            let headPic = res.headPic === "" ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" +  res.headPic;
                            res.sendTime = new Date().format('yyyy-MM-dd hh:mm:ss');
                            if (currentUserId=== res.userId) {
                                conversation_str += '<div class="chatItem user"><img src="' + headPic + '" alt="" class="avatar"> ';
                            }
                            else {
                                conversation_str += '<div class="chatItem"><img src="' + headPic + '" alt="" class="avatar">';
                            }
                            conversation_str += '<div class="content"><div class="bubble">' + res.message + '</div><footer>' + res.sendTime + '</footer></div></div>';
                            wrapper.append(conversation_str);
                            $(".conversation-list").scrollTop($(".conversation-list").prop('scrollHeight'));
                        };
                        socket.onopen = function(event){
                            layer.open({
                                content: currentUserName+ '加入了聊天！'
                                , skin: 'msg'
                                , time: 2
                            });
                            chatMessage.message='【'+currentUserName +'】加入了咨询对话！';
                            send(JSON.stringify(chatMessage));
                        };
                        socket.onclose = function(event){
                            $.NotificationApp.send("提醒：", "【" + currentUserName + "】离开了咨询对话！", "bottom-right", "rgba(0,0,0,0.2)", "warning");
                            chatMessage.message='【'+currentUserName +'】离开了咨询对话！';
                            send(JSON.stringify(chatMessage));
                        };
                    }else{
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>您的浏览器不支持WebSocket协议！'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                    t = setInterval(function () { countDown(endTime) }, 1000);
                }
            });
        };
        let send = function(user){
            if(!window.WebSocket){return;}
            if(socket.readyState === WebSocket.OPEN){
                socket.send(user);
            }else{
                layer.open({
                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>建立通讯连接失败！'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                });
            }
        }
        //倒计时
        let countDown=function(date) {
            let str = '', s;
            let sec = (new Date(date.replace(/-/ig, '/')).getTime() - new Date().getTime()) / 1000;
            if (sec > 0) {
                s = { '天': sec / 24 / 3600, '小时': sec / 3600 % 24, '分': sec / 60 % 60, '秒': sec % 60 };
                for (i in s) {
                    if (Math.floor(s[i]) > 0) str += Math.floor(s[i]) + i;
                }
                if (Math.floor(sec) === 0) { str = '0秒'; }
                $('#timer').html('<i class="fa fa-clock-o mr-1"></i>咨询剩余时间 ' + str + '</u>');
            }
            else {
                clearInterval(t);
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1" />咨询结束时间已到，如果还有疑问请另行安排咨询时间！',
                    btn: '确定',
                    shadeClose: false,
                    yes: function () {
                        closeConversation();
                        location.href = "/app/counseling/online";
                    }
                });
            }
        }
        //结束咨询
        let closeConversation = function () {
            $.post("/counselingroom/counseling/update_state", { id: orderId, state: 3 }, function (res) {
                if (res.resultCode === 200) {
                    location.href = "/app/counseling/online";
                }
                else {
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                }
            }, 'json');
        };
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            在线咨询
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent">
            <div class="listView detailed" id="order-list">

            </div>
            <div class="splashBlock hide">
                <div class="mb-3 mt-3">
                    <img th:src="@{/static/images/app_myorder_banner.jpg}" alt="" class="img-fluid">
                </div>
                <div class="sectionTitle text-center">
                    <div class="title">
                    </div>
                    <div class="lead">
                        <i class="fa fa-info-circle mr-1"></i>当前时间内没有任何可以进行咨询的预约。<a th:href="@{/app/counseling/index}" class="btn btn-outline-primary mt-2">预约咨询</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}" />
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let currentUserRole = [[${user.role.roleId}]];
        let currentUserId = [[${user.userId}]];
        $(function(){
            init();
        });
        let init = function () {
            let jsonObj = {};
            jsonObj.state = 1;
            jsonObj.visitorId = (currentUserRole === 3 || currentUserRole ===4)?currentUserId: 0;
            jsonObj.counselorId = (currentUserRole !== 3 && currentUserRole !==4) ? currentUserId : 0 ;
            $.ajax({
                type: 'POST',
                url: '/counselingroom/counseling/online_order',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    let wrapper = $("#order-list");
                    let orderList = "";
                    if (res.length > 0) {
                        for (let i = 0; i < res.length; i++) {
                            orderList += '<div class="card text-white bg-warning mb-0 mt-2 ml-1 mr-1">';
                            orderList += '<div class="card-body">';
                            orderList += '<div class="media">';
                            let headPic = res[i].counselorHeadPic === "" ? "/static/images/user.png" : "/static/upload/avatar/thumbnail/" + res[i].counselorHeadPic;
                            orderList += '<img src="' + headPic +'" alt="" class="mr-3 d-sm-block avatar-sm imageBlock large rounded-circle">'
                            orderList += '<div class="media-body">';
                            orderList += '<h5 class="mb-1 mt-0"><span class="font-weight-bold text-white font14">' + res[i].counselorName + '</span></h5>';
                            orderList += '<p class="font12 font-weight-bold">咨询时间：' + res[i].startTime + ' - ' + res[i].endTime +'</p>';
                            orderList += "<button class='btn btn-outline-light' type='button' onclick='start(" + JSON.stringify(res[i]) + ")'>进入咨询</button>";
                            orderList += '</div>';
                            orderList += '</div>';
                            orderList += '</div>';
                            orderList += '</div>';
                        }
                    }
                    else {
                        $(".splashBlock").removeClass('hide').addClass('show');
                    }
                    wrapper.empty();
                    wrapper.append(orderList);
                }
            });
        };
        let start = function (obj) {
            if (obj.startTime> getDateNowFormat()) {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>咨询开始时间未到，请耐心等待！'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                });
                return;
            }
            let isOrder;
            if (currentUserRole === 3) {
                if (currentUserId != obj.visitorId) {
                    isOrder = false;
                }
                else {
                    isOrder = true;
                }
            }
            else {
                if (currentUserId != obj.counselorId) {
                    isOrder = false;
                }
                else {
                    isOrder = true;
                }
            }
            if (!isOrder) {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>信息验证失败，您不是该预约的拥有者！'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 2
                });
            }
            else {
                location.href = "/app/counseling/online_counseling?id=" + obj.id;
            }
        };
    </script>
</th:block>
</body>
</html>
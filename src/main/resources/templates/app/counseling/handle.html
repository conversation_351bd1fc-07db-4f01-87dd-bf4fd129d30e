<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            处理预约
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <form id="frmHandle">
                <div class="form-group">
                    <label>预约咨询师</label>
                    <input type="text" readonly class="form-control" id="counselor" value="">
                </div>
                <div class="form-group visitor">
                    <label>来访者</label>
                    <input type="text" class="form-control" id="visitor" name="visitor" autocomplete="off" readonly />
                </div>
                <div class="form-group">
                    <label>咨询时间段</label>
                    <input type="text" readonly class="form-control" id="counselingDate" value="">
                </div>
                <div class="form-group">
                    <label>咨询方式</label>
                    <input type="text" readonly class="form-control" id="counselingWay" value="">
                </div>
                <div class="form-group">
                    <label>咨询问题类型</label>
                    <input type="text" readonly class="form-control" id="counselingType" value="">
                </div>
                <div class="form-group">
                    <label>咨询问题描述</label>
                    <textarea class="form-control" name="description" id="description" rows="5" readonly></textarea>
                </div>
                <div class="form-group selfComment">
                    <label>自我分析</label>
                    <textarea class="form-control" name="selfComment" id="selfComment" rows="5" readonly></textarea>
                </div>
                <div class="form-group isPoint">
                    <label>是否重点个案</label>
                    <div class="col-md-9 form-inline">
                        <div class="custom-control custom-radio mr-2">
                            <input type="radio" id="isPoint-yes" name="isPoint" class="custom-control-input" value="是">
                            <label class="custom-control-label" for="isPoint-yes">是</label>
                        </div>
                        <div class="custom-control custom-radio">
                            <input type="radio" id="isPoint-no" name="isPoint" class="custom-control-input" value="否" checked>
                            <label class="custom-control-label" for="isPoint-no">否</label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="counselingItem">咨询类型：</label>
                    <select class="form-control" name="counselingItem" id="counselingItem" style="width:100%;">
                    </select>
                </div>
                <div class="form-group">
                    <label for="state">处理状态：</label>
                    <select id="state" name="state" class="form-control">
                        <option value="">请选择</option>
                        <option value="0">未处理</option>
                        <option value="1">接受预约</option>
                        <option value="2">取消预约</option>
                        <option value="3">咨询已结束</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="handleInfo">处理意见：</label>
                    <textarea class="form-control" name="handleInfo" id="handleInfo" rows="5" maxlength="100" placeholder="填写咨询师处理预约意见，限100字以内。"></textarea>
                </div>
                <div>
                    <input type="submit" class="btn btn-primary btn-block" value="保存" id="btnSave">
                    <input type="hidden" id="hidUserID" value="0" />
                </div>
            </form>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script type="text/javascript">
        let id = getUrlParam('id');
        let initForm = function () {
            $.ajax({
                url: "/counselingroom/counseling/get_order",
                type: 'GET',
                data: { id: id },
                dataType: "json",
                async: true,
                success: function (res){
                    $("#counselor").val(res.counselorName);
                    $("#visitor").val(res.visitorName);
                    $("#counselingDate").val(res.startTime+ ' 至 ' + res.endTime.substring(11, 19));
                    let counselingWay = '';
                    if (res.counselingWay === 1)
                        counselingWay = "网上咨询";
                    if (res.counselingWay === 2)
                        counselingWay = "面询";
                    if (res.counselingWay === 3)
                        counselingWay = "电话咨询";
                    $("#counselingWay").val(counselingWay);
                    $("#counselingType").val(res.counselingType);
                    if (res.isPoint === 0) {
                        $("#isPoint-no").attr("checked", true);
                    }
                    else {
                        $("#isPoint-yes").attr("checked", true);
                    }
                    $("#selfComment").val(res.selfComment);
                    $("#description").val(res.description);
                    initSelect("#counselingItem", "/counselingroom/counselingitem/get_for_select", { schedulingId: res.schedulingId }, res.counselingItemName);
                }
            });
        };
        $(function () {
            initSelect("#counselingType", "/counselingroom/counselingtype/get_for_select", "");
            initForm();
            $("#frmHandle").validate({
                rules: {
                    state: { required: true }
                },
                messages: {
                    state: { required: "请选择处理状态" }
                },
                submitHandler: function () {
                    let jsonObj = {
                        id: id,
                        state: $("#state").val(),
                        handleInfo: $.trim($("#handleInfo").val()),
                        counselingItemId: $("#counselingItem").val()
                    };
                    $("#btnSave").val("保存中…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/counseling/update_order',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />' + res.resultMsg,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function () {
                                        location.href = "/app/counseling/order_record";
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
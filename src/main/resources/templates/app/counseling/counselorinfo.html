<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="#" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">咨询师介绍</div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-1">
            <div class="sectionTitle text-center mb-2">
                <img th:if="${counselor.headPic eq null or counselor.headPic eq ''}" th:src="@{/static/images/user.png}" class="imageBlock circle xlarge mb-2 mt-2" />
                <img th:unless="${counselor.headPic eq null or counselor.headPic eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${counselor.headPic}|" class="imageBlock circle xlarge mb-2 mt-2" />
                <div class="title">
                    <h3 class="font16" th:text="${counselor.realName}"></h3>
                </div>
            </div>
            <ul class="nav nav-tabs nav-bordered nav-justified" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab">简介</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab">评论</a>
                </li>
            </ul>
            <div class="tab-content mt-3" id="myTabContent">
                <div class="tab-pane fade show active text-left" id="home" role="tabpanel">
                    <div class="contentBox long mb-2">
                        <h5 class="title font14">个人介绍</h5>
                        <div class="contentBox-body font14 pl-3 pr-3">
                            <th:block th:if="${not #strings.isEmpty(counselor.counselorInfo.counselorIntro) }" th:utext="${counselor.counselorInfo.counselorIntro}" />
                            <th:block th:unless="${not #strings.isEmpty(counselor.counselorInfo.counselorIntro) }" th:utext="暂无" />
                        </div>
                    </div>
                    <div class="contentBox long mb-2">
                        <h5 class="title font14">擅长方向</h5>
                        <div class="contentBox-body font14 pl-3 pr-3" th:utext="${counselor.counselorInfo.beGoodAt}">
                        </div>
                    </div>
                    <div class="text-center">
                        <button id="btnCounseling" type="button" class="btn btn-outline-info rounded">
                            找Ta咨询
                        </button>
                    </div>
                </div>
                <div class="tab-pane fade text-left" id="profile" role="tabpanel">
                    <div class="comments pl-2 pr-2" style="max-height: 200px; overflow:auto;">
                        <div th:if="${(not #lists.isEmpty(counselor.comments)) and counselor.comments.size() gt 0 }">
                            <th:block th:each="comment:${counselor.comments}">
                                <div class="item">
                                    <div class="image">
                                        <img th:if="${comment.headImg  eq null or comment.headImg  eq ''}" th:src="@{/static/images/user.png}" class="avatar" />
                                        <img th:unless="${comment.headImg  eq null or comment.headImg  eq ''}" th:src="|@{/static/upload/avatar/thumbnail/}${comment.headImg }|" class="avatar" />
                                    </div>
                                    <div class="content">
                                        <strong class="font14" th:text="|${#strings.substring(comment.realName,0,1)}**|"></strong>
                                        <div class="text font13" th:utext="${comment.commentContent}">
                                        </div>
                                        <footer th:text="${#dates.format(comment.commentDate,'yyyy-MM-dd HH:mm:ss')}"></footer>
                                    </div>
                                </div>
                            </th:block>
                        </div>
                        <div th:unless="${(not #lists.isEmpty(counselor.comments)) and counselor.comments.size() gt 0 }">
                            <div class="splashBlock">
                                <div class="mb-3 mt-3">
                                    <img th:src="@{/static/images/nocomment.png}" alt="draw" class="imageBlock xlarge">
                                </div>
                                <div class="sectionTitle text-center">
                                    <div class="lead">
                                        <i class="fa fa-info-circle mr-1"></i>暂无评论
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- * comments -->

                    <div class="divider mt-3 mb-3"></div>
                    <th:block th:if="${#strings.isEmpty(user)}">
                        <div class="alert alert-primary mb-4 font13">您尚未登录，登录后可以发表评论！<a th:href="@{/app/account/mobile_login}">前往登录</a></div>
                    </th:block>
                    <th:block th:unless="${#strings.isEmpty(user)}">
                        <form id="frmComment" action="#" method="post">
                            <div class="form-group">
                                <textarea id="content" name="content" class="form-control" rows="4" placeholder="写下您的评论，限制100字..." maxlength="100"></textarea>
                            </div>
                            <input id="btnSave" type="submit" class="btn btn-primary btn-large btn-block" value="发表" />
                        </form>
                    </th:block>
                </div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script type="text/javascript">
        let userId = getUrlParam('userId');
        $(function () {
            $("#btnCounseling").click(function () {
                location.href = "/app/counseling/scheduling?counselorId=" + userId;
            });
            $("#frmComment").validate({
                rules: {
                    content: { required: true, maxlength: 100 }
                },
                messages: {
                    content: { required: "请填写评论内容", maxlength: "超过了评论字数限制" }
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.functionType=4;
                    jsonObj.productId = userId;
                    jsonObj.commentContent = $("#content").val();
                    $("#btnSave").val("请稍后…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        url: '/comment/comment/add',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            $("#content").val("");
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1"/>发表成功！'
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/daterangepicker/daterangepicker.css}" rel="stylesheet" type="text/css" />
    <style type="text/css">
        .daterangepicker {
            margin-bottom:100px;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            预约咨询
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <form id="frmOrder" class="form-horizontal" method="post">
                <div class="form-group">
                    <label class="mr-1">咨询师姓名</label>
                    <span id="counselor" class="badge badge-outline-primary"></span>
                </div>
                <div class="form-group">
                    <label>咨询时间</label>
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                        </div>
                        <input type="text" class="form-control" value="" id="schedulingDate" name="schedulingDate" autocomplete="off" readonly>
                    </div>
                    <input type="hidden" id="hidStartTime" name="hidStartTime" value="0" />
                    <input type="hidden" id="hidEndTime" name="hidEndTime" value="0" />
                </div>
                <div class="form-group">
                    <label for="counselingWay">咨询方式</label>
                    <select class="form-control" id="counselingWay" name="counselingWay">
                        <option value="">请选择</option>
                        <option value="1">网上咨询</option>
                        <option value="2">面询</option>
                        <option value="3">电话咨询</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="counselingType">咨询问题类型</label>
                    <select class="form-control" name="counselingType" id="counselingType">
                    </select>
                </div>
                <div class="form-group">
                    <label for="description">咨询问题描述</label>
                    <textarea class="form-control" name="description" id="description" rows="5" placeholder="您想咨询哪些方面的问题，请尽可能详细的说明。字数限制在100字以内。" maxlength="100"></textarea>
                </div>
                <div class="form-group selfComment">
                    <label for="selfComment">自我分析</label>
                    <textarea class="form-control" name="selfComment" id="selfComment" rows="5" placeholder="请列出您对自己现在面临问题的原因是怎么看的，近期是否遇到重大人生事件等。字数限制在100字以内。 " maxlength="100"></textarea>
                </div>
                <div class="form-group isPoint">
                    <label for="isPoint-yes">是否重点个案</label>
                    <div class="col-md-9 form-inline">
                        <div class="custom-control custom-radio mr-2">
                            <input type="radio" id="isPoint-yes" name="isPoint" class="custom-control-input" value="1">
                            <label class="custom-control-label" for="isPoint-yes">是</label>
                        </div>
                        <div class="custom-control custom-radio">
                            <input type="radio" id="isPoint-no" name="isPoint" class="custom-control-input" value="0" checked>
                            <label class="custom-control-label" for="isPoint-no">否</label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="counselingItem">咨询类型</label>
                    <select class="form-control" name="counselingItem" id="counselingItem">
                    </select>
                </div>
                <div>
                    <input type="submit" class="btn btn-primary btn-lg btn-block" value="保存" id="btnSave">
                </div>
            </form>
            <input type="hidden" id="hidUserID" value="0" />
        </div>
    </div>
    <th:block th:insert="~{layouts/footHome}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/daterangepicker.js}"></script>
    <script type="text/javascript">
        let search = decodeURIComponent(window.location.search);
        function getSearchString(key, Url) {
            let str = Url;
            str = str.substring(1, str.length); // 获取URL中?之后的字符（去掉第一位的问号）
            // 以&分隔字符串，获得类似name=xiaoli这样的元素数组
            let arr = str.split("&");
            let obj = {};
            // 将每一个数组元素以=分隔并赋给obj对象
            for (let i = 0; i < arr.length; i++) {
                let tmp_arr = arr[i].split("=");
                obj[decodeURIComponent(tmp_arr[0])] = decodeURIComponent(tmp_arr[1]);
            }
            return obj[key];
        };
        let schedulingId = getSearchString('id', search);
        let counselor = getSearchString('counselor', search);
        let start = getSearchString('start', search);
        let end = getSearchString('end', search);
        let startTime = start, endTime = end;
        $(function () {
            $("#counselor").html(counselor);
            $("#counselingDate").val(start + ' - ' + end);
            initSelect("#counselingType", "/counselingroom/counselingtype/get_for_select", "");
            initSelect("#counselingItem", "/counselingroom/counselingitem/get_for_select", { schedulingId: schedulingId });
            initDateRangePicker();
            $("#hidStartTime").val(start);
            $("#hidEndTime").val(end);
            if ('[[${user.role.roleId}]]' === '3') {
                $(".visitor").hide();
                $(".isPoint").hide();
            }
            else {
                $(".selfComment").hide();
            }
            $("#frmOrder").validate({
                ignore:"hidden",
                rules: {
                    schedulingDate: { required: true },
                    counselingWay: { required: true },
                    counselingType: { required: true },
                    description: { required: true },
                    selfComment: { required: true },
                    counselingItem: { required: true }
                },
                messages: {
                    schedulingDate: { required: "请选择咨询时间" },
                    counselingWay: { required: "请选择咨询方式" },
                    counselingType: { required: "请选择咨询问题类型" },
                    description: { required: "请填写咨询内容" },
                    selfComment: { required: "请填写自我分析" },
                    counselingItem: { required: "请选择咨询类型" }
                },
                submitHandler: function () {
                    $("#btnSave").val("保存中……");
                    $("#btnSave").attr("Disabled", true);
                    let jsonObj = {};
                    jsonObj.schedulingId = schedulingId;
                    if ('[[${user.role.roleId}]]'=='3')
                        $("#hidUserID").val('[[${user.userId}]]');
                    jsonObj.visitorId = $("#hidUserID").val();
                    jsonObj.startTime = startTime;
                    jsonObj.endTime = endTime;
                    jsonObj.counselingTypeId = $("#counselingType").val();
                    jsonObj.counselingWay = $("#counselingWay").val();
                    jsonObj.isPoint = $('input[name="isPoint"]:checked').val();
                    jsonObj.selfComment = $("#selfComment").val();
                    jsonObj.description = $("#description").val();
                    jsonObj.counselingItemId = $("#counselingItem").val();
                    $.ajax({
                        type: 'POST',
                        url: '/counselingroom/counseling/add_order',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />' + res.resultMsg,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function () {
                                        layer.closeAll();
                                        location.href = "/app/counseling/order_record";
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });
        let initDateRangePicker = function () {
            let locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " - ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            $('#schedulingDate').daterangepicker({
                "locale": locale,
                "showDropdowns": true,
                "linkedCalendars": false,
                "timePicker": true,
                "timePickerIncrement": 1,
                "timePicker24Hour": true,
                "minDate": start,
                "maxDate": end,
                "drops": "down"
            }, function (start, end, label) {
                if ((start - end) == 0) {
                    let _end = new Date(end);
                    let year = _end.getFullYear();
                    let month = _end.getMonth();
                    let day = _end.getDate();
                    let hour = _end.getHours();
                    let min = _end.getMinutes();
                    let s = _end.getSeconds();
                    end = new Date(year, month, day, hour + 23, min + 59, s + 59);
                    startTime = start.format('YYYY-MM-DD HH:mm:ss');
                    endTime = end.format('YYYY-MM-dd hh:mm:ss');
                }
                else {
                    let startTime = start.format('YYYY-MM-DD HH:mm:ss');
                    let endTime = end.format('YYYY-MM-DD HH:mm:ss');
                }
                $("#schedulingDate").val(startTime + ' - ' + endTime);
                $("#hidStartTime").val(startTime);
                $("#hidEndTime").val(endTime);
            });
        };
    </script>
</th:block>
</body>
</html>
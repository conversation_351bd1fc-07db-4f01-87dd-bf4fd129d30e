<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>

    <style>
        /* 整体背景 */
        .appContent {
            background: #f8f9fa;
            min-height: 100vh;
        }

        /* 欢迎区域 */
        .welcome-section {
            background: linear-gradient(135deg, #727cf5 0%, #8b5fbf 100%);
            border-radius: 20px;
            padding: 32px 24px;
            margin-bottom: 24px;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }

        .welcome-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .welcome-subtitle {
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* 功能入口 */
        .function-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .function-item {
            background: white;
            border-radius: 16px;
            padding: 20px 16px;
            text-align: center;
            text-decoration: none;
            color: #333;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .function-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            text-decoration: none;
            color: #333;
        }

        .function-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 20px;
            color: white;
        }

        .function-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .function-desc {
            font-size: 12px;
            color: #8c8c8c;
        }

        /* 内容区块 */
        .content-section {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid #f0f0f0;
        }

        .section-header {
            padding: 20px 20px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 50px; /* 确保有足够的高度 */
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            line-height: 1.2; /* 调整行高 */
            display: flex;
            align-items: center; /* 文字垂直居中 */
        }

        .section-more {
            font-size: 13px;
            color: #727cf5;
            text-decoration: none;
        }

        .section-content {
            padding: 0 20px 16px;
        }
        /* 我的活动 */
        .activity-item {
            padding: 12px 0;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            align-items: center;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #727cf5, #8b5fbf);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 12px;
            font-size: 16px;
        }

        .activity-info {
            flex: 1;
        }

        .activity-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
            line-height: 1.3;
        }

        .activity-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 13px;
            color: #8c8c8c;
        }

        .activity-time {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .activity-status {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-completed {
            background: #e8f5e8;
            color: #4caf50;
        }

        .status-ongoing {
            background: #e3f2fd;
            color: #2196f3;
        }

        .status-upcoming {
            background: #fff3e0;
            color: #ff9800;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 24px 20px;
            color: #6c757d;
        }

        .empty-icon {
            font-size: 48px;
            color: #dee2e6;
            margin-bottom: 16px;
        }

        .empty-state h4 {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .empty-state p {
            font-size: 14px;
            margin: 0 0 16px 0;
            line-height: 1.5;
        }

        .empty-action {
            margin-top: 16px;
        }

        .empty-action .btn {
            background: linear-gradient(135deg, #727cf5, #8b5fbf);
            border: none;
            color: white;
            border-radius: 12px;
            padding: 10px 24px;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .empty-action .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(114, 124, 245, 0.3);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="pageTitle" th:text="${pageData.sysConfigDto.platformName}">
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent">
            <!-- 欢迎区域 -->
            <div class="welcome-section">
                <div class="welcome-title">欢迎使用心理健康服务平台</div>
                <div class="welcome-subtitle">为您提供专业、便捷、高效的心理健康服务</div>
            </div>

            <!-- 功能入口 -->
            <div class="function-grid">
                <a th:href="@{/app/measuring/all}" class="function-item">
                    <div class="function-icon" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52);">
                        <i class="fa fa-heartbeat"></i>
                    </div>
                    <div class="function-title">心理测评</div>
                    <div class="function-desc">专业测评</div>
                </a>
                <a th:href="@{/app/activity/my_activities}" class="function-item">
                    <div class="function-icon" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);">
                        <i class="fa fa-gift"></i>
                    </div>
                    <div class="function-title">心理活动</div>
                    <div class="function-desc">活动参与</div>
                </a>
                <a th:href="@{/app/counseling/my_cases}" class="function-item">
                    <div class="function-icon" style="background: linear-gradient(135deg, #45b7d1, #96c93d);">
                        <i class="fa fa-comment-o"></i>
                    </div>
                    <div class="function-title">心理个案</div>
                    <div class="function-desc">专业咨询</div>
                </a>
            </div>


            <!-- 我的活动 -->
            <div class="content-section">
                <div class="section-header mb-2 pb-3">
                    <div class="section-title">我的活动</div>
                    <!-- 只有在已登录且有活动数据时才显示"查看全部"链接 -->
                    <a th:if="${session.user != null and myActivities != null and not #lists.isEmpty(myActivities)}"
                       class="section-more" th:href="@{/app/activity/my_activities}">查看全部</a>
                </div>
                <div class="section-content">
                    <!-- 已登录且有活动数据 -->
                    <th:block th:if="${session.user != null and myActivities != null and not #lists.isEmpty(myActivities)}">
                        <th:block th:each="activity : ${myActivities}">
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fa fa-gift"></i>
                                </div>
                                <div class="activity-info">
                                    <a th:href="@{/app/activity/detail(activityId=${activity.id})}">
                                        <div class="activity-name" th:text="${activity.activityName}">活动名称</div>
                                    </a>
                                    <div class="activity-meta">
                                        <div class="activity-time">
                                            <i class="fa fa-clock-o"></i>
                                            <span th:text="${#dates.format(activity.startTime, 'MM月dd日')}">时间</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </th:block>
                    </th:block>

                    <!-- 未登录状态 -->
                    <th:block th:if="${session.user == null}">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fa fa-user-circle-o"></i>
                            </div>
                            <h4>请先登录</h4>
                            <p class="text-center">登录后即可查看您参与的心理活动</p>
                            <div class="empty-action">
                                <a th:href="@{/app/account/login}" class="btn">立即登录</a>
                            </div>
                        </div>
                    </th:block>

                    <!-- 已登录但无活动数据 -->
                    <th:block th:if="${session.user != null and (myActivities == null or #lists.isEmpty(myActivities))}">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fa fa-calendar-o"></i>
                            </div>
                            <h4>暂无活动记录</h4>
                            <p class="text-center">您还没有参与任何心理活动</p>
                        </div>
                    </th:block>
                </div>
            </div>


        </div>
    </div>
    <th:block th:insert="~{layouts/footHome.html}"/>
</th:block>
<th:block layout:fragment="common_js">
</th:block>
</body>
</html>
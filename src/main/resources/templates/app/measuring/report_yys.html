<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="pageTitle">
            《<span class="scaleName"></span>》测试报告
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="alert hide text-center text-success" id="alert">
            <div class="splashBlock">
                <div class="mb-3 mt-3">
                    <img th:src="@{/static/images/app/success.png}" alt="draw" width="100">
                </div>
                <div class="sectionTitle text-center">
                    <div class="lead font16 font-weight-bold" id="msg">
                    </div>
                </div>
            </div>
        </div>
        <div class="appContent pl-0 pr-0" id="report">
            <div class="title pt-2">
            </div>
            <div class="card mb-3 border-0 p-0">
                <div class="card-body font14">
                    <h5 class="card-title text-primary"><i class="fa fa-address-card-o mr-1"></i>基本信息</h5>
                    <ul class="list-unstyled">
                        <li class="pb15">姓名：<span id="realName"></span></li>
                        <li class="pb15">所属组织：<span id="fullStructName"></span></li>
                        <li class="pb15">测试项目：<span class="scaleName"></span></li>
                        <li class="pb15">日期：<span id="startDate"></span></li>
                        <li class="pb15">耗时：<span id="costTime"></span></li>
                    </ul>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <h5 class="card-title text-primary"><i class="fa fa-check-square-o mr-1"></i>测评定性</h5>
                    <div class="card-body white-bg p-0" id="explain-nature">

                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <h5 class="card-title text-primary"><i class="fa fa-table mr-1"></i>EPQ-RSC测评结果</h5>
                    <div class="col-12">
                        <div class="mb-2" id="epq-type"></div>
                    </div>
                    <div class="card-body white-bg" id="explain-epq">

                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>
                    <h5 class="card-title text-primary"><i class="fa fa-bar-chart mr-1"></i>图表分析</h5>
                    <div class="col-12 text-center">
                        <canvas id="epq-container" style="height: 200px; width: 300px "></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMeasuring}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let record, scale, user, scaleName;
        let factors = [], originalFactors = [], scores = [], standartScores = [], chartsImgArray = [];
        $(function () {
            initReport();
        });
        let initReport = function () {
            layer.open({
                type: 2
                , content: '报告加载中…'
                , shadeClose: false
            });
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                async: false,
                success: function (res) {
                    layer.closeAll();
                    if (res.resultCode != undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".appHeader").hide();
                            $("#msg").append(res.resultMsg === "" ? "您已经做完测试，如需了解详细结果请联系负责咨询师！" : res.resultMsg);
                            $("#alert").removeClass('hide').addClass('show');
                            return;
                        }
                    }
                    record = res[0].testRecord;
                    scale = res[0].testRecord.scale;
                    user = res[0].testRecord.user;
                    scaleName = scale.scaleName;
                    $(".scaleName").html(scaleName);
                    generalReport(res);
                    if (getUrlParam("savecharts") === 'true') {
                        saveCharts();
                    }
                }
            });
        };
        let generalReport = function (res) {
            getBaseInfo();
            getFactorInfo(res);
            $("#explain-nature").html(res[0].testRecord.interpretation.split('|')[0]);
            $("#epq-type").html(res[0].testRecord.interpretation.split('|')[2]);
            $("#explain-epq").html(res[0].testRecord.interpretation.split('|')[1]);
            getEPQChart(res);
        };
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName === "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structFullName);
            $("#startDate").html(record.startTime);
            $("#costTime").html(formatSeconds(record.timeInterval));
        };
        let getFactorInfo = function (res) {
            factors.splice(0);
            originalFactors.splice(0);
            scores.splice(0);
            standartScores.splice(0);
            $.each(res, function (index, content) {
                let factor = content.factor;
                factors.push(factor.factorName);
                originalFactors.push(content.originalScore);
                standartScores.push(content.abnormalValue);
                scores.push(content.score);
            });
        };
        let getEPQChart = function (res) {
            let chart = Highcharts.chart('epq-container', {
                chart: {
                    type: 'column'
                },
                title: {
                    text: "",
                    x: 0
                },
                credits: {
                    enabled: false
                },
                xAxis: {
                    categories: factors,
                    tickmarkPlacement: 'on',
                    lineWidth: 0
                },
                yAxis: {
                    gridLineInterpolation: 'polygon',
                    lineWidth: 0,
                    min: 0
                },
                tooltip: {
                    shared: true,
                    pointFormat: '<span style="color:{series.color}">{series.name}: <b>{point.y:,.0f}</b><br/>'
                },
                legend: {
                    enabled: false,
                    align: 'center',
                    verticalAlign: 'top',
                    y: 70,
                    layout: 'vertical'
                },
                series: [{
                    name: factors,
                    data: scores,
                    pointPlacement: 'on'
                }]
            });

            let charData = $('#epq-container').highcharts().getSVG();
            canvg('epq-container', charData);
            let chartsImg = $('#epq-container')[0].toDataURL("image/png");
            chartsImgArray.push(chartsImg);
        };
        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.ChartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                }
            });
        };
    </script>
</th:block>
</body>
</html>
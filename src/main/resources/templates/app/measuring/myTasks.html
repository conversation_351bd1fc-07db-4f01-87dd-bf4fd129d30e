<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        /* 任务卡片样式 */
        .task-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 246, 252, 0.95) 100%);
            border-radius: 18px;
            margin-bottom: 12px;
            border: 1px solid rgba(111, 66, 193, 0.12);
            box-shadow: 0 3px 15px rgba(111, 66, 193, 0.08);
            backdrop-filter: blur(15px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .task-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(111, 66, 193, 0.02) 0%, transparent 50%, rgba(139, 95, 191, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .task-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(111, 66, 193, 0.15);
            border-color: rgba(111, 66, 193, 0.25);
        }

        .task-card:hover::before {
            opacity: 1;
        }

        .task-card-body {
            padding: 16px 18px;
            position: relative;
            z-index: 2;
        }

        .task-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .task-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.3;
            margin-bottom: 2px;
            flex: 1;
        }

        .task-status {
            flex-shrink: 0;
            margin-left: 10px;
        }

        .task-content {
            margin-bottom: 12px;
        }

        .task-info {
            display: flex;
            flex-direction: column;
            gap: 3px;
            align-items: flex-start;
        }

        .task-info .info-item {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 13px;
            margin-bottom: 0;
            justify-content: flex-start;
            padding: 0;
            border-bottom: none;
        }

        .info-item i {
            margin-right: 6px;
            width: 14px;
            color: #6f42c1;
            font-size: 12px;
        }

        .time-text {
            color: #8b5fbf;
            font-weight: 500;
        }

        .task-btn {
            background: linear-gradient(135deg, #6f42c1 0%, #8b5fbf 100%);
            color: white;
            padding: 10px 18px;
            border-radius: 18px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(111, 66, 193, 0.3);
            border: 1px solid rgba(111, 66, 193, 0.2);
        }

        .task-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(111, 66, 193, 0.4);
            text-decoration: none;
            color: white;
            background: linear-gradient(135deg, #5a359a 0%, #6f42c1 100%);
        }

        .task-btn i {
            font-size: 14px;
        }

        .task-btn-text {
            flex: 1;
            text-align: left;
        }

        /* 空状态优化 */
        .splashBlock {
            padding: 30px 20px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 246, 252, 0.9) 100%);
            border-radius: 16px;
            border: 1px solid rgba(111, 66, 193, 0.1);
            backdrop-filter: blur(15px);
        }

        .splashBlock .nodata-img {
            opacity: 0.6;
            filter: grayscale(20%);
        }

        /* 加载更多按钮美化 */
        .load-more .btn {
            background: linear-gradient(135deg, rgba(111, 66, 193, 0.1) 0%, rgba(139, 95, 191, 0.1) 100%);
            border: 1px solid rgba(111, 66, 193, 0.3);
            color: #6f42c1;
            border-radius: 16px;
            padding: 10px 20px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .load-more .btn:hover {
            background: linear-gradient(135deg, #6f42c1 0%, #8b5fbf 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 3px 12px rgba(111, 66, 193, 0.25);
        }

        /* 响应式优化 */
        @media (max-width: 576px) {
            .task-header {
                margin-bottom: 8px;
            }

            .task-card-body {
                padding: 14px 16px;
            }

            .task-title {
                font-size: 14px;
                flex: 1;
                margin-right: 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            测评任务
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <div class="splashBlock empty-state hide">
                <div class="empty-icon">
                    <img src="/static/images/nodata.png" width="100" />
                </div>
                <h4>暂无测评任务</h4>
            </div>
            <div class="list hide">
                <div class="mt-2 mb-3">
                    <img th:src="@{/static/images/app_task_banner.jpg}" alt="image" class="imageBlock img-fluid rounded">
                </div>
                <div class="detail font16">
                </div>
                <div class="mt-2 mb-2 load-more hide"><a href="javascript:" class="btn btn-primary btn-block" id="loadMore">查看更多</a></div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMeasuring}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let currentPage = 0;
        let pageSize = 5;
        let getQueryCondition = function (currentPage) {
            let param = {};
            param.pageSize = pageSize;
            param.pageIndex = currentPage;
            param.taskKind = 1;
            return param;
        };

        $(function () {
            init(currentPage);
            
            $(".load-more").on('click', '#loadMore', function () {
                currentPage = currentPage + 1;
                init(currentPage);
            });
        });
        let init = function (currentPage) {
            layer.open({type: 2, content: '加载中'});
            $.ajax({
                type: "post",
                url: '/measuringroom/task/my_tasks',
                dataType: "json",
                contentType:'application/json',
                data: JSON.stringify(getQueryCondition(currentPage)),
                success: function (res) {
                    layer.closeAll();
                    if (res.data.length === 0) {
                        if (currentPage === 0) {
                            $(".list").removeClass('show').addClass('hide');
                            $(".splashBlock").removeClass('hide').addClass('show');
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                    }
                    else {
                        let str = "";
                        for (let i = 0; i < res.data.length; i++) {
                            let myTask = res.data[i];
                            let startDate = myTask.startTime;
                            let endDate = myTask.endTime;
                            let state = "";
                            let labels = "";
                            if (startDate > getDateNowFormat()) {
                                state = 1;
                                labels = '<span class="badge badge-outline-secondary">未开始</span>';
                            }
                            if (startDate <= getDateNowFormat() && getDateNowFormat() < endDate) {
                                state = 2;
                                labels = '<span class="badge badge-outline-success">进行中</span>';
                            }
                            if (getDateNowFormat() >= endDate) {
                                state = 3;
                                labels = '<span class="badge badge-outline-danger">已结束</span>';
                            }

                            str += '<div class="task-card">';
                            str += '  <div class="task-card-body">';
                            str += '    <div class="task-header">';
                            str += '      <div class="task-title">' + myTask.taskName + '</div>';
                            str += '      <div class="task-status">' + labels + '</div>';
                            str += '    </div>';
                            str += '    <div class="task-content">';
                            str += '      <div class="task-info">';
                            str += '        <div class="info-item">';
                            str += '          <i class="fa fa-play-circle"></i>';
                            str += '          <span class="time-text">开始：' + startDate + '</span>';
                            str += '        </div>';
                            str += '        <div class="info-item">';
                            str += '          <i class="fa fa-stop-circle"></i>';
                            str += '          <span class="time-text">结束：' + endDate + '</span>';
                            str += '        </div>';
                            let taskType = "";
                            if (myTask.taskType === 1) taskType = "限定";
                            if (myTask.taskType === 2) taskType = "非限定";
                            str += '        <div class="info-item">';
                            str += '          <i class="fa fa-tag"></i>';
                            str += '          <span>类型：' + taskType + '</span>';
                            str += '        </div>';
                            str += '      </div>';
                            str += '    </div>';
                            if (state === 2) {
                                str += '    <div class="task-actions">';
                                str += '      <a href="/app/measuring/scale?taskId=' + myTask.id + '&type=' + myTask.taskType + '" class="btn btn-primary btn-block btn-sm rounded">';
                                str += '        <span class="font13">开始测评</span>';
                                str += '      </a>';
                                str += '    </div>';
                            }
                            str += '  </div>';
                            str += '</div>';
                        }
                        $(".detail").append(str);
                        $(".list").removeClass('hide').addClass('show');
                        if (res.data.length < pageSize) {
                            $(".load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(".load-more").removeClass('hide').addClass('show');
                        }
                        $(".splashBlock").removeClass('show').addClass('hide');
                    }
                }
            });
        };
    </script>
</th:block>
</body>
</html>
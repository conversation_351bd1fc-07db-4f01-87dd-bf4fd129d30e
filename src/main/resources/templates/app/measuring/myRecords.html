<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        /* Tab 美化样式 */
        #statusTabs {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 246, 252, 0.95) 100%);
            border: 1px solid rgba(111, 66, 193, 0.15);
            box-shadow: 0 4px 20px rgba(111, 66, 193, 0.08);
            backdrop-filter: blur(15px);
            margin: 0 -5px 20px -5px;
        }

        #statusTabs .nav-link {
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        #statusTabs .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(111, 66, 193, 0.1), transparent);
            transition: left 0.5s ease;
        }

        #statusTabs .nav-link:hover::before {
            left: 100%;
        }

        #statusTabs .nav-link.active {
            color: #6f42c1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 246, 252, 0.98) 100%);
            box-shadow: 0 2px 12px rgba(111, 66, 193, 0.15);
            border: 1px solid rgba(111, 66, 193, 0.2);
        }

        /* 记录卡片样式 */
        .record-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 246, 252, 0.95) 100%);
            border-radius: 16px;
            margin-bottom: 10px;
            border: 1px solid rgba(111, 66, 193, 0.1);
            box-shadow: 0 2px 12px rgba(111, 66, 193, 0.06);
            backdrop-filter: blur(15px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .record-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(111, 66, 193, 0.02) 0%, transparent 50%, rgba(139, 95, 191, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .record-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(111, 66, 193, 0.12);
            border-color: rgba(111, 66, 193, 0.2);
        }

        .record-card:hover::before {
            opacity: 1;
        }

        .record-card-body {
            padding: 14px 16px;
            position: relative;
            z-index: 2;
        }

        .record-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .record-icon {
            width: 32px;
            height: 32px;
            border-radius: 10px;
            background: linear-gradient(135deg, #6f42c1 0%, #8b5fbf 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            box-shadow: 0 2px 8px rgba(111, 66, 193, 0.25);
        }

        .record-icon i {
            color: white;
            font-size: 14px;
        }

        .record-title {
            font-size: 15px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.2;
        }

        .record-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
        }

        .record-info {
            flex: 1;
        }

        .info-item {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 13px;
            margin-bottom: 0;
        }

        .info-item i {
            margin-right: 6px;
            width: 14px;
            color: #6f42c1;
            font-size: 12px;
        }

        .record-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .report-btn {
            background: linear-gradient(135deg, #6f42c1 0%, #8b5fbf 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 16px;
            text-decoration: none;
            font-size: 11px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(111, 66, 193, 0.25);
            border: 1px solid rgba(111, 66, 193, 0.2);
        }

        .report-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 12px rgba(111, 66, 193, 0.35);
            text-decoration: none;
            color: white;
            background: linear-gradient(135deg, #5a359a 0%, #6f42c1 100%);
        }

        .report-btn i {
            font-size: 10px;
        }

        /* 空状态优化 */
        .splashBlock {
            padding: 30px 20px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 246, 252, 0.9) 100%);
            border-radius: 16px;
            border: 1px solid rgba(111, 66, 193, 0.1);
            backdrop-filter: blur(15px);
        }

        .splashBlock .nodata-img {
            opacity: 0.6;
            filter: grayscale(20%);
        }

        .splashBlock .sectionTitle .lead {
            color: #6c757d;
            font-size: 14px;
        }

        /* 加载更多按钮美化 */
        .load-more .btn {
            background: linear-gradient(135deg, rgba(111, 66, 193, 0.1) 0%, rgba(139, 95, 191, 0.1) 100%);
            border: 1px solid rgba(111, 66, 193, 0.3);
            color: #6f42c1;
            border-radius: 16px;
            padding: 10px 20px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .load-more .btn:hover {
            background: linear-gradient(135deg, #6f42c1 0%, #8b5fbf 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 3px 12px rgba(111, 66, 193, 0.25);
        }

        /* 响应式优化 */
        @media (max-width: 576px) {
            .record-header {
                margin-bottom: 8px;
            }

            .record-actions {
                flex-shrink: 0;
            }

            .record-card-body {
                padding: 12px 14px;
            }

            .record-title {
                font-size: 14px;
                flex: 1;
                margin-right: 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .record-icon {
                width: 28px;
                height: 28px;
                margin-right: 8px;
            }

            .record-icon i {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            我的测评记录
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule">
        <div class="appContent">
            <div class="mt-2 mb-3">
                <img th:src="@{/static/images/app_testrecord_banner2.jpg}" alt="image" class="imageBlock img-fluid rounded">
            </div>
            <!-- Tab 切换 -->
            <div class="nav-tabs size2 mb-3" id="statusTabs">
                <div class="nav-item">
                    <a class="nav-link active font13" href="javascript:" data-state="0">未完成</a>
                </div>
                <div class="nav-item">
                    <a class="nav-link font13" href="javascript:" data-state="1">已完成</a>
                </div>
            </div>
            <!-- Tab 内容 -->
            <div class="tab-content">
                <div class="tab-pane active" id="tab-incomplete">
                    <div class="list-content hide">
                        <div class="detail font14">
                        </div>
                        <div class="mt-2 mb-2 load-more hide"><a href="javascript:" class="btn btn-primary btn-sm btn-block" id="loadMoreIncomplete">查看更多</a></div>
                    </div>
                    <div class="splashBlock hide">
                        <div class="mb-3 mt-3">
                            <img class="nodata-img" th:src="@{/static/images/nodata.png}" alt="">
                        </div>
                        <div class="sectionTitle text-center">
                            <div class="title">
                            </div>
                            <div class="lead">
                                暂无测评数据！
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane" id="tab-completed">
                    <div class="list-content hide">
                        <div class="detail font14">
                        </div>
                        <div class="mt-2 mb-2 load-more hide"><a href="javascript:" class="btn btn-primary btn-sm btn-block" id="loadMoreCompleted">查看更多</a></div>
                    </div>
                    <div class="splashBlock hide">
                        <div class="mb-3 mt-3">
                            <img class="nodata-img" th:src="@{/static/images/nodata.png}" alt="">
                        </div>
                        <div class="sectionTitle text-center">
                            <div class="title">
                            </div>
                            <div class="lead">
                                暂无测评数据！
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:insert="~{layouts/footMeasuring}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let currentPage = {incomplete: 0, completed: 0};
        let pageSize = 8;
        let currentTab = 'incomplete';
        // 切换Tab
        let switchTab = function(tabType) {
            currentTab = tabType;
            $(".tab-pane").removeClass('active');
            if (tabType === 'incomplete') {
                $("#tab-incomplete").addClass('active');
            } else {
                $("#tab-completed").addClass('active');
            }
            clearTabContent(tabType);
            init(0, tabType);
        };
        // 清空Tab内容
        let clearTabContent = function(tabType) {
            let tabSelector = tabType === 'incomplete' ? '#tab-incomplete' : '#tab-completed';
            $(tabSelector + " .detail").empty();
            $(tabSelector + " .list-content").removeClass('show').addClass('hide');
            $(tabSelector + " .splashBlock").removeClass('show').addClass('hide');
            $(tabSelector + " .load-more").removeClass('show').addClass('hide');
            currentPage[tabType] = 0;
        };
        let getQueryCondition = function (currentPage, tabType) {
            let param = {};
            param.userId = '[[${user.userId}]]';
            param.pageSize = pageSize;
            param.pageIndex = currentPage * pageSize;
            param.state = tabType === 'incomplete' ? 0 : 1;
            return param;
        };
        let init = function (currentPage, tabType) {
            layer.open({
                type: 2
                , content: '加载中'
            });
            let tabSelector = tabType === 'incomplete' ? '#tab-incomplete' : '#tab-completed';
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/get_my_records',
                data: JSON.stringify(getQueryCondition(currentPage, tabType)),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    if (res.data.length === 0) {
                        if (currentPage === 0) {
                            $(tabSelector + " .list-content").removeClass('show').addClass('hide');
                            $(tabSelector + " .splashBlock").removeClass('hide').addClass('show');
                            $(tabSelector + " .load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(tabSelector + " .load-more").removeClass('show').addClass('hide');
                        }
                    }
                    else {
                        let str = "";
                        for (let i = 0; i < res.data.length; i++) {
                            let testRecord = res.data[i];
                            str += '<div class="record-card">';
                            str += '  <div class="record-card-body">';
                            str += '    <div class="record-header">';
                            str += '      <div class="record-title">' + testRecord.scaleName + '</div>';
                            str += '    </div>';
                            str += '    <div class="record-content">';
                            str += '      <div class="record-info">';
                            str += '        <div class="info-item">';
                            str += '          <span>测试时间：' + testRecord.startTime + '</span>';
                            str += '        </div>';
                            str += '      </div>';
                            str += '      <div class="record-actions">';
                            let lblReport = "";
                            if (testRecord.state === 1) {
                                if (testRecord.scaleId === 10000163) {
                                    lblReport = '<a href="/app/measuring/report_aqy?recordId=' +testRecord.id + '" class="report-btn" style="padding:0 10px"><i class="fa fa-file-text"></i> 查看报告</a>';
                                }
                                else if (testRecord.scaleId === 10000167) {
                                    lblReport = '<a href="/app/measuring/report_yys?recordId=' + testRecord.id + '" class="report-btn" style="padding:0 10px"><i class="fa fa-file-text"></i> 查看报告</a>';
                                }
                                else {
                                    lblReport = '<a href="/app/measuring/report?recordId=' + testRecord.id + '" class="report-btn" style="padding:0 10px"><i class="fa fa-file-text"></i> 查看报告</a>';
                                }
                            }
                            if (lblReport) str += lblReport;
                            str += '      </div>';
                            str += '    </div>';
                            str += '  </div>';
                            str += '</div>';
                        }
                        $(tabSelector + " .detail").append(str);
                        $(tabSelector + " .list-content").removeClass('hide').addClass('show');
                        if (res.data.length < pageSize) {
                            $(tabSelector + " .load-more").removeClass('show').addClass('hide');
                        }
                        else {
                            $(tabSelector + " .load-more").removeClass('hide').addClass('show');
                        }
                        $(tabSelector + " .splashBlock").removeClass('show').addClass('hide');
                    }
                }
            });
        };

        $(function () {
            init(0, 'incomplete');
            // Tab切换事件
            $("#statusTabs .nav-link").click(function () {
                $("#statusTabs .nav-link").removeClass('active');
                $(this).addClass('active');
                let state = $(this).data('state');
                let tabType = state === 0 ? 'incomplete' : 'completed';
                switchTab(tabType);
            });
            // 搜索事件
            $("#btnQuery").click(function () {
                clearTabContent(currentTab);
                init(0, currentTab);
            });
            // 加载更多事件
            $(document).on('click', '#loadMoreIncomplete, #loadMoreCompleted', function () {
                currentPage[currentTab] = currentPage[currentTab] + 1;
                init(currentPage[currentTab], currentTab);
            });
        });
    </script>
</th:block>
</body>
</html>
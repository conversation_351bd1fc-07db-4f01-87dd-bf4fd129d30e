<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            在线测试
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="measuring-guide-container">
        <div class="appContent measuring-content">
            <!-- 测试标题区域 -->
            <div class="measuring-header-section">
                <div class="measuring-title-wrapper">
                    <h1 class="measuring-scale-title" th:text="${scale.scaleName}"></h1>
                </div>
            </div>

            <!-- 测试信息卡片 -->
            <div class="measuring-info-section">
                <div class="measuring-info-card-container">
                    <div class="measuring-info-items">
                        <div class="measuring-info-item">
                            <div class="info-icon time-icon">
                                <i class="fa fa-clock-o"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">答题时间</div>
                                <div class="info-value"><th:block th:text="${scale.needTime}"/> 分钟</div>
                            </div>
                        </div>
                        <div class="measuring-info-item">
                            <div class="info-icon question-icon">
                                <i class="fa fa-list-alt"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">题目数量</div>
                                <div class="info-value">
                                    <th:block th:if="${scale.id eq 10000157}">48</th:block>
                                    <th:block th:unless="${scale.id eq 10000157}" th:text="${scale.qCount}"></th:block>
                                    题
                                </div>
                            </div>
                        </div>
                        <div class="measuring-info-item">
                            <div class="info-icon users-icon">
                                <i class="fa fa-users"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">参与人数</div>
                                <div class="info-value"><th:block th:text="${scale.testCount}"/> 人</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试说明和友情提示 -->
            <div class="measuring-content-section">
                <div class="measuring-content-card">
                    <!-- 测试说明 -->
                    <div class="content-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fa fa-file-text-o"></i>
                            </div>
                            <h3 class="section-title">测试指导语</h3>
                        </div>
                        <div class="section-content pl-1 pr-1">
                            <th:block th:utext="${scale.scaleGuide}"></th:block>
                        </div>
                    </div>

                    <!-- 友情提示 -->
                    <div class="content-section">
                        <div class="section-header">
                            <div class="section-icon tips-icon">
                                <i class="fa fa-lightbulb-o"></i>
                            </div>
                            <h3 class="section-title">友情提示</h3>
                        </div>
                        <div class="section-content">
                            <div class="simple-tips-list">
                                <div class="tip-item">
                                    <span class="tip-text">请以平常心态答题，答案没有对与错，不要有太多顾虑，反映自身真实情况即可。系统会为您进行严格保密。</span>
                                </div>
                                <div class="tip-item">
                                    <span class="tip-text">测试结果只是参考，不当做诊断标准。如有需要，请咨询专业心理医生。</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="appBottomMenu2 measuring-bottom-menu">
        <div class="measuring-button-container">
            <div class="btn measuring-back-btn pull-left">
                <a href="/app/measuring/all" class="measuring-back-link">
                    <i class="fa fa-arrow-left mr-2"></i>
                    <span>测评大厅</span>
                </a>
            </div>
            <div id="wrapper-start" class="btn measuring-start-btn pull-right">
                <span>开始测试</span><i class="fa fa-play ml-2"></i>
            </div>
        </div>
    </div>
    <!-- modal.基本信息 start-->
    <div id="myModalProfile" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index: 9999;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form id="frmProfile" role="form" action="" method="post">
                    <div class="modal-header modal-colored-header bg-primary">
                        <h5 class="modal-title font16 text-white">完善基本信息</h5>
                    </div>
                    <div class="modal-body">
                        <div class="form-group form-inline">
                            <label for="realName">姓名</label>
                            <input type="text" class="form-control" name="realName" id="realName" value="" />
                        </div>
                        <div class="form-group form-inline">
                            <label for="male" class="col-form-label mr-2">性别</label>
                            <div class="custom-control custom-radio mr-2">
                                <input type="radio" id="male" name="sex" class="custom-control-input" value="男" checked>
                                <label class="custom-control-label" for="male">男</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" id="female" name="sex" class="custom-control-input" value="女">
                                <label class="custom-control-label" for="female">女</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-form-label">出生日期</label>
                            <div class="input-group">
                                <input type="date" id="birth" name="birth" class="form-control col-sm-6" >
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">取消</button>
                        <input type="submit" id="btnSave" class="btn btn-primary btn-sm" value="保存" />
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- modal.基本信息 end-->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        let userId = '[[${user.userId}]]';
        let taskId = getUrlParam('taskId');
        let recordId = getUrlParam('recordId');
        let type = getUrlParam('type');
        let scaleId = getUrlParam('scaleId');
        $(function () {
            if (type == 1 || type == 2) {
                if (recordId === 0 || recordId === 'undefined') {
                    getRecordId();
                }
            }
            $("#wrapper-start").click(function () {
                startTesting();
            });
            $("#frmProfile").validate({
                ignore: "",
                errorPlacement: function (error, element) {
                    wrap = element.parent();
                    if (wrap.hasClass('input-group')) {
                        error.insertAfter(wrap);
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    realName: { required: true }
                },
                messages: {
                    realName: { required: "请填写姓名" }
                },
                submitHandler: function () {
                    let jsonObj = {};
                    jsonObj.userId = userId;
                    jsonObj.realName = $.trim($("#realName").val());
                    jsonObj.sex = $('input[name="sex"]:checked').val();
                    jsonObj.birth = $("#birth").val();
                    $("#btnSave").val("请稍后…");
                    $("#btnSave").attr("Disabled", true);
                    $.ajax({
                        type: 'POST',
                        async: false,
                        url: '/anteroom/user/update_for_scale',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                $("#myModalProfile").modal('hide');
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
        });
        function getRecordId() {
            layer.open({
                type: 2
                , content: '请稍后…',
                shadeClose: false
            });
            $.getJSON('/measuringroom/task/get_record?taskId=' + taskId + '&scaleId=' + scaleId + '', function (res) {
                recordId = res;
                layer.closeAll();
            });
        }
        function startTesting() {
            layer.open({
                type: 2
                , content: '请稍后…',
                shadeClose: false
            });
            if (type == 1 || type == 2) {
                if (recordId != 'undefined' && recordId != '' && recordId != 0) {
                    let jsonObj = { recordId: recordId };
                    let url = "/measuringroom/testing/update_starttime";
                    $.post(url, jsonObj,
                        function (res) {
                            layer.closeAll();
                            if (res.resultCode === 200) {
                                location.href = '/app/measuring/do_test?recordId=' + recordId+'&taskId='+taskId+'&taskType='+type;
                            }
                            else if (res.resultCode === 103) {
                                layer.open({
                                    content: '<img src="/static/images/warning.png" width="25" class="mr-1" />' + res.resultMsg,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function () {
                                        layer.closeAll();
                                        getUserInfo();
                                        $("#myModalProfile").modal();
                                        $("#myModalProfile").attr("z-index", "99999");
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }, "json"
                    );
                }
                else {
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>任务异常！'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                }
            }
            if (type == 3) {//自由测试
                let jsonObj = {};
                jsonObj.id = '[[${scale.id}]]';
                jsonObj.ageLimit = '[[${scale.ageLimit}]]';
                jsonObj.needTime = '[[${scale.needTime}]]';
                $.ajax({
                    type: 'POST',
                    url: "/measuringroom/testing/add_record",
                    data: JSON.stringify(jsonObj),
                    contentType:'application/json',
                    dataType: "json",
                    success: function (res) {
                        layer.closeAll();
                        if (res.resultCode === 200) {
                            location.href = '/app/measuring/do_test?recordId=' + parseInt(res.resultMsg) + '';
                        }
                        else if (res.resultCode === 103) {
                            layer.open({
                                content: '<img src="/static/images/warning.png" width="25" class="mr-1" />' + res.resultMsg,
                                btn: '确定',
                                shadeClose: false,
                                yes: function () {
                                    layer.closeAll();
                                    getUserInfo();
                                    $("#myModalProfile").modal();
                                    $("#myModalProfile").attr("z-index", "99999");
                                }
                            });
                        }
                        else {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                        }
                    }
                });
            }
        };
        function getUserInfo() {
            $.post("/anteroom/user/get?id=" + userId, '', function (res) {
                $("#realName").val(res.realName);
                if (res.sex === "男") {
                    $("#male").attr("checked", true);
                }
                else {
                    $("#female").attr("checked", true);
                }
                $("#birth").val(res.birth);
            });
        };

        // 页面加载完成后调整底部间距
        $(document).ready(function() {
            // 延迟执行，确保页面完全渲染
            setTimeout(function() {
                if (typeof adjustBottomSpacing === 'function') {
                    adjustBottomSpacing();
                }
            }, 100);
        });
    </script>
</th:block>
</body>
</html>
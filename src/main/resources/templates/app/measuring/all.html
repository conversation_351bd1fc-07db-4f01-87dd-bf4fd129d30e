<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <link th:href="@{/static/js/plugins/swiper/swiper-3.2.7.min.css}" rel="stylesheet" type="text/css" />
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            推荐测试
        </div>
        <div class="right mr-1">
            <label for="scale_name" class="mb-0 toggleSearchbox">
                <i class="fa fa-search font14"></i>
            </label>
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
        <!-- 搜索框覆盖层 -->
        <div class="searchOverlay">
            <form>
                <span class="inputIcon" id="btnQuery">
                    <i class="fa fa-search font14"></i>
                </span>
                <input type="text" class="form-control" id="scale_name" placeholder="搜索量表..." autofocus>
                <a href="javascript:" class="closeButton">
                    <i class="fa fa-times-circle font14"></i>
                </a>
            </form>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pt-3 mt-2">
        <div class="container pl-0 pr-0 pt-1">
            <div class="swiper-container swiper1">
                <div class="swiper-wrapper">
                    <th:block th:each="scaleType:${scaleTypes}">
                        <th:block th:if="${scaleTypeStat.index == 0}">
                            <div class="swiper-slide selected" th:id="${scaleType.id}"  th:text="${scaleType.scaleTypeName}"></div>
                        </th:block>
                        <th:block th:unless="${scaleTypeStat.index == 0}">
                            <div class="swiper-slide" th:id="${scaleType.id}"  th:text="${scaleType.scaleTypeName}"></div>
                        </th:block>
                    </th:block>
                </div>
            </div>
            <!-- swiper2 -->
            <div class="swiper-container swiper2 mt-2">
                <div class="swiper-wrapper" id="scaleList">

                </div>
            </div>
        </div>
        <div class="mt-2 row scaleList">
        </div>
    </div>
    <th:block th:insert="~{layouts/footMeasuring}"></th:block>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript" th:src="@{/static/js/plugins/swiper/swiper-3.4.0.jquery.min.js}"></script>
    <script type="text/javascript">
        let swiper1 = new Swiper('.swiper1', {
            slidesPerView: 'auto',
            paginationClickable: true,
            spaceBetween: 8,
            freeMode: true,
            loop: false,
            centeredSlides: false,
            onTab: function (swiper) {
                let n = swiper1.clickedIndex;
            }
        });
        let swiper2 = new Swiper('.swiper2', {
            //freeModeSticky  设置为true 滑动会自动贴合
            direction: 'horizontal',//Slides的滑动方向，可设置水平(horizontal)或垂直(vertical)。
            loop: false,
            autoHeight: true,//自动高度。设置为true时，wrapper和container会随着当前slide的高度而发生变化。
            onSlideChangeEnd: function (swiper) {  //回调函数，swiper从一个slide过渡到另一个slide结束时执行。
                let n = swiper.activeIndex;
                setCurrentSlide($(".swiper1 .swiper-slide").eq(n), n);
                swiper1.slideTo(n, 500, false);
            }
        });
        let getList= function(type,index){
            layer.open({type: 2, content: '请稍后…', shadeClose: false});
            let jsonObj = {};
            jsonObj.scaleTypeId = type;
            $.post('/measuringroom/scale/get_list_by_type', jsonObj, function (res) {
                let wrapper = $("#scaleList");
                let list_str = '<div class="swiper-slide swiper-no-swiping"><div class="itemList">';
                for (let i = 0; i < res.length; i++) {
                    let scale = res[i];
                    list_str += '<a href="/app/measuring/guide?scaleId=' + scale.id + '&type=3">';
                    let thumbnail = scale.thumbnail === "" ? "/static/images/nopic.png" : "/static/upload/scale/thumbnail/" + scale.thumbnail;
                    list_str += '<div class="item"><div class="image"><img src="' + thumbnail + '" alt="image"></div>';
                    list_str += '<div class="text" style="width:100%;"><div class="font14 font-weight-bold pt-0" style="color:#000;">' + scale.scaleName + '</div>';
                    let content = scale.scaleIntro.length > 45 ? scale.scaleIntro.substring(0, 45) + "..." : scale.scaleIntro;
                    list_str += '<div class="text-muted font12">' + content+ '</div>';
                    list_str += '</div></div>';
                    list_str += '</a>';
                    if (i < res.length - 1) {
                        list_str += '<div class="mt-2 mb-2"></div>';
                    }
                }
                list_str += '</div></div>';
                wrapper.empty();
                wrapper.append(list_str);
                layer.closeAll();
                swiper2.slideTo(index, 500, false);
            });
        }
        let setCurrentSlide = function(ele, index) {
            $(".swiper1 .swiper-slide").removeClass("selected");
            ele.addClass("selected");
            getList(ele[0].id,index);
        }
        let getScaleIsRecommend = function () {
            layer.open({type: 2, content: '请稍后…', shadeClose: false});
            let jsonObj = {};
            jsonObj.isRecommend = 1;
            jsonObj.scaleName = $("#scale_name").val();
            jsonObj.isDone = 1;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/scale/get_is_recommend',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    if (res.length > 0) {
                        let wrapper = $(".scaleList");
                        let scale_list = "";
                        let loop = res.length >= 4 ? 4 : res.length;
                        for (let i = 0; i < loop; i++) {
                            scale_list += '<div class="col-6">';
                            let scaleThumbnail = res[i].thumbnail === '' ? "/static/images/nopic.png" : "/static/upload/scale/thumbnail/" + res[i].thumbnail;
                            scale_list += '<a href="/app/measuring/guide?scaleId=' + res[i].id + '&type=3" class="postItem">';
                            scale_list += '<div class="imageWrapper">';
                            if (res[i].thumbnail === '')
                                scale_list += '<img src="' + scaleThumbnail + '" alt="image" class="img-thumbnail"></div>';
                            else {
                                scale_list += '<img src="' + scaleThumbnail + '" alt="image" class="image"></div>';
                            }
                            scale_list += '<h2 class="font14 font-weight-normal">' + res[i].scaleName + '</h2></a></div>';
                        }
                        wrapper.empty();
                        wrapper.append(scale_list);
                        $(".container").hide();
                        $(".scaleList").show();
                    }
                    else {
                        $(".container").show();
                        $(".scaleList").hide();
                    }
                    layer.closeAll();
                }
            });
        };
        let closeSearch = function () {
            $(".container").show();
            $(".scaleList").hide();
            // 隐藏搜索覆盖层
            $(".searchOverlay").removeClass("show");
            $("#scale_name").val("");
        };

        let showSearch = function () {
            $(".searchOverlay").addClass("show");
            setTimeout(function() {
                $("#scale_name").focus();
            }, 300);
        };

        $(function () {
            getList(swiper1.slides[0].id, 0);
            swiper1.slides.each(function (index, val) {
                let ele = $(this);
                ele.on("click", function () {
                    setCurrentSlide(ele, index);
                    swiper2.slideTo(index, 500, false);
                });
            });

            // 点击搜索图标显示搜索框
            $(".toggleSearchbox").click(function() {
                showSearch();
            });

            // 点击关闭按钮隐藏搜索框
            $(".searchOverlay .closeButton").click(function() {
                closeSearch();
            });
            $("#btnQuery").click(function () {
                if ($.trim($("#scale_name").val()) === '') {
                    getList(swiper1.slides[0].id, 0);
                    $(".container").show();
                    $(".scaleList").hide();
                }
                else {
                    $("#scaleList").empty();
                    getScaleIsRecommend(0);
                }
            });
            $('input').on('keydown', function(event) {
                // 检查是否按下了回车键
                if (event.key === 'Enter') { // 使用 event.key 来检测按键
                    // 阻止默认行为（例如：表单提交）
                    event.preventDefault();
                    $("#btnQuery").click();
                }
            });
        });
    </script>
</th:block>
</body>
</html>
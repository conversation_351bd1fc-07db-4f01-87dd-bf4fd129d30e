<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <style>
        /* 整体背景 - 简约渐变 */
        .appContent {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            position: relative;
        }

        /* 背景图片版本的报告头部 - 重新设计 */
        .report-header-bg {
            position: relative;
            min-height: 320px;
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            margin: -16px -16px 24px -16px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            overflow: hidden;
            border-radius: 0 0 32px 32px;
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.15),
                0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.02) 0%,
                rgba(0, 0, 0, 0.08) 40%,
                rgba(0, 0, 0, 0.25) 80%,
                rgba(0, 0, 0, 0.45) 100%
            );
        }

        .header-content {
            position: relative;
            z-index: 3;
            width: 100%;
            padding: 0 16px;
        }

        .report-header-bg .report-title-section {
            text-align: center;
            padding: 24px 20px 36px 20px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.25) 0%,
                rgba(255, 255, 255, 0.18) 50%,
                rgba(255, 255, 255, 0.12) 100%
            );
            backdrop-filter: blur(25px) saturate(1.3);
            border: 1px solid rgba(255, 255, 255, 0.25);
            margin: 0 8px;
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .report-header-bg .report-subtitle {
            color: rgba(255, 255, 255, 0.98);
            font-weight: 500;
            font-size: 15px;
            margin-bottom: 12px;
            text-shadow:
                0 2px 8px rgba(0, 0, 0, 0.3),
                0 1px 2px rgba(0, 0, 0, 0.4);
            letter-spacing: 0.5px;
        }

        .report-header-bg .report-main-title {
            color: #ffffff;
            font-weight: 700;
            font-size: 24px;
            margin-bottom: 0;
            text-shadow:
                0 3px 12px rgba(0, 0, 0, 0.4),
                0 1px 4px rgba(0, 0, 0, 0.5);
            letter-spacing: 1px;
            line-height: 1.2;
        }

        /* 普通版本的报告头部 - 简约设计 */
        .report-header {
            background: linear-gradient(135deg, #727cf5 0%, #8b5fbf 100%);
            border-radius: 24px;
            padding: 40px 24px;
            margin-bottom: 24px;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 12px 40px rgba(114, 124, 245, 0.25);
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: -30%;
            right: -15%;
            width: 150px;
            height: 150px;
            background: rgba(255,255,255,0.08);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .report-header::after {
            content: '';
            position: absolute;
            bottom: -20%;
            left: -10%;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.05);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite reverse;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .report-title-section {
            position: relative;
            z-index: 2;
        }

        .report-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 12px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .report-main-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 0;
            position: relative;
            letter-spacing: 1px;
        }

        /* 移动端响应式适配 - 优化背景图片版本 */
        @media (max-width: 768px) {
            .report-header-bg {
                min-height: 280px;
                margin: -16px -16px 20px -16px;
                border-radius: 0 0 24px 24px;
            }

            .report-header-bg .report-title-section {
                padding: 20px 16px 28px 16px;
                margin: 0 6px;
                border-radius: 16px;
            }

            .report-header-bg .report-main-title {
                font-size: 22px;
            }

            .report-header-bg .report-subtitle {
                font-size: 14px;
            }

            .report-header {
                padding: 32px 20px;
                margin-bottom: 20px;
                border-radius: 20px;
            }

            .report-main-title {
                font-size: 24px;
            }

            .report-subtitle {
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .report-header-bg {
                min-height: 260px;
                margin: -16px -16px 16px -16px;
                border-radius: 0 0 20px 20px;
            }

            .report-header-bg .report-title-section {
                padding: 16px 12px 24px 12px;
                margin: 0 4px;
                border-radius: 12px;
            }

            .report-header-bg .report-main-title {
                font-size: 20px;
                line-height: 1.3;
            }

            .report-header-bg .report-subtitle {
                font-size: 13px;
                margin-bottom: 10px;
            }

            .report-header {
                padding: 24px 16px;
                margin-bottom: 16px;
                border-radius: 16px;
            }

            .report-main-title {
                font-size: 20px;
            }

            .report-subtitle {
                font-size: 13px;
            }
        }

        /* 基本信息卡片 - 简约设计 */
        .info-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin-bottom: 24px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .info-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #727cf5, #8b5fbf);
        }

        .info-header {
            padding: 24px 24px 0;
            display: flex;
            align-items: center;
        }

        .info-icon {
            width: 48px;
            height: 48px;
            border-radius: 16px;
            background: linear-gradient(135deg, #727cf5, #8b5fbf);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 16px;
            font-size: 18px;
            box-shadow: 0 4px 16px rgba(114, 124, 245, 0.3);
        }

        .info-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            letter-spacing: 0.5px;
        }

        .info-grid {
            padding: 20px 24px 24px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .info-item {
            background: rgba(248, 249, 250, 0.6);
            padding: 16px;
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            background: rgba(114, 124, 245, 0.05);
            border-color: rgba(114, 124, 245, 0.2);
            transform: translateY(-2px);
        }

        .info-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 16px;
            color: #2c3e50;
            font-weight: 600;
            line-height: 1.3;
        }

        /* 因子分析区域 - 简约大气设计 */
        .factor-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin-bottom: 24px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            transition: all 0.3s ease;
        }

        .factor-section:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 48px rgba(0,0,0,0.12);
        }

        .factor-header {
            padding: 24px 24px 0;
            display: flex;
            align-items: center;
        }

        .factor-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            letter-spacing: 0.5px;
            margin: 0;
        }

        .factor-content {
            padding: 24px;
        }

        /* 表格重新设计 - 现代化风格 */
        .table-responsive {
            -webkit-overflow-scrolling: touch;
            border-radius: 20px;
            overflow: hidden;
            margin-bottom: 24px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.08),
                0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(114, 124, 245, 0.1);
            background: white;
        }

        .table {
            margin-bottom: 0;
            background: transparent;
            font-size: 14px;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, #727cf5 0%, #8b5fbf 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 18px 16px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            position: relative;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .table thead th:first-child {
            border-radius: 20px 0 0 0;
        }

        .table thead th:last-child {
            border-radius: 0 20px 0 0;
        }

        .table tbody td {
            padding: 18px 16px;
            border: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.04);
            vertical-align: middle;
            line-height: 1.6;
            background: white;
            transition: all 0.3s ease;
        }

        .table tbody tr:hover td {
            background: linear-gradient(135deg, rgba(114, 124, 245, 0.03) 0%, rgba(139, 95, 191, 0.02) 100%);
            transform: translateX(2px);
        }

        .table tbody tr:last-child td:first-child {
            border-radius: 0 0 0 20px;
        }

        .table tbody tr:last-child td:last-child {
            border-radius: 0 0 20px 0;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 表格内容样式 */
        .table td:first-child {
            font-weight: 600;
            color: #2c3e50;
        }

        .table td:last-child {
            color: #495057;
            line-height: 1.7;
        }

        /* 图表区域重新设计 - 修复比例问题 */
        .chart-section {
            margin-top: 24px;
            padding: 24px;
            background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.5) 100%);
            border-radius: 20px;
            border: 1px solid rgba(114, 124, 245, 0.12);
            position: relative;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
        }

        .chart-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #727cf5, #8b5fbf);
            border-radius: 20px 20px 0 0;
        }

        .chart-header {
            padding: 20px 20px 0;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            letter-spacing: 0.5px;
            margin: 0;
        }

        .chart-item {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.06),
                0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .chart-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(114, 124, 245, 0.02) 0%, transparent 50%, rgba(139, 95, 191, 0.02) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .chart-item:hover::before {
            opacity: 1;
        }

        .chart-item:hover {
            transform: translateY(-3px);
            box-shadow:
                0 12px 48px rgba(0, 0, 0, 0.1),
                0 4px 16px rgba(0, 0, 0, 0.06);
        }

        .chart-item:last-child {
            margin-bottom: 0;
        }

        /* 图表容器重新设计 - 修复比例协调问题 */
        [id^="chartContainer"] {
            width: 100% !important;
            max-width: 100% !important;
            height: 280px !important;
            min-height: 280px !important;
            margin: 0 auto;
            display: block;
            border-radius: 12px;
            overflow: hidden;
            background: rgba(248, 249, 250, 0.3);
        }

        [id^="chartCanvas"] {
            width: 100% !important;
            max-width: 100% !important;
            height: 280px !important;
            min-height: 280px !important;
            margin: 0 auto;
            display: block;
            border-radius: 12px;
        }

        /* 移动端响应式优化 - 图表和表格协调 */
        @media (max-width: 768px) {
            .appContent {
                padding-left: 12px;
                padding-right: 12px;
            }

            .info-section {
                border-radius: 16px;
                margin-bottom: 20px;
            }

            .info-header {
                padding: 20px 20px 0;
            }

            .info-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .info-title {
                font-size: 18px;
            }

            .info-grid {
                padding: 16px 20px 20px;
                gap: 16px;
            }

            .info-item {
                padding: 14px;
            }

            .factor-section {
                border-radius: 16px;
                margin-bottom: 20px;
            }

            .factor-header {
                padding: 20px 20px 0;
            }

            .factor-title {
                font-size: 18px;
            }

            .factor-content {
                padding: 20px;
            }

            .chart-section {
                padding: 20px;
                border-radius: 16px;
                margin-top: 20px;
            }

            .chart-header {
                padding: 16px 16px 0;
                margin-bottom: 16px;
            }

            .chart-title {
                font-size: 16px;
            }

            .chart-item {
                padding: 16px;
                border-radius: 12px;
                margin-bottom: 16px;
            }

            /* 移动端图表尺寸优化 */
            [id^="chartContainer"] {
                height: 240px !important;
                min-height: 240px !important;
            }

            [id^="chartCanvas"] {
                height: 240px !important;
                min-height: 240px !important;
            }

            /* 移动端表格优化 */
            .table-responsive {
                border-radius: 16px;
            }

            .table {
                font-size: 13px;
            }

            .table thead th {
                padding: 16px 12px;
                font-size: 13px;
                letter-spacing: 0.5px;
            }

            .table thead th:first-child {
                border-radius: 16px 0 0 0;
            }

            .table thead th:last-child {
                border-radius: 0 16px 0 0;
            }

            .table tbody td {
                padding: 16px 12px;
                font-size: 13px;
            }

            .table tbody tr:last-child td:first-child {
                border-radius: 0 0 0 16px;
            }

            .table tbody tr:last-child td:last-child {
                border-radius: 0 0 16px 0;
            }
        }

        @media (max-width: 480px) {
            .appContent {
                padding-left: 10px;
                padding-right: 10px;
            }

            .info-section {
                border-radius: 14px;
                margin-bottom: 16px;
            }

            .info-header {
                padding: 16px 16px 0;
            }

            .info-icon {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .info-title {
                font-size: 16px;
            }

            .info-grid {
                padding: 12px 16px 16px;
                gap: 12px;
                grid-template-columns: 1fr;
            }

            .info-item {
                padding: 12px;
            }

            .info-label {
                font-size: 12px;
            }

            .info-value {
                font-size: 14px;
            }

            .factor-section {
                border-radius: 14px;
                margin-bottom: 16px;
            }

            .factor-header {
                padding: 16px 16px 0;
            }

            .factor-title {
                font-size: 16px;
            }

            .factor-content {
                padding: 16px;
            }

            .chart-section {
                padding: 16px;
                border-radius: 12px;
                margin-top: 16px;
            }

            .chart-header {
                padding: 12px 12px 0;
                margin-bottom: 12px;
            }

            .chart-title {
                font-size: 15px;
            }

            .chart-item {
                padding: 14px;
                border-radius: 10px;
                margin-bottom: 12px;
            }

            /* 小屏幕图表尺寸优化 */
            [id^="chartContainer"] {
                height: 220px !important;
                min-height: 220px !important;
            }

            [id^="chartCanvas"] {
                height: 220px !important;
                min-height: 220px !important;
            }

            /* 小屏幕表格优化 */
            .table-responsive {
                border-radius: 12px;
            }

            .table {
                font-size: 12px;
            }

            .table thead th {
                padding: 14px 10px;
                font-size: 12px;
                letter-spacing: 0.3px;
            }

            .table thead th:first-child {
                border-radius: 12px 0 0 0;
            }

            .table thead th:last-child {
                border-radius: 0 12px 0 0;
            }

            .table tbody td {
                padding: 14px 10px;
                font-size: 12px;
                line-height: 1.5;
            }

            .table tbody tr:last-child td:first-child {
                border-radius: 0 0 0 12px;
            }

            .table tbody tr:last-child td:last-child {
                border-radius: 0 0 12px 0;
            }
        }

        /* 通用样式和交互效果 */
        .scroll-hint {
            text-align: center;
            font-size: 12px;
            color: #6c757d;
            padding: 10px 16px;
            background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.6) 100%);
            border-radius: 8px;
            margin-bottom: 12px;
            border: 1px solid rgba(114, 124, 245, 0.1);
            backdrop-filter: blur(5px);
        }

        .table td:nth-child(2) {
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            line-height: 1.6;
        }

        /* 加载动画 */
        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200px 100%;
            animation: shimmer 1.5s infinite;
        }

        /* 触摸优化 */
        .chart-item,
        .info-section,
        .factor-section {
            -webkit-tap-highlight-color: transparent;
        }

        .chart-item:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }

        .info-section:active,
        .factor-section:active {
            transform: scale(0.99);
            transition: transform 0.1s ease;
        }

        /* 滚动条美化 */
        .table-responsive::-webkit-scrollbar {
            height: 6px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 3px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #727cf5, #8b5fbf);
            border-radius: 3px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(90deg, #6c5ce7, #8b5fbf);
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            color: #dee2e6;
            margin-bottom: 16px;
        }

        .empty-state h4 {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #495057;
        }

        .empty-state p {
            font-size: 14px;
            margin: 0;
        }


    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="pageTitle">
            《<span class="scaleName"></span>》测试报告
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule">
        <div class="alert hide text-center text-success" id="alert">
            <div class="splashBlock">
                <div class="mb-3 mt-3">
                    <img th:src="@{/static/images/app/success.png}" alt="draw" width="100">
                </div>
                <div class="sectionTitle text-center">
                    <div class="lead font16 font-weight-bold" id="msg">

                    </div>
                </div>
            </div>
        </div>
        <div class="appContent" id="report">
            <!-- 背景图片版本的报告头部 -->
            <div class="report-header-bg" id="reportHeaderBg" style="display: none;">
                <div class="bg-overlay"></div>
                <div class="header-content">
                    <div class="report-title-section">
                        <p class="report-subtitle">《<span class="scaleName"></span>》</p>
                        <h1 class="report-main-title">心理测试报告</h1>
                    </div>
                </div>
            </div>

            <!-- 普通版本的报告头部 -->
            <div class="report-header" id="normalHeader">
                <div class="report-title-section">
                    <p class="report-subtitle">《<span class="scaleName"></span>》</p>
                    <h1 class="report-main-title">心理测试报告</h1>
                </div>
            </div>

            <!-- 基本信息卡片 -->
            <div class="info-section">
                <div class="info-header">
                    <div class="info-icon">
                        <i class="fa fa-info-circle"></i>
                    </div>
                    <h2 class="info-title">基本信息</h2>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">用户名</span>
                        <span class="info-value" id="overviewName"></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">所属组织</span>
                        <span class="info-value" id="overviewOrg"></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">测试耗时</span>
                        <span class="info-value" id="overviewTime"></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">测试日期</span>
                        <span class="info-value" id="overviewDate"></span>
                    </div>
                </div>
            </div>

            <!-- 因子结果分析区域 -->
            <div id="factorAnalysisSection"></div>

            <!-- 额外的底部间距，确保内容不被遮挡 -->
            <div class="report-footer-spacer"></div>
        </div>
    </div>

    <th:block th:insert="~{layouts/footMeasuring}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script th:src="@{/static/js/pages/chartDataConfig.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let reportData, testRecord, scale, user;
        let chartsImgArray = []; // 图表图片数组

        let initReport = function () {
            layer.open({type: 2, content: '报告加载中…', shadeClose: false});
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode !== undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".appHeader").hide();
                            $("#msg").append("您已经完成测试！");
                            $("#alert").removeClass('hide').addClass('show');
                            return;
                        } else if (res.resultCode !== 200) {
                            layer.msg(res.resultMsg || '获取报告失败', { icon: 2, time: 2000 });
                            return;
                        }
                    }
                    
                    if (!res.data || !res.data.testRecord) {
                        layer.msg('报告数据格式错误', { icon: 2, time: 2000 });
                        return;
                    }
                    
                    reportData = res.data;
                    testRecord = res.data.testRecord;
                    scale = res.data.testRecord.scale;
                    user = res.data.testRecord.user;

                    // 数据加载完成后初始化页面
                    getBaseInfo();
                    processReport();
                    if (getUrlParam("savecharts") === 'true') {
                        saveCharts();
                    }
                },
                error: function(xhr, status, error) {
                    layer.closeAll();
                    layer.msg('网络错误，请稍后重试', { icon: 2, time: 2000 });
                }
            });
        };
        
        let getBaseInfo = function () {
            let userName = user.realName === "" ? user.loginName : user.realName;
            let formattedDate = moment(testRecord.startTime).format("YYYY-MM-DD");
            let formattedTime = formatSeconds(testRecord.timeInterval);

            $(".scaleName").html(scale.scaleName);
            $("#realName, #heroName, #overviewName").html(userName);
            $("#fullStructName, #overviewOrg").html(user.structName);
            $("#startDate, #statusDate, #overviewDate").html(formattedDate);
            $("#costTime, #heroCostTime, #overviewTime").html(formattedTime);
        };

        let processFactorHierarchy = function(factorList) {
            if (!factorList || factorList.length === 0) {
                return { topLevel: [], parentGroups: [], independentFactors: [] };
            }

            let topLevelFactors = [];
            let parentGroups = [];
            let independentFactors = [];
            let processedFactorIds = new Set();

            // 递归收集因子的通用函数
            let collectFactors = function(factors, parentInfo) {
                $.each(factors, function(index, factorData) {
                    if (processedFactorIds.has(factorData.factorId)) return;
                    processedFactorIds.add(factorData.factorId);

                    if (factorData.children && factorData.children.length > 0) {
                        collectFactors(factorData.children, {
                            parentId: factorData.factorId,
                            parentName: factorData.factorName
                        });
                    } else {
                        if (parentInfo && factorData.factorType === 1) {
                            let existingGroup = parentGroups.find(group => group.parentId === parentInfo.parentId);
                            if (existingGroup) {
                                if (!existingGroup.children.some(child => child.factorId === factorData.factorId)) {
                                    existingGroup.children.push(factorData);
                                }
                            } else {
                                parentGroups.push({
                                    parentId: parentInfo.parentId,
                                    parentName: parentInfo.parentName,
                                    children: [factorData]
                                });
                            }
                        } else {
                            if (!independentFactors.some(factor => factor.factorId === factorData.factorId)) {
                                independentFactors.push(factorData);
                            }
                        }
                    }
                });
            };

            // 处理顶层因子和直接子因子
            $.each(factorList, function(index, factorData) {
                if (factorData.children && factorData.children.length > 0) {
                    topLevelFactors.push(factorData);
                    parentGroups.push({
                        parentId: factorData.factorId,
                        parentName: factorData.factorName,
                        children: factorData.children
                    });
                } else if (!processedFactorIds.has(factorData.factorId)) {
                    independentFactors.push(factorData);
                    processedFactorIds.add(factorData.factorId);
                }
            });

            collectFactors(factorList, null);
            return { topLevel: topLevelFactors, parentGroups: parentGroups, independentFactors: independentFactors };
        };

        // 通用的内容生成函数（移动端优化版）
        let generateFactorContent = function(factors, config) {
            if (!factors || factors.length === 0) return "";

            const { title, icon = 'fa-line-chart', chartPrefix, titleSuffix = '' } = config;
            let content = `<div class='factor-section'>
                <div class='factor-header'>
                    <div class='info-icon'>
                        <i class='fa ${icon}'></i>
                    </div>
                    <h3 class='factor-title'>${title}</h3>
                </div>
                <div class='factor-content'>
                    <div class='table-responsive'>
                        <table class='table table-striped'>
                            <thead>
                                <tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr>
                            </thead>
                            <tbody>`;

            $.each(factors, function(index, factorData) {
                content += `<tr><td class='text-left'>${factorData.factorName}</td><td class='text-left'>${factorData.interpretation || "暂无解释内容"}</td></tr>`;
            });

            content += `</tbody></table></div>
                <div class='chart-section'>
                    <div class='chart-header'>
                        <div class='info-icon'>
                            <i class='fa fa-bar-chart'></i>
                        </div>
                        <h5 class='chart-title'>${titleSuffix || title}得分图表</h5>
                    </div>`;

            content += generateMobileChartContainers(factors, chartPrefix);
            content += `</div></div></div>`;

            return content;
        };

        // 生成移动端图表容器的函数
        let generateMobileChartContainers = function(factors, chartPrefix) {
            let content = "";
            const chartStyle = "width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;";

            if (scale.listCharts && scale.listCharts.length > 0) {
                content += `<div class='chart-item' id='chartContainer_${chartPrefix}'>
                    <canvas id='chartCanvas_${chartPrefix}' style='${chartStyle}'></canvas>
                </div>`;
            } else {
                if (factors.length === 1) {
                    content += `<div class='chart-item' id='chartContainer_${chartPrefix}_column'>
                        <canvas id='chartCanvas_${chartPrefix}_column' style='${chartStyle}'></canvas>
                    </div>`;
                } else {
                    content += `<div class='chart-item' id='chartContainer_${chartPrefix}_line'>
                        <canvas id='chartCanvas_${chartPrefix}_line' style='${chartStyle}'></canvas>
                    </div>
                    <div class='chart-item' id='chartContainer_${chartPrefix}_column'>
                        <canvas id='chartCanvas_${chartPrefix}_column' style='${chartStyle}'></canvas>
                    </div>`;
                }
            }
            return content;
        };

        // 简化的内容生成函数
        let generateTopLevelContent = function(topLevelFactors) {
            return generateFactorContent(topLevelFactors, {
                title: '结果分析',
                icon: 'fa-bar-chart',
                chartPrefix: 'topLevel',
                titleSuffix: ''
            });
        };

        let generateParentGroupContent = function(parentGroup) {
            return generateFactorContent(parentGroup.children, {
                title: parentGroup.parentName,
                icon: 'fa-list-alt',
                chartPrefix: `parent_${parentGroup.parentId}`,
                titleSuffix: ``
            });
        };

        let generateIndependentFactorsContent = function(independentFactors) {
            return generateFactorContent(independentFactors, {
                title: '结果分析',
                icon: 'fa-pie-chart',
                chartPrefix: 'independent',
                titleSuffix: ''
            });
        };

        let processReport = function () {
            let content = "";

            if (reportData.listExplains && reportData.listExplains.length > 0) {
                let organizedData = processFactorHierarchy(reportData.listExplains);
                content += generateTopLevelContent(organizedData.topLevel);
                $.each(organizedData.parentGroups, function(index, parentGroup) {
                    content += generateParentGroupContent(parentGroup);
                });
                content += generateIndependentFactorsContent(organizedData.independentFactors);
            } else {
                content += "<div class='alert alert-info'>暂无因子分析数据</div>";
            }

            $("#factorAnalysisSection").html(content);
            createCharts();
        };

        let createCharts = function(){
            if (!reportData.listExplains || reportData.listExplains.length === 0) return;

            let organizedData = processFactorHierarchy(reportData.listExplains);

            if (organizedData.topLevel.length > 0) {
                createChart(organizedData.topLevel, 'topLevel');
            }

            $.each(organizedData.parentGroups, function(index, parentGroup) {
                createChart(parentGroup.children, `parent_${parentGroup.parentId}`);
            });

            if (organizedData.independentFactors.length > 0) {
                createChart(organizedData.independentFactors, 'independent');
            }
        };

        // 统一的图表创建函数（移动端优化版）
        let createChart = function(factors, chartPrefix) {
            if (!factors || factors.length === 0) return;

            if (scale.listCharts && scale.listCharts.length > 0) {
                createConfiguredChart(factors, chartPrefix);
            } else {
                createDefaultCharts(factors, chartPrefix);
            }
        };

        // 创建配置的图表（移动端优化 - 修复尺寸协调）
        let createConfiguredChart = function(factors, chartPrefix) {
            let containerId = `chartCanvas_${chartPrefix}`;
            let containerElement = document.getElementById(containerId);

            if (!containerElement) return;

            try {
                let chartConfig = scale.listCharts[0];
                if (!chartDataConfig[chartConfig.chartType]) {
                    console.warn('未找到图表配置:', chartConfig.chartType);
                    return;
                }

                let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));

                // 移动端尺寸优化
                let isMobile = window.innerWidth <= 768;
                let isSmallMobile = window.innerWidth <= 480;

                let containerWidth = containerElement.offsetWidth || (isMobile ? 320 : 400);
                let containerHeight = isSmallMobile ? 220 : (isMobile ? 240 : 280);

                Object.assign(chartOptions.chart, {
                    width: containerWidth,
                    height: containerHeight,
                    renderTo: containerId,
                    backgroundColor: 'transparent',
                    spacing: [10, 10, 10, 10]
                });

                chartOptions.title.text = "";

                // 移动端字体优化
                if (isMobile) {
                    chartOptions.legend = chartOptions.legend || {};
                    chartOptions.legend.itemStyle = { fontSize: '12px' };

                    if (chartOptions.xAxis) {
                        chartOptions.xAxis.labels = chartOptions.xAxis.labels || {};
                        chartOptions.xAxis.labels.style = { fontSize: '11px' };
                    }

                    if (chartOptions.yAxis) {
                        chartOptions.yAxis.labels = chartOptions.yAxis.labels || {};
                        chartOptions.yAxis.labels.style = { fontSize: '11px' };
                    }
                }

                if(chartOptions.chart.type === 'pie') {
                    let yData = factors.map((factorData, index) => {
                        let score = parseFloat(factorData.score) || 0;
                        console.log(`饼图 - 因子 ${factorData.factorName} 得分: ${score}`);
                        return {
                            name: factorData.factorName,
                            y: score,
                            selected: index === 0
                        };
                    });
                    chartOptions.series.push({
                        name: '因子分',
                        colorByPoint: true,
                        data: yData,
                        size: isMobile ? '80%' : '90%'
                    });
                } else {
                    let categories = factors.map(f => f.factorName);
                    let data = factors.map(f => {
                        let score = parseFloat(f.score) || 0;
                        console.log(`图表 - 因子 ${f.factorName} 得分: ${score}`);
                        return score;
                    });

                    if (chartOptions.xAxis) {
                        chartOptions.xAxis.categories = categories;
                    }
                    chartOptions.series.push({
                        name: '因子分',
                        data: data,
                        color: "#727cf5"
                    });
                }

                let chart = new Highcharts.Chart(chartOptions);

                // 移动端图表响应式调整
                setTimeout(() => {
                    if (chart && chart.reflow) chart.reflow();
                    if (chart && chart.setSize) {
                        chart.setSize(containerWidth, containerHeight, false);
                    }
                }, 150);

                saveChartImage(containerId);
            } catch(e) {
                console.error('创建图表失败:', e);
            }
        };

        // 优化的默认图表创建函数（移动端）- 使用真实得分
        let createDefaultCharts = function(factors, chartPrefix) {
            if (!factors || factors.length === 0) return;

            let categories = factors.map(f => f.factorName);
            let data = factors.map(f => {
                let score = parseFloat(f.score) || 0;
                console.log(`默认图表 - 因子 ${f.factorName} 得分: ${score}`);
                return score;
            });

            if (factors.length === 1) {
                createSingleColumnChart(factors[0], chartPrefix);
            } else {
                createLineAndColumnCharts(factors, chartPrefix, categories, data);
            }
        };

        // 通用的图片保存函数
        let saveChartImage = function(containerId) {
            try {
                let canvasId = "#" + containerId;
                let charData = $(canvasId).highcharts().getSVG();
                canvg(containerId, charData);
                let chartsImg = $(canvasId)[0].toDataURL("image/png");
                chartsImgArray.push(chartsImg);
            } catch(e) {
                console.log('图表图片保存失败:', e);
            }
        };

        // 创建单个因子柱状图（移动端尺寸优化）
        let createSingleColumnChart = function(factor, chartPrefix) {
            let containerId = `chartCanvas_${chartPrefix}_column`;
            let containerElement = document.getElementById(containerId);

            if (!containerElement) return;

            try {
                let columnChartOptions = JSON.parse(JSON.stringify(chartDataConfig.column));

                // 移动端尺寸优化
                let isMobile = window.innerWidth <= 768;
                let isSmallMobile = window.innerWidth <= 480;

                let containerWidth = containerElement.offsetWidth || (isMobile ? 320 : 400);
                let containerHeight = isSmallMobile ? 220 : (isMobile ? 240 : 280);

                Object.assign(columnChartOptions.chart, {
                    width: containerWidth,
                    height: containerHeight,
                    renderTo: containerId,
                    backgroundColor: 'transparent',
                    spacing: [10, 10, 10, 10]
                });

                columnChartOptions.title.text = "";
                columnChartOptions.xAxis.categories = [factor.factorName];

                // 移动端字体优化
                if (isMobile) {
                    columnChartOptions.legend = columnChartOptions.legend || {};
                    columnChartOptions.legend.itemStyle = { fontSize: '12px' };
                    columnChartOptions.xAxis.labels = { style: { fontSize: '11px' } };
                    columnChartOptions.yAxis.labels = { style: { fontSize: '11px' } };
                }

                let score = parseFloat(factor.score) || 0;
                console.log(`单因子柱状图 - 因子 ${factor.factorName} 得分: ${score}`);

                columnChartOptions.series.push({
                    name: '因子分',
                    data: [score],
                    color: "#727cf5"
                });

                let chart = new Highcharts.Chart(columnChartOptions);

                setTimeout(() => {
                    if (chart && chart.reflow) chart.reflow();
                    if (chart && chart.setSize) {
                        chart.setSize(containerWidth, containerHeight, false);
                    }
                }, 150);

                saveChartImage(containerId);
            } catch(e) {
                console.error('创建柱状图失败:', e);
            }
        };



        // 创建折线图和柱状图（多个因子，移动端优化）
        let createLineAndColumnCharts = function(factors, chartPrefix, categories, data) {
            createMobileLineChart(chartPrefix, categories, data);
            createMobileColumnChart(chartPrefix, categories, data);
        };

        // 创建移动端折线图
        let createMobileLineChart = function(chartPrefix, categories, data) {
            let containerId = `chartCanvas_${chartPrefix}_line`;
            let containerElement = document.getElementById(containerId);

            if (!containerElement) return;

            try {
                let lineChartOptions = JSON.parse(JSON.stringify(chartDataConfig.line));
                setupMobileChartOptions(lineChartOptions, containerId, containerElement);

                lineChartOptions.xAxis.categories = categories;
                lineChartOptions.series.push({
                    name: '因子分',
                    data: data,
                    color: "#727cf5"
                });

                let chart = new Highcharts.Chart(lineChartOptions);
                setupMobileChartResize(chart, containerElement);
                saveChartImage(containerId);
            } catch(e) {
                console.error('创建折线图失败:', e);
            }
        };

        // 创建移动端柱状图
        let createMobileColumnChart = function(chartPrefix, categories, data) {
            let containerId = `chartCanvas_${chartPrefix}_column`;
            let containerElement = document.getElementById(containerId);

            if (!containerElement) return;

            try {
                let columnChartOptions = JSON.parse(JSON.stringify(chartDataConfig.column));
                setupMobileChartOptions(columnChartOptions, containerId, containerElement);

                columnChartOptions.xAxis.categories = categories;
                columnChartOptions.series.push({
                    name: '因子分',
                    data: data,
                    color: "#727cf5"
                });

                let chart = new Highcharts.Chart(columnChartOptions);
                setupMobileChartResize(chart, containerElement);
                saveChartImage(containerId);
            } catch(e) {
                console.error('创建柱状图失败:', e);
            }
        };

        // 移动端图表配置设置（尺寸协调优化）
        let setupMobileChartOptions = function(chartOptions, containerId, containerElement) {
            let isMobile = window.innerWidth <= 768;
            let isSmallMobile = window.innerWidth <= 480;

            let containerWidth = containerElement.offsetWidth || (isMobile ? 320 : 400);
            let containerHeight = isSmallMobile ? 220 : (isMobile ? 240 : 280);

            Object.assign(chartOptions.chart, {
                width: containerWidth,
                height: containerHeight,
                renderTo: containerId,
                backgroundColor: 'transparent',
                spacing: [10, 10, 10, 10]
            });

            chartOptions.title.text = "";

            // 移动端字体和样式优化
            if (isMobile) {
                chartOptions.legend = chartOptions.legend || {};
                chartOptions.legend.itemStyle = { fontSize: '12px' };

                if (chartOptions.xAxis) {
                    chartOptions.xAxis.labels = chartOptions.xAxis.labels || {};
                    chartOptions.xAxis.labels.style = { fontSize: '11px' };
                }

                if (chartOptions.yAxis) {
                    chartOptions.yAxis.labels = chartOptions.yAxis.labels || {};
                    chartOptions.yAxis.labels.style = { fontSize: '11px' };
                }
            }
        };

        // 移动端图表尺寸调整（协调优化）
        let setupMobileChartResize = function(chart, containerElement) {
            setTimeout(() => {
                if (chart && chart.reflow) chart.reflow();
                if (chart && chart.setSize) {
                    let isMobile = window.innerWidth <= 768;
                    let isSmallMobile = window.innerWidth <= 480;

                    let containerWidth = containerElement.offsetWidth || (isMobile ? 320 : 400);
                    let containerHeight = isSmallMobile ? 220 : (isMobile ? 240 : 280);

                    chart.setSize(containerWidth, containerHeight, false);
                }
            }, 150);
        };

        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.chartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };

        $(function () {
            initReport();
            checkAndSetBackground();

            // 移动端专用设备处理
            let isMobile = window.innerWidth <= 768;
            let isSmallMobile = window.innerWidth <= 480;
            let isTinyMobile = window.innerWidth <= 360;
            
            // 移动端触摸优化
            if (isMobile) {
                // 禁用双击缩放
                let lastTouchEnd = 0;
                document.addEventListener('touchend', function (event) {
                    let now = (new Date()).getTime();
                    if (now - lastTouchEnd <= 300) {
                        event.preventDefault();
                    }
                    lastTouchEnd = now;
                }, false);

                // 优化滚动性能
                document.addEventListener('touchmove', function(e) {
                    if (e.target.closest('.table-responsive')) {
                        // 允许表格滚动
                        return;
                    }
                }, { passive: true });

                // 图表容器触摸优化
                $('.chart-item').on('touchstart', function() {
                    $(this).addClass('touching');
                }).on('touchend', function() {
                    $(this).removeClass('touching');
                });

                // 移动端表格触摸优化
                $('.table-responsive').on('touchstart', function() {
                    $(this).addClass('table-touching');
                }).on('touchend', function() {
                    $(this).removeClass('table-touching');
                });
            }

            // 移动端专用图表响应式处理（尺寸协调优化）
            setTimeout(function() {
                if (typeof Highcharts !== 'undefined') {
                    Highcharts.charts.forEach(function(chart) {
                        if (chart) {
                            // 根据设备类型设置统一的图表尺寸
                            let chartHeight, chartWidth;

                            if (isSmallMobile) {
                                chartHeight = 220;
                                chartWidth = Math.min(320, window.innerWidth - 40);
                            } else if (isMobile) {
                                chartHeight = 240;
                                chartWidth = Math.min(400, window.innerWidth - 40);
                            } else {
                                chartHeight = 280;
                                chartWidth = Math.min(500, window.innerWidth - 40);
                            }

                            // 确保图表容器有正确的尺寸
                            if (chart.container && chart.container.parentNode) {
                                let container = chart.container.parentNode;
                                container.style.height = chartHeight + 'px';
                                container.style.width = '100%';
                            }

                            // 设置图表尺寸
                            chart.setSize(chartWidth, chartHeight, false);

                            // 移动端图表字体和样式优化
                            if (isMobile) {
                                chart.update({
                                    chart: {
                                        backgroundColor: 'transparent',
                                        spacing: [10, 10, 10, 10]
                                    },
                                    title: {
                                        style: {
                                            fontSize: isSmallMobile ? '13px' : '14px'
                                        }
                                    },
                                    legend: {
                                        itemStyle: {
                                            fontSize: '12px'
                                        }
                                    },
                                    xAxis: {
                                        labels: {
                                            style: {
                                                fontSize: '11px'
                                            }
                                        }
                                    },
                                    yAxis: {
                                        labels: {
                                            style: {
                                                fontSize: '11px'
                                            }
                                        }
                                    }
                                }, false);

                                chart.reflow();
                            }
                        }
                    });
                }
            }, 1000);

            // 移动端表格横向滚动提示
            $('.table-responsive').each(function() {
                let $this = $(this);
                let $table = $this.find('.table');

                if ($table.width() > $this.width()) {
                    $this.addClass('scrollable');

                    // 移动端滚动提示优化
                    if (isMobile) {
                        let hintText = isTinyMobile ? '← 滑动查看更多 →' : 
                                      isSmallMobile ? '← 左右滑动查看更多 →' : 
                                      '← 左右滑动查看更多内容 →';
                        $this.before('<div class="scroll-hint">' + hintText + '</div>');
                    }
                }
            });

            // 移动端专用图表自适应处理
            $(window).on('resize orientationchange', function() {
                setTimeout(function() {
                    // 重新检测设备类型
                    let currentIsMobile = window.innerWidth <= 768;
                    let currentIsSmallMobile = window.innerWidth <= 480;
                    let currentIsTinyMobile = window.innerWidth <= 360;
                    
                    // 重新渲染图表以适应新尺寸
                    if (typeof Highcharts !== 'undefined') {
                        Highcharts.charts.forEach(function(chart) {
                            if (chart) {
                                // 根据当前设备类型设置图表尺寸
                                let chartHeight, chartWidth;
                                
                                                            if (currentIsTinyMobile) {
                                chartHeight = 200;
                            } else if (currentIsSmallMobile) {
                                chartHeight = 220;
                            } else if (currentIsMobile) {
                                chartHeight = 250;
                            } else {
                                chartHeight = 300;
                            }
                                
                                chartWidth = chart.chartWidth || chart.container.offsetWidth;
                                
                                if (chart.options.chart.type === 'gauge' || chart.options.chart.type === 'solidgauge') {
                                    // 移动端gauge图表特殊处理
                                    let paneSize, paneCenter;
                                    
                                    if (currentIsTinyMobile) {
                                        paneSize = '75%';
                                        paneCenter = ['50%', '55%'];
                                    } else if (currentIsSmallMobile) {
                                        paneSize = '80%';
                                        paneCenter = ['50%', '60%'];
                                    } else if (currentIsMobile) {
                                        paneSize = '85%';
                                        paneCenter = ['50%', '65%'];
                                    } else {
                                        paneSize = '90%';
                                        paneCenter = ['50%', '70%'];
                                    }
                                    
                                    chart.update({
                                        pane: {
                                            size: paneSize,
                                            center: paneCenter
                                        }
                                    });
                                } else {
                                    // 其他图表类型移动端优化
                                    chart.setSize(chartWidth, chartHeight, false);
                                    
                                    // 移动端图表字体优化
                                    if (currentIsMobile) {
                                        chart.update({
                                            title: {
                                                style: {
                                                    fontSize: currentIsTinyMobile ? '12px' : currentIsSmallMobile ? '13px' : '14px'
                                                }
                                            },
                                            xAxis: {
                                                labels: {
                                                    style: {
                                                        fontSize: currentIsTinyMobile ? '10px' : currentIsSmallMobile ? '11px' : '12px'
                                                    }
                                                }
                                            },
                                            yAxis: {
                                                labels: {
                                                    style: {
                                                        fontSize: currentIsTinyMobile ? '10px' : currentIsSmallMobile ? '11px' : '12px'
                                                    }
                                                }
                                            }
                                        });
                                    }
                                }
                                chart.reflow();
                            }
                        });
                    }
                }, 300);
            });
        });

        // 检查并设置背景图片
        function checkAndSetBackground() {
            $.ajax({
                url: "/measuringroom/testing/get_background",
                type: 'POST',
                data: { recordId: recordId },
                dataType: "JSON",
                success: function (res) {
                    if (res.resultCode === 200 && res.data && res.data.showBackground && res.data.backgroundUrl) {
                        $("#reportHeaderBg").css("background-image", "url('" + res.data.backgroundUrl + "')").show();
                        $("#normalHeader").hide();
                    } else {
                        $("#reportHeaderBg").hide();
                        $("#normalHeader").show();
                    }
                },
                error: function () {
                    $("#reportHeaderBg").hide();
                    $("#normalHeader").show();
                }
            });
        }
    </script>
</th:block>
</body>
</html>
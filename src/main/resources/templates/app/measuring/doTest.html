<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="pageTitle" id="scaleName">
        </div>
        <div class="right mr-1">
            <div class="header-timer">
                <div class="text-muted time"></div>
            </div>
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <div class="test-progress-info">
                <div class="test-progress-container">
                    <div class="progress test-progress">
                        <div role="progressbar" class="progress-bar test-progress-bar">
                            <span class="sr-only"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="test-main-card">
                <div class="test-guide-section">
                    <div class="scale-guide"></div>
                </div>
                <div class="test-content-section">
                    <div class="test-question-container" id="test">
                        <div class="question_title text-dark"></div>
                        <div class="test-options-container">
                            <ul class="list-unstyled question" id="">
                            </ul>
                        </div>
                        <div class="test-navigation-buttons d-flex justify-content-between align-items-center mt-3">
                            <div class="nav-buttons-group">
                                <button id="preQuestion" class="btn btn-outline-primary test-nav-btn-icon" type="button" title="上一题">
                                    <i class="fa fa-chevron-left"></i>
                                </button>
                                <button id="nextQuestion" class="btn btn-outline-primary test-nav-btn-icon" type="button" title="下一题">
                                    <i class="fa fa-chevron-right"></i>
                                </button>
                            </div>
                            <button class="btn btn-primary test-submit-btn" id="submitQuestions">
                                <i class="fa fa-check mr-2"></i>提交
                            </button>
                        </div>
                        <input type="hidden" id="hidState" value="0">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- modal.选择量表  -->
    <div id="scale-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <div class="modal-title text-white">
                        <i class="fa fa-info-circle mr-1"></i><span class="font13">本量表测试已经完成，您还有以下量表未完成：</span>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table id="tbScale" class="table table-striped" width="100%">
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="done-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body p-4">
                    <div class="text-center">
                        <i class="fa fa-check-circle h1 text-success"></i>
                        <h5 class="mt-1">感谢参与测评</h5>
                        <p class="mt-2 font14">您已完成该测评任务下的所有量表测评，如需查看测评报告请前往"我的测评记录"里查看。</p>
                        <a href="/app/measuring/my_records" type="button" class="btn btn-warning rounded my-2">查看测评报告</a>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <th:block th:insert="~{layouts/footMeasuring}"></th:block>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let scaleId = 0;
        let currentQuestion;
        let questions;
        let itemList = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]
        let itemList2 = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"]
        let activeQuestion = 0; //当前操作的考题编号
        let questioned = 0; //
        let checkQues = []; //已做答的题的集合
        let intDiff = parseInt(60 * 30);//倒计时总秒数量
        let checkCache = {};
        checkCache.recordId = recordId;
        checkCache.checkItemsCache = checkQues;
        $(function () {
            init();
            //上一题
            $("#preQuestion").click(function () {
                if (activeQuestion === 0) return;
                /* if ((activeQuestion + 1) !== questions.length) {
                     showQuestion(activeQuestion - 1);
                 }*/
                showQuestion(activeQuestion - 1);
                //showQuestion(activeQuestion);
            });
            //进入下一题
            $("#nextQuestion").click(function () {
                if ((activeQuestion + 1) !== questions.length) showQuestion(activeQuestion + 1);
                showQuestion(activeQuestion);
            });
            //提交试卷
            $("#submitQuestions").click(function () {
                if(checkQues.length === questions.length){
                    save();
                }
                else if(checkQues.length < questions.length){
                    let left = questions.length - checkQues.length;
                    layer.open({
                        content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>已做答：' + checkQues.length+ ' 道题，还有： ' + left + ' 道题。'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                }
                else {
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1" />测试异常，请尝试重新作答。',
                        btn: '确定',
                        shadeClose: false,
                        yes: function () {
                            location.reload();
                        }
                    });
                }
            })
        });
        let init = function () {
            layer.open({type: 2, content: '数据加载中…'});
            $.ajax({
                "type": "post",
                "url": "/measuringroom/testing/init",
                "dataType": "json",
                "async": false,
                "data": { recordId: recordId },
                "success": function (data) {
                    let res =JSON.parse(data);
                    let scale = res.scale;
                    $("#scaleName").html(scale.scaleName);
                    $(".scale-guide").html(scale.scaleGuide);
                    intDiff = parseInt(60 * scale.needTime);
                    timer(intDiff);
                    scaleId = scale.id;
                    questions = scale.listQuestions;
                    layer.closeAll();
                }
            });
            //showQuestion(0);
            if (window.localStorage.getItem('checkCache_' + recordId) != null) {
                let checkItems = JSON.parse(window.localStorage.getItem('checkCache_' + recordId));
                if(checkItems.checkItemsCache.length===0) {
                    showQuestion(0);
                }
                else{
                    if (checkItems.recordId === recordId) {
                        layer.open({
                            content: '<img src="/static/images/warning.png" width="25" class="mr-1" />系统检测到您上次的作答未完成，是否继续？',
                            btn: ['继续答题', '重新开始'],
                            shadeClose: false,
                            yes: function () {
                                checkQues = checkItems.checkItemsCache;
                                checkQues.forEach(item=>{
                                    $("#ques" + (item.qNo-1)).removeClass("question_id").addClass("active_question_id");
                                    $("#ques" + (item.qNo-1)).removeClass("question_id").addClass("clickQue");
                                });
                                let maxQuestionId = Math.max.apply(Math, checkQues.map(function (i) { return i.id }));
                                showQuestion(maxQuestionId);
                                layer.closeAll();
                            }, no: function () {
                                showQuestion(0);
                            }
                        });
                    }
                }
            }
            else {
                showQuestion(0);
            }
        };
        let timer = function (intDiff) {
            window.setInterval(function () {
                if($("#hidState").val()==='1') return;
                let day = 0, hour = 0, minute = 0, second = 0; //时间默认值
                if (intDiff > 0) {
                    day = Math.floor(intDiff / (60 * 60 * 24));
                    hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
                    minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
                    second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
                }
                if (minute <= 9) minute = '0' + minute;
                if (second <= 9) second = '0' + second;
                let timeTips = day + '' + hour + '' + ':' + minute + '' + ':' + second + '';
                if (intDiff === 0) {
                    layer.open({
                        content: '<img src="/static/images/warning.png" width="25" class="mr-1" />测试结束时间已到，请重新安排测试',
                        btn: '确定',
                        shadeClose: false,
                        yes: function () {
                            location.href = "/app/measuring/my_tasks";
                        }
                    });
                }
                intDiff--;
                $(".time").html(timeTips);
            }, 1000);
        };
        //题目信息
        let showQuestion = function (id) {
            $(".questioned").text(id + 1);
            questioned = (id + 1) / questions.length
            if (activeQuestion != undefined) {
                $("#ques" + activeQuestion).removeClass("question_id").addClass("active_question_id");
            }
            activeQuestion = id;
            $(".question").find(".title").remove();
            $(".question").find(".question_info").remove();
            $(".question").find(".question_info_text").remove();
            $(".question").find(".rating-scale-container").remove();
            $(".question").find(".forced-choice-container").remove();
            let question = questions[id];
            $(".question_title").html("第<span class='ml-1 mr-1 font-16 text-primary'>" + (id + 1) + " / "+ questions.length + "</span>题：" + question.qContent);
            //单选题
            if (question.qType === 1) {
                //父母养育方式问卷(EMBU)，此量表特殊处理。
                if (scaleId === [[${embu}]]) {
                    $("#nextQuestion").removeClass('hide').addClass('show');
                    $("#preQuestion").removeClass('hide').addClass('show');

                    let items = question.listAnswers;
                    let item = "<span class='title'>父亲</span>";
                    for (let i = 0; i < items.length; i++) {
                        item += "<li class='question_info' onclick='clickTrim(this)' id='fa_item" + i + "'><input type='radio' name='fa_item' value='" + itemList[i] + "'>&nbsp;" + itemList2[i] + "、" + items[i].aContent + "</li>";

                    }
                    item += "<span class='title'>母亲</span>";
                    for (let i = 0; i < items.length; i++) {
                        item += "<li class='question_info' onclick='clickTrim(this)' id='mo_item" + i + "'><input type='radio' name='mo_item' value='" + itemList[i] + "'>&nbsp;" + itemList2[i] + "、" + items[i].aContent + "</li>";
                    }
                    $(".question").append(item);
                    $(".question").attr("id", "question" + id);
                    $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                    for (let i = 0; i < checkQues.length; i++) {
                        if (checkQues[i].id === id) {
                            $("#" + checkQues[i].fa_item).find("input").prop("checked", "checked");
                            $("#" + checkQues[i].mo_item).find("input").prop("checked", "checked");
                            $("#" + checkQues[i].fa_item).addClass("clickTrim");
                            $("#" + checkQues[i].mo_item).addClass("clickTrim");
                            $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                        }
                    }
                }
                else {
                    let items = question.listAnswers;
                    let item = "";
                    for (let i = 0; i < items.length; i++) {
                        item = "<li class='question_info' onclick='clickTrim(this)' id='item" + i + "'><input type='radio' name='item' value='" + itemList[i] + "'>&nbsp;" + itemList2[i] + "、 " + items[i].aContent+ "</li>";
                        $(".question").append(item);
                    }
                    $(".question").attr("id", "question" + id);
                    $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                    for (let i = 0; i < checkQues.length; i++) {
                        if (checkQues[i].id === id) {
                            $("#" + checkQues[i].item).find("input").prop("checked", "checked");
                            $("#" + checkQues[i].item).addClass("clickTrim");
                            $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                        }
                    }
                }
            }
            //多选题
            if (question.qType === 2) {
                $("#nextQuestion").removeClass('hide').addClass('show');
                $("#preQuestion").removeClass('hide').addClass('show');
                let items = question.listAnswers;
                let item = "";
                for (let i = 0; i < items.length; i++) {
                    item = "<li class='question_info mb-2' onclick='clickTrim(this)' id='item_muti" + i + "'><input type='checkbox' name='item' value='" + itemList[i] + "'>&nbsp;" + itemList2[i] + "、 " + items[i].aContent + "</li>";
                    $(".question").append(item);
                }
                $(".question").attr("id", "question" + id);
                $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                for (let i = 0; i < checkQues.length; i++) {
                    if (checkQues[i].id === id) {
                        let itemNum = checkQues[i].item.split(',');
                        for (let i = 0; i < itemNum.length; i++) {
                            $("#" + itemNum[i]).find("input").prop("checked", "checked");
                            $("#" + itemNum[i]).addClass("clickTrim");
                        }
                        $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    }
                }
            }
            //填空题
            if (question.qType === 3) {
                $("#nextQuestion").removeClass('hide').addClass('show');
                $("#preQuestion").removeClass('hide').addClass('show');
                $(".question").append("<li class='question_info_text' id='item1'><textarea class='form-control' name='completion' id='completion" + id + "'  rows='3' onblur='clickTrim(this)' maxLength='100'></textarea></li>");
                $(".question").attr("id", "question" + id);
                $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                for (let i = 0; i < checkQues.length; i++) {
                    if (checkQues[i].id == id) {
                        $("#" + checkQues[i].item).val(checkQues[i].answer);
                        $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    }
                }
            }
            //评分单选题
            if (question.qType === 4) {
                $("#nextQuestion").removeClass('hide').addClass('show');
                $("#preQuestion").removeClass('hide').addClass('show');
                let items = question.listAnswers;
                let item = "<div class='rating-scale-container'>";
                item += "<div class='rating-scale-labels'>";
                if (items.length > 0) {
                    item += "<span class='rating-label-left'>" + items[0].aContent + "</span>";
                    item += "<span class='rating-label-right'>" + items[items.length - 1].aContent + "</span>";
                }
                item += "</div>";
                item += "<div class='rating-scale-line'></div>";
                item += "<div class='rating-scale-options'>";
                for (let i = 0; i < items.length; i++) {
                    item += "<div class='rating-option' onclick='clickTrim(this)' id='item" + i + "'>";
                    item += "<input type='radio' name='item' value='" + itemList[i] + "'>";
                    item += "<span class='rating-circle'></span>";
                    item += "</div>";
                }
                item += "</div>";
                item += "</div>";
                $(".question").append(item);
                $(".question").attr("id", "question" + id);
                $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                for (let i = 0; i < checkQues.length; i++) {
                    if (checkQues[i].id === id) {
                        $("#" + checkQues[i].item).find("input").prop("checked", "checked");
                        $("#" + checkQues[i].item).addClass("clickTrim");
                        $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    }
                }
            }
            //迫选题
            if (question.qType === 5) {
                $("#nextQuestion").removeClass('hide').addClass('show');
                $("#preQuestion").removeClass('hide').addClass('show');
                let items = question.listAnswers;
                let item = "<div class='forced-choice-container'>";
                item += "<div class='forced-choice-tip'>";
                item += "<i class='fas fa-exclamation-triangle'></i>";
                item += "<span>迫选题：请从以下选项中必须选择一项，不可跳过</span>";
                item += "</div>";
                for (let i = 0; i < items.length; i++) {
                    item += "<div class='forced-choice-option' onclick='clickTrim(this)' id='item" + i + "'>";
                    item += "<input type='radio' name='item' value='" + itemList[i] + "'>";
                    item += "<div class='forced-choice-text'>" + itemList2[i] + "、 " + items[i].aContent + "</div>";
                    item += "</div>";
                }
                item += "</div>";
                $(".question").append(item);
                $(".question").attr("id", "question" + id);
                $("#ques" + id).removeClass("active_question_id").addClass("question_id");
                for (let i = 0; i < checkQues.length; i++) {
                    if (checkQues[i].id === id) {
                        $("#" + checkQues[i].item).find("input").prop("checked", "checked");
                        $("#" + checkQues[i].item).addClass("clickTrim");
                        $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");
                    }
                }
            }

            progress(questions);
            //mbti标准版
            if (scaleId === [[${mbtis}]]) {
                $(".question_info").css('display', 'inline');
                $(".question_info").css('padding', '5px 10px 5px 5px');
                $(".question_info").css('margin-right', '10px');
                $(".question_title p").css('display','block')
            }
        };
        //进度条
        let progress = function () {
            let prog = (checkQues.length) / questions.length;
            $(".progress-bar").width((prog * 100).toString().substr(0, 4) + "%");
        };
        //选中
        let lastClickTime = 0;
        const minInterval = 1000;
        let clickTrim = function(source) {
            let questionType;
            const currentTime = new Date().getTime();
            if (currentTime - lastClickTime >= minInterval) {
                lastClickTime = currentTime;
                let id = source.id;

                // 填空题处理
                if (id.indexOf("completion") !== -1) {
                    questionType = 3;
                    $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");

                    // 查找是否已存在该题目的答案
                    let existingIndex = checkQues.findIndex(item => item.id === activeQuestion);

                    if (existingIndex !== -1) {
                        // 更新已存在的答案
                        checkQues[existingIndex].item = id;
                        checkQues[existingIndex].aNo = $("#" + id).val();
                    } else {
                        // 添加新答案
                        let check = {
                            id: activeQuestion,
                            item: id,
                            qNo: activeQuestion + 1,
                            aNo: $("#" + id).val(),
                            qType: 3,
                            recordId: recordId
                        };
                        checkQues.push(check);
                    }
                }
                // 多选题处理
                else if (id.indexOf("item_muti") !== -1) {
                    questionType = 2;
                    $("#" + id).addClass("clickTrim");
                    $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");

                    let array1 = [];
                    let array2 = [];
                    $("#question" + activeQuestion).find("input[name=item]:checked").each(function () {
                        array1.push($(this).val());
                        array2.push($(this).parent().attr('id'));
                    });

                    // 查找是否已存在该题目的答案
                    let existingIndex = checkQues.findIndex(item => item.id === activeQuestion);

                    if (existingIndex !== -1) {
                        // 更新已存在的答案
                        checkQues[existingIndex].item = array2.join(',');
                        checkQues[existingIndex].aNo = array1.join(',');
                    } else {
                        // 添加新答案
                        let check = {
                            id: activeQuestion,
                            item: array2.join(','),
                            qNo: activeQuestion + 1,
                            aNo: array1.join(','),
                            qType: 2,
                            recordId: recordId
                        };
                        checkQues.push(check);
                    }
                }
                // 单选题处理
                else {
                    questionType = 1;
                    $("#" + id).find("input").prop("checked", "checked");
                    $("#" + id).addClass("clickTrim");
                    $("#ques" + activeQuestion).removeClass("question_id").addClass("clickQue");

                    if (scaleId === [[${embu}]]) {
                        // EMBU量表特殊处理
                        let existingIndex = checkQues.findIndex(item => item.id === activeQuestion);

                        if (id.indexOf('fa_item') !== -1) {
                            if (existingIndex !== -1) {
                                checkQues[existingIndex].fa_item = id;
                                checkQues[existingIndex].fa_answer = $("#" + id).find("input[name=fa_item]:checked").val();
                                checkQues[existingIndex].qType = 1;
                            } else {
                                let check = {
                                    id: activeQuestion,
                                    fa_item: id,
                                    fa_answer: $("#" + id).find("input[name=fa_item]:checked").val(),
                                    qType: 1
                                };
                                checkQues.push(check);
                            }
                        }

                        if (id.indexOf('mo_item') !== -1) {
                            if (existingIndex !== -1) {
                                checkQues[existingIndex].mo_item = id;
                                checkQues[existingIndex].mo_answer = $("#" + id).find("input[name=mo_item]:checked").val();
                                checkQues[existingIndex].qType = 1;
                            } else {
                                let check = {
                                    id: activeQuestion,
                                    mo_item: id,
                                    mo_answer: $("#" + id).find("input[name=mo_item]:checked").val(),
                                    qType: 1
                                };
                                checkQues.push(check);
                            }
                        }
                    } else {
                        // 普通单选题处理
                        let existingIndex = checkQues.findIndex(item => item.id === activeQuestion);

                        if (existingIndex !== -1) {
                            checkQues[existingIndex].item = id;
                            checkQues[existingIndex].aNo = $("#" + id).find("input[name=item]:checked").val();
                        } else {
                            let check = {
                                id: activeQuestion,
                                item: id,
                                qNo: activeQuestion + 1,
                                aNo: $("#" + id).find("input[name=item]:checked").val(),
                                qType: 1,
                                recordId: recordId
                            };
                            checkQues.push(check);
                        }

                        // 清除其他选项的选中状态
                        $(".question_info").each(function () {
                            let otherId = $(this).attr("id");
                            if (otherId !== id) {
                                let otherWrapper = $("#" + otherId);
                                otherWrapper.find("input").prop("checked", false);
                                otherWrapper.removeClass("clickTrim");
                            }
                        });
                    }
                }
                currentQuestion = activeQuestion;
                progress();
                if (scaleId !== [[${embu}]] || scaleId !== 10000161 && questionType !== 2 && questionType !== 3) {
                    $("#nextQuestion").click();
                }
                if (window.localStorage) {
                    window.localStorage.setItem('checkCache_' + recordId, JSON.stringify(checkCache));
                }
            } else {
                layer.open({
                    content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>为了确保回答的准确性，请稍作停顿再进行点击！'
                    , style: 'background-color:#ffffff; border:none;'
                    , time: 1
                });
            }
        };
        /*保存考题状态 已做答的状态*/
        let saveQuestionState = function(clickId) {
            showQuestion(clickId);
        };
        let save = function () {
            let url = scaleId === [[${embu}]] ? "/measuringroom/testing/save_result_embu" : "/measuringroom/testing/save_result";
            let jsonObj = {};
            jsonObj.scaleId = scaleId;
            jsonObj.recordId = recordId;
            jsonObj.listResults = checkQues;
            jsonObj.listEmbuResults =checkQues;
            $("#submitQuestions").attr("Disabled", true);
            layer.open({type: 2, content: '报告生成中…', shadeClose: false});
            $.ajax({
                type: 'POST',
                url: url,
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {
                    layer.closeAll();
                    $("#submitQuestions").attr("Disabled", false);
                    if (res.resultCode === 200) {
                        $("#hidState").val(1);
                        window.localStorage.removeItem('checkCache_' + recordId);
                        if(getUrlParam('taskType') === '1'){
                            getUndoneScales();
                        }
                        else{
                            if (scaleId === [[${aqy}]]) {
                                location.href = '/app/measuring/report_aqy?recordId=' + recordId + '&savecharts=true';
                            }
                            else if (scaleId === [[${yys}]]) {
                                location.href = '/app/measuring/report_yys?recordId=' + recordId + '&savecharts=true';
                            }
                            else {
                                location.href = '/app/measuring/report?recordId=' + recordId + '&savecharts=true';
                            }
                        }
                    }
                    else {
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                }
            });
        }
        let getUndoneScales = function(){
            $.post('/measuringroom/task/get_list_for_scale_undone',{ taskId: getUrlParam('taskId')},function(res){
                if(res.length > 0){//还有未完成的测评量表
                    $("#scale-modal").modal();
                    let htmlStr = '';
                    res.forEach(function(value,index,array){
                        htmlStr += '<tr><td class="font14">'+value.scaleName+'</td><td><a href="/app/measuring/guide?taskId=' + getUrlParam('taskId') + '&scaleId=' + value.scaleId + '&type=' + value.taskType + '&recordId=' + value.recordId + '" class="btn btn-outline-primary rounded btn-sm">开始测评</a></td></tr>';
                    });
                    $('tbody').append(htmlStr);
                }
                else{
                    $("#done-modal").modal();
                }
            })
        }
    </script>
</th:block>
</body>
</html>
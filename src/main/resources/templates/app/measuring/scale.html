<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        /* 全新问卷表单设计 */
        .survey-modal {
            z-index: 99999;
            backdrop-filter: blur(8px);
        }

        .survey-modal-dialog {
            max-width: 700px;
            margin: 0.5rem auto;
            height: calc(100vh - 1rem);
        }

        .survey-modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* 头部简化设计 */
        .survey-modal-header {
            background: #6f42c1;
            border-bottom: none;
            padding: 24px 30px;
            text-align: center;
        }

        .survey-modal-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .survey-progress {
            margin-top: 16px;
        }

        .progress-bar-container {
            background: rgba(255, 255, 255, 0.3);
            height: 3px;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-bar {
            background: white;
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 2px;
        }

        .progress-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
            margin-top: 8px;
        }

        /* 主体内容简化设计 */
        .survey-modal-body {
            padding: 0;
            background: #f8f9fa;
            flex: 1;
            overflow-y: auto;
            position: relative;
        }

        .survey-form {
            padding: 24px;
            font-size: 14px;
        }

        /* 问题卡片简化设计 */
        .survey-question-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .survey-question-container:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        /* 问题标题简化设计 */
        .survey-question-title {
            margin-bottom: 20px;
            display: block;
        }

        .question-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .question-number {
            background: #6f42c1;
            color: white;
            font-weight: 600;
            font-size: 13px;
            padding: 4px 10px;
            border-radius: 16px;
            min-width: 28px;
            text-align: center;
            flex-shrink: 0;
        }

        .question-type-badge {
            background: #f8f9fa;
            color: #6c757d;
            font-size: 11px;
            padding: 3px 8px;
            border-radius: 10px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .question-text {
            font-weight: 600;
            font-size: 15px;
            color: #2c3e50;
            line-height: 1.5;
            margin: 0;
        }

        /* 选项组设计 */
        .survey-options-group {
            margin-top: 16px;
        }

        .survey-option-item {
            margin-bottom: 10px;
        }



        .survey-option-label {
            font-size: 14px;
            color: #495057;
            line-height: 1.5;
            cursor: pointer;
            margin-bottom: 0;
            padding-left: 0;
        }

        /* 文本输入框简化设计 */
        .survey-text-input {
            border-radius: 8px;
            border: 2px solid #dee2e6;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;
            width: 100%;
            font-family: inherit;
        }

        .survey-text-input:focus {
            border-color: #6f42c1;
            box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
            background: white;
            outline: none;
        }

        .survey-text-input::placeholder {
            color: #6c757d;
        }

        /* 其他选项输入框 */
        .survey-other-input {
            margin-top: 12px;
            padding-left: 0;
        }

        /* 问题分隔线 */
        .survey-question-divider {
            display: none;
        }

        /* 底部操作区简化设计 */
        .survey-modal-footer {
            background: white;
            border-top: 1px solid #dee2e6;
            padding: 20px 24px;
            text-align: center;
        }

        .survey-submit-btn {
            background: #6f42c1;
            border: none;
            color: white;
            padding: 12px 32px;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 600;
            min-width: 120px;
            transition: all 0.2s ease;
        }

        .survey-submit-btn:hover {
            background: #5a2d91;
            color: white;
        }

        .survey-submit-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }



        /* 错误提示样式 */
        .survey-form .error {
            color: #dc3545;
            font-size: 12px;
            margin-top: 6px;
            display: block;
            padding: 6px 12px;
            background: #f8d7da;
            border-radius: 6px;
            border-left: 3px solid #dc3545;
        }

        /* 滚动条美化 */
        .survey-modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .survey-modal-body::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        .survey-modal-body::-webkit-scrollbar-thumb {
            background: #6f42c1;
            border-radius: 3px;
        }

        .survey-modal-body::-webkit-scrollbar-thumb:hover {
            background: #5a2d91;
        }

        /* 简化动画效果 */
        .survey-question-container {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .survey-modal-dialog {
                margin: 0;
                max-width: none;
                height: 100vh;
            }

            .survey-modal-content {
                border-radius: 0;
                height: 100vh;
            }

            .survey-modal-header {
                padding: 20px 24px;
            }

            .survey-modal-title {
                font-size: 18px;
            }

            .survey-form {
                padding: 20px;
            }

            .survey-question-container {
                padding: 20px;
                margin-bottom: 20px;
                border-radius: 12px;
            }

            .question-text {
                font-size: 15px;
            }



            .survey-option-label {
                font-size: 14px;
            }

            .survey-text-input {
                padding: 14px 16px;
                font-size: 14px;
            }

            .survey-modal-footer {
                padding: 20px 24px;
            }

            .survey-submit-btn {
                width: 100%;
                padding: 16px 24px;
                font-size: 15px;
                border-radius: 12px;
            }

            .question-number {
                font-size: 12px;
                padding: 5px 10px;
            }


        }

        /* 小屏幕适配 */
        @media (max-width: 480px) {
            .survey-modal-header {
                padding: 16px 20px;
            }

            .survey-modal-title {
                font-size: 16px;
            }

            .survey-form {
                padding: 16px;
            }

            .survey-question-container {
                padding: 16px;
                margin-bottom: 16px;
            }

            .question-text {
                font-size: 14px;
            }



            .survey-option-label {
                font-size: 13px;
            }

            .survey-text-input {
                padding: 12px 14px;
                font-size: 13px;
            }

            .survey-modal-footer {
                padding: 16px 20px;
            }

            .survey-submit-btn {
                padding: 14px 20px;
                font-size: 14px;
            }
        }

        /* 触摸优化 */
        .survey-submit-btn {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }

        /* 加载状态 */
        .survey-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .survey-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 已回答问题的状态 */
        .survey-question-container.answered {
            border-left: 3px solid #28a745;
            background: #f8fff9;
        }



        /* 提交按钮禁用状态 */
        .survey-submit-btn.disabled {
            background: #6c757d;
            color: #fff;
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 进度条动画 */
        .progress-bar {
            transition: width 0.3s ease;
        }

        /* 排序题样式 */
        .sortable-list {
            min-height: 100px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            touch-action: none; /* 防止触摸滚动 */
            overflow: visible;
        }
        .sortable-item {
            cursor: move;
            user-select: none;
            transition: all 0.2s ease;
            touch-action: none; /* 防止触摸滚动 */
            position: relative;
            z-index: 1;
        }
        .sortable-item.dragging {
            z-index: 1000;
            pointer-events: none;
        }
        .sortable-item:hover .sortable-content {
            background-color: #e3f2fd !important;
            border-color: #6f42c1 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }
        .sortable-content {
            transition: all 0.2s ease;
            touch-action: none; /* 防止触摸滚动 */
        }
        .sortable-placeholder {
            background-color: #e3f2fd;
            border: 2px dashed #6f42c1;
            height: 60px;
            margin-bottom: 8px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6f42c1;
            font-weight: 500;
        }
        .sortable-ghost {
            opacity: 0.3;
        }
        .drag-handle {
            cursor: grab;
            color: #6c757d;
            font-size: 16px;
            touch-action: none; /* 防止触摸滚动 */
            padding: 5px;
        }
        .drag-handle:active {
            cursor: grabbing;
        }
        .drag-handle:hover {
            color: #6f42c1;
        }
        .sort-number {
            background-color: #6f42c1;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        /* 评分单选题样式 */
        .rating-options-container {
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .rating-options-row {
            display: flex;
            flex-wrap: nowrap;
            gap: 8px;
            padding: 8px 0;
            min-width: max-content;
        }
        
        .rating-option-item {
            flex: 0 0 auto;
            min-width: 60px;
            text-align: center;
        }
        
        .rating-option-item .custom-control {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 4px;
            border-radius: 8px;
            transition: all 0.2s ease;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        
        .rating-option-item .custom-control:hover {
            background: #e3f2fd;
            border-color: #6f42c1;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(111, 66, 193, 0.2);
        }
        
        .rating-option-item .custom-control-input:checked + .custom-control-label {
            color: #6f42c1;
            font-weight: 600;
        }
        
        .rating-option-item .custom-control-input:checked ~ .custom-control {
            background: #e3f2fd;
            border-color: #6f42c1;
            box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
        }
        
        .rating-option-label {
            font-size: 14px;
            line-height: 1.2;
            margin-top: 4px;
            text-align: center;
            word-break: break-word;
            max-width: 100%;
        }
        
        /* 移动端优化 */
        @media (max-width: 480px) {
            .rating-options-row {
                gap: 6px;
                padding: 6px 0;
            }
            
            .rating-option-item {
                min-width: 50px;
            }
            
            .rating-option-item .custom-control {
                padding: 6px 3px;
            }
            
            .rating-option-label {
                font-size: 13px;
            }
        }

        /* 模态框中的排序题特殊处理 */
        .survey-modal-body .sortable-list {
            position: relative;
            z-index: 1;
        }

        /* 防止模态框滚动干扰 */
        .survey-modal.sorting-active {
            pointer-events: none;
        }
        .survey-modal.sorting-active .survey-modal-content {
            pointer-events: auto;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle" th:text="${myTask.taskName}">
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent pb-5">
            <th:block th:if="${not #lists.isEmpty(myTask.scales) and myTask.scales.size() gt 0}">
                <div class="mt-2">
                    <div class="itemList">
                        <th:block th:each="scale:${myTask.scales}">
                            <div class="mb-1">
                                <a href="javascript:void(0)" th:onclick="check([[${myTask.id}]],[[${scale.Id}]], [[${myTask.taskType}]])">
                                    <div class="item">
                                        <div class="image">
                                            <th:block th:if="${scale.thumbnail ne null and scale.thumbnail ne ''}">
                                                <img th:src="|@{/static/upload/scale/thumbnail/}${scale.thumbnail}|" alt="image">
                                            </th:block>
                                            <th:block th:unless="${scale.thumbnail ne null and scale.thumbnail ne ''}">
                                                <img th:src="@{/static/images/nopic.png}" alt="image">
                                            </th:block>
                                        </div>
                                        <div class="text" style="width:100%;">
                                            <div class="font14 font-weight-bold pt-0 mb-2 text-muted" th:text="${scale.scaleName}"></div>
                                            <span class="badge badge-outline-danger text-danger pull-right mt-4">前往测评</span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </th:block>
                    </div>
                </div>
            </th:block>
            <th:block th:unless="${not #lists.isEmpty(myTask.scales) and myTask.scales.size() gt 0}">
                <div class="splashBlock hide">
                    <div class="mb-3 mt-3">
                        <img th:src="@{/static/app/img/sample/draw-2.png}" alt="draw" class="img-fluid">
                    </div>
                    <div class="sectionTitle text-center">
                        <div class="title">
                        </div>
                        <div class="lead">
                            <i class="fa fa-info-circle mr-1"></i>没有数据
                        </div>
                    </div>
                </div>
            </th:block>
        </div>
    </div>
    <div id="multiple-one" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index: 9999;">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body p-2">
                    <p class="align-content-between font13" style="line-height: 250%;">尊敬的参与者：<br/>
                        您好！在进行测试之前我们会收集一些关于你的信息，仅作为数据分析使用。您的回答将对我们的研究具有极其重要的价值，我们承诺所有信息将严格保密，仅用于统计分析，不会泄露给任何第三方。
                        请您花几分钟时间，根据自己的实际情况，真诚地填写以下问卷。您的每一份回答都是对我们莫大的支持。</p>
                    <div class="text-center"><button type="button" class="btn btn-primary rounded my-2 " data-target="#multiple-two" data-toggle="modal" data-dismiss="modal">参与调查</button></div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

    <!-- Modal -->
    <div id="multiple-two" class="modal fade survey-modal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index: 99999;">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered text-dark survey-modal-dialog">
            <div class="modal-content survey-modal-content">
                <div class="modal-header survey-modal-header">
                    <div class="modal-title survey-modal-title" th:text="${myTask.surveyName}"></div>
                    <div class="survey-progress">
                        <div class="progress-bar-container">
                            <div class="progress-bar" id="surveyProgressBar"></div>
                        </div>
                        <div class="progress-text">
                            <span id="currentQuestion">1</span> / <span id="totalQuestions">[[${#lists.size(listQuestions)}]]</span> 题
                        </div>
                    </div>
                </div>
                <div class="modal-body survey-modal-body">
                    <th:block th:if="${isSurvey eq 1}">
                        <form id="frmSurvey" class="survey-form">
                            <th:block th:each="question,iterStat:${listQuestions}">
                                <div class="survey-question-container" th:attr="data-question-index=${iterStat.index + 1}">
                                    <div class="survey-question-title">
                                        <div class="question-header">
                                            <span class="question-number"><th:block th:utext="${question.qNumber}"/></span>
                                            <th:block th:if="${question.qType == 1}">
                                                <span class="question-type-badge">单选</span>
                                            </th:block>
                                            <th:block th:if="${question.qType == 2}">
                                                <span class="question-type-badge">多选</span>
                                            </th:block>
                                            <th:block th:if="${question.qType == 3}">
                                                <span class="question-type-badge">填空</span>
                                            </th:block>
                                            <th:block th:if="${question.qType == 4}">
                                                <span class="question-type-badge">排序</span>
                                            </th:block>
                                            <th:block th:if="${question.qType == 5}">
                                                <span class="question-type-badge">评分</span>
                                            </th:block>
                                        </div>
                                        <div class="question-text"><th:block th:utext="${#strings.replace(question.qContent,'<p>','')}"/></div>
                                    </div>
                                    <div class="survey-options-group" th:if="${question.qType eq 1}">
                                        <th:block th:each="item:${question.listItems}">
                                            <div class="survey-option-item">
                                                <div class="custom-control custom-radio">
                                                    <input class="custom-control-input" type="radio"   th:with="prefix1='option_',prefix2='question_'"
                                                           th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}"  th:value="${item.itemContent}"
                                                           th:onclick="toggleOtherInput(this,[[${item.id}]],[[${item.isOther}]],[[${question.id}]])"  />
                                                    <label class="custom-control-label survey-option-label" th:with="prefix='option_'"  th:attr="for=${prefix}+${item.id}" th:utext="${item.itemContent}"></label>
                                                </div>
                                                <th:block th:if="${item.isOther eq 1}">
                                                    <div class="survey-other-input" style="display:none;" th:with="prefix='otherInput_'" th:attr="id=${prefix}+${question.id}">
                                                        <input type="text" class="form-control survey-text-input" th:with="prefix='_other'" th:attr="name=${question.id}+${prefix}" placeholder="请输入具体内容" />
                                                    </div>
                                                </th:block>
                                            </div>
                                        </th:block>
                                    </div>
                                    <div class="survey-options-group" th:if="${question.qType eq 2}">
                                        <th:block th:each="item:${question.listItems}">
                                            <div class="survey-option-item">
                                                <div class="custom-control custom-checkbox">
                                                    <input class="custom-control-input" type="checkbox"   th:with="prefix1='option_',prefix2='question_'"
                                                           th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}"  th:value="${item.itemContent}"
                                                           th:onclick="toggleOtherInput(this,[[${item.id}]],[[${item.isOther}]],[[${question.id}]])" />
                                                    <label class="custom-control-label survey-option-label" th:with="prefix='option_'"  th:attr="for=${prefix}+${item.id}" th:text="${item.itemContent}"></label>
                                                </div>
                                                <th:block th:if="${item.isOther eq 1}">
                                                    <div class="survey-other-input" style="display:none;" th:with="prefix='otherInput_'" th:attr="id=${prefix}+${question.id}">
                                                        <input type="text" class="form-control survey-text-input" th:with="prefix='_other'" th:attr="name=${question.id}+${prefix}"  placeholder="请输入具体内容" />
                                                    </div>
                                                </th:block>
                                            </div>
                                        </th:block>
                                    </div>
                                    <div class="survey-options-group" th:if="${question.qType eq 3}">
                                        <input type="text" class="form-control survey-text-input" th:with="prefix='question_'"  th:attr="name=${prefix}+${question.id}" placeholder="请输入内容" />
                                    </div>
                                    <div class="survey-options-group" th:if="${question.qType eq 4}">
                                        <div class="alert alert-info mb-3">
                                            <small><i class="fa fa-info-circle mr-1"></i>请拖拽下方选项进行排序，排在前面的表示优先级更高</small>
                                        </div>
                                        <div class="sortable-list" th:with="prefix='sortable_'" th:attr="id=${prefix}+${question.id}">
                                            <th:block th:each="item,iterStat:${question.listItems}">
                                                <div class="sortable-item" th:attr="data-value=${item.itemContent}">
                                                    <div class="sortable-content" style="display: flex; align-items: center; padding: 12px; border-radius: 8px; margin-bottom: 8px; background-color: white; border: 1px solid #dee2e6;">
                                                        <div class="sort-number" th:text="${iterStat.count}" style="background-color: #6f42c1; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px;"></div>
                                                        <i class="fa fa-bars drag-handle" style="margin-right: 12px; cursor: grab; color: #6c757d; font-size: 16px; touch-action: none; padding: 5px;"></i>
                                                        <span th:text="${item.itemContent}" style="flex-grow: 1;"></span>
                                                        <i class="fa fa-arrows-v" style="margin-left: 8px; color: #6c757d;"></i>
                                                    </div>
                                                </div>
                                            </th:block>
                                        </div>
                                        <input type="hidden" th:with="prefix='question_'" th:attr="name=${prefix}+${question.id}" class="sort-result" />
                                    </div>
                                    <!-- 评分单选题 -->
                                    <div class="survey-options-group" th:if="${question.qType eq 5}">
                                        <div class="rating-options-container">
                                            <div class="rating-options-row">
                                                <th:block th:each="item:${question.listItems}">
                                                    <div class="rating-option-item">
                                                        <div class="custom-control custom-radio">
                                                            <input class="custom-control-input" type="radio" th:with="prefix1='option_',prefix2='question_'"
                                                                   th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}" th:value="${item.itemContent}" />
                                                            <label class="custom-control-label rating-option-label" th:with="prefix='option_'" th:attr="for=${prefix}+${item.id}" th:utext="${item.itemContent}"></label>
                                                        </div>
                                                    </div>
                                                </th:block>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="survey-question-divider"></div>
                                </div>
                            </th:block>
                            <div class="survey-modal-footer">
                                <input type="submit" id="btnSave" class="btn rounded survey-submit-btn" value="提交" />
                            </div>
                        </form>
                    </th:block>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <th:block th:insert="~{layouts/footMeasuring}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script type="text/javascript">
        let taskType = getUrlParam('type');
        let taskId = getUrlParam('taskId');
        $(function(){
            isTaskValid();
            //启用调查问卷
            if([[${isSurvey}]] ===1){
                isSurveyValid();
                initSurveyProgress();

                // 初始化排序功能
                setTimeout(function() {
                    $('.sortable-list').each(function() {
                        let questionId = $(this).attr('id').replace('sortable_', '');
                        console.log('初始化排序题:', questionId);

                        // 使用自定义排序功能
                        initSortable(this, {
                            onEnd: function() {
                                console.log('拖拽结束');
                                updateSortResult(questionId);
                            }
                        });

                        // 初始化排序结果
                        updateSortResult(questionId);
                    });
                }, 100);
            }
            // 动态生成验证规则
            let rules = {};
            let messages = {};

            // 获取所有的问题
            $('.survey-question-container').each(function() {
                let questionId = $(this).find('input[name^="question_"]').first().attr('name');
                if (questionId) {
                    questionId = questionId.replace('question_', '');
                    let questionType = $(this).find('input[name^="question_"]').first().attr('type');

                    if (questionType === 'radio' || questionType === 'checkbox') {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请选择一个选项";
                    } else if (questionType === 'text') {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请填写内容";
                    } else if (questionType === 'hidden' && $(this).find('.sortable-list').length > 0) {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请完成排序";
                    }
                }
            });
            $("#frmSurvey").validate({
                errorPlacement: function (error, element) {
                    if (element.attr("name").indexOf("_other_input") !== -1) {
                        // 如果是其他选项的填空题，将错误信息放置在其下方
                        error.insertAfter(element);
                    } else {
                        // 将错误信息放置在 form-group 的后面
                        error.insertAfter(element.closest('.form-group'));
                    }
                },
                rules:rules,
                messages:messages,
                submitHandler: function (form){
                    let answers = [];
                    // 处理单选按钮
                    $('.custom-control-input[type="radio"]').each(function() {
                        if (this.checked) {
                            let questionId = this.name.replace('question_', '');
                            answers.push({
                                qId: questionId,
                                itemId: this.value,
                                otherAnswer: ''
                            });
                        }
                    });
                    // 处理复选按钮
                    $('.custom-control-input[type="checkbox"]').each(function() {
                        if (this.checked && !this.name.endsWith('_other')) {
                            let questionId = this.name.replace('question_', '');
                            let optionId = this.id.replace('option_','');
                            let existingAnswer = answers.find(answer => answer.qId === questionId);
                            if (existingAnswer) {
                                if (!existingAnswer.itemId) {
                                    existingAnswer.itemId = '';
                                }
                                existingAnswer.itemId += (existingAnswer.itemId ? '|' : '') + this.value;
                            } else {
                                answers.push({
                                    qId: questionId,
                                    itemId: this.value,
                                    otherAnswer: ''
                                });
                            }
                        }
                    });

                    // 处理文本输入
                    $('.form-control[type="text"]').each(function() {
                        let questionId = this.name.replace('question_', '');
                        if (this.value.trim() !== '' ) {
                            if(questionId.indexOf('_other') !== -1){
                                let answer = answers.find(answer=> answer.qId === questionId.replace('_other',''));
                                if(answer){
                                    answer.itemId += '|其他：'+this.value.trim();
                                }
                            }
                            else{
                                let existingAnswer = answers.find(answer => answer.qId === questionId);
                                if (existingAnswer) {
                                    existingAnswer.itemId = this.value.trim();
                                } else {
                                    answers.push({
                                        qId: questionId,
                                        itemId: this.value.trim(),
                                        otherAnswer: ''
                                    });
                                }
                            }
                        }
                    });

                    // 处理排序题
                    $('.survey-options-group input[type="hidden"].sort-result').each(function() {
                        console.log('检查排序题:', this.name, this.value); // 调试日志
                        if (this.name.indexOf('question_') === 0 && this.value.trim() !== '') {
                            let questionId = this.name.replace('question_', '');
                            console.log('添加排序题答案:', questionId, this.value.trim()); // 调试日志
                            answers.push({
                                qId: questionId,
                                itemId: this.value.trim(),
                                otherAnswer: ''
                            });
                        }
                    });

                    // 转换为JSON字符串
                    let jsonObj = {};
                    jsonObj.surveyId = '[[${myTask.surveyId}]]';
                    jsonObj.taskId = taskId;
                    jsonObj.listAnswers = answers;

                    console.log('提交的答案数据:', jsonObj); // 调试日志
                    $("#btnSave").attr('Disabled',true);
                    $("#btnSave").val('请稍后……');
                    $.ajax({
                        type: 'POST',
                        url: '/survey/surveyrecord/add',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").val("保存");
                            $("#btnSave").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />提交成功！请接着完成量表作答。' ,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function (index) {
                                        layer.close(index);
                                        $("#multiple-two").modal('hide');
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });

            // 添加选项选择监听
            $('.custom-control-input').on('change', function() {
                updateSurveyProgress();
            });

            // 文本输入监听
            $('.survey-text-input').on('input', function() {
                updateSurveyProgress();
            });
        });
        let isTaskValid = function (){
            layer.open({
                type: 2
                , content: '请稍后…',
                shadeClose: false
            });
            //判断任务的有效性
            $.post("/measuringroom/task/is_task_valid", {taskId: taskId},function(res){
                layer.closeAll();
                if(res.resultCode === 106){
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>无效的测评！'
                        , style: 'background-color:#ffffff; border:none;'
                        , shadeClose: false
                    });
                }
                if(res.resultCode === 105){
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>测评任务已结束！'
                        , style: 'background-color:#ffffff; border:none;'
                        , shadeClose: false
                    });
                }
            })
        }
        let isSurveyValid = function(){
            layer.open({
                type: 2
                , content: '请稍后…',
                shadeClose: false
            });
            $.post("/measuringroom/task/is_task_survey_done",{taskId: taskId},function(res){
                layer.closeAll();
                if(res.resultCode !== 107){
                    $("#multiple-one").modal();
                }
            })
        }
        function check(a, b, c) {
            $.post('/measuringroom/testing/is_scale_done', { taskId: a, scaleId: b }, function (res) {
                if (res.id == '0' || (res.id!='0' && res.state != '1' && res.state != '2')) {
                    location.href = '/app/measuring/guide?taskId=' + a + '&scaleId=' + b + '&type=' + c + '&recordId=' + res.id + '';
                }
                else {
                    layer.open({
                        content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>您已经完成该测试！'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                }
            });
        }
        let toggleOtherInput  = function(d,a,b,c) {
            if($(d).is('[type="radio"]')){
                if(b!==0){
                    if ($('#option_'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
                else{
                    $("#otherInput_" + c).toggle(false);
                }
            }
            if($(d).is('[type="checkbox"]')){
                if(b!==0){
                    if ($('#option_'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
            }
        }

        // 初始化问卷进度
        function initSurveyProgress() {
            const totalQuestions = $('.survey-question-container').length;
            $('#totalQuestions').text(totalQuestions);
            updateSurveyProgress();
        }

        // 更新问卷进度
        function updateSurveyProgress() {
            const totalQuestions = $('.survey-question-container').length;
            let answeredQuestions = 0;

            $('.survey-question-container').each(function() {
                const $container = $(this);
                let hasAnswer = false;

                // 检查单选和多选
                if ($container.find('.custom-control-input:checked').length > 0) {
                    hasAnswer = true;
                }

                // 检查文本输入
                const textInput = $container.find('.survey-text-input').val();
                if (textInput && textInput.trim() !== '') {
                    hasAnswer = true;
                }

                // 检查排序题
                const sortResult = $container.find('.sort-result').val();
                if (sortResult && sortResult.trim() !== '') {
                    hasAnswer = true;
                }

                // 检查评分单选题
                const ratingResult = $container.find('.rating-options-row input[type="radio"]:checked').val();
                if (ratingResult && ratingResult.trim() !== '') {
                    hasAnswer = true;
                }

                if (hasAnswer) {
                    answeredQuestions++;
                    $container.addClass('answered');
                } else {
                    $container.removeClass('answered');
                }
            });

            const progress = (answeredQuestions / totalQuestions) * 100;
            $('#surveyProgressBar').css('width', progress + '%');
            $('#currentQuestion').text(answeredQuestions);

            // 更新提交按钮状态
            if (answeredQuestions === totalQuestions) {
                $('#btnSave').removeClass('disabled').prop('disabled', false);
            } else {
                $('#btnSave').addClass('disabled').prop('disabled', true);
            }
        }

        // 添加问卷容器滚动到视图的功能
        function scrollToQuestion(questionIndex) {
            const $question = $('.survey-question-container').eq(questionIndex);
            if ($question.length) {
                $question[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        }

        // 排序功能实现 - 优化版，支持触摸和鼠标，避免页面滚动冲突
        function initSortable(element, options) {
            let draggedElement = null;
            let placeholder = null;
            let isDragging = false;

            // 创建占位符
            function createPlaceholder() {
                placeholder = document.createElement('div');
                placeholder.className = 'sortable-placeholder';
                placeholder.innerHTML = '<div style="height: 50px; display: flex; align-items: center; justify-content: center; color: #6f42c1;"><i class="fa fa-arrows-v"></i> 拖拽到此处</div>';
                return placeholder;
            }

            // 获取拖拽后的位置
            function getDragAfterElement(container, y) {
                const draggableElements = [...container.querySelectorAll('.sortable-item:not(.dragging)')];

                return draggableElements.reduce((closest, child) => {
                    const box = child.getBoundingClientRect();
                    const offset = y - box.top - box.height / 2;

                    if (offset < 0 && offset > closest.offset) {
                        return { offset: offset, element: child };
                    } else {
                        return closest;
                    }
                }, { offset: Number.NEGATIVE_INFINITY }).element;
            }

            // 开始拖拽
            function startDrag(e, item) {
                e.preventDefault();
                e.stopPropagation();

                draggedElement = item;
                isDragging = true;

                // 添加拖拽样式
                draggedElement.classList.add('dragging');
                draggedElement.style.opacity = '0.5';

                // 创建并插入占位符
                createPlaceholder();
                draggedElement.parentNode.insertBefore(placeholder, draggedElement.nextSibling);

                // 阻止模态框滚动
                const modal = element.closest('.survey-modal');
                if (modal) {
                    modal.classList.add('sorting-active');
                }

                console.log('开始拖拽');
            }

            // 拖拽中
            function onDrag(e) {
                if (!isDragging || !draggedElement) return;

                e.preventDefault();
                e.stopPropagation();

                const clientY = e.clientY || (e.touches && e.touches[0].clientY);
                if (!clientY) return;

                const afterElement = getDragAfterElement(element, clientY);

                if (afterElement == null) {
                    element.appendChild(placeholder);
                } else {
                    element.insertBefore(placeholder, afterElement);
                }
            }

            // 结束拖拽
            function endDrag(e) {
                if (!isDragging || !draggedElement) return;

                e.preventDefault();
                e.stopPropagation();

                // 移动元素到占位符位置
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.insertBefore(draggedElement, placeholder);
                    placeholder.parentNode.removeChild(placeholder);
                }

                // 恢复样式
                draggedElement.classList.remove('dragging');
                draggedElement.style.opacity = '';

                // 恢复模态框滚动
                const modal = element.closest('.survey-modal');
                if (modal) {
                    modal.classList.remove('sorting-active');
                }

                // 清理状态
                draggedElement = null;
                placeholder = null;
                isDragging = false;

                console.log('拖拽结束');
                if (options.onEnd) options.onEnd();
            }

            // 为每个排序项添加事件监听
            element.querySelectorAll('.sortable-item').forEach(item => {
                const dragHandle = item.querySelector('.drag-handle');

                // 鼠标事件
                dragHandle.addEventListener('mousedown', function(e) {
                    startDrag(e, item);
                });

                // 触摸事件
                dragHandle.addEventListener('touchstart', function(e) {
                    startDrag(e, item);
                }, { passive: false });
            });

            // 全局事件监听
            document.addEventListener('mousemove', onDrag);
            document.addEventListener('touchmove', onDrag, { passive: false });
            document.addEventListener('mouseup', endDrag);
            document.addEventListener('touchend', endDrag);
        }

        // 更新排序结果
        function updateSortResult(questionId) {
            let sortableList = $('#sortable_' + questionId);
            let sortedItems = [];

            console.log('更新排序结果，题目ID:', questionId);

            sortableList.find('.sortable-item').each(function(index) {
                let value = $(this).attr('data-value');
                if (value) {
                    sortedItems.push((index + 1) + '.' + value);
                    // 更新序号显示
                    $(this).find('.sort-number').text(index + 1);
                    console.log('排序项:', (index + 1), value);
                }
            });

            let result = sortedItems.join('|');
            $('input[name="question_' + questionId + '"]').val(result);
            console.log('排序结果:', result);
        }
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            填写活动总评
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <form id="frmOverallEvaluation" class="form-horizontal" method="post">
                <div class="form-group">
                    <label>活动主题</label>
                    <span class="badge badge-outline-primary" th:text="${overallEvaluation.activityName}"></span>
                </div>
                <div class="form-group">
                    <label for="overall_evaluation">活动总评</label>
                    <textarea class="form-control" name="overall_evaluation" id="overall_evaluation" rows="8" placeholder="填写活动的总体评价..." maxlength="500" th:text="${overallEvaluation.overallEvaluation}"></textarea>
                </div>
                <div>
                    <input type="submit" class="btn btn-primary btn-lg btn-block rounded" value="保存" id="btnSave">
                </div>
            </form>
        </div>
    </div>
    <!-- * appCapsule -->
    <th:block th:insert="~{layouts/footActivity}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script type="text/javascript">
        let activityId = getUrlParam("activityId");
        $(function () {
            $("#frmOverallEvaluation").validate({
                rules: {
                    overall_evaluation: {
                        required: true,
                        maxlength: 500
                    }
                },
                messages: {
                    overall_evaluation: {
                        required: "活动总评不能为空",
                        maxlength: "不能超过500个字"
                    }
                },
                submitHandler: function (form) {
                    let jsonObj = {
                        "activityId": activityId,
                        "overallEvaluation": $("#overall_evaluation").val()
                    };
                    $("#btnSave").attr("disabled", "disabled");
                    $("#btnSave").val("保存中...");
                    $.ajax({
                        url: "/activityroom/activity/update_overall_evaluation",
                        type: "post",
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSave").removeAttr("disabled");
                            $("#btnSave").val("保存");
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    })
                }
            })
        })
    </script>
</th:block>
</body>
</html>
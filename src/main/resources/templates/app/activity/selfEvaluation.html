<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title>个人点评</title>
    <style>
        /* 全局样式 - 简洁风格 */
        body {
            background: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* 应用头部 - 简洁风格 */
        .appHeader {
            background: #fff;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .appHeader .pageTitle {
            color: #333;
            font-weight: 600;
            font-size: 16px;
        }
        
        .appHeader .icon {
            color: #666;
            transition: all 0.3s ease;
        }
        
        /* 内容区域 */
        #appCapsule {
            min-height: 100vh;
            overflow-y: auto;
            background: transparent;
            padding: 20px 0 80px 0;
            box-sizing: border-box;
            margin-bottom: 0;
        }
        
        /* Card styles */
        .card {
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: none;
            background: #fff;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        /* 活动标题区域美化 */
        .activity-title-section {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: #fff;
            padding: 25px 20px;
            border-radius: 16px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(111, 66, 193, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .activity-title-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }
        
        .activity-title-section .text-black-50 {
            color: #fff !important;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            position: relative;
            z-index: 1;
        }
        
        /* 卡片设计 - 美化风格 */
        .card {
            border-radius: 16px;
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            background: #fff;
            margin-bottom: 25px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .card-body {
            padding: 25px;
        }
        
        /* 参与者评价项美化 */
        .evaluation-item {
            background: #fff;
            border-radius: 12px;
            border: 1px solid #f0f0f0;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            position: relative;
            animation: slideInUp 0.5s ease-out;
        }
        
        .evaluation-item:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            border-color: #e0e0e0;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 参与者头部美化 */
        .evaluation-item .d-flex {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #6f42c1;
        }
        
        .evaluation-item .font14 {
            font-size: 16px !important;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        
        /* 标签选择区域美化 */
        .tag-select {
            margin-bottom: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            border: 1px solid #e9ecef;
            position: relative;
        }
        
        .tag-select::before {
            content: '选择标签';
            position: absolute;
            top: -8px;
            left: 15px;
            background: #fff;
            padding: 0 8px;
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }
        
        /* 标签样式美化 */
        .selected-tags .badge,
        .tag-select .badge {
            position: relative;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px !important;
            margin: 4px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            min-width: 60px;
            justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .tag-select .badge-outline-warning {
            background: #fff;
            color: #6f42c1;
            border-color: #6f42c1;
        }
        
        .tag-select .badge-outline-warning:hover {
            background: rgba(111, 66, 193, 0.1);
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(111, 66, 193, 0.2);
        }
        
        .tag-select .badge-warning {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: #fff;
            border-color: #6f42c1;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
        }
        
        .tag-select .badge-warning:hover {
            transform: scale(1.08);
            box-shadow: 0 6px 16px rgba(111, 66, 193, 0.4);
        }
        
        /* 表单控件美化 */
        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 14px;
            line-height: 1.6;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        
        .form-control:focus {
            border-color: #6f42c1;
            box-shadow: 0 0 0 4px rgba(111, 66, 193, 0.1);
            outline: none;
            background: #fff;
        }
        
        .form-control::placeholder {
            color: #999;
            font-style: italic;
        }
        
        /* 分割线美化 */
        .divider {
            border: none;
            height: 2px;
            background: linear-gradient(90deg, transparent, #6f42c1, transparent);
            margin: 25px 0;
            border-radius: 1px;
        }
        
        /* 提交按钮美化 */
        .btn {
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            padding: 15px 30px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: #fff;
            box-shadow: 0 6px 20px rgba(111, 66, 193, 0.3);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a2d91 0%, #4a1d7a 100%);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(111, 66, 193, 0.4);
        }
        
        .btn-primary:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
        }
        
        /* 进度指示器 */
        .progress-indicator {
            background: #fff;
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }
        
        .progress-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            text-align: center;
            font-weight: 500;
        }
        
        .progress-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #6f42c1 0%, #5a2d91 100%);
            transition: width 0.5s ease;
            border-radius: 4px;
        }
        
        /* 搜索框样式 */
        .search-container {
            background: #fff;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
        }
        
        .search-wrapper {
            position: relative;
            width: 100%;
        }
        
        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            color: #999;
            z-index: 10;
            pointer-events: none;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            background: #fafafa;
            transition: all 0.3s ease;
            outline: none;
            box-sizing: border-box;
            line-height: 1.5;
        }
        
        .search-input:focus {
            border-color: #6f42c1;
            box-shadow: 0 0 0 4px rgba(111, 66, 193, 0.1);
            background: #fff;
        }
        
        .search-input::placeholder {
            color: #999;
            font-style: italic;
        }
        
        .search-results {
            margin-top: 10px;
            font-size: 13px;
            color: #666;
            text-align: center;
        }
        
        .search-results .highlight {
            color: #6f42c1;
            font-weight: 600;
        }
        
        /* 隐藏的评价项 */
        .evaluation-item.hidden {
            display: none;
            animation: fadeOut 0.3s ease-out;
        }
        
        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(0.95);
            }
        }
        
        /* 显示的评价项 */
        .evaluation-item.visible {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }
        
        /* 无搜索结果提示 */
        .no-results {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-size: 14px;
            background: #f8f9fa;
            border-radius: 12px;
            margin: 20px 0;
            border: 1px dashed #e9ecef;
        }
        
        .no-results::before {
            content: '🔍';
            display: block;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .activity-title-section {
                padding: 20px 15px;
                margin-bottom: 20px;
            }
            
            .activity-title-section .text-black-50 {
                font-size: 16px;
            }
            
            .card-body {
                padding: 20px;
            }
            
            .evaluation-item {
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .tag-select {
                padding: 12px;
            }
            
            .tag-select .badge {
                padding: 6px 10px !important;
                font-size: 12px;
                margin: 3px;
            }
            
            .form-control {
                padding: 12px 15px;
                font-size: 13px;
            }
            
            .btn {
                padding: 12px 20px;
                font-size: 15px;
            }

            .search-container {
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .search-input {
                padding: 10px 15px 10px 45px;
                font-size: 13px;
            }
            
            .search-icon {
                left: 12px;
                font-size: 14px;
                width: 18px;
                height: 18px;
            }
        }
        
        @media (max-width: 480px) {
            .activity-title-section {
                padding: 15px;
                margin-bottom: 15px;
            }
            
            .activity-title-section .text-black-50 {
                font-size: 15px;
            }
            
            .card-body {
                padding: 15px;
            }
            
            .evaluation-item {
                padding: 12px;
                margin-bottom: 12px;
            }
            
            .tag-select {
                padding: 10px;
            }
            
            .tag-select .badge {
                padding: 5px 8px !important;
                font-size: 11px;
                margin: 2px;
                min-width: 50px;
            }
            
            .form-control {
                padding: 10px 12px;
                font-size: 12px;
            }
            
            .btn {
                padding: 10px 15px;
                font-size: 14px;
            }

            .search-container {
                padding: 12px;
                margin-bottom: 12px;
            }
            
            .search-input {
                padding: 8px 12px 8px 40px;
                font-size: 12px;
            }
            
            .search-icon {
                left: 10px;
                font-size: 12px;
                width: 16px;
                height: 16px;
            }
            
            .no-results {
                padding: 30px 15px;
                font-size: 13px;
            }
        }
        
        /* 加载状态 */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        /* 成功状态 */
        .evaluation-item.completed {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
        }
        
        .evaluation-item.completed::before {
            content: '✓';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            background: #28a745;
            color: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            个人点评
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <!-- 活动标题区域 -->
            <div class="activity-title-section">
                <span class="text-black-50 font-weight-bold font18" th:text="${selfEvaluations[0].activityName}"></span>
            </div>
            
            <!-- 进度指示器 -->
            <div class="progress-indicator">
                <div class="progress-text">评价进度</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>

            <!-- 搜索框 -->
            <div class="search-container">
                <div class="search-wrapper">
                    <span class="search-icon">🔍</span>
                    <input type="text" class="search-input" placeholder="搜索参与者姓名">
                </div>
            </div>

            <!-- 参与者列表和点评区域 -->
            <div class="section">
                <div class="card">
                    <div class="card-body">
                        <div th:each="evaluation, stat : ${selfEvaluations}" class="evaluation-item mb-3"
                             th:attr="data-user-id=${evaluation.userId}, data-initial-tags=${evaluation.tags}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="font14 font-weight-bold mb-0" th:text="${evaluation.realName + '的评价：'}"></span>
                            </div>
                            <div class="tag-select mb-2"></div>
                            <div class="form-group">
                                <input type="text" class="form-control evaluation-content"
                                       th:placeholder="'请输入对' + ${evaluation.realName} + '的点评内容...'"
                                       th:value="${evaluation.evaluationContent}">
                            </div>
                            <div th:if="${!stat.last}" class="divider dashed large mt-2 mb-2"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="section mt-2 mb-2">
                <button class="btn btn-primary rounded btn-block">提交所有点评</button>
            </div>
        </div>
    </div>
    <!-- * appCapsule -->

    <th:block th:insert="~{layouts/footActivity}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let activityId = getUrlParam('activityId');
        // 前端模拟标签数据
        let tagList = [
            "团队协作", "沟通表达", "领导能力", "创新思维", "执行力", "情绪管理", "问题解决", "学习能力",
            "时间管理", "压力管理", "目标导向", "责任心", "适应能力", "决策能力", "团队贡献", "职业素养",
            "自我认知", "人际交往", "抗压能力", "主动性", "专注力", "同理心", "批判性思维", "资源整合"
        ];
        $(function() {
            // 渲染标签到每个评价项
            $('.evaluation-item').each(function() {
                const $evaluationItem = $(this);
                var tagSelectDiv = $evaluationItem.find('.tag-select');
                const initialTagsString = $evaluationItem.data('initial-tags');
                const initialTags = initialTagsString ? initialTagsString.split(',') : [];

                tagSelectDiv.empty();
                tagList.forEach(function(tag) {
                    const isSelected = initialTags.includes(tag);
                    var tagSpan = $('<span class="badge mr-1 mb-1"></span>')
                        .text(tag)
                        .attr('data-value', tag)
                        .addClass(isSelected ? 'badge-warning' : 'badge-outline-warning');
                    tagSelectDiv.append(tagSpan);
                });
            });

            // 标签点击切换选中状态
            $(document).on('click', '.badge-outline-warning, .badge-warning', function() {
                if ($(this).hasClass('badge-outline-warning')) {
                    $(this).removeClass('badge-outline-warning').addClass('badge-warning');
                } else {
                    $(this).removeClass('badge-warning').addClass('badge-outline-warning');
                }
                updateProgress();
            });
            
            // 监听输入框变化
            $(document).on('input', '.evaluation-content', function() {
                updateProgress();
            });
            
            // 更新进度条
            function updateProgress() {
                const totalItems = $('.evaluation-item').length;
                let completedItems = 0;
                
                $('.evaluation-item').each(function() {
                    const content = $(this).find('.evaluation-content').val();
                    const tags = $(this).find('.tag-select .badge-warning').length;
                    
                    if (content && tags > 0) {
                        completedItems++;
                        $(this).addClass('completed');
                    } else {
                        $(this).removeClass('completed');
                    }
                });
                
                const progress = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;
                $('.progress-fill').css('width', progress + '%');
                $('.progress-text').text(`评价进度 (${completedItems}/${totalItems})`);
            }
            
            // 初始化进度
            updateProgress();
            
            // 搜索功能
            function initSearch() {
                const searchInput = $('.search-input');
                const evaluationItems = $('.evaluation-item');
                const cardBody = $('.card-body');
                
                // 搜索函数
                function performSearch() {
                    const searchTerm = searchInput.val().trim().toLowerCase();
                    let visibleCount = 0;
                    let totalCount = evaluationItems.length;
                    
                    // 移除之前的搜索结果提示
                    $('.search-results').remove();
                    $('.no-results').remove();
                    
                    if (searchTerm === '') {
                        // 如果搜索框为空，显示所有评价项
                        evaluationItems.removeClass('hidden').addClass('visible');
                        visibleCount = totalCount;
                    } else {
                        // 执行搜索
                        evaluationItems.each(function() {
                            const $item = $(this);
                            const participantName = $item.find('.font14').text().toLowerCase();
                            
                            if (participantName.includes(searchTerm)) {
                                $item.removeClass('hidden').addClass('visible');
                                visibleCount++;
                            } else {
                                $item.removeClass('visible').addClass('hidden');
                            }
                        });
                    }
                    
                    // 显示搜索结果统计
                    if (searchTerm !== '') {
                        const resultsText = visibleCount > 0 
                            ? `找到 <span class="highlight">${visibleCount}</span> 个参与者（共 ${totalCount} 个）`
                            : `未找到包含 "<span class="highlight">${searchTerm}</span>" 的参与者`;
                        
                        const resultsDiv = $('<div class="search-results"></div>').html(resultsText);
                        $('.search-container').append(resultsDiv);
                        
                        // 如果没有搜索结果，显示提示
                        if (visibleCount === 0) {
                            const noResultsDiv = $('<div class="no-results">没有找到匹配的参与者，请尝试其他关键词</div>');
                            cardBody.append(noResultsDiv);
                        }
                    }
                    
                    // 更新进度条（只计算可见的评价项）
                    updateProgressForVisible();
                }
                
                // 为可见的评价项更新进度
                function updateProgressForVisible() {
                    const visibleItems = $('.evaluation-item:not(.hidden)');
                    let completedItems = 0;
                    
                    visibleItems.each(function() {
                        const content = $(this).find('.evaluation-content').val();
                        const tags = $(this).find('.tag-select .badge-warning').length;
                        
                        if (content && tags > 0) {
                            completedItems++;
                            $(this).addClass('completed');
                        } else {
                            $(this).removeClass('completed');
                        }
                    });
                    
                    const totalVisible = visibleItems.length;
                    const progress = totalVisible > 0 ? (completedItems / totalVisible) * 100 : 0;
                    $('.progress-fill').css('width', progress + '%');
                    
                    // 更新进度文本
                    if ($('.search-input').val().trim() !== '') {
                        $('.progress-text').text(`可见评价进度 (${completedItems}/${totalVisible})`);
                    } else {
                        $('.progress-text').text(`评价进度 (${completedItems}/${totalVisible})`);
                    }
                }
                
                // 实时搜索（防抖）
                let searchTimeout;
                searchInput.on('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(performSearch, 300);
                });
                
                // 清空搜索框时恢复所有显示
                searchInput.on('keyup', function(e) {
                    if (e.key === 'Escape') {
                        $(this).val('');
                        performSearch();
                    }
                });
                
                // 点击搜索框时全选文本
                searchInput.on('focus', function() {
                    if ($(this).val() !== '') {
                        $(this).select();
                    }
                });
                
                // 初始化搜索状态
                performSearch();
            }
            
            // 初始化搜索功能
            initSearch();
            
            // 修改原有的updateProgress函数，使其与搜索功能兼容
            const originalUpdateProgress = updateProgress;
            updateProgress = function() {
                if ($('.search-input').val().trim() !== '') {
                    // 如果正在搜索，只更新可见项的进度
                    const visibleItems = $('.evaluation-item:not(.hidden)');
                    let completedItems = 0;
                    
                    visibleItems.each(function() {
                        const content = $(this).find('.evaluation-content').val();
                        const tags = $(this).find('.tag-select .badge-warning').length;
                        
                        if (content && tags > 0) {
                            completedItems++;
                            $(this).addClass('completed');
                        } else {
                            $(this).removeClass('completed');
                        }
                    });
                    
                    const totalVisible = visibleItems.length;
                    const progress = totalVisible > 0 ? (completedItems / totalVisible) * 100 : 0;
                    $('.progress-fill').css('width', progress + '%');
                    $('.progress-text').text(`可见评价进度 (${completedItems}/${totalVisible})`);
                } else {
                    // 如果没有搜索，使用原来的逻辑
                    originalUpdateProgress();
                }
            };
            
            // 提交点评
            $('.btn-block').click(function() {
                const evaluations = [];
                let hasError = false;

                // 收集所有参与者的点评数据
                $('.evaluation-item').each(function() {
                    const participantId = $(this).data('user-id');
                    const content = $(this).find('.evaluation-content').val();
                    const tags = [];
                    $(this).find('.tag-select .badge-warning').each(function() {
                        tags.push($(this).data('value'));
                    });

                    if (content || tags.length > 0) {
                        if (!content) {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>请输入点评内容'
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                            hasError = true;
                            return false;
                        }
                        if (tags.length === 0) {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>请至少选择一个标签'
                                , style: 'background-color:#ffffff; border:none;'
                                , time: 2
                            });
                            hasError = true;
                            return false;
                        }

                        evaluations.push({
                            // 活动ID从页面获取
                            activityId: activityId,
                            // 被点评人ID
                            userId: participantId,
                            // 点评内容
                            evaluationContent: content,
                            // 标签（逗号分隔）
                            tags: tags.join(',')
                        });
                    }
                });

                if (hasError) {
                    return;
                }

                if (evaluations.length === 0) {
                    layer.open({
                        content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>请至少完成一个参与者的点评'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                    return;
                }
                layer.open({type: 2, content: '请稍后…', shadeClose: false});
                // 提交数据到后端
                $.ajax({
                    url: '/activityroom/activity/add_self_evaluation',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(evaluations),
                    success: function(res) {
                        layer.closeAll();
                        if(res.resultCode === 200) {
                            layer.open({
                                content: '<img src="/static/images/success.png" width="25" class="mr-1"/>'+ res.resultMsg
                               , style: 'background-color:#ffffff; border:none;'
                               , time: 2
                            });
                        }
                        else {
                            layer.open({
                                content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>'+res.resultMsg
                              , style: 'background-color:#ffffff; border:none;'
                              , time: 2
                            });
                        }
                    },
                    error: function() {
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>提交失败，请重试'
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                });
            });
        });
    </script>
</th:block>
</body>
</html>
<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            我的活动
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent">
            <div class="mt-1 mb-3">
                <img th:src="@{/static/images/app_activity_banner.jpg}" alt="image" class="imageBlock img-fluid rounded" title="活动" />
            </div>
            <th:block th:if="${(not #lists.isEmpty(myActivities)) and myActivities.size() gt 0 }">
                <div class="divider mt-1 mb-2 hide"></div>
                <!-- * Card Overlay Carousel -->
                <div class="sectionTitle mb-1">
                    <div class="title">
                        <h2 class="font18">我的活动</h2>
                    </div>
                </div>
                <div class="itemList">
                    <th:block th:each="activity,itemStat:${myActivities}">
                        <div class="mb-1">
                            <a th:href="@{/app/activity/detail(activityId=${activity.id})}">
                                <div class="item">
                                    <div class="image mr-2">
                                        <th:block th:if="${activity.activityCover ne null and activity.activityCover ne ''}">
                                            <img th:src="|@{/static/upload/activity_cover/}${activity.activityCover}|" alt="image">
                                        </th:block>
                                        <th:block th:unless="${activity.activityCover ne null and activity.activityCover ne ''}">
                                            <img th:src="@{/static/images/nopic.png}" alt="image">
                                        </th:block>
                                    </div>
                                    <div class="text" style="width:100%;">
                                        <div class="font16 font-weight-bold pt-0 pb-1 text-dark" th:text="${activity.activityName}"></div>
                                        <div class="text-muted font12">
                                            活动时间：<th:block th:text="${#dates.format(activity.startTime,'yyyy-MM-dd HH:mm')}"></th:block> - <th:block th:text="${#dates.format(activity.endTime,'HH:mm')}"></th:block>
                                            <span class="badge badge-outline-primary pull-right camp-detail">查看详细</span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                            <div class="text-muted font12 mt-2" th:if="${activity.tags ne null and activity.tags ne ''}">
                                获得的个人评价：
                                <th:block th:each="tag : ${#strings.arraySplit(activity.tags, ',')}">
                                    <span class="badge badge-outline-danger badge-pill mr-1" th:text="${tag}"></span>
                                </th:block>
                            </div>
                        </div>
                        <th:block th:unless="${itemStat.last}">
                            <div class="divider dashed large mt-3 mb-3"></div></th:block>
                    </th:block>
                </div>
            </th:block>
            <th:block th:unless="${(not #lists.isEmpty(myActivities)) and myActivities.size() gt 0 }">
                <div class="lead text-center mb-3 mt-3">
                    <img src="/static/images/warning.png" width="22" class="mr-1"><span>没有活动记录！</span>
                </div>
            </th:block>
        </div>
    </div>
    <!-- * appCapsule -->
    <th:block th:insert="~{layouts/footActivity}"/>
</th:block>
<th:block layout:fragment="common_js">

</th:block>
</body>
</html>
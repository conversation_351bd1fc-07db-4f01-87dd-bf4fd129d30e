<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title>活动图库</title>
    <style>
        /* 图片网格布局 */
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            padding: 10px;
        }
        
        .gallery-item {
            position: relative;
            width: 100%;
            padding-bottom: 100%; /* 1:1 宽高比 */
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .gallery-item img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:active img {
            transform: scale(0.95);
        }

        /* 删除按钮样式 */
        .delete-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            opacity: 0;
            transition: opacity 0.3s ease;
            border: none;
            padding: 0;
        }

        .gallery-item:hover .delete-btn,
        .gallery-item.selected .delete-btn {
            opacity: 1;
        }

        .delete-btn:active {
            transform: scale(0.9);
        }

        .delete-btn i {
            font-size: 12px;
        }
        
        /* 空状态提示 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #dee2e6;
        }
        
        /* 图片查看器样式 */
        .image-viewer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            display: none;
        }
        
        .image-viewer img {
            max-width: 100%;
            max-height: 100%;
            margin: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .image-viewer .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 24px;
            z-index: 10000;
        }

        /* 上传按钮样式 */
        .upload-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            padding: 0;
        }

        .upload-btn:active {
            transform: scale(0.95);
        }

        .upload-btn i {
            font-size: 20px;
        }

        /* 上传进度条样式 */
        .upload-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: #f0f0f0;
            z-index: 10001;
            display: none;
        }

        .upload-progress-bar {
            height: 100%;
            background: #007bff;
            width: 0;
            transition: width 0.3s ease;
        }

        /* 确认删除对话框样式 */
        .confirm-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10002;
            width: 80%;
            max-width: 300px;
            display: none;
        }

        .confirm-dialog .message {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .confirm-dialog .buttons {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }

        .confirm-dialog .btn {
            flex: 1;
            padding: 8px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }

        .confirm-dialog .btn-cancel {
            background: #f0f0f0;
            color: #333;
        }

        .confirm-dialog .btn-confirm {
            background: #dc3545;
            color: white;
        }

        /* 选中状态样式 */
        .gallery-item.selected {
            box-shadow: 0 0 0 2px #007bff;
        }

        /* 批量删除按钮样式 */
        .batch-delete-btn {
            position: fixed;
            bottom: 80px;
            right: 90px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            display: none;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1000;
            border: none;
        }

        .batch-delete-btn:active {
            transform: scale(0.95);
        }

        .batch-delete-btn i {
            font-size: 24px;
        }

        .batch-delete-btn.show {
            display: flex;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .gallery-grid {
                gap: 8px;
                padding: 8px;
            }
            
            .gallery-item {
                border-radius: 6px;
            }
            
            .delete-btn {
                width: 20px;
                height: 20px;
                top: 6px;
                right: 6px;
            }
            
            .delete-btn i {
                font-size: 10px;
            }
            
            .batch-delete-btn {
                bottom: 70px;
                right: 80px;
                width: 50px;
                height: 50px;
            }
            
            .batch-delete-btn i {
                font-size: 20px;
            }
        }
        
        @media (max-width: 480px) {
            .gallery-grid {
                gap: 6px;
                padding: 6px;
            }
            
            .gallery-item {
                border-radius: 4px;
            }
            
            .delete-btn {
                width: 18px;
                height: 18px;
                top: 4px;
                right: 4px;
            }
            
            .delete-btn i {
                font-size: 9px;
            }
            
            .batch-delete-btn {
                bottom: 60px;
                right: 70px;
                width: 45px;
                height: 45px;
            }
            
            .batch-delete-btn i {
                font-size: 18px;
            }
            
            .empty-state {
                padding: 30px 15px;
            }
            
            .empty-state i {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="javascript:" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle">
            <th:block th:text="${activity.activityName}"/>
        </div>
        <div class="right">
            <a class="icon" href="javascript:" onclick="triggerFileInput()">
                <i class="fa fa-camera text-primary"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    
    <!-- App Capsule -->
    <div id="appCapsule">
        <div class="appContent pt-2">
            <!-- 图片网格 -->
            <div class="gallery-grid" id="galleryContainer">
                <!-- 图片将通过JavaScript动态加载 -->
            </div>
            
            <!-- 空状态提示 -->
            <div class="empty-state" style="display: none;">
                <i class="fa fa-image"></i>
                <p>暂没有活动图片</p>
            </div>
            
            <!-- 图片查看器 -->
            <div class="image-viewer" id="imageViewer">
                <a href="javascript:" class="close-btn" onclick="closeImageViewer()">
                    <i class="fa fa-times"></i>
                </a>
                <img id="fullImage" src="" alt="查看大图">
            </div>

            <!-- 批量删除按钮 -->
            <button class="batch-delete-btn" id="batchDeleteBtn" onclick="showBatchDeleteConfirm()">
                <i class="fa fa-trash"></i>
            </button>
            <input type="file" id="fileInput" accept="image/*" multiple style="display: none;" onchange="handleFileSelect(event)">

            <!-- 上传进度条 -->
            <div class="upload-progress" id="uploadProgress">
                <div class="upload-progress-bar" id="uploadProgressBar"></div>
            </div>

            <!-- 确认删除对话框 -->
            <div class="confirm-dialog" id="confirmDialog">
                <div class="message">确定要删除选中的图片吗？</div>
                <div class="buttons">
                    <button class="btn btn-cancel" onclick="closeConfirmDialog()">取消</button>
                    <button class="btn btn-confirm" onclick="confirmDelete()">删除</button>
                </div>
            </div>
        </div>
    </div>
    <!-- * appCapsule -->
    <th:block th:insert="~{layouts/footActivity}"/>
</th:block>

<th:block layout:fragment="common_js">
    <script type="text/javascript">
        let activityId = getUrlParam('activityId');
        let currentDeleteId = null;
        let isSelectionMode = false;
        let selectedImages = new Set();

        $(function() {
            // 页面加载完成后获取图片列表
            getGalleryImages();
            
            // 返回按钮事件
            $('.goBack').on('click', function() {
                if (isSelectionMode) {
                    exitSelectionMode();
                    return;
                }
                history.back();
            });

            // 添加长按事件处理
            $(document).on('touchstart', '.gallery-item', function(e) {
                const item = $(this);
                const timer = setTimeout(function() {
                    isSelectionMode = true;
                    item.addClass('selected');
                    selectedImages.add(item.data('id'));
                    updateBatchDeleteButton();
                    showToast('已进入选择模式');
                }, 500);

                item.data('timer', timer);
            });

            $(document).on('touchend touchcancel', '.gallery-item', function(e) {
                const item = $(this);
                clearTimeout(item.data('timer'));
            });
        });
        
        // 获取图片列表
        function getGalleryImages() {
            $.ajax({
                type: "POST",
                url: "/activityroom/activity_pic/getPicList",
                data: 'activityId=' + activityId,
                dataType: "json",
                success: function(data) {
                    if(data.length > 0) {
                        let html = '';
                        data.forEach(function(pic) {
                            html += '<div class="gallery-item" data-id="' + pic.id + '">';
                            html += '<img src="/static/upload/activity/mobile/' + pic.fileName + '" alt="活动图片" onclick="handleImageClick(\'' + pic.fileName + '\', \'' + pic.id + '\', event)">';
                            html += '<button class="delete-btn" onclick="showDeleteConfirm(\'' + pic.id + '\')">';
                            html += '<i class="fa fa-times"></i>';
                            html += '</button>';
                            html += '</div>';
                        });
                        $('#galleryContainer').html(html);
                        $('.empty-state').hide();
                    } else {
                        $('#galleryContainer').html('');
                        $('.empty-state').show();
                    }
                },
                error: function() {
                    showToast('获取图片列表失败');
                }
            });
        }

        // 处理图片点击事件
        function handleImageClick(fileName, id, event) {
            if (isSelectionMode) {
                // 在选中模式下，切换选中状态
                const item = $(event.target).closest('.gallery-item');
                item.toggleClass('selected');
                
                if (item.hasClass('selected')) {
                    selectedImages.add(id);
                } else {
                    selectedImages.delete(id);
                }
                
                updateBatchDeleteButton();
            } else {
                // 非选中模式下，查看大图
                viewImage(fileName);
            }
        }

        // 更新批量删除按钮显示状态
        function updateBatchDeleteButton() {
            if (selectedImages.size > 0) {
                $('#batchDeleteBtn').addClass('show');
            } else {
                $('#batchDeleteBtn').removeClass('show');
            }
        }

        // 退出选择模式
        function exitSelectionMode() {
            isSelectionMode = false;
            selectedImages.clear();
            $('.gallery-item').removeClass('selected');
            $('#batchDeleteBtn').removeClass('show');
            showToast('已退出选择模式');
        }

        // 添加取消选择模式的功能
        $(document).on('click', function(e) {
            if (isSelectionMode && !$(e.target).closest('.gallery-item').length && !$(e.target).closest('#batchDeleteBtn').length) {
                exitSelectionMode();
            }
        });

        // 查看大图
        function viewImage(fileName) {
            $('#fullImage').attr('src', '/static/upload/activity/mobile/' + fileName);
            $('#imageViewer').fadeIn();
            
            // 禁止背景滚动
            $('body').css('overflow', 'hidden');
        }
        
        // 关闭图片查看器
        function closeImageViewer() {
            $('#imageViewer').fadeOut();
            
            // 恢复背景滚动
            $('body').css('overflow', '');
        }
        
        // 点击图片查看器背景关闭
        $('#imageViewer').on('click', function(e) {
            if(e.target === this) {
                closeImageViewer();
            }
        });

        // 触发文件选择
        function triggerFileInput() {
            $('#fileInput').click();
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length === 0) return;

            // 显示进度条
            $('#uploadProgress').show();
            $('#uploadProgressBar').css('width', '0%');

            // 创建FormData对象
            const formData = new FormData();
            formData.append('activityId', activityId);
            for (let i = 0; i < files.length; i++) {
                formData.append('file', files[i]);
            }

            // 上传文件
            $.ajax({
                url: '/fileUpload/activity_pic',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            const percent = Math.round((e.loaded / e.total) * 100);
                            $('#uploadProgressBar').css('width', percent + '%');
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    if (response.resultCode === 200) {
                        showToast('上传成功');
                        // 重新加载图片列表
                        getGalleryImages();
                    } else {
                        showToast(response.resultMsg || '上传失败');
                    }
                },
                error: function() {
                    showToast('上传失败，请重试');
                },
                complete: function() {
                    // 隐藏进度条
                    setTimeout(function() {
                        $('#uploadProgress').hide();
                        $('#uploadProgressBar').css('width', '0%');
                    }, 500);
                    
                    // 清空文件输入框
                    $('#fileInput').val('');
                }
            });
        }

        // 显示删除确认对话框
        function showDeleteConfirm(id) {
            currentDeleteId = id;
            $('#confirmDialog').fadeIn();
            $('body').css('overflow', 'hidden');
        }

        // 关闭确认对话框
        function closeConfirmDialog() {
            $('#confirmDialog').fadeOut();
            $('body').css('overflow', '');
            currentDeleteId = null;
        }

        // 显示批量删除确认对话框
        function showBatchDeleteConfirm() {
            if (selectedImages.size === 0) return;
            
            $('#confirmDialog .message').text('确定要删除选中的 ' + selectedImages.size + ' 张图片吗？');
            $('#confirmDialog').fadeIn();
            $('body').css('overflow', 'hidden');
        }

        // 确认删除
        function confirmDelete() {
            if (isSelectionMode && selectedImages.size > 0) {
                // 批量删除
                const ids = Array.from(selectedImages);
                $.ajax({
                    type: "POST",
                    url: "/activityroom/activity_pic/batch_del_pic",
                    data: {ids: ids.join(',')},
                    dataType: "json",
                    success: function(res) {
                        if(res.resultCode === 200) {
                            showToast('删除成功');
                            //exitSelectionMode();
                            getGalleryImages();
                        } else {
                            showToast(res.resultMsg || '删除失败');
                        }
                    },
                    error: function() {
                        showToast('删除失败，请重试');
                    },
                    complete: function() {
                        closeConfirmDialog();
                    }
                });
            } else if (currentDeleteId) {
                // 单张删除
                $.ajax({
                    type: "POST",
                    url: "/activityroom/clocking/batch_del_pic",
                    data: currentDeleteId,
                    contentType: "application/json",
                    dataType: "json",
                    success: function(res) {
                        if(res.resultCode === 200) {
                            showToast('删除成功');
                            getGalleryImages();
                        } else {
                            showToast(res.resultMsg || '删除失败');
                        }
                    },
                    error: function() {
                        showToast('删除失败，请重试');
                    },
                    complete: function() {
                        closeConfirmDialog();
                    }
                });
            }
        }

        // 显示提示消息
        function showToast(message) {
            // 创建提示元素
            const toast = $('<div class="toast-message">' + message + '</div>');
            
            // 添加样式
            toast.css({
                'position': 'fixed',
                'top': '50%',
                'left': '50%',
                'transform': 'translate(-50%, -50%)',
                'background': 'rgba(0, 0, 0, 0.7)',
                'color': '#fff',
                'padding': '10px 20px',
                'border-radius': '4px',
                'z-index': '10000',
                'font-size': '14px',
                'max-width': '80%',
                'text-align': 'center'
            });
            
            // 添加到页面
            $('body').append(toast);
            
            // 2秒后移除
            setTimeout(function() {
                toast.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 2000);
        }
    </script>
</th:block>
</body>
</html>
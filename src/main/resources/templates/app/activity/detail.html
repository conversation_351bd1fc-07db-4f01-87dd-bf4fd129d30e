<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        /* 全局样式 - 简洁风格 */
        body {
            background: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* 应用头部 - 简洁风格 */
        .appHeader {
            background: #fff;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .appHeader .pageTitle {
            color: #333;
            font-weight: 600;
            font-size: 16px;
        }
        
        .appHeader .icon {
            color: #666;
            transition: all 0.3s ease;
        }
        
        .appHeader .icon:hover {
            transform: scale(1.1);
            opacity: 0.8;
        }
        
        /* 底部菜单 - 简洁风格 */
        .appBottomMenu2 {
            position: fixed;
            z-index: 9999;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            border-top: 1px solid #e9ecef;
            padding: 15px 20px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            min-height: 90px;
            box-sizing: border-box;
        }
        
        /* 小屏幕设备优化 */
        @media screen and (max-height: 600px) {
            .appBottomMenu2 {
                min-height: 70px;
                padding: 10px 15px;
            }
        }
        
        @media screen and (max-height: 500px) {
            .appBottomMenu2 {
                min-height: 60px;
                padding: 8px 12px;
            }
        }
        
        /* 横屏模式优化 */
        @media screen and (orientation: landscape) and (max-height: 400px) {
            .appBottomMenu2 {
                min-height: 50px;
                padding: 6px 10px;
            }
        }
        
        /* 底部按钮容器 */
        .bottom-buttons-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;
        }
        
        /* 底部按钮样式 */
        .bottom-btn {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 12px 8px;
            border-radius: 8px;
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            text-decoration: none;
            min-height: 70px;
            box-sizing: border-box;
        }
        
        .bottom-btn:hover {
            background: #e9ecef;
            color: #333;
            transform: translateY(-1px);
            text-decoration: none;
        }
        
        .bottom-btn i {
            font-size: 20px;
            margin-bottom: 6px;
            display: block;
        }
        
        .bottom-btn .btn-text {
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            line-height: 1.4;
            letter-spacing: 0.5px;
        }
        
        /* 移动端底部按钮优化 */
        @media (max-width: 480px) {
            .bottom-buttons-container {
                gap: 8px;
            }
            
            .bottom-btn {
                padding: 10px 6px;
                min-height: 60px;
            }
            
            .bottom-btn i {
                font-size: 18px;
                margin-bottom: 4px;
            }
            
            .bottom-btn .btn-text {
                font-size: 12px;
                letter-spacing: 0.3px;
            }
        }
        
        /* 内容区域 */
        #appCapsule {
            min-height: 100vh;
            overflow-y: auto;
            background: transparent;
            padding: 20px 0 20px 0;
            box-sizing: border-box;
            margin-bottom: 0;
        }
        
        /* 移动端特殊处理 */
        @media screen and (max-height: 600px) {
            #appCapsule {
                padding-bottom: 30px !important;
            }
        }
        
        @media screen and (max-height: 500px) {
            #appCapsule {
                padding-bottom: 25px !important;
            }
        }
        
        /* 横屏模式处理 */
        @media screen and (orientation: landscape) and (max-height: 400px) {
            #appCapsule {
                padding-bottom: 20px !important;
            }
        }
        
        /* 强制确保内容不被遮挡 */
        #appCapsule::after {
            content: '';
            display: block;
            height: 30px;
            width: 100%;
            pointer-events: none;
        }
        
        /* 使用CSS变量动态调整 */
        #appCapsule {
            --bottom-padding: 30px;
            padding-bottom: var(--bottom-padding) !important;
        }
        
        /* 强制确保底部有足够空间 */
        .appContent {
            padding-bottom: 30px !important;
        }
        
        /* 最后一个元素强制底部间距 */
        .appContent > *:last-child {
            margin-bottom: 30px !important;
        }
        
        /* 卡片设计 - 简洁风格 */
        
        /* 评分单选题样式 - 重新设计为简洁的滑块式 */
        .rating-options-container {
            width: 100%;
            margin: 15px 0;
        }
        
        .rating-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .rating-container.has-error {
            border-color: #dc3545 !important;
            background: #fff5f5 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }
        
        .rating-slider {
            position: relative;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .rating-slider:hover {
            background: #dee2e6;
        }
        
        .rating-fill {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, #2196f3, #1976d2);
            border-radius: 6px;
            transition: width 0.2s ease-out;
            width: 0%;
            pointer-events: none;
        }
        
        .rating-thumb {
            position: absolute;
            top: 50%;
            width: 24px;
            height: 24px;
            background: #2196f3;
            border: 4px solid white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 3px 12px rgba(33, 150, 243, 0.4);
            cursor: grab;
            transition: all 0.15s ease-out;
            left: 0%;
            z-index: 10;
        }
        
        .rating-thumb:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 4px 16px rgba(33, 150, 243, 0.5);
        }
        
        .rating-thumb:active,
        .rating-thumb.dragging {
            cursor: grabbing;
            transform: translate(-50%, -50%) scale(1.15);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6);
            transition: transform 0.1s ease-out;
        }
        
        .rating-marks {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .rating-mark {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
            flex: 1;
        }
        
        .rating-mark:hover {
            background: rgba(33, 150, 243, 0.1);
            transform: translateY(-2px);
        }
        
        .rating-mark.active {
            background: rgba(33, 150, 243, 0.15);
            color: #2196f3;
        }
        
        .rating-number {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .rating-label {
            font-size: 11px;
            text-align: center;
            line-height: 1.2;
        }
        
        .rating-value-display {
            text-align: center;
            margin-top: 15px;
            font-size: 16px;
            font-weight: 600;
            color: #2196f3;
            min-height: 24px;
        }
        
        .rating-error {
            color: #dc3545;
            font-size: 13px;
            margin-top: 10px;
            text-align: center;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 移动端优化 */
        @media (max-width: 480px) {
            .rating-container {
                padding: 15px;
            }
            
            .rating-thumb {
                width: 24px;
                height: 24px;
            }
            
            .rating-number {
                font-size: 16px;
            }
            
            .rating-label {
                font-size: 10px;
            }
        }
        
        /* 删除旧的按钮样式 */
        .rating-buttons,
        .rating-btn {
            display: none !important;
        }
        
        /* 删除旧的评分题样式 */
        .rating-scale-container,
        .rating-scale-bar,
        .rating-scale-fill,
        .rating-scale-thumb,
        .rating-scale-marks,
        .rating-scale-mark,
        .rating-scale-number,
        .rating-scale-label,
        .rating-scale-value {
            display: none !important;
        }
        
        /* 图片容器 - 简洁风格 */
        .image-container {
            position: relative;
            overflow: hidden;
            background: #fff;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        

        
        .image-container img {
            width: 100%;
            height: auto;
            display: block;
            filter: brightness(0.9);
        }
        
        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.6);
            padding: 30px 20px 20px;
            color: #fff;
            text-align: center;
        }
        
        .image-overlay h2 {
            color: #fff;
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            line-height: 1.3;
        }
        

        
        /* 标签页设计 - 简洁风格 */
        .nav-tabs {
            border: none;
            background: #fff;
            border-radius: 8px;
            padding: 4px;
            border: 1px solid #e9ecef;
            margin-bottom: 20px;
        }
        
        .nav-tabs .nav-link {
            border: none;
            border-radius: 6px;
            color: #666;
            font-weight: 500;
            font-size: 14px;
            padding: 10px 16px;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link.active {
            background: #6f42c1;
            color: #fff;
        }
        
        .nav-tabs .nav-link:hover:not(.active) {
            background: #f8f9fa;
            color: #333;
        }
        
        /* 内容盒子设计 - 简洁风格 */
        .contentBox {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: none;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .contentBox .title {
            background: #f8f9fa;
            color: #333;
            padding: 15px 20px;
            margin: 0;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .contentBox-body {
            padding: 20px;
            line-height: 1.6;
            color: #333;
            font-size: 14px;
        }

        /* 时间信息设计 - 简洁风格 */
        .time-info {
            padding: 0;
        }
        
        .time-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
            position: relative;
        }
        
        .time-item:hover {
            background: #f8f9fa;
        }
        
        .time-item:last-child {
            border-bottom: none;
        }
        
        .time-item i {
            font-size: 16px;
            margin-right: 10px;
            width: 20px;
            text-align: center;
            color: #666;
        }
        
        .time-label {
            font-weight: 600;
            color: #333;
            margin-right: 15px;
            min-width: 90px;
        }
        
        .time-value {
            color: #666;
            font-weight: 500;
            font-size: 14px;
            flex: 1;
        }
        
        /* 模态框设计 - 治愈风格 */
        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            border: none;
            padding: 25px 30px;
        }
        
        .modal-header .modal-title {
            color: #fff;
            font-weight: 700;
            font-size: 18px;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .modal-header .close {
            color: #fff;
            opacity: 0.8;
            transition: all 0.3s ease;
        }
        
        .modal-header .close:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        
        .modal-body {
            padding: 30px;
            background: #f8f9fa;
        }
        
        .modal-footer {
            border: none;
            padding: 25px 30px;
            background: #f8f9fa;
        }
        
        /* 排序题样式 */
        .sortable-list {
            min-height: 100px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            touch-action: none; /* 防止触摸滚动 */
            overflow: visible;
        }
        .sortable-item {
            cursor: move;
            user-select: none;
            transition: all 0.2s ease;
            touch-action: none; /* 防止触摸滚动 */
            position: relative;
            z-index: 1;
        }
        .sortable-item.dragging {
            z-index: 1000;
            pointer-events: none;
        }
        .sortable-item:hover .sortable-content {
            background-color: #e3f2fd !important;
            border-color: #2196f3 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }
        .sortable-content {
            transition: all 0.2s ease;
            touch-action: none; /* 防止触摸滚动 */
        }
        .sortable-placeholder {
            background-color: #e3f2fd;
            border: 2px dashed #2196f3;
            height: 60px;
            margin-bottom: 8px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2196f3;
            font-weight: 500;
        }
        .sortable-ghost {
            opacity: 0.3;
        }
        .drag-handle {
            cursor: grab;
            touch-action: none; /* 防止触摸滚动 */
            padding: 5px;
        }
        .drag-handle:active {
            cursor: grabbing;
        }
        .drag-handle:hover {
            color: #2196f3 !important;
        }

        /* 模态框中的排序题特殊处理 */
        .modal-body .sortable-list {
            position: relative;
            z-index: 1;
        }

        /* 防止模态框滚动干扰 */
        .modal.sorting-active {
            pointer-events: none;
        }
        .modal.sorting-active .modal-content {
            pointer-events: auto;
        }

        /* 表单控件设计 - 治愈风格 */
        .form-control {
            border-radius: 12px;
            border: 2px solid #dee2e6;
            padding: 15px 20px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus {
            border-color: #6f42c1;
            box-shadow: 0 0 0 4px rgba(111, 66, 193, 0.1);
            outline: none;
            background: #fff;
        }
        
        /* 单选和复选框设计 - 治愈风格 */
        .custom-control-input:checked ~ .custom-control-label::before {
            background-color: #6f42c1;
            border-color: #6f42c1;
            box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
        }
        
        .custom-control-label {
            color: #495057;
            font-size: 15px;
            cursor: pointer;
            font-weight: 500;
        }
        
        /* 问题容器设计 - 治愈风格 */
        .question-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            border: 2px solid rgba(111, 66, 193, 0.1);
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .question-container:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .question-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .question-number {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: #fff;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 4px 15px rgba(111, 66, 193, 0.4);
        }
        
        .question-content {
            font-weight: 700;
            color: #2c3e50;
            line-height: 1.6;
            font-size: 16px;
        }
        
        /* 调查问卷介绍 - 治愈风格 */
        .survey-intro .alert {
            border-radius: 15px;
            border: none;
            background: linear-gradient(135deg, rgba(111, 66, 193, 0.1) 0%, rgba(90, 45, 145, 0.1) 100%);
            color: #495057;
            padding: 20px;
            font-weight: 500;
        }
        
        /* 成功图标 - 治愈风格 */
        .success-icon {
            color: #28a745;
            font-size: 60px;
            margin-bottom: 20px;
            animation: bounceIn 0.8s ease-out;
        }
        
        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        /* 分割线 - 简洁风格 */
        .divider {
            border: none;
            height: 1px;
            background: #e9ecef;
            margin: 20px 0;
        }
        
        /* 滚动条美化 - 治愈风格 */
        #appCapsule::-webkit-scrollbar {
            width: 8px;
        }
        
        #appCapsule::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        #appCapsule::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            border-radius: 4px;
        }
        
        #appCapsule::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a2d91 0%, #4a1d7a 100%);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .image-overlay h2 {
                font-size: 20px;
            }
            
            .contentBox .title {
                font-size: 15px;
                padding: 18px 20px;
            }
            
            .contentBox-body {
                padding: 20px;
            }
            
            .btn {
                padding: 10px 20px;
                font-size: 14px;
            }
            
            .modal-body {
                padding: 25px;
            }
            
            .bottom-buttons-container {
                gap: 10px;
            }
            
            .bottom-btn {
                padding: 10px 6px;
                min-height: 65px;
            }
            
            .bottom-btn i {
                font-size: 18px;
                margin-bottom: 5px;
            }
            
            .bottom-btn .btn-text {
                font-size: 11px;
            }
            
            .image-overlay h2 {
                font-size: 16px;
            }
        }
        
        @media (max-width: 480px) {
            #appCapsule {
                padding: 15px 0 15px 0;
            }
            
            .card {
                margin-bottom: 20px;
            }
            
            .contentBox {
                margin-bottom: 20px;
            }
            
            .nav-tabs {
                margin-bottom: 20px;
            }
            
            .bottom-buttons-container {
                gap: 8px;
            }
            
            .bottom-btn {
                padding: 8px 4px;
                min-height: 60px;
            }
            
            .bottom-btn i {
                font-size: 16px;
                margin-bottom: 4px;
            }
            
            .bottom-btn .btn-text {
                font-size: 11px;
                letter-spacing: 0.2px;
            }
            
            .time-item {
                padding: 12px 0;
            }
            
            .time-item:hover {
                padding-left: 10px;
                padding-right: 10px;
                margin: 0 -10px;
            }
            
            .image-overlay h2 {
                font-size: 14px;
            }
        }
        
        /* 小屏幕高度优化 */
        @media screen and (max-height: 600px) {
            .bottom-btn {
                min-height: 50px;
                padding: 6px 4px;
            }
            
            .bottom-btn i {
                font-size: 16px;
                margin-bottom: 3px;
            }
            
            .bottom-btn .btn-text {
                font-size: 11px;
                letter-spacing: 0.2px;
            }
        }
        
        @media screen and (max-height: 500px) {
            .bottom-btn {
                min-height: 45px;
                padding: 5px 3px;
            }
            
            .bottom-btn i {
                font-size: 14px;
                margin-bottom: 2px;
            }
            
            .bottom-btn .btn-text {
                font-size: 10px;
                letter-spacing: 0.1px;
            }
        }
        
        /* 横屏模式按钮优化 */
        @media screen and (orientation: landscape) and (max-height: 400px) {
            .bottom-btn {
                min-height: 40px;
                padding: 4px 2px;
            }
            
            .bottom-btn i {
                font-size: 12px;
                margin-bottom: 2px;
            }
            
            .bottom-btn .btn-text {
                font-size: 9px;
                letter-spacing: 0.1px;
            }
        }
        
        /* 加载状态 */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        /* 错误提示样式 - 治愈风格 */
        .error {
            color: #dc3545;
            font-size: 13px;
            margin-top: 8px;
            display: block;
            padding: 10px 15px;
            background: rgba(220, 53, 69, 0.1);
            border-radius: 10px;
            border-left: 4px solid #dc3545;
        }
    </style>
    </style>
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="left">
            <a href="#" class="icon goBack">
                <i class="fa fa-angle-left"></i>
            </a>
        </div>
        <div class="pageTitle" th:text="${activity.activityName}"></div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <!-- App Capsule -->
    <div id="appCapsule" class="pb-2">
        <div class="appContent">
            <!-- 活动封面卡片 -->
            <div class="card">
                <div class="image-container">
                    <img th:if="${activity.activityCover eq null or activity.activityCover eq ''}" th:src="@{/static/images/nopic.png}" class="img-fluid" />
                    <img th:unless="${activity.activityCover eq null or activity.activityCover eq ''}" th:src="|@{/static/upload/activity_cover/}${activity.activityCover}|" class="img-fluid" />
                        <div class="image-overlay">
                            <h2 th:text="${activity.activityName}"></h2>
                        </div>
                    </div>
                </div>
            
            <!-- 标签页导航 -->
                <ul class="nav nav-tabs nav-bordered nav-justified" role="tablist">
                    <li class="nav-item">
                    <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab">
                        <i class="fa fa-info-circle mr-1"></i>活动信息
                    </a>
                    </li>
                </ul>
            
            <!-- 标签页内容 -->
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="home" role="tabpanel">
                    <!-- 活动详情卡片 -->
                    <div class="contentBox">
                        <h5 class="title">
                            <i class="fa fa-file-text-o mr-2"></i>活动详情
                        </h5>
                        <div class="contentBox-body">
                                <th:block th:if="${not #strings.isEmpty(activity.activityIntro) }" th:utext="${activity.activityIntro}" />
                                <th:block th:unless="${not #strings.isEmpty(activity.activityIntro) }" th:utext="暂无活动详情" />
                            </div>
                        </div>
                    
                    <!-- 活动时间卡片 -->
                    <div class="contentBox">
                        <h5 class="title">
                            <i class="fa fa-clock-o mr-2"></i>活动时间
                        </h5>
                        <div class="contentBox-body">
                            <div class="time-info">
                                <div class="time-item">
                                    <i class="fa fa-play-circle text-success mr-2"></i>
                                    <span class="time-label">开始时间：</span>
                                    <span class="time-value" th:text="${#dates.format(activity.startTime,'yyyy-MM-dd HH:mm:ss')}"></span>
                                </div>
                                <div class="time-item">
                                    <i class="fa fa-stop-circle text-danger mr-2"></i>
                                    <span class="time-label">结束时间：</span>
                                    <span class="time-value" th:text="${#dates.format(activity.endTime,'yyyy-MM-dd HH:mm:ss')}"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="appBottomMenu2">
        <th:block th:if="${roleId eq 3}">
            <th:block th:if="${activity.activityStateForUser eq 0}">
                <input type="button" class="btn btn-success btn-block rounded clocking" value="签到" onclick="clocking(1)" />
            </th:block>
            <th:block th:if="${activity.activityStateForUser eq 2}">
                <input type="button" class="btn btn-warning btn-block rounded clocking" value="签退" onclick="clocking(2)" />
            </th:block>
            <th:block th:if="${activity.activityStateForUser eq 3}">
                <input type="button" class="btn btn-light btn-block rounded clocking" value="已签退" />
                <th:block th:if="${activity.isSurveyDone eq 0}">
                    <div class="mt-2">
                        <a href="javascript:void(0)" onclick="showSurvey(); return false;" class="btn btn-outline-primary btn-block rounded">
                            <i class="fa fa-clipboard mr-1"></i>问卷调查
                        </a>
                    </div>
                </th:block>
            </th:block>
        </th:block>
        <th:block th:unless="${roleId eq 3}">
            <div class="bottom-buttons-container">
                <a class="btn bg-primary text-light bottom-btn" href="#" onclick="overallEvaluation()">
                    <i class="fa fa-star"></i>
                    <span class="btn-text">活动总评</span>
                </a>
                <a class="btn bg-primary text-light bottom-btn" href="#" onclick="selfEvaluation()">
                    <i class="fa fa-user"></i>
                    <span class="btn-text">个人点评</span>
                </a>
                <a class="btn bg-primary text-light bottom-btn" href="#" onclick="gallery()">
                    <i class="fa fa-image"></i>
                    <span class="btn-text">活动图库</span>
                </a>
            </div>
        </th:block>
    </div>
    <!-- Modal. 调查问卷 -->
    <div id="surveyModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" style="z-index: 99999;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <th:block th:if="${activity.survey  != null}">
                    <div class="modal-header">
                        <div class="modal-title">
                            <i class="fa fa-clipboard mr-2"></i>
                            <span th:text="${activity.surveyName}"></span>
                        </div>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form id="frmSurvey">
                        <div class="modal-body">
                            <div class="survey-intro mb-4">
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle mr-2"></i>
                                    请认真填写以下问卷，您的反馈对我们很重要！
                                </div>
                            </div>
                            <th:block th:each="question:${activity.survey.listQuestions}">
                                <div class="question-container">
                                    <div class="question-header">
                                        <span class="question-number" th:text="${question.qNumber}"></span>
                                        <span class="question-content" th:utext="${#strings.replace(question.qContent,'<p>','')}"></span>
                                </div>
                                    
                                    <!-- 单选题 -->
                                    <div class="form-group mt-3" th:if="${question.qType eq 1}">
                                    <th:block th:each="item:${question.listItems}">
                                            <div class="custom-control custom-radio mb-2">
                                                <input class="custom-control-input" type="radio" th:with="prefix1='option_',prefix2='question_'"
                                                       th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}" th:value="${item.itemContent}"
                                                       th:onclick="toggleOtherInput(this,[[${item.id}]],[[${item.isOther}]],[[${question.id}]])" />
                                                <label class="custom-control-label" th:with="prefix='option_'" th:attr="for=${prefix}+${item.id}" th:utext="${item.itemContent}"></label>
                                        </div>
                                        <th:block th:if="${item.isOther eq 1}">
                                                <div class="form-group ml-4" style="display:none;" th:with="prefix='otherInput_'" th:attr="id=${prefix}+${question.id}">
                                                <input type="text" class="form-control" th:with="prefix='_other'" th:attr="name=${question.id}+${prefix}" placeholder="请输入具体内容" />
                                            </div>
                                        </th:block>
                                    </th:block>
                                </div>
                                    
                                    <!-- 多选题 -->
                                    <div class="form-group mt-3" th:if="${question.qType eq 2}">
                                    <th:block th:each="item:${question.listItems}">
                                            <div class="custom-control custom-checkbox mb-2">
                                                <input class="custom-control-input" type="checkbox" th:with="prefix1='option_',prefix2='question_'"
                                                       th:attr="id=${prefix1} + ${item.id}, name=${prefix2}+${question.id}" th:value="${item.itemContent}"
                                                   th:onclick="toggleOtherInput(this,[[${item.id}]],[[${item.isOther}]],[[${question.id}]])" />
                                                <label class="custom-control-label" th:with="prefix='option_'" th:attr="for=${prefix}+${item.id}" th:text="${item.itemContent}"></label>
                                        </div>
                                        <th:block th:if="${item.isOther eq 1}">
                                                <div class="form-group ml-4" style="display:none;" th:with="prefix='otherInput_'" th:attr="id=${prefix}+${question.id}">
                                                    <input type="text" class="form-control" th:with="prefix='_other'" th:attr="name=${question.id}+${prefix}" placeholder="请输入具体内容" />
                                            </div>
                                        </th:block>
                                    </th:block>
                                </div>
                                    
                                    <!-- 填空题 -->
                                    <div class="form-group mt-3" th:if="${question.qType eq 3}">
                                        <input type="text" class="form-control" th:with="prefix='question_'" th:attr="name=${prefix}+${question.id}" placeholder="请输入内容" />
                                </div>

                                    <!-- 排序题 -->
                                    <div class="form-group mt-3" th:if="${question.qType eq 4}">
                                        <div class="alert alert-info mb-3">
                                            <small><i class="fa fa-info-circle mr-1"></i>请拖拽下方选项进行排序，排在前面的表示优先级更高</small>
                                        </div>
                                        <div class="sortable-list" th:with="prefix='sortable_'" th:attr="id=${prefix}+${question.id}">
                                            <th:block th:each="item,iterStat:${question.listItems}">
                                                <div class="sortable-item" th:attr="data-value=${item.itemContent}">
                                                    <div class="sortable-content" style="display: flex; align-items: center; padding: 12px; border-radius: 8px; margin-bottom: 8px; background-color: white; border: 1px solid #dee2e6;">
                                                        <div class="sort-number" th:text="${iterStat.count}" style="background-color: #2196f3; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold; margin-right: 10px;"></div>
                                                        <i class="fa fa-bars drag-handle" style="margin-right: 12px; cursor: grab; color: #6c757d; font-size: 16px;"></i>
                                                        <span th:text="${item.itemContent}" style="flex-grow: 1;"></span>
                                                        <i class="fa fa-arrows-v" style="margin-left: 8px; color: #6c757d;"></i>
                                                    </div>
                                                </div>
                                            </th:block>
                                        </div>
                                        <input type="hidden" th:with="prefix='question_'" th:attr="name=${prefix}+${question.id}" class="sort-result" />
                                    </div>

                                    <!-- 评分单选题 -->
                                    <div class="form-group mt-3" th:if="${question.qType eq 5}">
                                        <div class="rating-options-container">
                                            <div class="rating-container" th:with="prefix='rating_'" th:attr="data-question-id=${question.id},data-option-count=${#lists.size(question.listItems)}">
                                                <div class="rating-slider">
                                                    <div class="rating-fill" style="width: 0%;"></div>
                                                    <div class="rating-thumb"></div>
                                                </div>
                                                <div class="rating-marks">
                                                    <th:block th:each="item,iterStat:${question.listItems}">
                                                        <div class="rating-mark" th:attr="data-value=${iterStat.count}">
                                                            <div class="rating-number" th:text="${iterStat.count}"></div>
                                                            <div class="rating-label" th:text="${item.itemContent}"></div>
                                                        </div>
                                                    </th:block>
                                                </div>
                                                <div class="rating-value-display">请选择评分</div>
                                                <input type="hidden" th:with="prefix='question_'" th:attr="name=${prefix}+${question.id}" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="divider"></div>
                                </div>
                            </th:block>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-dismiss="modal">
                                <i class="fa fa-times mr-1"></i>取消
                            </button>
                            <input type="submit" class="btn btn-primary" id="btnSaveSurvey" value="保存" />
                        </div>
                    </form>
                </th:block>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div id="done-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="text-center">
                        <div class="success-icon">
                            <i class="fa fa-check-circle"></i>
                        </div>
                        <h5 class="text-success font-weight-bold">感谢参与本次活动</h5>
                        <p class="text-muted">为了使我们的活动更加贴合每一位成员的需求，我们将进行一个简短的满意度调查。您的见解对我们来说至关重要，它将有助于我们在未来的活动中为大家提供更好的支持。</p>
                        <a href="#" data-toggle="modal" data-target="#surveyModal" type="button" class="btn btn-warning">
                            <i class="fa fa-clipboard mr-1"></i>开始填写
                        </a>
                    </div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/static/js/plugins/validate/messages_zh.js}"></script>
    <script type="text/javascript">
        let activityId = getUrlParam('activityId');
        let activityStateForUser = '[[${activity.activityStateForUser}]]';
        let userId = '[[${user.userId}]]';
        let clocking = function (type) {
            let jsonObj = {
                activityId: activityId,
                actionType: type,
            };
            $.ajax({
                url: '/activityroom/clocking/add_clocking_record',
                type: 'post',
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(jsonObj),
                success: function (res) {
                    if (res.resultCode === 200) {
                        if(type === 1){
                            // 先显示签到成功提示
                            layer.open({
                                content: '<img src="/static/images/success.png" width="25" class="mr-1"/>签到成功！',
                                style: 'background-color:#ffffff; border:none;',
                                time: 1,
                                end: function() {
                                    // 提示消失后显示图片
                                    layer.open({
                                        type: 1,
                                        area: ['auto', 'auto'],
                                        shadeClose: true,
                                        content: '<div class="text-center position-relative"><img src="/static/upload/clocking/mobile/' + res.resultMsg + '" style="max-width: 100%; max-height: 80vh; object-fit: contain;"><a class="layui-layer-close" href="javascript:;" onclick="layer.closeAll()" style="position: absolute; right: 10px; top: 10px; z-index: 9999; color: #fff; font-size: 20px; text-shadow: 0 1px 2px rgba(0,0,0,0.5);"><i class="fa fa-times"></i></a></div>',
                                        success: function(layero, index) {
                                            // 移除白色背景
                                            $(layero).find('.layui-layer-content').css('background', 'transparent');
                                        }
                                    });
                                }
                            });
                            $('.clocking').val('签退');
                            $('.clocking').removeClass('btn-success').addClass('btn-warning');
                            $('.clocking').attr('disabled',true);
                        }
                        if(type === 2){
                            layer.open({
                                content: '<img src="/static/images/success.png" width="25" class="mr-1" />签退成功！' ,
                                btn: '确定',
                                shadeClose: false,
                                yes: function (index) {
                                    layer.close(index);
                                    $('.clocking').val('已签退');
                                    $('.clocking').attr('disabled',true);
                                    $('.clocking').removeClass('btn-warning').addClass('btn-secondary');
                                    $('#done-modal').modal();
                                }
                            });
                        }
                    }
                    else {
                        let errMsg;
                        if(type === 1){
                            errMsg = '签到失败！';
                        }
                        if(type === 2){
                            errMsg = '签退失败！';
                        }
                        layer.open({
                            content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>'+errMsg
                            , style: 'background-color:#ffffff; border:none;'
                            , time: 2
                        });
                    }
                }
            });
        }
        let overallEvaluation = function () {
            window.location.href = '/app/activity/update_overall_evaluation?activityId='+activityId;
        }
        let selfEvaluation = function () {
            window.location.href = '/app/activity/self_evaluation?activityId='+activityId;
        }
        let gallery = function () {
            window.location.href = '/app/activity/gallery?activityId='+activityId;
        }
        $(function () {
            // 动态调整内容区域底部间距
            function adjustBottomPadding() {
                const bottomMenu = document.querySelector('.appBottomMenu2');
                const appCapsule = document.getElementById('appCapsule');
                
                if (bottomMenu && appCapsule) {
                    // 获取底部菜单的实际高度和位置
                    const menuRect = bottomMenu.getBoundingClientRect();
                    const menuHeight = menuRect.height;
                    
                    // 获取视口高度
                    const viewportHeight = window.innerHeight;
                    
                    // 计算菜单距离视口底部的距离
                    const menuBottom = menuRect.bottom;
                    const distanceFromBottom = viewportHeight - menuBottom;
                    
                    // 设置合适的底部内边距，确保不被遮挡
                    const safePadding = Math.max(menuHeight + 5, 25); // 菜单高度 + 5px，至少25px
                    
                    // 使用CSS变量设置底部内边距
                    appCapsule.style.setProperty('--bottom-padding', safePadding + 'px');
                    appCapsule.style.paddingBottom = safePadding + 'px';
                    
                    console.log('Menu height:', menuHeight, 'Menu bottom:', menuBottom, 'Viewport height:', viewportHeight, 'Safe padding:', safePadding);
                }
            }
            
            // 页面加载时立即调整
            adjustBottomPadding();
            
            // 确保DOM完全加载后再次调整
            setTimeout(adjustBottomPadding, 50);
            setTimeout(adjustBottomPadding, 200);
            setTimeout(adjustBottomPadding, 500);
            setTimeout(adjustBottomPadding, 1000);
            
            // 最终检查，确保没有遮挡
            setTimeout(function() {
                const bottomMenu = document.querySelector('.appBottomMenu2');
                const appCapsule = document.getElementById('appCapsule');
                
                if (bottomMenu && appCapsule) {
                    const menuHeight = bottomMenu.offsetHeight;
                    const currentPadding = parseInt(getComputedStyle(appCapsule).paddingBottom);
                    
                    if (currentPadding < menuHeight + 8) {
                        const newPadding = menuHeight + 8;
                        appCapsule.style.paddingBottom = newPadding + 'px';
                        appCapsule.style.setProperty('--bottom-padding', newPadding + 'px');
                        console.log('Final adjustment - Menu height:', menuHeight, 'New padding:', newPadding);
                    }
                }
            }, 2000);
            
            // 防抖函数
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
            
            // 窗口大小改变时重新调整（使用防抖）
            window.addEventListener('resize', debounce(adjustBottomPadding, 100));
            
            // 屏幕方向变化时重新调整
            window.addEventListener('orientationchange', function() {
                // 方向变化后延迟调整，确保新尺寸生效
                setTimeout(adjustBottomPadding, 100);
                setTimeout(adjustBottomPadding, 500);
            });
            
            // 监听DOM变化，确保动态内容加载后也能正确调整
            const observer = new MutationObserver(function(mutations) {
                adjustBottomPadding();
            });
            
            // 观察底部菜单的变化
            const bottomMenu = document.querySelector('.appBottomMenu2');
            if (bottomMenu) {
                observer.observe(bottomMenu, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['style', 'class']
                });
            }
            
            // 监听页面可见性变化，确保从后台切换回来时重新计算
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    setTimeout(adjustBottomPadding, 100);
                }
            });
            
            // 监听触摸事件，确保在移动端滚动时也能正确显示
            let touchStartY = 0;
            document.addEventListener('touchstart', function(e) {
                touchStartY = e.touches[0].clientY;
            });
            
            document.addEventListener('touchend', function(e) {
                const touchEndY = e.changedTouches[0].clientY;
                const diff = touchStartY - touchEndY;
                
                // 如果是向上滑动（显示底部菜单）或向下滑动（隐藏底部菜单）
                if (Math.abs(diff) > 50) {
                    setTimeout(adjustBottomPadding, 100);
                }
            });
            
            // 检查是否已经执行过签到/签退操作
            let hasSignInExecuted = sessionStorage.getItem('signin_executed_' + userId + '_' + activityId);
            let hasSignOutExecuted = sessionStorage.getItem('signout_executed_' + userId + '_' + activityId);
            
            if(activityStateForUser === '0' && !hasSignInExecuted){
                clocking(1);
                sessionStorage.setItem('signin_executed_' + userId + '_' + activityId, 'true');
            }
            else if(activityStateForUser === '2' && !hasSignOutExecuted){
                clocking(2);
                sessionStorage.setItem('signout_executed_' + userId + '_' + activityId, 'true');
            }
            
            // 初始化排序功能
            setTimeout(function() {
                $('.sortable-list').each(function() {
                    let questionId = $(this).attr('id').replace('sortable_', '');
                    console.log('初始化排序题:', questionId);

                    // 使用自定义排序功能
                    initSortable(this, {
                        onEnd: function() {
                            console.log('拖拽结束');
                            updateSortResult(questionId);
                        }
                    });

                    // 初始化排序结果
                    updateSortResult(questionId);
                });
            }, 100);

            // 动态生成验证规则
            let rules = {};
            let messages = {};
            // 获取所有的问题
            $('.form-group').each(function() {
                let questionId = $(this).find('input, textarea').first().attr('name');
                if (questionId) {
                    questionId = questionId.replace('question_', '');
                    let questionType = $(this).find('input, textarea').first().attr('type');

                    if (questionType === 'radio' || questionType === 'checkbox') {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请选择一个选项";
                    } else if (questionType === 'text') {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请填写内容";
                    } else if (questionType === 'hidden' && $(this).find('.sortable-list').length > 0) {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请完成排序";
                    } else if (questionType === 'hidden' && $(this).find('.rating-container').length > 0) {
                        rules['question_' + questionId] = "required";
                        messages['question_' + questionId] = "请选择评分";
                    }
                }
            });
            
            // 为评分题添加自定义验证方法
            $.validator.addMethod("ratingRequired", function(value, element) {
                return value && value.trim() !== '';
            }, "请选择评分");
            
            // 移除这里的initRatingScales调用，放到文档就绪函数的最后
            // initRatingScales();
            $("#frmSurvey").validate({
                errorPlacement: function (error, element) {
                    if (element.attr("name").indexOf("_other_input") !== -1) {
                        // 如果是其他选项的填空题，将错误信息放置在其下方
                        error.insertAfter(element);
                    } else if (element.closest('.rating-container').length > 0) {
                        // 如果是评分题，将错误信息放置在评分容器内
                        let $container = element.closest('.rating-container');
                        showRatingError($container, error.text());
                        error.hide(); // 隐藏默认错误信息
                    } else {
                        // 将错误信息放置在 form-group 的后面
                        error.insertAfter(element.closest('.form-group'));
                    }
                },
                success: function(label, element) {
                    // 当验证成功时，清除评分题的错误状态
                    if ($(element).closest('.rating-container').length > 0) {
                        let $container = $(element).closest('.rating-container');
                        clearRatingError($container);
                    }
                },
                onkeyup: false, // 禁用键盘验证，避免频繁触发
                onfocusout: function(element) {
                    // 失去焦点时验证
                    if ($(element).closest('.rating-container').length > 0) {
                        let $container = $(element).closest('.rating-container');
                        let $hidden = $container.find('input[type="hidden"]');
                        if ($hidden.val() && $hidden.val().trim() !== '') {
                            clearRatingError($container);
                        }
                    }
                },
                rules: rules,
                messages: messages,
                submitHandler: function (form){
                    // 提交前清除所有错误状态
                    clearAllRatingErrors();
                    
                    // 提交前验证所有评分题
                    let ratingValid = true;
                    $('.rating-container').each(function() {
                        let $container = $(this);
                        let $hidden = $container.find('input[type="hidden"]');
                        if (!$hidden.val() || $hidden.val().trim() === '') {
                            showRatingError($container, '请选择评分');
                            ratingValid = false;
                        }
                    });
                    
                    if (!ratingValid) {
                        return false;
                    }
                    
                    let answers = [];
                    // 处理单选按钮
                    $('.custom-control-input[type="radio"]').each(function() {
                        if (this.checked) {
                            let questionId = this.name.replace('question_', '');
                            answers.push({
                                qId: questionId,
                                itemId: this.value,
                                otherAnswer: ''
                            });
                        }
                    });
                    // 处理复选按钮
                    $('.custom-control-input[type="checkbox"]').each(function() {
                        if (this.checked && !this.name.endsWith('_other')) {
                            let questionId = this.name.replace('question_', '');
                            let optionId = this.id.replace('option_','');
                            let existingAnswer = answers.find(answer => answer.qId === questionId);
                            if (existingAnswer) {
                                if (!existingAnswer.itemId) {
                                    existingAnswer.itemId = '';
                                }
                                existingAnswer.itemId += (existingAnswer.itemId ? '|' : '') + this.value;
                            } else {
                                answers.push({
                                    qId: questionId,
                                    itemId: this.value,
                                    otherAnswer: ''
                                });
                            }
                        }
                    });

                    // 处理文本输入
                    $('.form-control[type="text"]').each(function() {
                        let questionId = this.name.replace('question_', '');
                        if (this.value.trim() !== '' ) {
                            if(questionId.indexOf('_other') !== -1){
                                let answer = answers.find(answer=> answer.qId === questionId.replace('_other',''));
                                if(answer){
                                    answer.itemId += '|其他：'+this.value.trim();
                                }
                            }
                            else{
                                let existingAnswer = answers.find(answer => answer.qId === questionId);
                                if (existingAnswer) {
                                    existingAnswer.itemId = this.value.trim();
                                } else {
                                    answers.push({
                                        qId: questionId,
                                        itemId: this.value.trim(),
                                        otherAnswer: ''
                                    });
                                }
                            }
                        }
                    });

                    // 处理排序题
                    $('.form-group input[type="hidden"].sort-result').each(function() {
                        if (this.name.indexOf('question_') === 0 && this.value.trim() !== '') {
                            let questionId = this.name.replace('question_', '');
                            answers.push({
                                qId: questionId,
                                itemId: this.value.trim(),
                                otherAnswer: ''
                            });
                        }
                    });

                    // 处理评分单选题
                    $('.rating-container input[type="hidden"]').each(function() {
                        if (this.name.indexOf('question_') === 0 && this.value.trim() !== '') {
                            let questionId = this.name.replace('question_', '');
                            let answer = {
                                qId: questionId,
                                itemId: this.value.trim(),
                                otherAnswer: ''
                            };
                            answers.push(answer);
                            
                            // 调试信息
                            console.log('评分题提交:', answer);
                        }
                    });

                    // 转换为JSON字符串
                    let jsonObj = {};
                    jsonObj.surveyId = '[[${activity.surveyId}]]';
                    jsonObj.activityId = activityId;
                    jsonObj.listAnswers = answers;
                    $("#btnSaveSurvey").attr('Disabled',true);
                    $("#btnSaveSurvey").val('请稍后……');
                    $.ajax({
                        type: 'POST',
                        url: '/survey/surveyrecord/add_for_activity',
                        data: JSON.stringify(jsonObj),
                        contentType:'application/json',
                        dataType: "json",
                        success: function (res) {
                            $("#btnSaveSurvey").val("保存");
                            $("#btnSaveSurvey").attr("Disabled", false);
                            if (res.resultCode === 200) {
                                layer.open({
                                    content: '<img src="/static/images/success.png" width="25" class="mr-1" />提交成功！' ,
                                    btn: '确定',
                                    shadeClose: false,
                                    yes: function (index) {
                                        layer.close(index);
                                        $("#done-modal").modal('hide');
                                        $("#surveyModal").modal('hide');
                                    }
                                });
                            }
                            else {
                                layer.open({
                                    content: '<img src="/static/images/wrong.png" width="25" class="mr-1"/>' + res.resultMsg
                                    , style: 'background-color:#ffffff; border:none;'
                                    , time: 2
                                });
                            }
                        }
                    });
                }
            });
            
            // 在所有初始化完成后，最后初始化评分选择器
            setTimeout(function() {
                initRatingScales();
            }, 200);
        });
        
        // 初始化评分选择器 - 重新设计为简洁的滑块式
        function initRatingScales() {
            console.log('开始初始化评分题...');
            
            // 强制清除所有评分题的错误状态
            $('.rating-container').removeClass('has-error').removeAttr('style');
            $('.rating-error').remove();
            
            let ratingContainers = $('.rating-container');
            console.log('找到评分题容器数量:', ratingContainers.length);
            
            ratingContainers.each(function(index) {
                let $container = $(this);
                let questionId = $container.attr('data-question-id');
                let $slider = $container.find('.rating-slider');
                let $fill = $container.find('.rating-fill');
                let $thumb = $container.find('.rating-thumb');
                let $marks = $container.find('.rating-mark');
                let $display = $container.find('.rating-value-display');
                let $hidden = $container.find('input[type="hidden"]');
                
                console.log('初始化评分题 ' + (index + 1) + ':', {
                    questionId: questionId,
                    hasSlider: $slider.length > 0,
                    marksCount: $marks.length,
                    hasDisplay: $display.length > 0,
                    hasHidden: $hidden.length > 0,
                    hiddenName: $hidden.attr('name')
                });
                
                let currentValue = 0;
                let isDragging = false;
                
                // 初始化状态
                $marks.removeClass('active');
                $display.text('请选择评分');
                $hidden.val('');
                $fill.css('width', '0%');
                $thumb.css('left', '0%');
                
                // 设置评分值的函数 - 动态选项版本
                function setRating(value, smooth = true) {
                    currentValue = parseInt(value);
                    let optionCount = parseInt($container.attr('data-option-count')) || 5;
                    
                    // 动态计算位置百分比
                    let percentage;
                    if (currentValue > 0 && currentValue <= optionCount) {
                        percentage = ((currentValue - 1) / (optionCount - 1)) * 100;
                    } else {
                        percentage = 0;
                        currentValue = 0;
                    }
                    
                    // 根据是否需要平滑过渡设置动画
                    if (smooth) {
                        $fill.css('transition', 'width 0.2s ease-out');
                        $thumb.css('transition', 'left 0.2s ease-out');
                    } else {
                        $fill.css('transition', 'none');
                        $thumb.css('transition', 'none');
                    }
                    
                    $fill.css('width', percentage + '%');
                    $thumb.css('left', percentage + '%');
                    
                    // 恢复过渡效果
                    setTimeout(function() {
                        $fill.css('transition', 'width 0.2s ease-out');
                        $thumb.css('transition', 'all 0.15s ease-out');
                    }, smooth ? 200 : 0);
                    
                    $marks.removeClass('active');
                    if (currentValue > 0) {
                        $marks.filter('[data-value="' + currentValue + '"]').addClass('active');
                        let label = $marks.filter('[data-value="' + currentValue + '"]').find('.rating-label').text();
                        $display.text('已选择：' + currentValue + ' (' + label + ')');
                    } else {
                        $display.text('请选择评分');
                    }
                    
                    $hidden.val(currentValue || '');
                    
                    // 强制清除错误状态
                    clearRatingError($container);
                    
                    // 触发change事件
                    $hidden.trigger('change');
                    
                    console.log('评分设置完成:', {
                        questionId: questionId,
                        value: currentValue,
                        percentage: percentage,
                        hiddenValue: $hidden.val()
                    });
                }
                
                // 根据位置计算评分值 - 动态选项版本
                function getValueFromPosition(x, sliderWidth) {
                    let percentage = Math.max(0, Math.min(100, (x / sliderWidth) * 100));
                    let optionCount = parseInt($container.attr('data-option-count')) || 5;
                    
                    // 动态计算区间
                    for (let i = 1; i <= optionCount; i++) {
                        let stepPercentage = ((i - 1) / (optionCount - 1)) * 100;
                        let nextStepPercentage = i < optionCount ? ((i) / (optionCount - 1)) * 100 : 100;
                        let threshold = (stepPercentage + nextStepPercentage) / 2;
                        
                        if (percentage < threshold || i === optionCount) {
                            return i;
                        }
                    }
                    
                    return 1;
                }
                
                // 点击滑块条 - 优化版本
                $slider.on('click', function(e) {
                    if (isDragging) return;
                    
                    let rect = this.getBoundingClientRect();
                    let x = e.clientX - rect.left;
                    let value = getValueFromPosition(x, rect.width);
                    
                    setRating(value, true);
                });
                
                // 点击标记
                $marks.on('click', function() {
                    let value = $(this).attr('data-value');
                    setRating(value, true);
                });
                
                // 拖拽功能 - 大幅优化
                let startX = 0;
                let startLeft = 0;
                
                $thumb.on('mousedown touchstart', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    isDragging = true;
                    $(this).addClass('dragging');
                    
                    let clientX = e.clientX || (e.touches && e.touches[0].clientX);
                    startX = clientX;
                    startLeft = parseFloat($thumb.css('left')) || 0;
                    
                    // 禁用文档选择
                    $('body').css('user-select', 'none');
                    
                    console.log('开始拖拽');
                });
                
                $(document).on('mousemove touchmove', function(e) {
                    if (!isDragging) return;
                    
                    e.preventDefault();
                    
                    let rect = $slider[0].getBoundingClientRect();
                    let clientX = e.clientX || (e.touches && e.touches[0].clientX);
                    let deltaX = clientX - startX;
                    let newLeft = startLeft + deltaX;
                    let percentage = Math.max(0, Math.min(100, (newLeft / rect.width) * 100));
                    
                    // 实时更新位置（无动画）
                    $thumb.css('left', percentage + '%');
                    $fill.css('width', percentage + '%');
                    
                    // 计算对应的值并更新显示（但不触发change事件）
                    let value = getValueFromPosition(newLeft, rect.width);
                    if (value !== currentValue) {
                        currentValue = value;
                        $marks.removeClass('active');
                        $marks.filter('[data-value="' + value + '"]').addClass('active');
                        let label = $marks.filter('[data-value="' + value + '"]').find('.rating-label').text();
                        $display.text('已选择：' + value + ' (' + label + ')');
                    }
                });
                
                $(document).on('mouseup touchend', function(e) {
                    if (!isDragging) return;
                    
                    isDragging = false;
                    $thumb.removeClass('dragging');
                    
                    // 恢复文档选择
                    $('body').css('user-select', '');
                    
                    // 最终对齐到准确位置
                    setRating(currentValue, true);
                    
                    console.log('拖拽结束，最终值:', currentValue);
                });
                
                console.log('评分题 ' + (index + 1) + ' 事件绑定完成');
            });
            
            console.log('所有评分题初始化完成');
        }
        
        // 清除评分题错误状态 - 终极强化版本
        function clearRatingError($container) {
            // 移除所有错误相关的类
            $container.removeClass('has-error error');
            $container.find('.rating-error').remove();
            
            // 强制重置所有可能的样式属性
            $container.css({
                'border-color': '#e9ecef !important',
                'background': '#f8f9fa !important',
                'box-shadow': 'none !important',
                'border': '2px solid #e9ecef !important'
            });
            
            // 移除style属性并重新设置
            setTimeout(function() {
                $container.removeAttr('style');
                $container.css({
                    'background': '#f8f9fa',
                    'border-radius': '12px',
                    'padding': '20px',
                    'border': '2px solid #e9ecef',
                    'transition': 'all 0.3s ease'
                });
            }, 50);
            
            let $hidden = $container.find('input[type="hidden"]');
            $hidden.removeClass('error');
            $container.find('label.error').remove();
            
            console.log('强化清除评分题错误状态');
        }
        
        // 显示评分题错误
        function showRatingError($container, message) {
            clearRatingError($container);
            $container.addClass('has-error');
            $container.append('<div class="rating-error"><i class="fa fa-exclamation-circle mr-1"></i>' + message + '</div>');
            console.log('显示评分题错误:', message);
        }
        
        // 清除所有评分题错误状态
        function clearAllRatingErrors() {
            $('.rating-container').each(function() {
                clearRatingError($(this));
            });
            console.log('清除所有评分题错误状态');
        }
        
        // 页面加载完成后初始化评分选择器
        $(document).ready(function() {
            // initRatingScales(); // 已移除，避免重复调用
        });
        
        // 在模态框显示时重新初始化评分选择器
        $('#surveyModal').on('shown.bs.modal', function() {
            initRatingScales();
        });
        
        // 在模态框关闭时清除所有评分题错误状态
        $('#surveyModal').on('hidden.bs.modal', function() {
            clearAllRatingErrors();
        });
        
        let showSurvey = function(){
            layer.open({
                type: 2
                , content: '请稍后…',
                shadeClose: false
            });
            $.post("/activityroom/activity/is_survey_done",{activityId: activityId, surveyId: '[[${activity.surveyId}]]'},function(res){
                layer.closeAll();
                if(res.resultCode === 200){
                    layer.open({
                        content: '<img src="/static/images/warning.png" width="25" class="mr-1"/>您已经完成该调查问卷'
                        , style: 'background-color:#ffffff; border:none;'
                        , time: 2
                    });
                }
                else{
                    $("#surveyModal").modal();
                }
            })
        }
        let toggleOtherInput  = function(d,a,b,c) {
            if($(d).is('[type="radio"]')){
                if(b!==0){
                    if ($('#option_'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
                else{
                    $("#otherInput_" + c).toggle(false);
                }
            }
            if($(d).is('[type="checkbox"]')){
                if(b!==0){
                    if ($('#option_'+a+'').prop("checked")){
                        $("#otherInput_" + c).toggle(b===1);
                    }
                    else{
                        $("#otherInput_" + c).toggle(false);
                    }
                }
            }
        }

        // 排序功能实现 - 优化版，支持触摸和鼠标
        function initSortable(element, options) {
            let draggedElement = null;
            let placeholder = null;
            let isDragging = false;
            let startY = 0;
            let currentY = 0;

            // 创建占位符
            function createPlaceholder() {
                placeholder = document.createElement('div');
                placeholder.className = 'sortable-placeholder';
                placeholder.innerHTML = '<div style="height: 50px; display: flex; align-items: center; justify-content: center; color: #2196f3;"><i class="fa fa-arrows-v"></i> 拖拽到此处</div>';
                return placeholder;
            }

            // 获取拖拽后的位置
            function getDragAfterElement(container, y) {
                const draggableElements = [...container.querySelectorAll('.sortable-item:not(.dragging)')];

                return draggableElements.reduce((closest, child) => {
                    const box = child.getBoundingClientRect();
                    const offset = y - box.top - box.height / 2;

                    if (offset < 0 && offset > closest.offset) {
                        return { offset: offset, element: child };
                    } else {
                        return closest;
                    }
                }, { offset: Number.NEGATIVE_INFINITY }).element;
            }

            // 开始拖拽
            function startDrag(e, item) {
                e.preventDefault();
                e.stopPropagation();

                draggedElement = item;
                isDragging = true;

                // 添加拖拽样式
                draggedElement.classList.add('dragging');
                draggedElement.style.opacity = '0.5';

                // 创建并插入占位符
                createPlaceholder();
                draggedElement.parentNode.insertBefore(placeholder, draggedElement.nextSibling);

                // 阻止模态框滚动
                const modal = element.closest('.modal');
                if (modal) {
                    modal.classList.add('sorting-active');
                }

                console.log('开始拖拽');
            }

            // 拖拽中
            function onDrag(e) {
                if (!isDragging || !draggedElement) return;

                e.preventDefault();
                e.stopPropagation();

                const clientY = e.clientY || (e.touches && e.touches[0].clientY);
                if (!clientY) return;

                const afterElement = getDragAfterElement(element, clientY);

                if (afterElement == null) {
                    element.appendChild(placeholder);
                } else {
                    element.insertBefore(placeholder, afterElement);
                }
            }

            // 结束拖拽
            function endDrag(e) {
                if (!isDragging || !draggedElement) return;

                e.preventDefault();
                e.stopPropagation();

                // 移动元素到占位符位置
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.insertBefore(draggedElement, placeholder);
                    placeholder.parentNode.removeChild(placeholder);
                }

                // 恢复样式
                draggedElement.classList.remove('dragging');
                draggedElement.style.opacity = '';

                // 恢复模态框滚动
                const modal = element.closest('.modal');
                if (modal) {
                    modal.classList.remove('sorting-active');
                }

                // 清理状态
                draggedElement = null;
                placeholder = null;
                isDragging = false;

                console.log('拖拽结束');
                if (options.onEnd) options.onEnd();
            }

            // 为每个排序项添加事件监听
            element.querySelectorAll('.sortable-item').forEach(item => {
                const dragHandle = item.querySelector('.drag-handle');

                // 鼠标事件
                dragHandle.addEventListener('mousedown', function(e) {
                    startDrag(e, item);
                });

                // 触摸事件
                dragHandle.addEventListener('touchstart', function(e) {
                    startDrag(e, item);
                }, { passive: false });
            });

            // 全局事件监听
            document.addEventListener('mousemove', onDrag);
            document.addEventListener('touchmove', onDrag, { passive: false });
            document.addEventListener('mouseup', endDrag);
            document.addEventListener('touchend', endDrag);
        }


        // 更新排序结果
        function updateSortResult(questionId) {
            let sortableList = $('#sortable_' + questionId);
            let sortedItems = [];

            console.log('更新排序结果，题目ID:', questionId);

            sortableList.find('.sortable-item').each(function(index) {
                let value = $(this).attr('data-value');
                if (value) {
                    sortedItems.push((index + 1) + '.' + value);
                    // 更新序号显示
                    $(this).find('.sort-number').text(index + 1);
                    console.log('排序项:', (index + 1), value);
                }
            });

            let result = sortedItems.join('|');
            $('input[name="question_' + questionId + '"]').val(result);
            console.log('排序结果:', result);
        }
    </script>
</th:block>
</body>
</html>
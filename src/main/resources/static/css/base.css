body {
}

.letter-spacing-1 {
    letter-spacing: 1px;
}

.letter-spacing-2 {
    letter-spacing: 2px;
}

.letter-spacing-3 {
    letter-spacing: 3px;
}

.letter-spacing-4 {
    letter-spacing: 4px;
}

.letter-spacing-5 {
    letter-spacing: 5px;
}
/* -----------------------------------------
    Form wizard
----------------------------------------- */
.form-wizard {
    position: relative;
}

.form-wizard .wizard-steps {
    text-align: center;
}

.form-wizard .wizard-steps.show {
    width: 100%;
    height: auto;
    padding: 0 0 15px 0;
    margin-bottom: 20px;
    text-align: center;
}

.form-wizard .wizard-steps .hide {
    display: none;
}

.form-wizard .wizard-steps .wstep {
    display: inline-block;
    width: 150px;
    position: relative;
    font-weight: normal;
    font-size: 16px;
    margin-right: 12px;
}

.form-wizard .wizard-steps .wstep.current, .form-wizard .wizard-steps .wstep.current span.txt {
    color: #727cf5;
}

.form-wizard .wizard-steps .wstep.current .donut {
    border-color: #727cf5;
}

.form-wizard .wizard-steps .wstep.current .donut i {
    color: #727cf5;
}

    .form-wizard .wizard-steps .wstep.done, .form-wizard .wizard-steps .wstep.done span.txt {
        color: #0acf97;
    }

    .form-wizard .wizard-steps .wstep.done .donut {
        border-color: #0acf97;
    }

    .form-wizard .wizard-steps .wstep.done .donut i {
        color: #0acf97;
    }

.form-wizard .wizard-steps .wstep.done:after {
    border-top: 1px solid #727cf5;
}

.form-wizard .wizard-steps .wstep:after {
    content: "";
    position: absolute;
    top: 24px;
    left: 110px;
    border-top: 1px solid #a4a5a5;
    height: 2px;
    width: 100px;
}

.form-wizard .wizard-steps .wstep:last-child:after {
    border: none;
}

    .form-wizard .wizard-steps .wstep .donut {
        border: 1px solid #a4a5a5;
        border-radius: 50px;
        height: 50px;
        width: 50px;
        margin-left: 50px;
        position: relative;
        margin-bottom: 5px;
    }

.form-wizard .wizard-steps .wstep .donut i {
    color: #a4a5a5;
    margin-top: 12px;
    font-size: 24px;
}

.form-wizard .wizard-actions {
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 20px;
    margin-bottom: 5px;
    float: left;
    width: 100%;
}

.form-wizard .wizard-actions .ui-formwizard-button.pull-left i {
    margin-left: 0;
}

.form-wizard .wizard-actions .ui-formwizard-button.pull-right i {
    margin-right: 0;
}

.form-wizard.wizard-vertical .wizard-steps.show {
    width: 200px;
    border-bottom: none;
    border-right: 1px solid #e7e7e2;
    float: left;
    margin-bottom: 10px;
}

.form-wizard.wizard-vertical .wizard-steps .wstep {
    width: 150px;
    margin-bottom: 30px;
}

.form-wizard.wizard-vertical .wizard-steps .wstep .donut {
    margin-left: 0;
    position: relative;
    margin-bottom: 5px;
    float: left;
}

.form-wizard.wizard-vertical .wizard-steps .wstep .txt {
    display: inline-block;
    width: 100px;
    margin-top: 13px;
    padding-left: 20px;
    font-size: 14px;
    text-align: left;
    color: #a4a5a5
}

.form-wizard.wizard-vertical .wizard-steps .wstep:after {
    top: 55px;
    left: 24px;
    border-top: none;
    border-left: 1px solid #a4a5a5;
    height: 25px;
    width: 2px;
}

.form-wizard.wizard-vertical .wizard-steps .wstep:last-child:after {
    border: none;
}

.form-wizard.wizard-vertical .step {
    display: inline-block;
    float: left;
    width: 100%;
}

.form-wizard.wizard-vertical .wrap {
    padding-left: 210px;
}

.form-wizard.wizard-vertical .wizard-actions {
    clear: both;
    padding-left: 225px;
}
/* Validation */
label.error {
    color: #fb5d5d;
    display: inline-block;
    font-size: 13px;
}

.form-control.error {
    border: 1px dotted #ff0000;
}

.hide {
    display: none !important;
}

.show {
    display: block !important;
}

.table tbody tr td {
    vertical-align: middle;
}

.login-logo {
    height: 38px;
}

.table th {
    white-space: nowrap !important;
}

.cursor-pointer {
    cursor: pointer;
}

.text-decoration-underline {
    text-decoration: underline;
}
/*********测评部分*********/
.question {
    line-height: 200%;
    font-size: 14px;
    color: #000;
}

.question_title, .question_title p {
    margin: 0px;
    font-size: 14px;
}

.question_title p {
    display: inline;
}

.question_info {
    padding-left: 20px;
    line-height: 250%;
    height: auto;
    cursor: pointer;
    margin: 10px 0px 10px 0px;
    border: 1px solid #e3eaef;
    border-radius: .25rem;
}

.question_info input {
    line-height: 45px;
}

.clickTrim {
    color: #000;
    background: #F3F3F3;
    border: 1px solid #e3eaef;
}

/* 评分单选题特殊处理，避免全局clickTrim样式影响 */
.rating-option.clickTrim {
    background: transparent !important;
    border: none !important;
    color: inherit !important;
}

.question_info:hover {
    color: #fff;
    background: #e3eaef;
    border: 1px solid #6b5eae;
}

.operation span {
    cursor: pointer;
}

.operation span:hover {
    cursor: pointer;
}

.questionId, .questionId2 {
    color: #DCE7F2;
    width: 35px;
    height: 35px;
    text-align: center;
    line-height: 35px;
    margin: 5px;
    float: left;
    cursor: pointer;
    border: 1px solid white;
}

.questionId {
    color: #DCE7F2;
}

.questionId2 {
    width: 50px;
    color: #727cf5;
    background: none;
    border: 1px solid #CCC;
}

.questionId3 {
    color: #727cf5;
    width: auto;
    height: 35px;
    text-align: center;
    line-height: 35px;
    margin: 5px;
    float: left;
    cursor: pointer;
    background: none;
    border: 1px solid #CCC;
}

.questionId:hover, .question_id {
    color: #000;
    background: #e7eaec;
    border: 1px solid #ed5565;
    border-radius: .25rem;
}

.active_question_id {
    color: #727cf5;
    background: none;
    border: 1px solid #CCC;
}

#closeCard, #openCard {
    width: 100%;
    border: none;
}

#closeCard span, #openCard span {
    cursor: pointer;
}

#openCard:hover, #closeCard:hover {
    color: #727cf5;
}

.clickQue {
    color: #000;
    background: #DCE7F2;
}

/* 评分单选题样式 */
.rating-scale-container {
    width: 100%;
    margin: 20px 0;
    text-align: center;
}

.rating-scale-labels {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.rating-label-left {
    text-align: left;
}

.rating-label-right {
    text-align: right;
}

.rating-scale-line {
    height: 2px;
    background: #e3eaef;
    margin: 15px 0;
    position: relative;
}

.rating-scale-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.rating-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    position: relative;
    z-index: 2;
}

.rating-option input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    pointer-events: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.rating-circle {
    width: 20px;
    height: 20px;
    border: 2px solid #e3eaef;
    border-radius: 50%;
    background: white;
    transition: all 0.3s ease;
    margin-top: 5px;
}

.rating-option:hover .rating-circle {
    border-color: #727cf5;
    background: #f8f9ff;
}

.rating-option input[type="radio"]:checked + .rating-circle {
    border-color: #727cf5;
    background: #727cf5;
    box-shadow: 0 0 0 3px rgba(114, 124, 245, 0.2);
    border-radius: 50% !important;
}

.rating-option.clickTrim .rating-circle {
    border-color: #727cf5;
    background: #727cf5;
    box-shadow: 0 0 0 3px rgba(114, 124, 245, 0.2);
    border-radius: 50% !important;
}

.progress2 {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px;
}

.divider {
    height: 1px;
    background: transparent;
    border-bottom: 1px solid #E5E9F2;
}

.divider.dotted {
    border-bottom: 1px dotted #E5E9F2;
}

.divider.dashed {
    border-bottom: 1px dashed #E5E9F2;
}

.divider.large {
    border-bottom-width: 2px;
}

.divider.xlarge {
    border-bottom-width: 4px;
}

.divider.xxlarge {
    border-bottom-width: 6px;
}

/************  ztree *************/
.ztree li span.button.switch.level0 {
    visibility: hidden;
    width: 1px;
}

.ztree li ul.level0 {
    padding: 0;
    background: none;
}

ul.ztree {
    border: 1px solid #ccc;
    border-radius: .15rem;
}
/* ------------------ Callout --------------------*/
.bs-callout-primary {
    display: block;
    margin: 20px 0;
    padding: 15px 30px 15px 15px;
    border-left: 3px solid #75b9e6;
    background-color: #f7fbfd;
}

.bs-callout-primary h1,
.bs-callout-primary h2,
.bs-callout-primary h3,
.bs-callout-primary h4,
.bs-callout-primary h5,
.bs-callout-primary h6 {
    margin-top: 0;
    color: #75b9e6;
}

.bs-callout-primary p {
    color: #5e6a7e;
}

.bs-callout-primary p:last-child {
    margin-bottom: 0;
}

.bs-callout-primary code,
.bs-callout-primary .highlight {
    background-color: #fff;
}

.bs-callout-primary .close {
    color: #3498da;
    opacity: 1;
}

.bs-callout-primary .close:hover,
.bs-callout-primary .close:focus {
    color: #52a7e0;
}

.bs-callout-danger {
    display: block;
    margin: 20px 0;
    padding: 15px 30px 15px 15px;
    border-left: 3px solid #f68484;
    background-color: #fef6f6;
}

.bs-callout-danger h1,
.bs-callout-danger h2,
.bs-callout-danger h3,
.bs-callout-danger h4,
.bs-callout-danger h5,
.bs-callout-danger h6 {
    margin-top: 0;
    color: #f68484;
}

.bs-callout-danger p {
    color: #5e6a7e;
}

.bs-callout-danger p:last-child {
    margin-bottom: 0;
}

.bs-callout-danger code,
.bs-callout-danger .highlight {
    background-color: #fff;
}

.bs-callout-danger .close {
    color: #f13d3d;
    opacity: 1;
}

.bs-callout-danger .close:hover,
.bs-callout-danger .close:focus {
    color: #f35e5e;
}

.bs-callout-warning {
    display: block;
    margin: 20px 0;
    padding: 15px 30px 15px 15px;
    border-left: 3px solid #f4b162;
    background-color: #fef8f1;
}

.bs-callout-warning h1,
.bs-callout-warning h2,
.bs-callout-warning h3,
.bs-callout-warning h4,
.bs-callout-warning h5,
.bs-callout-warning h6 {
    margin-top: 0;
    color: #f4b162;
}

.bs-callout-warning p {
    color: #5e6a7e;
}

.bs-callout-warning p:last-child {
    margin-bottom: 0;
}

.bs-callout-warning code,
.bs-callout-warning .highlight {
    background-color: #fff;
}

.bs-callout-warning .close {
    color: #ef8d1b;
    opacity: 1;
}

.bs-callout-warning .close:hover,
.bs-callout-warning .close:focus {
    color: #f19e3c;
}

.bs-callout-info {
    display: block;
    margin: 20px 0;
    padding: 15px 30px 15px 15px;
    border-left: 3px solid #97d3c5;
    background-color: #f8fcfb;
}

.bs-callout-info h1,
.bs-callout-info h2,
.bs-callout-info h3,
.bs-callout-info h4,
.bs-callout-info h5,
.bs-callout-info h6 {
    margin-top: 0;
    color: #97d3c5;
}

.bs-callout-info p {
    color: #5e6a7e;
}

.bs-callout-info p:last-child {
    margin-bottom: 0;
}

.bs-callout-info code,
.bs-callout-info .highlight {
    background-color: #fff;
}

.bs-callout-info .close {
    color: #61bca7;
    opacity: 1;
}

.bs-callout-info .close:hover,
.bs-callout-info .close:focus {
    color: #7ac7b5;
}

.bs-callout-success {
    display: block;
    margin: 20px 0;
    padding: 15px 30px 15px 15px;
    border-left: 3px solid #71d398;
    background-color: #eefaf2;
}

.bs-callout-success h1,
.bs-callout-success h2,
.bs-callout-success h3,
.bs-callout-success h4,
.bs-callout-success h5,
.bs-callout-success h6 {
    margin-top: 0;
    color: #71d398;
}

.bs-callout-success p {
    color: #5e6a7e;
}

.bs-callout-success p:last-child {
    margin-bottom: 0;
}

.bs-callout-success code,
.bs-callout-success .highlight {
    background-color: #fff;
}

.bs-callout-success .close {
    color: #3bbd6e;
    opacity: 1;
}

.bs-callout-success .close:hover,
.bs-callout-success .close:focus {
    color: #52c981;
}
.ck-content {
    min-height: 200px;
}
.announcement p {
    line-height:30px;
}
.announcemnt-title:hover{
    text-decoration:underline;
}
.course_player {
    width:100%;
}
.play-body img {
    width: 100%;
}
#Intro img {
    width: 100%;
}

/* 迫选题样式 */
.forced-choice-container {
    width: 100%;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9ff;
    border: 2px solid #727cf5;
    border-radius: 8px;
    position: relative;
    box-shadow: 0 4px 16px rgba(114, 124, 245, 0.2);
    animation: borderGlow 3s ease-in-out infinite alternate;
}

@keyframes borderGlow {
    0% {
        border-color: #727cf5;
        box-shadow: 0 4px 16px rgba(114, 124, 245, 0.2);
    }
    100% {
        border-color: #8b7cf6;
        box-shadow: 0 4px 20px rgba(114, 124, 245, 0.4);
    }
}

.forced-choice-tip {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #ff6b6b, #ffa726);
    color: white;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.forced-choice-tip i {
    margin-right: 8px;
    font-size: 16px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.forced-choice-option {
    display: block;
    width: 100%;
    margin: 12px 0;
    padding: 16px 20px;
    background: white;
    border: 2px solid #e3eaef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.forced-choice-option:hover {
    border-color: #727cf5;
    background: #f8f9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(114, 124, 245, 0.1);
}

.forced-choice-option.clickTrim {
    border-color: #727cf5 !important;
    background: linear-gradient(135deg, #727cf5, #8b7cf6) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(114, 124, 245, 0.3);
}

.forced-choice-option input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    pointer-events: none !important;
}

.forced-choice-text {
    font-size: 15px;
    font-weight: 500;
    line-height: 1.5;
    color: #333;
    transition: color 0.3s ease;
}

.forced-choice-option.clickTrim .forced-choice-text {
    color: white !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .forced-choice-container {
        padding: 16px;
        margin: 16px 0;
    }

    .forced-choice-option {
        padding: 14px 16px;
    }

    .forced-choice-text {
        font-size: 14px;
    }
}
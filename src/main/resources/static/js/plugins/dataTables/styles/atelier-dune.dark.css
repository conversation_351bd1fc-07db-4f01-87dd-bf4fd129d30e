/* Base16 Atelier Dune Dark - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/dune) */ 
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON><PERSON>/base16) */
/* https://github.com/jmblog/color-themes-for-highlightjs */

/* Atelier Dune Dark Comment */
.hljs-comment,
.hljs-title {
  color: #999580;
}

/* Atelier Dune Dark Red */
.hljs-variable,
.hljs-attribute,
.hljs-tag,
.hljs-regexp,
.ruby .hljs-constant,
.xml .hljs-tag .hljs-title,
.xml .hljs-pi,
.xml .hljs-doctype,
.html .hljs-doctype,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo {
  color: #d73737;
}

/* Atelier Dune Dark Orange */
.hljs-number,
.hljs-preprocessor,
.hljs-pragma,
.hljs-built_in,
.hljs-literal,
.hljs-params,
.hljs-constant {
  color: #b65611;
}

/* Atelier Dune Dark Yellow */
.ruby .hljs-class .hljs-title,
.css .hljs-rules .hljs-attribute {
  color: #cfb017;
}

/* Atelier Dune Dark Green */
.hljs-string,
.hljs-value,
.hljs-inheritance,
.hljs-header,
.ruby .hljs-symbol,
.xml .hljs-cdata {
  color: #60ac39;
}

/* Atelier Dune Dark Aqua */
.css .hljs-hexcolor {
  color: #1fad83;
}

/* Atelier Dune Dark Blue */
.hljs-function,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-function .hljs-title,
.ruby .hljs-title .hljs-keyword,
.perl .hljs-sub,
.javascript .hljs-title,
.coffeescript .hljs-title {
  color: #6684e1;
}

/* Atelier Dune Dark Purple */
.hljs-keyword,
.javascript .hljs-function {
  color: #b854d4;
}

.hljs {
  display: block;
  background: #292824;
  color: #a6a28c;
  padding: 0.5em;
}

.coffeescript .javascript,
.javascript .xml,
.tex .hljs-formula,
.xml .javascript,
.xml .vbscript,
.xml .css,
.xml .hljs-cdata {
  opacity: 0.5;
}

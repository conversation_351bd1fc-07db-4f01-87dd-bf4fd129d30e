; (function ($) {
  $.fn.extend({
    "initEcharts": function (options) {
      // 设置属性
      options = $.extend({
        id: this[0],//echarts 幕布
        wrapper: null,//echarts实例
        allCode: [],//全国地区编码
        addresslist: ['宁波市'],//地址数组
        currentAddress: '宁波市',//当前区域
        // 动画，控制按钮，操作方法
        ani() {
          if ($(options.btnClass).length == 0) return;
          let hide = `${options.btnClass} button:nth-of-type(1)`;
          let animate = `${options.btnClass} button:nth-of-type(2)`;
          let stop = `${options.btnClass} button:nth-of-type(3)`;
          let iShow = `${options.showClass}`;
          let inputClass = `${options.inputClass}`;
          $(hide).on('click', function () {
            $('.content').addClass('bounceOutRight');
            $('.content').removeClass('bounceInRight animation1');
            setTimeout(() => {
              $(iShow).removeClass('bounceOutUp');
              $(iShow).addClass('bounceInDown active');
            })
          });
          $(stop).on('click', function () {
            $(inputClass).removeClass('animation1');
          });
          $(iShow).click(() => {
            $(iShow).addClass('bounceOutUp');
            setTimeout(() => {
              $(iShow).removeClass('bounceInDown active');
              $('.content').addClass('bounceInRight');
              $('.content').removeClass('bounceOutRight animation1');
            })
          })
          $(animate).click(() => {
            $(inputClass).addClass('animation1');
          })
        },
        // li点击重新渲染echarts方法
        ulChange() {
          $(options.ulClass).on("click", "li", (data) => {
            options.currentAddress = data.currentTarget.dataset.name;
            if (options.addresslist.length < 6) {
              options.addresslist.push(options.currentAddress);
            } else {
              options.addresslist.pop();
              options.addresslist.push(options.currentAddress);
            }
            options.getStr(options.addresslist);
            let echartData = {
              address: options.currentAddress,
              id: options.id,
              type: 0,
              option: null
            }
            options.wrapper.dispose();
            options.initEchartsMap(echartData).catch(() => {
              echartData.type = 1;
              options.initEchartsMap(echartData);
            })
            $(options.ulClass).hide();
          })
        },
        // 获取每个地区的json
        getCode() {
          return new Promise((resolve, reject) => {
            $.ajax({
              url: '/static/js/plugins/map/all.json',
              success(data) {
                options.allCode = data;
                resolve(data)
              },
              error(err) {
                reject(err);
              }
            })
          })
        },
        // 动态加入地址tag
        getStr(data) {
          let bArr = [], str = '';
          for (let i = 0; i < data.length; i++) {
            if (bArr.indexOf(data[i]) == -1) {
              bArr.push(data[i]);
            }
          }
          $.each(bArr, function (index, item) {
            if (options.currentAddress === item) {
              console.log('log', item);
              str += `<span class='isActive' data-name='${item}'>${item}</span>`
            } else {
              str += `<span class='noActive' data-name='${item}'>${item}</span>`
            }
          });
          $('.address').html(str)
        },
        // 创建echarts地图
        createMap(address, id) {
          options.wrapper = echarts.init(id, "dark");
          let optionMap = {
            backgroundColor: 'transparent',
            tooltip: { // 指示器
              trigger: 'item',
              formatter: function (params) {
                let item = options.allCode.find((item) => {
                  if (item.name == params.name) {
                    return item;
                  }
                })
                return item ? item.name : params.name;
              }
            },
            legend: {
              orient: "vertical",
              top: "5%",
              right: "5%",
              itemWidth: 18,
              itemHeight: 18,
              data: [{
                name: "宁波市"
              }],
              textStyle: {
                color: "#000",
                fontSize: 14
              }
            },
            geo: {
              map: address,
              zoom: 1.5, // 调整初始缩放级别
              label: {
                normal: {
                  show: true,
                  fontSize: 12,
                  color: "#000"
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    color: "black"
                  }
                }
              },
              roam: 'scale',
              layoutCenter: ['50%', '50%'],
              layoutSize: "100%", // 使用百分比值
              itemStyle: {
                normal: {
                  color: '#FEEDCF',
                  borderColor: '#FF0000'
                },
                emphasis: {
                  color: '#F9D6AE'
                }
              }
            },
            series: [],
            graphic: [
              {
                type: 'text',
                left: 'center',
                bottom: 0,
                style: {
                  text: '本地图是由Apache基金会的开源项目ECharts生成',
                  textAlign: 'center',
                  fill: '#000',
                  fontSize: 12
                }
              }
            ]
          };
          options.getStr(options.addresslist);
          options.wrapper.setOption(optionMap);
          const districtData = [
            {name: "海曙区", url: "/app/caremap/all?tabIndex=0"},
            {name: "江北区", url: "/app/caremap/all?tabIndex=1"},
            {name: "镇海区", url: "/app/caremap/all?tabIndex=2"},
            {name: "北仑区", url: "/app/caremap/all?tabIndex=3"},
            {name: "鄞州区", url: "/app/caremap/all?tabIndex=4"},
            {name: "奉化区", url: "/app/caremap/all?tabIndex=5"},
            {name: "宁海县", url: "/app/caremap/all?tabIndex=6"},
            {name: "象山县", url: "/app/caremap/all?tabIndex=7"},
            {name: "慈溪市", url: "/app/caremap/all?tabIndex=8"},
            {name: "余姚市", url: "/app/caremap/all?tabIndex=9"}
          ];
          // 点击事件
          options.wrapper.on('click', function (params) {
            if (params.componentType === 'geo') {
              let selectedDistrict = districtData.find(district => district.name === params.name);
              if (selectedDistrict) {
                window.location.href = selectedDistrict.url;
              } else {
                alert("未找到对应的区县");
              }
            }
          });

          options.wrapper.on('georoam', function (params) {});
        },
        // 初始化echarts实例
        async initEchartsMap(obj) {
          let {
            address, id, code, type
          } = obj;
          let codeData = await options.getCode();
          codeData.forEach((item) => {
            if (item.name.indexOf(address) > -1 || address.indexOf(item.name) > -1) {
              code = item.adcode;
            }
          })
          return new Promise((resolve, reject) => {
            $.ajax({
              url: '/static/js/plugins/map/330200_full.json',
              success(data) {
                echarts.registerMap(address, data);
                options.createMap(address, id);
                resolve('success');
              },
              error(err) {
                console.log('log', err);
                reject('fail');
              }
            })
          })
        }
      }, options);

      // 确保容器有合适的宽度和高度
      $(this).css({
        width: '100%',
        height: 'calc(100vh + 80px)' // 使用视口高度
      });

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        options.wrapper.resize();
      });

      // li点击重新渲染echarts方法
      options.ulChange();

      // 动画，控制按钮，操作方法
      options.ani();

      // 使用方法
      let echartData = {
        address: '宁波市',
        id: options.id,
        type: 0,
        option: null
      }

      options.initEchartsMap(echartData).then((data) => {
        console.log('log', data);
      });

      // 返回对象，以便支持链式语法
      return this;
    }
  });
})(jQuery)
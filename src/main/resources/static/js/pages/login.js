$(function () {
    document.onkeydown = function (event) {
        if (event && event.keyCode == 13) {
            signIn();
        }
    };
    $("#wrapper-signin").on('click', '#signIn', function () {
        signIn();
    });
    var captchawidth = $("#loginName").parent().width();
    $("#captcha").slideVerify({
        type: 1,		//类型
        vOffset: 5,	//误差量，根据需求自行调整
        barSize: {
            width: '' + captchawidth + '',
            height: '39px',
        },
        ready: function () {
        },
        success: function () {
            $("#hidcaptcha").val("1");
        },
        error: function () {

        }
    });
    $("#btnDgBind").click(function () {
        layer.msg('浙政钉授权中…', {
            icon: 17, shade: 0.05, time: false
        });
        dd.getAuthCode({
            corpId: ""
        }).then(res => {
            var param = {};
            param.authCode = res.auth_code;
            $.ajax({
                url: "/dingtalk/account/qrcode_bind",
                type: "post",
                data: param,
                dataType: "json",
                success: function (data) {
                    if (data.ResultCode == 200) {
                        layer.alert("绑定成功，请重新登录！", {
                            icon: 1, yes: function (index) {
                                $("#zwdd-modal").modal('hide');
                            }
                        });
                    }
                    else {
                        layer.msg(data.ResultMsg, { icon: 2, time: 2000 });
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(XMLHttpRequest.status);
                }
            });
            /*
            $.post('/dingtalk/account/qrcode_bind', param, function (data) {
                if (data.ResultCode == 200) {
                    layer.alert("绑定成功，请重新登录！", {
                        icon: 1, yes: function (index) {
                            $("#zwdd-modal").modal('hide');
                        }
                    });
                }
                else {
                    layer.msg(data.ResultMsg, { icon: 2, time: 2000 });
                }
            });*/
        }).catch(err => { })
    });
});
function checkForm() {
    var loginName = $("#loginName").val();
    var password = $("#password").val();
    if (loginName == "") {
        layer.msg("请输入用户名");
        return false;
    }
    else if (password == "") {
        layer.msg("请输入密码");
        return false;
    }
    else if ($("#hidcaptcha").val() == "0") {
        layer.msg("请滑动解锁完成验证");
        return false;
    }
    else
        return true;
};
function signIn() {
    if (checkForm()) {
        $("#wrapper-signin").empty();
        $("#wrapper-signin").html('<button id="signIn" class="btn btn-primary btn-block" type="button" disabled><span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>登录中...</button>');
        var jsonObj = {};
        jsonObj.LoginName = $("#loginName").val();
        jsonObj.Password = $("#password").val();
        jsonObj.IsRemember = $("#isRemember").prop("checked") ? 1 : 0;
        $.ajax({
            type: 'POST',
            url: '/account/login/',
            data: jsonObj,
            dataType: "json",
            success: function (res) {
                $("#wrapper-signin").html('<button id="signIn" class="btn btn-primary btn-block" type="button">登录</button>');
                if (res.resultCode == 100) { //账号密码错误
                    layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                }
                else if (res.resultCode == 101) {  //未绑定政务钉钉
                    layer.alert(res.resultMsg, {
                        icon: 0, yes: function (index) {
                            layer.close(layer.index);
                            $("#zwdd-modal").modal();
                        }
                    });
                }
                else {
                    var returnUrl = getUrlParam('returnUrl');
                    location.href = returnUrl == undefined ? "/home/<USER>" : decodeURIComponent(returnUrl);
                }
            },
            error: function (data) {
                layer.msg("系统发生错误");
                $("#wrapper-signin").html('<button id="signIn" class="btn btn-primary btn-block" type="button">登录</button>');
            }
        });
    }
};

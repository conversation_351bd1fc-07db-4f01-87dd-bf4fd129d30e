var returnUrl = encodeURIComponent(window.location.href);
//初始化select
var initSelect = function (ctrl, url, filter) {
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'json',
        data: filter,
        success: function (data) {
            var options = '<option value="">请选择</option>';
            for (var i = 0; i < data.length; i++) {
                options += '<option value="' + data[i].id + '">' + data[i].text + '</option>';
            }
            $(ctrl).html("");
            $(ctrl).append(options);
        }
        ,error: function (jqXHR, textStatus, errorThrown) {
           alert(errorThrown);
        }
    });
}
var initSelectWithoutSelected = function (ctrl, url, filter) {
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'json',
        data: filter,
        success: function (data) {
            var options = '';
            for (var i = 0; i < data.length; i++) {
                options += '<option value="' + data[i].id + '">' + data[i].text + '</option>';
            }
            $(ctrl).html("");
            $(ctrl).append(options);
        }
        ,error: function (jqXHR, textStatus, errorThrown) {
            alert(errorThrown);
        }
    });
}
var initSelectJson = function (ctrl, url, filter) {
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'json',
        data: JSON.stringify(filter),
        contentType:'application/json',
        success: function (data) {
            var options = '<option value="">请选择</option>';
            for (var i = 0; i < data.length; i++) {
                options += '<option value="' + data[i].id + '">' + data[i].text + '</option>';
            }
            $(ctrl).html("");
            $(ctrl).append(options);
        }
        ,error: function (jqXHR, textStatus, errorThrown) {
            alert(errorThrown);
        }
    });
}
var initSelectAndSelectedFirst = function (ctrl, url, filter) {
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'json',
        data: filter,
        success: function (data) {
            var options = '';
            for (var i = 0; i < data.length; i++) {
                options += '<option value="' + data[i].id + '">' + data[i].text + '</option>';
            }
            $(ctrl).html("");
            $(ctrl).append(options);
        }
        , error: function (jqXHR, textStatus, errorThrown) {
            alert(errorThrown);
        }
    });
}
//初始化select,并选中项
var initSelect = function (ctrl, url, filter, selectedValue, placeHolder = '请选择') {
    $.ajax({
        url: url,
        type: 'POST',
        dataType: 'json',
        data: filter,
        success: function (data) {
            var options = '<option value="">' + placeHolder + '</option>';
            for (var i = 0; i < data.length; i++) {
                if (data[i].text == selectedValue) {
                    options += '<option value="' + data[i].id + '" selected>' + data[i].text + '</option>';
                }
                else
                    options += '<option value="' + data[i].id + '">' + data[i].text + '</option>';
            }
            $(ctrl).html("");
            $(ctrl).append(options);
        }
        , error: function (jqXHR, textStatus, errorThrown) {
            alert(errorThrown);
        }
    });
}

//获取URL参数
var getUrlParam = function (key) {
    // 获取参数
    var url = window.location.search;
    // 正则筛选地址栏
    var reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
    // 匹配目标参数
    var result = url.substr(1).match(reg);
    //返回参数值
    return result ? decodeURIComponent(result[2]) : null;
}

var formatJsonDate = function (cellval) {
    var date = new Date(parseInt(cellval.replace("/Date(", "").replace(")/", ""), 10));
    var month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
    var currentDate = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
    var hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
    var minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
    var second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
    return date.getFullYear() + "-" + month + "-" + currentDate + " " + hour + ":" + minute + ":" + second;
}

function formatSeconds(value) {
    let result = parseInt(value)
    let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
    let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60));
    let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));

    let res = '';
    if (h !== '00') res += `${h}小时`;
    if (m !== '00') res += `${m}分`;
    res += `${s}秒`;
    return res;
}

var toDecimal = function (x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x * 100) / 100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}

Date.prototype.format = function (format) {
    var date = {
        "M+": this.getMonth() + 1,
        "d+": this.getDate(),
        "h+": this.getHours(),
        "m+": this.getMinutes(),
        "s+": this.getSeconds(),
        "q+": Math.floor((this.getMonth() + 3) / 3),
        "S+": this.getMilliseconds()
    };

    if (/(y+)/i.test(format)) {
        format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    for (var k in date) {
        if (new RegExp("(" + k + ")").test(format)) {
            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? date[k] : ("00" + date[k]).substr(("" + date[k]).length));
        }
    }

    return format;
}

var getDateNowFormat = function () {
    var date = new Date();
    var seperator1 = "-";
    var seperator2 = ":";
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    var hours = date.getHours();
    var min = date.getMinutes();
    var sec = date.getSeconds();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    if (hours >= 0 && hours <= 9) {
        hours = "0" + hours;
    }
    if (min >= 0 && min <= 9) {
        min = "0" + min;
    }
    if (sec >= 0 && sec <= 9) {
        sec = "0" + sec;
    }
    var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate
        + " " + hours + seperator2 + min + seperator2 + sec;
    + seperator2 + date.getSeconds();
    return currentdate;
}

var getAge =function(val){
    let currentYear = new Date().getFullYear() //当前的年份
    let calculationYear = new Date(val).getFullYear() //计算的年份
    const wholeTime = currentYear + val.substring(4) //周岁时间
    const calculationAge = currentYear - calculationYear //按照年份计算的年龄
    //判断是否过了生日
    /*if (new Date().getTime() > new Date(wholeTime).getTime()){
        return calculationAge
    }else {
        return calculationAge - 1
    }*/
    return calculationAge;
}

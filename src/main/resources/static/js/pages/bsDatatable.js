var oTable = null;
(function ($) {
    $.fn.bsDataTables = function (options) {
        var opts = $.extend({}, $.fn.bsDataTables.defaults, options);
        if (oTable != null) {
            oTable.destroy();
        }
        oTable = $(this).DataTable({
            bProcessing: true,
            bServerSide: true,
            bAutoWidth: false,
            bSort: false,
            searching: false,
            bLengthChange: false,
            language: {
                sLengthMenu: "每页显示 _MENU_ 条记录",
                sZeroRecords: '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                sInfo: "_START_ - _END_ ，共 _TOTAL_ 条",
                sInfoEmtpy: '<i class="fa fa-info-circle mr-1"></i>暂无相关数据',
                sInfoFiltered: "共 _MAX_ 条记录",
                sProcessing: "加载中...",
                sSearch: "搜索",
                sInfoEmpty: "显示 0 至 0 共 0 项",
                oPaginate: { "sFirst": "首页", "sPrevious": "&laquo; ", "sNext": "&raquo; ", "sLast": "末页 " }
            },
            paging: opts.paging,
            pageLength: opts.pageLength,
            sPaginationType: 'simple_numbers',
            columns: opts.columns,
            columnDefs: opts.columnDefs,
            sAjaxSource: opts.url,
            fnServerData: opts.retrieveData,
        });
    };
    $.fn.bsDataTables.defaults = {
        columns: [],
        columnDefs: [],
        paging: true,
        url: '',
        pageLength: 20,
        dataParam: {},
        successCallback: function (data) { },
        retrieveData: function (sSource, aoData, fnCallback) {
        }
    };
})(jQuery);
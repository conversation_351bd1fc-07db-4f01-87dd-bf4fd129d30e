var interValObj; //timer变量，控制时间
var count = 120; //间隔函数，1秒执行
var curCount;//当前剩余秒数
var obj = $("#btnSendSms");
var sendSms = function (sendObj) {
    curCount = count;
    //设置button效果，开始计时
    obj.attr("disabled", "true");
    obj.val("重新发送（" + curCount + "）秒");
    interValObj = window.setInterval(countDown, 1000); //启动计时器，1秒执行一次
    $.ajax({
        type: 'POST',
        url: '/sms/send',
        data: JSON.stringify(sendObj),
        contentType:'application/json',
        dataType: "json",
        success: function (res) {
            if (res.resultCode === 200) {
                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
            }
            else{
                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
            }
        }
    });
}
//timer处理函数
var countDown = function () {
    if (curCount === 0) {
        window.clearInterval(interValObj);//停止计时器
        obj.removeAttr("disabled");//启用按钮
        obj.val("重新发送");
    }
    else {
        curCount--;
        obj.val("重新发送（" + curCount + "）秒");
    }
}
var smsLogin = function () {
    $("#wrapper-sms-signin").empty();
    $("#wrapper-sms-signin").html('<button id="btnSmsLogin" class="btn btn-primary btn-block" type="submit" disabled><span class="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>登录中...</button>');
    var jsonObj = {};
    jsonObj.mobile = $("#mobile").val();
    $.ajax({
        type: 'POST',
        url: '/account/smsLogin/',
        data: jsonObj,
        dataType: "json",
        success: function (res) {
            if (res.resultCode === 100) {
                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
                $("#wrapper-signin").html('<button id="btnSmsLogin" class="btn btn-primary btn-block" type="submit">登录</button>');
            }
            else {
                var returnUrl = getUrlParam('returnUrl');
                location.href = returnUrl == undefined ? "/home/<USER>" : decodeURIComponent(returnUrl);
            }
        },
        error: function (data) {
            layer.msg("登录失败");
            $("#wrapper-signin").html('<button id="btnSmsLogin" class="btn btn-primary btn-block" type="submit">登录</button>');
        }
    });
};
/**
 * 对密码进行加密处理
 * @param password 原始密码
 * @returns 加密后的密码
 */
var encryptData= function(password) {
    const publicKey = getPublicKey();
    return RSAEncrypt(password, publicKey);
}
/**
 * 从服务器获取RSA公钥
 * @returns 公钥字符串
 */
var getPublicKey = function () {
    let publicKey = '';
    $.ajax({
        url: '/account/getPublicKey',
        type: 'GET',
        contentType:'application/json',
        dataType: "json",
        async: false,
        success: function(res) {
            if(res.resultCode === 200){
                publicKey = res.data;
            }
            else{
                layer.msg('登录失败，请重试！', { icon: 2, time: 3000 });
            }
        }
    });
    return publicKey;
}
/**
 * 使用RSA公钥加密数据
 * @param password 要加密的密码
 * @param publicKey RSA公钥
 * @returns 加密后的密文
 */
var RSAEncrypt = function(password, publicKey) {
    let encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);
    return encrypt.encrypt(password);
}
$(function () {
    var captchaWidth = $("#loginName").parent().width();
    $("#captchaSms").slideVerify({
        type: 1,		//类型
        vOffset: 5,	//误差量，根据需求自行调整
        barSize: {
            width: '' + captchaWidth + '',
            height: '36px',
        },
        ready: function () {
        },
        success: function () {
            $("#hidCaptchaSms").val("1");
        },
        error: function () {

        }
    });
    $("#btnSendVerifySms").click(function () {
        if ($("#mobile").val() === '') {
            layer.msg("请填写手机号码", { icon: 0, time: 2000 });
            return;
        }
        else if ($("#hidCaptchaSms").val() === "0") {
            layer.msg("请先滑动解锁验证", { icon: 0, time: 2000 });
            return;
        }
        else {
            var jsonObj = {};
            jsonObj.phoneNumber = encryptData($("#mobile").val());
            jsonObj.signName = $("#hidSignName").val();
            jsonObj.templateCode = $("#hidTemplateCode").val();
            jsonObj.smsType= 1;
            obj = $("#btnSendVerifySms");
            sendSms(jsonObj);
        }
    });
    $.validator.addMethod('checkMobile', function (value, element, param) {
        var phomeNumber = $(element).val();
        var mobileReg = /^(((13[0-9]{1})|(14[0-9]{1})|(17[0]{1})|(15[0-3]{1})|(15[5-9]{1})|(18[0-9]{1}))+\d{8})$/;
        if (mobileReg.test(phomeNumber)) {
            return true;
        }
        else {
            return false;
        }
    }, "请输入正确格式的手机号码");
    $.validator.addMethod('checkCaptcha', function (value, element, param) {
        var hidCaptchaSmsVal = $("#hidCaptchaSms").val();
        if (hidCaptchaSmsVal != "1") {
            return false;
        }
        else {
            return true;
        }
    }, "请滑动解锁验证");
    $("#frmSendSms").validate({
        ignore: [],
        errorPlacement: function (error, element) {
            wrap = element.parent();
            if (wrap.hasClass('input-group')) {
                error.insertAfter(wrap);
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            mobile: {
                required: true,
                checkMobile: true,
                remote: {
                    type: "POST",
                    url: "/account/check_mobile",
                    dataType: "json",
                    data: {
                        mobile: function () {
                            return $("#mobile").val();
                        }
                    },
                    dataFilter: function (data, type) {
                        var res = JSON.parse(data);
                        if (res.resultCode === 200) {
                            return false;
                        }
                        else
                            return true;
                    }
                }
            },
            verifySms: {
                required: true,
                remote:{
                    type: "POST",
                    url: "/sms/verify",
                    dataType: "json",
                    data: {
                        phoneNumber: function () {
                            return $.trim($("#mobile").val());
                        },
                        templateCode: function () {
                            return $("#hidTemplateCode").val();
                        },
                        smsCode: function () {
                            return $("#verifySms").val();
                        }
                    },
                    dataFilter: function (data, type) {
                        var res = JSON.parse(data);
                        if (res.resultCode === 200) {
                            return true;
                        }
                        else
                            return true;
                    }
                }
            },
            hidCaptchaSms: { checkCaptcha: true }
        },
        messages: {
            mobile: {
                required: "请填写手机号码",
                checkMobile: "请输入正确格式的手机号码",
                remote: "该手机号码未绑定"
            },
            verifySms: { required: "请填写短信验证码",remote: "验证码有误" },
            hidCaptchaSms: { checkCaptcha: "请滑动解锁完成验证" }
        },
        submitHandler: function () {
            smsLogin();
        }
    });
});
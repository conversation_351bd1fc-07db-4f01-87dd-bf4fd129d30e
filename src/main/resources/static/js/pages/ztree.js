var ztreeData;
var setting = {
    view: {
        dblClickExpand: false,
        selectedMulti: false
    },
    data: {
        simpleData: {
            enable: true,
            idKey: "id",
            pIdKey: "pId",
            rootPId: 0
        },
    },
    callback: {
        beforeClick: beforeClick,
        onClick: onfrmClick
    }
};
function showIconForTree(treeId, treeNode) {
    return !treeNode.isParent;
};

function beforeClick(treeId, treeNode) {

}

function onfrmClick(e, treeId, treeNode) {
    var zTree = $.fn.zTree.getZTreeObj("structTree"), nodes = zTree.getSelectedNodes(), v = "", id = "";
    nodes.sort(function compare(a, b) { return a.id - b.id; });
    for (var i = 0, l = nodes.length; i < l; i++) {
        v += nodes[i].name + ",";
        id += nodes[i].id + ",";
    }
    if (v.length > 0) v = v.substring(0, v.length - 1);
    if (id.length > 0) id = id.substring(0, id.length - 1);
    var pObj = $("#structName");
    pObj.val(v);
    $("#hidStructParentID").attr("value", id);
    hideTree();
}

function hideTree() {
    $("#structContent").fadeOut("fast");
    $("body").unbind("mousedown", onfrmBodyDown);
}
function onfrmBodyDown(event) {
    if (!(event.target.id == "menuBtn" || event.target.id == "structContent" || $(event.target).parents("#structContent").length > 0)) {
        hideTree();
    }
}
async function initTree() {
    $("#structTree").html('<img src="/static/images/loading.gif" height="18" class="mr-1">数据加载中…');
    $("#structContent").slideDown("fast");
    $("body").bind("mousedown", onfrmBodyDown);
    if (ztreeData == undefined) {
        await $.ajax({
            url: "/anteroom/structs/index",
            type: 'POST',
            data: "",
            dataType: "JSON",
            async: true,
            success: function (res) {
                $("#structTree").html('');
                ztreeData = JSON.parse(res);
            }
        });
    }
    $.fn.zTree.init($("#structTree"), setting, ztreeData);
    fuzzySearch('structTree', '#search', false, true); //初始化模糊搜索方法
    var zTree = $.fn.zTree.getZTreeObj("structTree");
    var nodes = zTree.getNodes();
    for (var i = 0; i < nodes.length; i++) {
        zTree.expandNode(nodes[i], true, false, true);
    }
    $("#search").val('');
}
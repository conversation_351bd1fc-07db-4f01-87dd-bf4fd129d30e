var chartDataConfig = {
    gauge:{
        chart: {
            type: 'gauge',
            spacing: [5, 5, 5, 5],
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        pane: {
            startAngle: -90,
            endAngle: 89.9,
            background: null,
            center: ['50%', '65%'],
            size: '90%'
        },
        // the value axis
        yAxis: {
            min: 0,
            max: 1,
            title: {
                text: ''
            },
            plotBands: []
        },
        series: []
    },
    solidgauge:{
        chart: {
            type: 'solidgauge',
            spacing: [5, 5, 5, 5],
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        pane: {
            center: ['50%', '70%'],
            size: '100%',
            startAngle: -90,
            endAngle: 90,
            background: {
                backgroundColor: (Highcharts.theme && Highcharts.theme.background2) || '#EEE',
                innerRadius: '60%',
                outerRadius: '100%',
                shape: 'arc'
            }
        },
        tooltip: {
            enabled: false
        },
        yAxis: {
            min: 0,
            max: 1,
            stops: [
                [0.1, '#55BF3B'], // green
                [0.5, '#DDDF0D'], // yellow
                [0.9, '#DF5353'] // red
            ],
            lineWidth: 0,
            minorTickInterval: null,
            tickWidth: 0,
            title: {
                y: -170
            },
            labels: {
                y: 16
            }
        },
        series: []
    },
    line:{
        chart: {
            type: 'line',
            renderTo: '',
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        xAxis: {
            categories: []
        },
        yAxis: {
            title: {
                text: '得分范围'
            }
        },
        series: []
    },
    column:{
        chart: {
            type: 'column',
            renderTo: '',
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        xAxis: {
            categories: []
        },
        yAxis: {
            title: {
                text: '得分范围'
            }
        },
        series: []
    },
    bar:{
        chart: {
            type: 'bar',
            renderTo: '',
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        xAxis: {
            categories: []
        },
        yAxis: {
            title: {
                text: '得分范围'
            }
        },
        series: []
    },
    columnpyramid:{
        chart: {
            type: 'columnpyramid',
            renderTo: '',
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        xAxis: {
            categories: []
        },
        yAxis: {
            title: {
                text: '得分范围'
            }
        },
        series: []
    },
    spline:{
        chart: {
            type: 'spline',
            renderTo: '',
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        xAxis: {
            categories: []
        },
        yAxis: {
            title: {
                text: '得分范围'
            }
        },
        series: []
    },
    waterfall:{
        chart: {
            type: 'waterfall',
            renderTo: '',
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        xAxis: {
            categories: []
        },
        yAxis: {
            title: {
                text: '得分范围'
            }
        },
        series: []
    },
    pie:{
        chart: {
            type: 'pie',
            renderTo: '',
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        plotOptions: {
            pie: {
                allowPointSelect: true,
                cursor: 'pointer',
                dataLabels: {
                    enabled: true,
                    format: '<b>{point.name}</b>: {point.percentage:.1f} %'
                }
            }
        },
        series: []
    },
    radar:{
        chart: {
            polar: true,
            width: null,
            height: 350
        },
        title: {
            text: ''
        },
        credits: { enabled: false },
        xAxis: {
            labels: {
                format: '{value}'
            }
        },
        yAxis: {
            min: 0
        },
        series: []
    },
    spiderweb:{
        chart: {
            polar: true,
            type: 'line',
            width: null,
            height: 350
        },
        title: {
            text: '',
            x: -80
        },
        credits: { enabled: false },
        xAxis: {
            categories: [],
            tickmarkPlacement: 'on',
            lineWidth: 0
        },
        yAxis: {
            gridLineInterpolation: 'polygon',
            lineWidth: 0,
            min: 0
        },
        series: []
    }
}
var interValObj; //timer变量，控制时间
var count = 120; //间隔函数，1秒执行
var curCount;//当前剩余秒数
var obj = $("#btnSendSms");
var sendSms = function (sendObj) {
    curCount = count;
    //设置button效果，开始计时
    obj.attr("disabled", "true");
    obj.val("重新发送（" + curCount + "）秒");
    interValObj = window.setInterval(countDown, 1000); //启动计时器，1秒执行一次
    $.ajax({
        type: 'POST',
        url: '/sms/send',
        data: JSON.stringify(sendObj),
        contentType:'application/json',
        dataType: "json",
        success: function (res) {
            if (res.resultCode === 200) {
                layer.msg(res.resultMsg, { icon: 1, time: 2000 });
            }
            else{
                layer.msg(res.resultMsg, { icon: 2, time: 2000 });
            }
        }
    });
}
//timer处理函数
var countDown = function () {
    if (curCount == 0) {
        window.clearInterval(interValObj);//停止计时器
        obj.removeAttr("disabled");//启用按钮
        obj.val("重新发送");
    }
    else {
        curCount--;
        obj.val("重新发送（" + curCount + "）秒");
    }
}
$.validator.addMethod('checkMobile', function (value, element, param) {
    var phomeNumber = $(element).val();
    var mobileReg = /^(((13[0-9]{1})|(14[0-9]{1})|(17[0]{1})|(15[0-3]{1})|(15[5-9]{1})|(18[0-9]{1}))+\d{8})$/; 
    if (mobileReg.test(phomeNumber)) {
        return true;
    }
    else {
        return false;
    }
}, "请输入正确格式的手机号码");

$("#btnSendSms").click(function () {
    if ($("#mobile").val() == '') {
        layer.msg("请填写手机号码！");
        return;
    }
    var jsonObj = {};
    jsonObj.phoneNumber =$("#mobile").val();
    jsonObj.signName = $("#hidSignName").val();
    jsonObj.templateCode = $("#hidTemplateCode").val();
    jsonObj.smsType =1;
    obj = $("#btnSendSms");
    sendSms(jsonObj);
});
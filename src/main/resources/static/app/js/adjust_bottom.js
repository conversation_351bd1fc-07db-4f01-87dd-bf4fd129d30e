// 通用底部间距调整函数
function adjustBottomPadding() {
    const bottomMenu = document.querySelector('.appBottomMenu2');
    const appCapsule = document.getElementById('appCapsule');

    if (bottomMenu && appCapsule) {
        // 获取底部菜单的实际高度和位置
        const menuRect = bottomMenu.getBoundingClientRect();
        const menuHeight = menuRect.height;

        // 获取视口高度
        const viewportHeight = window.innerHeight;

        // 计算菜单距离视口底部的距离
        const menuBottom = menuRect.bottom;
        const distanceFromBottom = viewportHeight - menuBottom;

        // 设置合适的底部内边距，确保不被遮挡
        const safePadding = Math.max(menuHeight + 5, 25); // 菜单高度 + 5px，至少25px

        // 使用CSS变量设置底部内边距
        appCapsule.style.setProperty('--bottom-padding', safePadding + 'px');
        appCapsule.style.paddingBottom = safePadding + 'px';

        console.log('Universal - Menu height:', menuHeight, 'Menu bottom:', menuBottom, 'Viewport height:', viewportHeight, 'Safe padding:', safePadding);
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 页面加载完成后初始化
$(document).ready(function() {
    // 初始调整
    adjustBottomPadding();

    // 多重延迟调整，确保在各种情况下都能正确显示
    setTimeout(adjustBottomPadding, 50);
    setTimeout(adjustBottomPadding, 200);
    setTimeout(adjustBottomPadding, 500);
    setTimeout(adjustBottomPadding, 1000);

    // 移动端额外延迟
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        setTimeout(adjustBottomPadding, 1000);
        setTimeout(adjustBottomPadding, 2000);
    }

    // 事件监听器
    window.addEventListener('resize', debounce(adjustBottomPadding, 100));
    window.addEventListener('orientationchange', function() {
        setTimeout(adjustBottomPadding, 100);
        setTimeout(adjustBottomPadding, 500);
    });

    // 监听DOM变化
    const observer = new MutationObserver(function(mutations) {
        adjustBottomPadding();
    });

    // 观察底部菜单的变化
    const bottomMenu = document.querySelector('.appBottomMenu2');
    if (bottomMenu) {
        observer.observe(bottomMenu, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(adjustBottomPadding, 100);
        }
    });

    // 监听触摸事件
    let touchStartY = 0;
    document.addEventListener('touchstart', function(e) {
        touchStartY = e.touches[0].clientY;
    });

    document.addEventListener('touchend', function(e) {
        const touchEndY = e.changedTouches[0].clientY;
        const diff = touchStartY - touchEndY;

        if (Math.abs(diff) > 50) {
            setTimeout(adjustBottomPadding, 100);
        }
    });

    // 最终检查
    setTimeout(function() {
        const bottomMenu = document.querySelector('.appBottomMenu2');
        const appCapsule = document.getElementById('appCapsule');

        if (bottomMenu && appCapsule) {
            const menuHeight = bottomMenu.offsetHeight;
            const currentPadding = parseInt(getComputedStyle(appCapsule).paddingBottom);

            if (currentPadding < menuHeight + 8) {
                const newPadding = menuHeight + 8;
                appCapsule.style.paddingBottom = newPadding + 'px';
                appCapsule.style.setProperty('--bottom-padding', newPadding + 'px');
                console.log('Universal - Final adjustment - Menu height:', menuHeight, 'New padding:', newPadding);
            }
        }
    }, 2000);
});
package cn.psycloud.psyplatform;

import cn.psycloud.psyplatform.util.chat.NettyServer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.SessionCookieConfig;
import javax.servlet.SessionTrackingMode;
import java.util.Collections;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@MapperScan("cn.psycloud.psyplatform.dao")
@SpringBootApplication
public class PsyPlatformApplication extends SpringBootServletInitializer {
    @Value("${netty.enabled}")
    private static boolean nettyEnabled;

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(PsyPlatformApplication.class);
    }

    public static void main(String[] args) {
        ApplicationContext ctx = SpringApplication.run(PsyPlatformApplication.class, args);
        //是否启用netty服务
        if(nettyEnabled){
            NettyServer nettyServer = ctx.getBean("NettyServer",NettyServer.class);
            ExecutorService service = Executors.newCachedThreadPool();
            service.execute(nettyServer);
            service.shutdown();
        }
    }

    public void onStartup(ServletContext servletContext) throws ServletException {
        super.onStartup(servletContext);
        servletContext.setSessionTrackingModes(Collections.singleton(SessionTrackingMode.COOKIE));
        SessionCookieConfig sessionCookieConfig = servletContext.getSessionCookieConfig();
        sessionCookieConfig.setHttpOnly(true);
    }

}

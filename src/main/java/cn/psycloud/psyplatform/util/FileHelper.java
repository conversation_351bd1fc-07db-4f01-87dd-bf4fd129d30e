package cn.psycloud.psyplatform.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 *  文件上传工具类
 */
@Component
@Slf4j
public class FileHelper {
    public String fileUpload(MultipartFile file, String fileType, String uploadPath, HttpServletRequest request){
        String projectRealPath = uploadPath+"/"+fileType;
        File saveDir = new File(projectRealPath);
        if(!saveDir.exists()){
            saveDir.mkdirs();
        }
        String newFileName = getNewFileName(file);
        try{
            file.transferTo(new File(saveDir,newFileName));
        }
        catch (Exception e){
            e.printStackTrace();
            log.error("文件上传失败："+e.getMessage());
        }
        return newFileName;
    }

    private String getNewFileName(MultipartFile file){
        SimpleDateFormat simpleDateFormat =new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String timeStamp=simpleDateFormat.format(new Date());
        String fileName = file.getOriginalFilename();
        String fileExtension =fileName.substring(fileName.lastIndexOf("."));
        return timeStamp+fileExtension;
    }
}

package cn.psycloud.psyplatform.util;

import org.apache.commons.codec.binary.Base64;
import javax.crypto.Cipher;
import java.security.*;

/**
 * RSA加解密工具类
 * 用于处理密码的加密和解密
 */
public class RSAUtil {
    // RSA算法名称
    private static final String KEY_ALGORITHM = "RSA";
    // 密钥长度
    private static final int KEY_SIZE = 2048;
    // 存储生成的密钥对
    private static KeyPair keyPair;

    /*
      静态初始化块
      生成RSA密钥对
     */
    static {
        try {
            KeyPairGenerator generator = KeyPairGenerator.getInstance(KEY_ALGORITHM);
            generator.initialize(KEY_SIZE);
            keyPair = generator.generateKeyPair();
        } catch (Exception e) {
            throw new RuntimeException("初始化RSA密钥对失败", e);
        }
    }

    /**
     * 获取RSA公钥
     * @return Base64编码的公钥字符串
     */
    public static String getPublicKey() {
        return Base64.encodeBase64String(keyPair.getPublic().getEncoded());
    }

    /**
     * 使用私钥解密数据
     * @param encryptedData Base64编码的加密数据
     * @return 解密后的明文字符串
     * @throws Exception 解密过程中的异常
     */
    public static String decrypt(String encryptedData) throws Exception {
        byte[] decoded = Base64.decodeBase64(encryptedData);
        PrivateKey privateKey = keyPair.getPrivate();

        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);

        byte[] decryptedBytes = cipher.doFinal(decoded);
        return new String(decryptedBytes);
    }
}

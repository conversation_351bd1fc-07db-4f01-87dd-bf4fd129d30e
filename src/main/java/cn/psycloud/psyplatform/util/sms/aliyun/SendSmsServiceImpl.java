package cn.psycloud.psyplatform.util.sms.aliyun;

import cn.psycloud.psyplatform.dao.platform.SmsSendRecordDao;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.platform.SendSmsDto;
import cn.psycloud.psyplatform.dto.platform.VerifySmsCodeDto;
import cn.psycloud.psyplatform.entity.platform.SmsSendRecordEntity;
import cn.psycloud.psyplatform.util.redis.JedisUtil;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class SendSmsServiceImpl implements SendSmsService{
    @Autowired
    private AliyunSms aliyunSms;
    @Autowired
    private SmsSendRecordDao smsSendRecordDao;
    /**
     *  发送短信验证码
     * @param dto 实体对象
     * @return 结果实体对象
     */
    @Override
    public JsonResult<Object> sendSms(SendSmsDto dto){
        var result = new JsonResult<>();
        //验证缓存里的验证码是否过期
        var smsMap = JedisUtil.getMap(dto.getPhoneNumber());
        if(smsMap != null) {
            var templateCode = smsMap.get("templateCode");
            if(templateCode.equals(dto.getTemplateCode())){
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.SEND_SMS_NOTEXPIRED);
            }
        }
        else{
            int smsVerifyCode= (int)((Math.random()*9+1)*100000);
            dto.setSmsContent(String.valueOf(smsVerifyCode));
            try {
                SendSmsResponse response = aliyunSms.sendSms(dto);
                if("OK".equals(response.getBody().getCode())){
                    //缓存验证码
                    smsMap = new HashMap<>();
                    smsMap.put("smsVerifyCode", String.valueOf(smsVerifyCode));
                    smsMap.put("templateCode",dto.getTemplateCode());
                    JedisUtil.setMap(dto.getPhoneNumber(),smsMap,120); //短信验证码有效期：2分钟
                    //添加发送记录
                    addSendRecord(dto);

                    result.setResultCode(ResultCodeAndMsg.SuccessCode);
                    result.setResultMsg(ResultCodeAndMsg.SEND_SMS_SUCCESS);
                }
                else{
                    result.setResultCode(ResultCodeAndMsg.FailureCode);
                    result.setResultMsg(response.getBody().getMessage());
                }
            }
            catch (Exception e){
                log.error("发送短信时发生错误：{}",e.getMessage());
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.SEND_SMS_FAILED);
            }
        }
        return result;
    }

    /**
     *  发送通知短信
     * @param dto 实体对象
     */
    @Override
    public void sendNotify(SendSmsDto dto){
        try {
            SendSmsResponse response = aliyunSms.sendSms(dto);
            addSendRecord(dto);
            if(!"OK".equals(response.getBody().getCode())){
                log.error("发送短信时发生错误：{}",response.getBody().getMessage());
            }
        }
        catch (Exception e){
            log.error("发送短信时发生错误：{}",e.getMessage());
        }
    }

    /**
     *  短信校验
     * @param verifySmsCodeDto 短信校验实体对象
     * @return 校验结果
     */
    @Override
    public boolean verifySmsCode(VerifySmsCodeDto verifySmsCodeDto) {
        boolean result = false;
        var smsMap = JedisUtil.getMap(verifySmsCodeDto.getPhoneNumber());
        if(smsMap != null) {
            var templateCode = smsMap.get("templateCode");
            var smsVerifyCode = smsMap.get("smsVerifyCode");
            if(templateCode.equals(verifySmsCodeDto.getTemplateCode()) && verifySmsCodeDto.getSmsCode().equals(smsVerifyCode)){
                result = true;
            }
        }
        return result;
    }

    /**
     *  添加发送记录
     * @param dto 实体对象
     * @return 影响行数
     */
    private void addSendRecord(SendSmsDto dto){
        var smsSendRecordEntity = new SmsSendRecordEntity();
        smsSendRecordEntity.setMobile(dto.getPhoneNumber());
        smsSendRecordEntity.setSignName(dto.getSignName());
        smsSendRecordEntity.setTemplateCode(dto.getTemplateCode());
        smsSendRecordDao.addRecord(smsSendRecordEntity);
    }
}

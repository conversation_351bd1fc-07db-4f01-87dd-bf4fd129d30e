package cn.psycloud.psyplatform.util.sms.aliyun;

import cn.psycloud.psyplatform.dto.platform.SendSmsDto;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.tea.*;
import com.aliyun.teaopenapi.models.Config;
import lombok.var;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AliyunSms {
    @Value("${sms.accessKeyId}")
    private String accessKeyId;
    @Value("${sms.accessKeySecret}")
    private  String accessKeySecret;
    @Value("${sms.domain}")
    private String domain;

    private Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = domain;
        return new Client(config);
    }

    /**
     *  发送验证码短信
     * @param dto 短信实体对象
     * @return sendSmsRequest
     */
    private SendSmsRequest sendSmsCodeRequest(SendSmsDto dto) {
        return new SendSmsRequest()
                .setSignName(dto.getSignName())
                .setTemplateCode(dto.getTemplateCode())
                .setPhoneNumbers(dto.getPhoneNumber())
                .setTemplateParam("{ \"code\":\"" + dto.getSmsContent() + "\"}");
    }

    /**
     *  发送通知短信
     * @param dto 短信实体对象
     * @return sendSmsRequest
     */
    private SendSmsRequest sendSmsNotifyRequest(SendSmsDto dto) {
        SendSmsRequest sendSmsRequest = new SendSmsRequest();
        sendSmsRequest.setSignName(dto.getSignName());
        sendSmsRequest.setTemplateCode(dto.getTemplateCode());
        sendSmsRequest.setPhoneNumbers(dto.getPhoneNumber());
        if(dto.getSmsContent() != null && !dto.getSmsContent().equals("")){
            sendSmsRequest.setTemplateParam(dto.getSmsContent());
        }
        return  sendSmsRequest;
    }

    /**
     *  发送短信业务
     * @param dto 短信实体对象
     * @return 响应
     * @throws Exception 异常
     */
    public SendSmsResponse sendSms(SendSmsDto dto) throws Exception {
        var response = new SendSmsResponse();
        Client client = createClient(accessKeyId, accessKeySecret);
        SendSmsRequest sendSmsRequest = null;
        if(dto.getSmsType() == 1) {
            sendSmsRequest = sendSmsCodeRequest(dto);
        }
        if(dto.getSmsType() == 2){
            sendSmsRequest = sendSmsNotifyRequest(dto);
        }
        try {
            response =  client.sendSmsWithOptions(sendSmsRequest, new com.aliyun.teautil.models.RuntimeOptions());
        } catch (TeaException error) {
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return response;
    }
}

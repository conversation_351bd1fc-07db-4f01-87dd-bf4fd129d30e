package cn.psycloud.psyplatform.util.sms.aliyun;

import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.platform.SendSmsDto;
import cn.psycloud.psyplatform.dto.platform.VerifySmsCodeDto;

public interface SendSmsService {
    /**
     *  发送短信验证码
     * @param dto 实体对象
     * @return 结果实体对象
     */
    JsonResult<Object> sendSms(SendSmsDto dto);

    /**
     *  发送通知短信
     * @param dto 实体对象
     */
    void sendNotify(SendSmsDto dto);

    /**
     *  短信校验
     * @param verifySmsCodeDto 短信校验实体对象
     * @return 校验结果
     */
    boolean verifySmsCode(VerifySmsCodeDto verifySmsCodeDto);
}

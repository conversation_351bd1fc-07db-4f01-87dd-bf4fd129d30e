package cn.psycloud.psyplatform.util.chat;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingRecordEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingRecordService;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.util.AttributeKey;
import io.netty.util.concurrent.GlobalEventExecutor;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
@ChannelHandler.Sharable
public class ChatChannelHandler extends SimpleChannelInboundHandler<TextWebSocketFrame> {
    @Autowired
    private CounselingRecordService counselingRecordService;
    public static ChannelGroup channelGroup;
    static {
        channelGroup = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);
    }

    //客户端与服务器建立连接的时候触发，
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        //添加到channelGroup通道组
        NettyConfig.getChannelGroup().add(ctx.channel());
    }
    //客户端与服务器关闭连接的时候触发，
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        AttributeKey<String> key = AttributeKey.valueOf("userId");
        String userId = ctx.channel().attr(key).get();
        NettyConfig.getChannelMap().remove(userId);
        NettyConfig.getChannelGroup().remove(ctx.channel());
    }
    //服务器接受客户端的数据信息，
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, TextWebSocketFrame msg){
        JSONObject jsonObject = JSONUtil.parseObj(msg.text());
        // 获取用户ID,关联channel
        String userId = jsonObject.getStr("userId");
        NettyConfig.getChannelMap().put(userId,ctx.channel());
        // 将用户ID作为自定义属性加入到channel中，方便随时channel中获取用户ID
        AttributeKey<String> key = AttributeKey.valueOf("userId");
        ctx.channel().attr(key).setIfAbsent(userId);


        Channel channelToUser = NettyConfig.getChannel(jsonObject.getStr("toUserId"));
        Channel channelFromUser = NettyConfig.getChannel(jsonObject.getStr("userId"));
        if (Objects.isNull(channelToUser) || Objects.isNull(channelFromUser) ) {
            throw new RuntimeException("未连接socket服务器");
        }
        //保存咨询内容
        var counselingRecordEntity = new CounselingRecordEntity();
        counselingRecordEntity.setOrderId(Integer.parseInt(jsonObject.getStr("orderId")));
        counselingRecordEntity.setRealName(jsonObject.getStr("realName"));
        counselingRecordEntity.setCounselingContent(jsonObject.getStr("message"));
        counselingRecordEntity.setSendTime(new Date());
        counselingRecordService.addContent(counselingRecordEntity);

        channelToUser.writeAndFlush(new TextWebSocketFrame(JSONUtil.toJsonStr(jsonObject)));
        channelFromUser.writeAndFlush(new TextWebSocketFrame(JSONUtil.toJsonStr(jsonObject)));
    }
}

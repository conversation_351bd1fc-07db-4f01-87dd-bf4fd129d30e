package cn.psycloud.psyplatform.util;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.ddr.poi.html.HtmlRenderPolicy;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTFonts;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Map;

@Component
@Slf4j
public class POIWordHelper {
    @Value("${file.location}")
    String uploadPath;
    public XWPFDocument document = null;
    private XWPFParagraph paragraph = null;

    public  POIWordHelper() {
        document = new XWPFDocument();
    }

    /**
     * 创建一个段落
     * @param position 段落位置
     * 0：居左
     * 1：居中
     * 2：居右
     */
    public void createParagraph(Integer position){
        paragraph = document.createParagraph();
        switch (position){
            case 1:
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                break;
            case 2:
                paragraph.setAlignment(ParagraphAlignment.RIGHT);
                break;
            case 0:
            default:
                paragraph.setAlignment(ParagraphAlignment.LEFT);
                break;
        }
    }

    /**
     * 创建文本信息
     * @param text 文本信息
     * @param bold 是否加粗 true为加粗
     * @param italic 是否倾斜 true为倾斜
     * @param color 颜色码
     * @param fontSize 字体大小
     * @param fontFamily 设置字体
     */
    public void createRun(String text, Boolean bold,Boolean italic, String color,Integer fontSize,String fontFamily){
        XWPFRun r = paragraph.createRun();//创建段落文本
        r.setText(text);
        r.setBold(bold);//设置为粗体 true 为粗体
        r.setItalic(italic);//设置为倾斜 true 为粗体
        r.setColor(color);//设置颜色
        r.setFontSize(fontSize);
        CTRPr rpr = r.getCTR().isSetRPr() ? r.getCTR().getRPr() : r.getCTR().addNewRPr();
        CTFonts fonts = rpr.isSetRFonts() ? rpr.getRFonts() : rpr.addNewRFonts();
        fonts.setAscii(fontFamily);
        fonts.setEastAsia(fontFamily);
        fonts.setHAnsi(fontFamily);
    }

    /**
     * 首行缩进
     * @param indentation 首行缩进
     */
    public void addTextIndent(Integer indentation){
        paragraph.setIndentationFirstLine(indentation);
    }

    /**
     * 创建空行
     * @param counts 空行个数
     */
    public void createEmpty(Integer counts){
        XWPFParagraph empty = document.createParagraph();
        XWPFRun e = empty.createRun();
        e.setText(" ");
    }

    /**
     *  Poi-tl模板生成word：测评报告
     * @param paramMap 填充的数据
     */
    public void downloadWord(String templatePath,String filePath, Map<String, Object> paramMap) {
        try {
            File newFile = new File(filePath);
            //判断目标文件所在目录是否存在
            if(!newFile.getParentFile().exists()){
                //如果目标文件所在的目录不存在，则创建父目录
                newFile.getParentFile().mkdirs();
            }

            HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy();
            Configure config = Configure.builder()
                    .bind("interpretation",htmlRenderPolicy)
                    .build();
            // 读取模板templatePath并将paramMap的内容填充进模板，即编辑模板(compile)+渲染数据(render)
            XWPFTemplate template = XWPFTemplate.compile(templatePath,config).render(paramMap);

            OutputStream out = new FileOutputStream(filePath);//输出路径(下载到指定路径)
            // 将填充之后的模板写入filePath
            template.write(out);//将template写到OutputStream中
            out.flush();
            out.close();
            template.close();
        } catch (Exception e) {
            log.error("生成word失败：{}",e.getMessage());
            throw new RuntimeException("生成Word文档失败", e);
        }
    }


    /**
     *  Poi-tl模板生成word：心理档案
     * @param response 响应
     * @param paramMap 填充的数据
     */
    public void createArchive(HttpServletResponse response, HttpServletRequest request,String templatePath,String filePath, Map<String, Object> paramMap){
        try{
            File newFile = new File(filePath);
            //判断目标文件所在目录是否存在
            if(!newFile.getParentFile().exists()){
                //如果目标文件所在的目录不存在，则创建父目录
                newFile.getParentFile().mkdirs();
            }
            HackLoopTableRenderPolicy  policy = new HackLoopTableRenderPolicy();
            HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy();
            Configure config = Configure.builder()
                    .bind("testRecords", policy)
                    .bind("interpretation",htmlRenderPolicy)
                    .build();
            // 读取模板templatePath并将paramMap的内容填充进模板，即编辑模板(compile)+渲染数据(render)
            XWPFTemplate template = XWPFTemplate.compile(templatePath,config).render(paramMap);

            OutputStream out = response.getOutputStream();
            out = new FileOutputStream(filePath);//输出路径(下载到指定路径)
            // 将填充之后的模板写入filePath
            template.write(out);//将template写到OutputStream中
            out.flush();
            out.close();
            template.close();
        }
        catch (Exception e){
            log.error("生成档案失败：{}",e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成问卷调查结果统计报告
     * @param response
     * @param request
     * @param templatePath
     * @param filePath
     * @param paramMap
     */
    public void createSurveyReport(HttpServletResponse response, HttpServletRequest request,String templatePath,String filePath, Map<String, Object> paramMap){
        try{
            File newFile = new File(filePath);
            //判断目标文件所在目录是否存在
            if(!newFile.getParentFile().exists()){
                //如果目标文件所在的目录不存在，则创建父目录
                newFile.getParentFile().mkdirs();
            }
            HackLoopTableRenderPolicy  policy = new HackLoopTableRenderPolicy();
            Configure config = Configure.builder()
                    .bind("listResultCounts",policy)
                    .build();
            // 读取模板templatePath并将paramMap的内容填充进模板，即编辑模板(compile)+渲染数据(render)
            XWPFTemplate template = XWPFTemplate.compile(templatePath,config).render(paramMap);

            OutputStream out = response.getOutputStream();
            out = new FileOutputStream(filePath);//输出路径(下载到指定路径)
            // 将填充之后的模板写入filePath
            template.write(out);//将template写到OutputStream中
            out.flush();
            out.close();
            template.close();
        }
        catch (Exception e){
            log.error("生成问卷调查结果统计报告：{}",e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成咨询个案报告
     * @param response HTTP响应
     * @param request HTTP请求
     * @param templatePath 模板路径
     * @param filePath 输出文件路径
     * @param paramMap 参数映射
     */
    public void createConsultationCaseReport(HttpServletResponse response, HttpServletRequest request, String templatePath, String filePath, Map<String, Object> paramMap) {
        try {
            File newFile = new File(filePath);
            //判断目标文件所在目录是否存在
            if(!newFile.getParentFile().exists()){
                //如果目标文件所在的目录不存在，则创建父目录
                newFile.getParentFile().mkdirs();
            }
            
            Configure config = Configure.builder().build();
            // 读取模板templatePath并将paramMap的内容填充进模板，即编辑模板(compile)+渲染数据(render)
            XWPFTemplate template = XWPFTemplate.compile(templatePath, config).render(paramMap);

            OutputStream out = response.getOutputStream();
            out = new FileOutputStream(filePath);//输出路径(下载到指定路径)
            // 将填充之后的模板写入filePath
            template.write(out);//将template写到OutputStream中
            out.flush();
            out.close();
            template.close();
        }
        catch (Exception e){
            log.error("生成咨询个案报告失败：{}", e.getMessage());
            e.printStackTrace();
        }
    }
}

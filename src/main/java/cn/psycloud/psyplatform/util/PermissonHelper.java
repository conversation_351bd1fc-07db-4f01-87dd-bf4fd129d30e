package cn.psycloud.psyplatform.util;

import cn.psycloud.psyplatform.dto.anteroom.StructsDto;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.service.anteroom.StructsService;
import cn.psycloud.psyplatform.service.anteroom.SysFunctionService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PermissonHelper {
    @Autowired
    private SysFunctionService sysFunctionService;
    @Autowired
    private StructsService structsService;
    public  static PermissonHelper permissonHelper;

    @PostConstruct // 初始化
    public void init(){
        permissonHelper = this;
        permissonHelper.sysFunctionService = this.sysFunctionService;
        permissonHelper.structsService = this.structsService;
    }
    /**
     *  取得兄弟节点
     * @param originalList 集合
     * @param parentCode 父功能编码
     * @return 平台功能集合
     */
    public static List<SysFunctionDto> createJson(List<SysFunctionDto> originalList, String parentCode) {
        var newList = new ArrayList<SysFunctionDto>();
        var list = originalList.stream().filter(sysFunction -> parentCode.equals(sysFunction.getParentCode())).collect(Collectors.toList());
        if (list.size()> 0)
        {
            for (SysFunctionDto dto : list)
            {
                createTreeJson(originalList, dto, dto.getFunctionCode());
                newList.add(dto);
            }
        }
        return newList;
    }

    public static List<SysFunctionDto> createJson(List<SysFunctionDto> originalList)
    {
        var newList = new ArrayList<SysFunctionDto>();
        var list = originalList.stream().filter(a->"00".equals(a.getParentCode())).collect(Collectors.toList());
        if (list.size() > 0)
        {
            for(SysFunctionDto dto : list)
            {
                createTreeJson(originalList, dto, dto.getFunctionCode());
                newList.add(dto);
            }
        }
        return newList;
    }

    /**
     *  递归出子对象
     * @param originalListFunctions
     * @param t
     * @param parentCode
     */
    private static void createTreeJson(List<SysFunctionDto> originalListFunctions, SysFunctionDto t, String parentCode)
    {
        var list = originalListFunctions.stream().filter(sysFunction -> parentCode.equals(sysFunction.getParentCode())).collect(Collectors.toList());
        if (list.size() > 0)
        {
            List<SysFunctionDto> listChild = new ArrayList<SysFunctionDto>();
            for (SysFunctionDto dto : list)
            {
                createTreeJson(originalListFunctions, dto, dto.getFunctionCode());
                listChild.add(dto);
            }
            t.setChildren(listChild);
        }
    }

    /**
     *  获取当前用户的数据查看权限
     * @return 数据权限代码
     */
    public static int getDataPrivilege(UserDto user){
        int dataPrivilege = 0; //默认没有查看权限
        //根据用户名获取用户所有权限
        var listFunctions =permissonHelper.sysFunctionService
                .getGrantByRole(user.getRole().getRoleId())
                .stream()
                .filter(sysFunction->("01030104".equals(sysFunction.getFunctionCode()) || "01030105".equals(sysFunction.getFunctionCode())))
                .collect(Collectors.toList());
        for (SysFunctionDto dto: listFunctions) {
            if (Objects.equals(dto.getFunctionCode(), "01030105")) {
                dataPrivilege = 1; //允许看自己所属组织及下级
                break;
            }
            if (Objects.equals(dto.getFunctionCode(), "01030104")) {
                dataPrivilege = 2; //允许看所有
                break;
            }
        }
        return dataPrivilege;
    }

    public  static  List<Integer> getChildStructIds(Integer structId){
        List<Integer> childIds = new ArrayList<>();
        UserDto user = (UserDto) SessionUtil.getSession().getAttribute("user");
        var dataPrivilege = getDataPrivilege(user);
        if(dataPrivilege == 2){
            List<StructsDto> listChilds;
            if(structId!=null && structId !=0){
                listChilds = permissonHelper.structsService.getChild(structId);
            }
            else{
                listChilds = permissonHelper.structsService.getChild(user.getStructId());
            }
            childIds = permissonHelper.structsService.getChildIds(listChilds);
        }
        if(dataPrivilege ==1){ //允许查看负责的组织
            var listChilds = permissonHelper.structsService.getChild((structId!=null &&structId!=0)?structId :  user.getStructId());
            childIds = permissonHelper.structsService.getChildIds(listChilds);
        }
        if(dataPrivilege == 0){
            childIds.add(0);
        }
        return childIds;
    }
}

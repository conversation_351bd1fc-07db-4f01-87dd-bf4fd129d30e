package cn.psycloud.psyplatform.util;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

public class <PERSON>ieHelper {
    public static void removeCookie(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                cookie.setMaxAge(0);
                cookie.setPath("/");
                response.addCookie(cookie);
            }
        }
    }

    public static void removeCookie(HttpServletRequest request, HttpServletResponse response, String key) {
        Cookie[] cookies = request.getCookies();
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(key)) {
                    cookie.setMaxAge(0);
                    cookie.setPath("/");
                    response.addCookie(cookie);
                }
            }
        }
    }

    public static void setCookie(HttpServletResponse response, String key, String val, Integer expiresDay) {
        Cookie cookie = new Cookie(key, val.trim());
        int expires = expiresDay*24*60*60;
        cookie.setMaxAge(expires);
        cookie.setPath("/");
        response.addCookie(cookie);
    }

    //获取cookie
    public static String getCookie(HttpServletRequest request, String key) {
        Map<String, Cookie> cookieMap = ReadCookieMap(request);
        if (cookieMap.containsKey(key)) {
            Cookie cookie = (Cookie) cookieMap.get(key);
            return cookie.getValue().trim();
        } else {
            return null;
        }
    }

    //将cookie封装到Map里面
    private static Map<String, Cookie> ReadCookieMap(HttpServletRequest request) {
        Map<String, Cookie> cookieMap = new HashMap<String, Cookie>();
        Cookie[] cookies = request.getCookies();
        if (null != cookies) {
            for (Cookie cookie : cookies) {
                cookieMap.put(cookie.getName(), cookie);
            }
        }
        return cookieMap;
    }
}

package cn.psycloud.psyplatform.util;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

public class ExcelUtil {
    public static List<List<String>> createdHead(String[] headMap) {
        List<List<String>> headList = new ArrayList<List<String>>();
        for (String head : headMap) {
            List<String> list = new ArrayList<String>();
            list.add(head);
            headList.add(list);
        }
        return headList;
    }
    
    /**
     * 创建不换行的表头
     * 清理表头文本中可能导致换行的字符
     */
    public static List<List<String>> createdNoWrapHead(String[] headMap) {
        List<List<String>> headList = new ArrayList<List<String>>();
        for (String head : headMap) {
            List<String> list = new ArrayList<String>();
            // 清理可能导致换行的字符
            String cleanHead = cleanHeaderText(head);
            list.add(cleanHead);
            headList.add(list);
        }
        return headList;
    }
    
    /**
     * 清理表头文本，移除换行符和多余空格
     */
    private static String cleanHeaderText(String text) {
        if (text == null) {
            return "";
        }
        // 移除换行符、回车符、制表符
        text = text.replaceAll("[\r\n\t]", " ");
        // 移除多余的空格
        text = text.replaceAll("\\s+", " ");
        // 移除首尾空格
        text = text.trim();
        return text;
    }
}

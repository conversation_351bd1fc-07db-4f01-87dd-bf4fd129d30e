package cn.psycloud.psyplatform.util;

import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.psycloud.psyplatform.entity.platform.UserLoginLogEntity;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *  工具类
 */
@Slf4j
public class CommonHelper {
    /**
     *  生成GUID
     * @return guid字符串
     */
    public static String getGUID() {
        return UUID.randomUUID().toString();
    }
    /**
     *  获取客户端IP地址
     * @param request 请求
     * @return 客户端IP
     */
    public static String getClinetIP(HttpServletRequest request){
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            if( ip.indexOf(",")!=-1 ){
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     *  组合用户登录信息
     * @param request 请求
     * @return 登录信息实体类对象
     */
    public  static UserLoginLogEntity getUserLoginLog(HttpServletRequest request){
        var loginLogEntity = new UserLoginLogEntity();
        loginLogEntity.setLoginDate(new Date());
        loginLogEntity.setIpAddress(getClinetIP(request));
        UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        loginLogEntity.setDeviceInfo(String.format("客户端操作系统：%s，浏览器：%s",userAgent.getOs().toString(),userAgent.getBrowser().toString()));
        return loginLogEntity;
    }

    public static UserAgent getUserAgent(HttpServletRequest request){
        return UserAgentUtil.parse(request.getHeader("User-Agent"));
    }

    /**
     *  获取当前日期时间并转换为指定格式
     * @return 日期
     */
    public static String getCurrentDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(new Date());
    }

    /**
     *  把出生日期字符串转换为日期格式
     * @param dateStr 出生日期字符串
     * @return 日期格式
     * @throws ParseException 转换异常
     */
    public static Date parse(String dateStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.parse(dateStr);
    }

    /**
     *  验证字符串是否日期格式
     * @param date 日期字符串
     * @return 是否日期格式
     */
    public static boolean isDateValid(String date) {
        boolean isDate = false;
        if (date == null || "".equals(date)) return false;
        List<String> formatStrings = Arrays.asList("yyyy-MM-dd","yyyy/MM/dd");
        SimpleDateFormat sdf;
        for (String formatString : formatStrings)
        {
            try
            {
                sdf = new SimpleDateFormat(formatString);
                sdf.setLenient(false);
                sdf.parse(date);
                isDate = true;
                break;
            }
            catch (ParseException e) {
                log.error("字符串转日期发生错误："+e.getMessage());
            }

        }
        return isDate;
    }

    /**
     *  计算年龄
     * @param birth 出生日期字符串
     * @return 年龄
     * @throws Exception 异常
     */
    public static int getAge(String birth) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date birthDay = sdf.parse(birth);
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthDay)) { //出生日期晚于当前时间，无法计算
            return 0;
        }
        int yearNow = cal.get(Calendar.YEAR); //当前年份
        int monthNow = cal.get(Calendar.MONTH); //当前月份
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH); //当前日期
        cal.setTime(birthDay);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        int age = yearNow - yearBirth; //计算整岁数
        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                //当前日期在生日之前，年龄减一
                if (dayOfMonthNow < dayOfMonthBirth) age--;
            }else{
                //当前月份在生日之前，年龄减一
                age--;
            }
        }
        return age;
    }

    /**
     *  获取resources文件夹下的文件绝对路径
     * @param filePath 文件
     * @return 绝对路径
     */
    public static String getResourcesFilePath(String filePath) {
        String abFilePath ="";
        try{
            ClassPathResource classPathResource = new ClassPathResource(filePath);
            // 获取文件
            File file = classPathResource.getFile();
            // 获取文件路径
            abFilePath = file.getPath();
        }
        catch (Exception e){
            e.printStackTrace();
        }
        return abFilePath;
    }
}

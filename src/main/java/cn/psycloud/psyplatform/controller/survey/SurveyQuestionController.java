package cn.psycloud.psyplatform.controller.survey;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.ImportScaleQDto;
import cn.psycloud.psyplatform.dto.survey.ImportSurveyQDto;
import cn.psycloud.psyplatform.dto.survey.SurveyQuestionDto;
import cn.psycloud.psyplatform.entity.survey.SurveyItemEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyQuestionEntity;
import cn.psycloud.psyplatform.service.survey.SurveyItemService;
import cn.psycloud.psyplatform.service.survey.SurveyQuestionService;
import com.alibaba.excel.EasyExcel;
import lombok.SneakyThrows;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@RequestMapping("/survey/surveyquestion")
public class SurveyQuestionController {
    @Autowired
    private SurveyQuestionService surveyQuestionService;
    @Autowired
    private SurveyItemService surveyItemService;

    /**
     *  查询题目集合：分页
     * @param dto 题目实体对象
     * @return BootstrapDatatables格式的对象
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByPaged(@RequestBody SurveyQuestionDto dto){
        return JSONUtil.toJsonStr(surveyQuestionService.getListBySurveyId(dto));
    }

    /**
     *  获取问卷的题目集合
     * @param surveyId 问卷id
     * @return 集合
     */
    @RequestMapping(value="get_list_by_surveyId")
    @ResponseBody
    public Object getListBySurveyId(@RequestParam Integer surveyId){
        return JSONUtil.toJsonStr(surveyQuestionService.getListBySurveyId(surveyId));
    }

    /**
     *  添加题目
     * @param entity 题目实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method= RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody SurveyQuestionEntity entity){
        var result = new JsonResult<SurveyQuestionEntity>();
        var questionId = surveyQuestionService.add(entity);
        if(questionId > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
            var question = new SurveyQuestionEntity();
            question.setId(questionId);
            result.setData(question);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改题目
     * @param entity 题目实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method= RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody SurveyQuestionEntity entity){
        var result = new JsonResult<>();
        if(surveyQuestionService.update(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除题目
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(surveyQuestionService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  查询题目的选项集合
     * @param qId 题目id
     * @return 选项集合
     */
    @RequestMapping("/get_all_items")
    @ResponseBody
    public Object getAllItems(@RequestParam Integer qId) {
        return JSONUtil.toJsonStr(surveyItemService.getItemsByQId(qId));
    }

    /**
     *  设置选项（单个题目）
     * @param entity 选项实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_item",method=RequestMethod.POST)
    @ResponseBody
    public Object AddItem(@RequestBody SurveyItemEntity entity){
        var result = new JsonResult<>();
        if(surveyItemService.add(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除选项
     * @param itemId 选项id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete_item",method=RequestMethod.POST)
    @ResponseBody
    public Object deleteItem(@RequestParam Integer itemId) {
        var result = new JsonResult<>();
        if(surveyItemService.delete(itemId) > 0) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  Excel导入题目和选项
     * @param file excel文件
     * @param request 请求
     * @return 结果实体对象
     */
    @SneakyThrows
    @RequestMapping(value = "/import_question",method = RequestMethod.POST)
    @ResponseBody
    public Object importQuestion(@RequestParam("file") MultipartFile file, @RequestParam Integer surveyId , HttpServletRequest request){
        var result = new JsonResult<>();
        List<ImportSurveyQDto> list = EasyExcel
                .read(file.getInputStream())
                .head(ImportSurveyQDto.class)
                .sheet()
                .doReadSync();
        surveyQuestionService.importQuestion(list, surveyId);
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        return result;
    }
}

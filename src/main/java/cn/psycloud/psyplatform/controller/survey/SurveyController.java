package cn.psycloud.psyplatform.controller.survey;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import cn.psycloud.psyplatform.dto.survey.SurveyNameDto;
import cn.psycloud.psyplatform.entity.survey.SurveyEntity;
import cn.psycloud.psyplatform.service.survey.SurveyService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  调查问卷
 */
@Controller
@RequestMapping("/survey/survey")
public class SurveyController {
    @Autowired
    private SurveyService surveyService;

    /**
     *  调查问卷管理页面
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/list")
    public String list(HttpServletRequest request) {
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("011001".equals(a.getFunctionCode()) || "011001".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01100101".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("01100102".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("01100103".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "survey/list";
    }

    /**
     *  创建问卷第一步
     * @return 视图
     */
    @GetMapping("/add_survey_st1")
    public String addSurveySt1() { return "survey/add_survey_st1";}

    /**
     * 创建问卷第二步
     * @return 视图
     */
    @GetMapping("/add_survey_st2")
    public String addSurveySt2() { return "survey/add_survey_st2";}

    /**
     *  查询调查问卷集合：分页
     * @param dto 调查问卷实体对象
     * @return BootstrapDatatables格式的对象
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByPaged(@RequestBody SurveyDto dto){
        return surveyService.getListByPaged(dto);
    }

    /**
     *  获取调查问卷列表：select
     * @return 集合
     */
    @RequestMapping(value = "/get_for_select",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForSelect() {
        return surveyService.getListForSelect();
    }

    /**
     *  获取调查问卷列表：DualList
     * @return 集合
     */
    @RequestMapping(value = "/get_list_for_dual",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForDual() {
        return surveyService.getListForDualList();
    }

    @RequestMapping(value = "/get_for_select_by_taskid",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByTaskIdForSelect(@RequestParam Integer taskId){
        return surveyService.getListByTaskId(taskId);
    }

    @RequestMapping(value = "/get_for_select_by_measuring_taskid",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByMeasuringTaskIdForSelect(@RequestParam Integer taskId){
        return surveyService.getListByMeasuringTaskId(taskId);
    }


    /**
     *  新增调查问卷
     * @param entity 调查问卷实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method=RequestMethod.POST)
    @ResponseBody
    public  Object insert(@RequestBody SurveyEntity entity){
        var result = new JsonResult<>();
        var surveyId = surveyService.addSurvey(entity);
        if(surveyId > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(String.valueOf(surveyId));
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改调查问卷
     * @param entity 调查问卷实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public  Object update(@RequestBody SurveyEntity entity){
        var result = new JsonResult<>();
        if(surveyService.updateSurvey(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除（单个）
     * @param id 调查问卷分类id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public Object delete(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(surveyService.delete(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(surveyService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     验证问卷名称是否存在
     */
    @RequestMapping(value="/verify_name",method=RequestMethod.POST)
    @ResponseBody
    public Object verifyName(@RequestBody SurveyNameDto dto) {
        return surveyService.isSurveyNameExists(dto);
    }

    /**
     *  修改问卷状态
     * @param surveyId 问卷id
     * @param state 状态
     * @return 结果实体对象
     */
    @RequestMapping(value = "/done",method=RequestMethod.POST)
    @ResponseBody
    public  Object updateDone(@RequestParam Integer surveyId, @RequestParam Integer state){
        var result = new JsonResult<>();
        if(surveyService.updateDone(surveyId, state) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

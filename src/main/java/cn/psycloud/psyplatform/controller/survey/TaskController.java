package cn.psycloud.psyplatform.controller.survey;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyEntity;
import cn.psycloud.psyplatform.service.measuringroom.TaskService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 问卷调查任务管理
 */
@Controller("surveytask")
@RequestMapping("/survey/task")
public class TaskController {
    @Autowired
    private TaskService taskService;
    /**
     *  选择调查问卷任务类型
     * @return 视图
     */
    @GetMapping("/choose_type")
    public String chooseType() {
        return "survey/task/chooseType";
    }

    /**
     *  添加限定调查对象的问卷任务
     * @return 视图
     */
    @GetMapping("/add_limited_task")
    public String addLimitedTask() {
        return "survey/task/addLimitedTask";
    }

    /**
     *  添加非限定调查对象的问卷任务
     * @return 视图
     */
    @GetMapping("/add_unlimited_task")
    public String addUnLimitedTask() {
        return "survey/task/addUnLimitedTask";
    }

    /**
     *  问卷调查任务管理页面
     * @param request 请求
     * @return 视图
     */
    @RequestMapping ("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01100301".equals(a.getFunctionCode()) || "01100302".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01100301".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("0110030201".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("0110030202".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "survey/task/list";
    }

    /**
     *  修改问卷调查任务
     * @return 视图
     */
    @GetMapping("/update")
    public String updateTask(){
        return "survey/task/updateTask";
    }

    /**
     *  判断任务中的问卷是否完成
     * @param taskId 任务Id
     * @param surveyId 问卷Id
     * @return 是否完成
     */
    @RequestMapping("/is_survey_done")
    @ResponseBody
    public Object isSurveyDone(@RequestParam Integer taskId, @RequestParam Integer surveyId, HttpServletRequest request) {
        var userDto = (UserDto)request.getSession().getAttribute("user");
        return taskService.isSurveyOfTaskDone(userDto.getUserId(),taskId, surveyId);
    }

    /**
     * 统计问卷结果
     * @return 视图
     */
    @GetMapping("/stat")
    public String surveyStat(){
        return "survey/task/stat";
    }

    @RequestMapping("/get_survey_stat")
    @ResponseBody
    public Object getTaskSurveyStat(@RequestBody TaskSurveyEntity entity){
        return taskService.getTaskSurveyStat(entity);
    }

    /**
     *  调查问卷的结果统计报告生成
     * @param entity 条件
     * @return
     */
    @RequestMapping(value = "/create_report_word",method= RequestMethod.POST)
    @ResponseBody
    public Object create(HttpServletResponse response, HttpServletRequest request, @RequestBody TaskSurveyEntity entity){
        var result = new JsonResult<>();
        var fileName = taskService.createSurveyStatReport(response,request,entity);
        if(!"".equals(fileName)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(fileName);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
        }
        return result;
    }
}

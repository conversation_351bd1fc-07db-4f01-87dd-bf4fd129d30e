package cn.psycloud.psyplatform.controller.counselingroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto;
import cn.psycloud.psyplatform.entity.counselingroom.SchedulingEntity;
import cn.psycloud.psyplatform.service.counselingroom.SchedulingService;
import lombok.SneakyThrows;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  咨询师排班
 */
@RequestMapping("/counselingroom/scheduling")
@Controller
public class SchedulingController {
    @Autowired
    private SchedulingService schedulingService;

    @RequestMapping("/arrange")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010503".equals(a.getFunctionCode()) || "010503".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01050301".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("01050302".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("01050303".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "counselingroom/scheduling/arrange";
    }

    /**
     *  创建排班
     * @param entity 排班实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/arrange",method=RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody SchedulingEntity entity){
        var result = new JsonResult<>();
        if(schedulingService.arrange(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  更新排班
     * @param entity 排班实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody SchedulingEntity entity){
        var result = new JsonResult<>();
        if(schedulingService.updateScheduling(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除排班
     * @param id 排班id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/del",method=RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(schedulingService.delete(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  验证咨询师的排班是否重复
     * @return 是否重复
     */
    @SneakyThrows
    @GetMapping ( "/check_scheduling")
    @ResponseBody
    public Object isSchedulingExists(@RequestParam Integer counselorId, @RequestParam String startTime, @RequestParam String endTime){
        var dto = new SchedulingDto();
        dto.setCounselorId(counselorId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = sdf.parse(startTime);
        Date end = sdf.parse(endTime);
        dto.setStartTime(start);
        dto.setEndTime(end);
        return schedulingService.isSchedulingExists(dto);
    }

    /**
     *  获取排班信息集合
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/get_for_calendar", method = RequestMethod.POST)
    @ResponseBody
    public Object getListForCalendar(@RequestBody SchedulingDto dto){
        return schedulingService.getList(dto);
    }

    /**
     *  获取预约的排班信息集合
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/get_for_order_calendar", method = RequestMethod.POST)
    @ResponseBody
    public Object getListForOrderCalendar(@RequestBody SchedulingDto dto){
        return schedulingService.getOrderSchedulingList(dto);
    }
}

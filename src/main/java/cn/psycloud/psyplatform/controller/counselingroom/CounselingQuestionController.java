package cn.psycloud.psyplatform.controller.counselingroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionEntity;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionRespondEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingQuestionRespondService;
import cn.psycloud.psyplatform.service.counselingroom.CounselingQuestionService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  在线答疑
 */
@RequestMapping("/counselingroom/counselingquestion")
@Controller
public class CounselingQuestionController {
    @Autowired
    private CounselingQuestionService counselingQuestionService;
    @Autowired
    private CounselingQuestionRespondService counselingQuestionRespondService;

    /**
     * 心理答疑首页
     * @return 视图
     */
    @GetMapping("/index")
    public String index() {
        return "counselingroom/counselingQuestion/index";
    }

    /**
     * 提问
     * @return 视图
     */
    @GetMapping("/post")
    public String addQuestion() {
        return "counselingroom/counselingQuestion/addQuestion";
    }

    /**
     * 修改提问
     * @return 视图
     */
    @GetMapping("/update")
    public String update() {
        return "counselingroom/counselingQuestion/update";
    }

    /**
     * 提问详情
     * @param request 请求
     * @param id      问题id
     * @return 视图
     */
    @GetMapping("/detail")
    public String detail(HttpServletRequest request, @RequestParam Integer id) {
        var listFunctions = (List<SysFunctionDto>) request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a -> ("01050902".equals(a.getFunctionCode())))
                .collect(Collectors.toList());
        boolean canDelRespond = false;
        for (SysFunctionDto privilege : privilegeList) {
            //删除回复权限
            if ("01050902".equals(privilege.getFunctionCode())) {
                canDelRespond = true;
            }
        }
        var questionDto = counselingQuestionService.getById(id);
        request.setAttribute("canDelRespond", canDelRespond);
        request.setAttribute("question", questionDto);
        return "counselingroom/counselingQuestion/detail";
    }

    /**
     * 心理答疑管理
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/list")
    public String list(HttpServletRequest request) {
        var listFunctions = (List<SysFunctionDto>) request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a -> ("010509".equals(a.getFunctionCode()) || "010509".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canDelete = false;
        for (SysFunctionDto privilege : privilegeList) {
            //删除权限
            if ("01050901".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canDelete", canDelete);
        return "counselingroom/counselingQuestion/list";
    }

    /**
     * 我的提问
     * @return 视图
     */
    @GetMapping("/my_questions")
    public String myQuestions() {
        return "counselingroom/counselingQuestion/myQuestions";
    }

    /**
     * 我的回答
     * @return 视图
     */
    @GetMapping("/my_responds")
    public String myResponds() {
        return "counselingroom/counselingQuestion/myResponds";
    }

    /**
     * 提问
     * @param entity 心理答疑实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/post", method = RequestMethod.POST)
    @ResponseBody
    public Object addQuestion(@RequestBody CounselingQuestionEntity entity) {
        var result = new JsonResult<>();
        var questionId = counselingQuestionService.post(entity);
        if (questionId > 0) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(String.valueOf(questionId));
        } else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 修改
     * @param entity 心理答疑实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody CounselingQuestionEntity entity) {
        var result = new JsonResult<>();
        if (counselingQuestionService.update(entity) > 0) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        } else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 删除(单个)
     * @param id 提问Id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object delete(@RequestParam Integer id) {
        var result = new JsonResult<>();
        if (counselingQuestionService.delete(id) > 0) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        } else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 批量删除
     * @param ids 提问id集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_del", method = RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids) {
        var result = new JsonResult<>();
        if (counselingQuestionService.batchDel(ids)) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        } else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  获取心理答疑列表：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/list",method=RequestMethod.POST)
    @ResponseBody
    public Object list(@RequestBody CounselingQuestionDto dto){
        return counselingQuestionService.getListByPaged(dto);
    }

    /**
     *  提问详情
     * @param questionId 提问Id
     * @return 结果实体对象
     */
    @GetMapping("/get")
    @ResponseBody
    public Object get(@RequestParam Integer questionId){
        return counselingQuestionService.getById(questionId);
    }

    /**
     *  发表回复
     * @param entity 回复实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_respond",method=RequestMethod.POST)
    @ResponseBody
    public Object addRespond(@RequestBody CounselingQuestionRespondEntity entity){
        var result = new JsonResult<>();
        if(counselingQuestionRespondService.post(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除回复
     * @param id 回复id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/del_respond",method=RequestMethod.POST)
    @ResponseBody
    public Object delRespond(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(counselingQuestionRespondService.delete(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

package cn.psycloud.psyplatform.controller.counselingroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingItemDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingItemEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingItemService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  咨询师的咨询类型管理
 */
@RequestMapping("/counselingroom/counselingitem")
@Controller
public class CounselingItemController {
    @Autowired
    private CounselingItemService counselingItemService;

    @RequestMapping("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010502".equals(a.getFunctionCode()) || "010502".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01050201".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("01050202".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("01050203".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "counselingroom/counselingItem/list";
    }

    /**
     *  查询咨询师咨询类型集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value="/list",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByPaged(@RequestBody CounselingItemDto dto){
        return counselingItemService.getListByPaged(dto);
    }

    /**
     *  查询咨询师咨询类型集合：select2
     * @return  咨询师咨询类型集合
     */
    @RequestMapping(value = "/get_for_select_by_counselor",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForSelectByCounselor(@RequestParam Integer counselorId){
        return counselingItemService.getListForSelectByCounselor(counselorId);
    }

    @RequestMapping(value = "/get_for_select",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForSelect(@RequestParam Integer schedulingId){
        return counselingItemService.getListForSelect(schedulingId);
    }

    /**
     *  新增咨询师咨询类型
     * @param entity 咨询师咨询类型实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method=RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody CounselingItemEntity entity){
        var result = new JsonResult<>();
        if(counselingItemService.addCounselingItem(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改咨询师咨询类型
     * @param entity 咨询师咨询类型实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody CounselingItemEntity entity){
        var result = new JsonResult<>();
        if(counselingItemService.updateCounselingItem(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除咨询师咨询类型
     * @param id 咨询师咨询类型id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(counselingItemService.deleteById(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public  Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(counselingItemService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

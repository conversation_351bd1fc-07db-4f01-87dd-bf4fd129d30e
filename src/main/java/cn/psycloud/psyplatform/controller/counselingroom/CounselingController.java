package cn.psycloud.psyplatform.controller.counselingroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingOrderEntity;
import cn.psycloud.psyplatform.service.anteroom.UserService;
import cn.psycloud.psyplatform.service.counselingroom.CounselingOrderService;
import cn.psycloud.psyplatform.service.counselingroom.CounselingRecordService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  预约咨询
 */
@RequestMapping("/counselingroom/counseling")
@Controller
public class CounselingController {
    @Autowired
    private CounselingOrderService counselingOrderService;

    @Autowired
    private CounselingRecordService counselingRecordService;

    @Autowired
    private UserService userService;
    /**
     *  预约咨询
     * @return 视图
     */
    @GetMapping("/order")
    public String order(){
        return "counselingroom/counseling/order";
    }

    /**
     *  我的预约
     * @return 视图
     */
    @GetMapping("/myorder")
    public String myOrder(){
        return "counselingroom/counseling/myOrder";
    };

    /**
     *  预约记录
     * @ 视图
     */
    @GetMapping("/order_record")
    public String orderRecord(){
        return "counselingroom/counseling/orderRecord";
    }

    /**
     *  添加预约
     * @param entity 预约信息实体对象
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_order",method= RequestMethod.POST)
    @ResponseBody
    public Object addOrder(@RequestBody CounselingOrderEntity entity, HttpServletRequest request){
        var result = new JsonResult<>();
        var user = (UserDto) request.getSession().getAttribute("user");
        var roleId = user.getRole().getRoleId();
        entity.setState(roleId==3 || roleId ==4 ? 0: 1);
        entity.setOperator(user.getUserId());
        entity.setAddDate(new Date());
        if(counselingOrderService.addOrder(request,entity) > 0){
            String retMsg = (roleId==3 || roleId ==4)? ResultCodeAndMsg.CounselingOrderVisitorAddSuccess : ResultCodeAndMsg.CounselingOrderCounselorAddSuccess;
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(retMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  更新预约
     * @param entity 预约信息实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update_order",method=RequestMethod.POST)
    @ResponseBody
    public Object updateOrder(@RequestBody CounselingOrderEntity entity){
        var result = new JsonResult<>();
        if(counselingOrderService.updateOrder(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  更新预约状态
     * @param id 预约id
     * @param state 预约状态
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update_state",method=RequestMethod.POST)
    @ResponseBody
    public Object updateState(@RequestParam Integer id, @RequestParam Integer state){
        var result = new JsonResult<>();
        if(counselingOrderService.updateState(id, state) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  验证预约来访者信息
     * @param loginName 来访者用户名
     * @param schedulingId 排班id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/check_visitor",method=RequestMethod.POST)
    @ResponseBody
    public Object checkVisitor(@RequestParam String loginName, @RequestParam Integer schedulingId) {
        var result = new JsonResult<>();
        var userId = userService.checkUserInfo(loginName);
        if(userId ==null){
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.CheckVisitorExistFailed);
        }
        else {
            if(schedulingId == 0){
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(userId);
            }
            else{
                var order = counselingOrderService.getBySchedulingId(schedulingId);
                if (order != null && (order.getId() != 0 && (order.getState() == 2 || order.getState() == 3))) {
                    result.setResultCode(ResultCodeAndMsg.FailureCode);
                    result.setResultMsg(ResultCodeAndMsg.CounselingOrderExistFailed);
                }
                else {
                    result.setResultCode(ResultCodeAndMsg.SuccessCode);
                    result.setResultMsg(userId);
                }
            }
        }
        return result;
    }

    /**
     *  分页查询预约咨询记录
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/order_record",method= RequestMethod.POST)
    @ResponseBody
    public Object orderRecord(@RequestBody CounselingOrderDto dto) {
        return counselingOrderService.getListByPaged(dto);
    }

    /**
     *  根据id查询预约信息
     * @param id 预约id
     * @return 预约信息实体对象
     */
    @GetMapping( "/get_order")
    @ResponseBody
    public Object getOrder(@RequestParam Integer id){
        return counselingOrderService.getById(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public  Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(counselingOrderService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  在线咨询首页
     * @return 视图
     */
    @GetMapping("/online")
    public String online(){
        return "counselingroom/counseling/online";
    }

    /**
     * 在线咨询：咨询对话
     * @return 视图
     */
    @GetMapping("/online_counseling")
    public String onlineCounseling(){
        return "counselingroom/counseling/onlineCounseling";
    }

    /**
     *  在线咨询页面获取预约列表
     * @param dto 条件
     * @return 集合
     */
    @RequestMapping(value = "/online_order",method=RequestMethod.POST)
    @ResponseBody
    public Object onlineOrder(@RequestBody CounselingOrderDto dto){
        return counselingOrderService.getOrderUnDoneList(dto);
    }

    /**
     *  咨询记录
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/counseling_record")
    public String counselingRecord(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions
                .stream()
                .filter(a->"01050801".equals(a.getFunctionCode()))
                .collect(Collectors.toList());
        boolean canAdd = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01050801".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        return "counselingroom/counseling/counselingRecord";
    }

    /**
     *  获取咨询记录集合：分页
     * @param dto 条件
     * @return 集合
     */
    @RequestMapping(value = "/counseling_record",method=RequestMethod.POST)
    @ResponseBody
    public Object counselingRecord(@RequestBody CounselingOrderDto dto){
        return counselingOrderService.getOrderDoneList(dto);
    }

    /**
     *  根据预约id查询咨询记录
     * @param orderId 预约id
     * @return 集合
     */
    @GetMapping("/getCounselingRecordContent")
    @ResponseBody
    public Object getCounselingRecordContent(@RequestParam Integer orderId){
        return counselingRecordService.getList(orderId);
    }

    /**
     *  手动添加咨询记录
     * @param dto 咨询预约实体类
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_counseling_record",method=RequestMethod.POST)
    @ResponseBody
    public Object addCounselingRecord(@RequestBody CounselingOrderDto dto){
        var result = new JsonResult<>();
        if(counselingRecordService.addCounselingRecord(dto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

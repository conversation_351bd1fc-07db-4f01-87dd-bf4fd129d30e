package cn.psycloud.psyplatform.controller.counselingroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingTypeDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingTypeEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingTypeService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  咨询问题类型
 */
@RequestMapping("/counselingroom/counselingtype")
@Controller
public class CounselingTypeController {
    @Autowired
    private CounselingTypeService counselingTypeService;

    @RequestMapping("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010501".equals(a.getFunctionCode()) || "010501".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01050101".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("01050102".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("01050103".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "counselingroom/counselingType/list";
    }

    /**
     *  查询咨询问题分类集合：分页
     * @param dto 咨询问题分类实体对象
     * @return BootstrapDatatables格式的对象
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByPaged(@RequestBody CounselingTypeDto dto){
        return counselingTypeService.getListByPaged(dto);
    }

    /**
     *  查询咨询问题分类集合：select2
     * @return  咨询问题分类集合
     */
    @RequestMapping(value = "/get_for_select",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForSelect(){
        return counselingTypeService.getListForSelect(new CounselingTypeDto());
    }

    /**
     *  新增咨询问题分类
     * @param entity 咨询问题分类实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method=RequestMethod.POST)
    @ResponseBody
    public  Object insert(@RequestBody CounselingTypeEntity entity){
        var result = new JsonResult<>();
        if(counselingTypeService.add(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改咨询问题分类
     * @param entity 咨询问题分类实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public  Object update(@RequestBody CounselingTypeEntity entity){
        var result = new JsonResult<>();
        if(counselingTypeService.update(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除（单个)
     * @param id 咨询问题分类id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public  Object delete(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(counselingTypeService.delete(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public  Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(counselingTypeService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

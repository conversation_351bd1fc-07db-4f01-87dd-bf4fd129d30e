package cn.psycloud.psyplatform.controller.measuringroom;

import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleFactorExplainService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 *  因子结果解释管理
 */
@Controller
@RequestMapping("/measuringroom/factorExplain")
public class ScaleFactorExplainController {
    @Autowired
    private ScaleFactorExplainService scaleFactorExplainService;

    /**
     *  查询结果解释集合
     * @param factorId 因子id
     * @return 结果解释集合
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object list(@RequestParam Integer factorId) {
        return scaleFactorExplainService.getListById(factorId);
    }

    /**
     *  删除因子解释
     * @param id 因子解释id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method= RequestMethod.POST)
    @ResponseBody
    public Object delete(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(scaleFactorExplainService.deleteById(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  添加因子解释
     * @param entity 因子解释实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method=RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody ScaleFactorExplainEntity entity){
        var result = new JsonResult<>();
        if(scaleFactorExplainService.addExplain(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改因子解释
     * @param entity 因子解释实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody ScaleFactorExplainEntity entity){
        var result = new JsonResult<>();
        if(scaleFactorExplainService.updateExplain(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

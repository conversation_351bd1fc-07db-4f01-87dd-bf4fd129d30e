package cn.psycloud.psyplatform.controller.measuringroom;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto;
import cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto;
import cn.psycloud.psyplatform.service.measuringroom.GroupReportService;
import cn.psycloud.psyplatform.serviceImpl.measuringroom.stat.TestStat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/measuringroom/stat")
public class StatController {
    @Autowired
    private TestStat testStat;
    
    @Autowired
    private GroupReportService groupReportService;
    /**
     *  测评统计
     * @return 视图
     */
    @GetMapping("/task_stat")
    public String taskStat(){
        return "measuringroom/stat/taskStat";
    }

    /**
     *  测评任务统计
     * @param dto 条件
     * @return 测评统计实体对象
     */
    @RequestMapping(value = "/task_stat",method = RequestMethod.POST)
    @ResponseBody
    public Object taskStat(@RequestBody TestRecordDto dto){
        return JSONUtil.toJsonStr(testStat.get(dto));
    }

    /**
     * 团体测评报告页面
     * @return 视图
     */
    @GetMapping("/index")
    public String index(){
        return "measuringroom/stat/index";
    }

    /**
     * 生成团体测评报告
     * @param request 查询条件
     * @return 团体测评报告数据
     */
    @RequestMapping(value = "/group_report", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<GroupReportResponseDto> generateGroupReport(@RequestBody GroupReportRequestDto request){
        try {
            GroupReportResponseDto report = groupReportService.generateGroupReport(request);
            return new JsonResult<>(report);
        } catch (Exception e) {
            JsonResult<GroupReportResponseDto> result = new JsonResult<>();
            result.setResultCode(-1);
            result.setResultMsg("生成团体报告失败：" + e.getMessage());
            return result;
        }
    }

}

package cn.psycloud.psyplatform.controller.measuringroom;

import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.service.measuringroom.ScaleChartsService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/measuringroom/scalecharts")
@Controller
public class ScaleChartsController {
    @Autowired
    private ScaleChartsService scaleChartsService;

    /**
     *  保存量表的测评报告图表类型
     * @param scaleId 量表Id
     * @param factorType 因子类型
     * @param charts 图表类型集合
     * @return 结果实体对象
     */
    @RequestMapping(value ="/save", method= RequestMethod.POST)
    @ResponseBody
    public Object saveScaleCharts(@RequestParam Integer scaleId, @RequestParam Integer factorType, @RequestParam String charts){
        var result = new JsonResult<>();
        if(scaleChartsService.save(scaleId, factorType, charts)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

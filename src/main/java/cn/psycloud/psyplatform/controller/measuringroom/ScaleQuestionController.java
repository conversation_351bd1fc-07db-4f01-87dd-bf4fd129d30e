package cn.psycloud.psyplatform.controller.measuringroom;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.ImportScaleQDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleQuestionDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleAService;
import cn.psycloud.psyplatform.service.measuringroom.ScaleQService;
import com.alibaba.excel.EasyExcel;
import lombok.SneakyThrows;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 *  量表题目管理
 */
@Controller
@RequestMapping("/measuringroom/scaleQuestion")
public class ScaleQuestionController {
    @Autowired
    private ScaleQService scaleQService;
    @Autowired
    private ScaleAService scaleAService;

    /**
     *  题目集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value="/list")
    @ResponseBody
    public Object getListByScaleId(@RequestBody ScaleQuestionDto dto) {
        return JSONUtil.toJsonStr(scaleQService.getListByScaleId(dto));
    }

    /**
     *  获取条目列表返回 duallist格式
     * @param scaleId 量表id
     * @return 题目集合
     */
    @RequestMapping("/get_list_for_dual")
    @ResponseBody
    public Object getQListForDualList(@RequestParam Integer scaleId) {
        return scaleQService.getQListForDualList(scaleId);
    }

    /**
     *  查询条目的答案集合
     * @param qId 题目id
     * @return 答案集合
     */
    @RequestMapping("/get_all_answers")
    @ResponseBody
    public Object getAllAnswers(@RequestParam Integer qId) {
        return JSONUtil.toJsonStr(scaleAService.getAnswersByQId(qId));
    }

    /**
     *  添加条目
     * @param entity 题目实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method= RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody ScaleQuestionEntity entity){
        var result = new JsonResult<ScaleQuestionEntity>();
        var questionId = scaleQService.addQuestion(entity);
        if(questionId > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
            var question = new ScaleQuestionEntity();
            question.setId(questionId);
            result.setData(question);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改题目
     * @param entity 题目实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method= RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody ScaleQuestionEntity entity){
        var result = new JsonResult<>();
        if(scaleQService.updateQuestion(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除题目（单个)
     * @param id 题目id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public Object delete(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(scaleQService.delete(id)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除题目
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(scaleQService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  设置答案（单个题目）
     * @param entity 答案实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_answer",method=RequestMethod.POST)
    @ResponseBody
    public Object AddAnswer(@RequestBody ScaleAnswerEntity entity){
        var result = new JsonResult<>();
        if(scaleAService.addAnswer(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  设置答案（批量）
     * @param entity 实体对象
     * @param qIds 题目id集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_add_answer",method=RequestMethod.POST)
    @ResponseBody
    public Object batchAddAnswer(@RequestBody ScaleAnswerEntity entity, @RequestParam String qIds){
        var result = new JsonResult<>();
        if(scaleAService.batchAddAnswer(entity,qIds)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除答案
     * @param aId 答案id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete_answer",method=RequestMethod.POST)
    @ResponseBody
    public Object deleteAnswer(@RequestParam Long aId) {
        var result = new JsonResult<>();
        if(scaleAService.delete(aId) > 0) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  Excel导入题目和答案
     * @param file excel文件
     * @param request 请求
     * @return 结果实体对象
     */
    @SneakyThrows
    @RequestMapping(value = "/import_question",method = RequestMethod.POST)
    @ResponseBody
    public Object importQuestion(@RequestParam("file") MultipartFile file, @RequestParam Integer scaleId , HttpServletRequest request){
        var result = new JsonResult<>();
        List<ImportScaleQDto> list = EasyExcel
                .read(file.getInputStream())
                .head(ImportScaleQDto.class)
                .sheet()
                .doReadSync();
        scaleQService.importQuestion(list, scaleId);
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        return result;
    }

    /**
     *  题目排序
     * @param qId 题目id
     * @param flag 排序标识
     * @return 结果实体对象
     */
    @RequestMapping(value = "/sort",method=RequestMethod.POST)
    @ResponseBody
    public Object sort(@RequestParam Integer qId, @RequestParam String flag){
        var result = new JsonResult<>();
        if(scaleQService.sort(qId,flag)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

package cn.psycloud.psyplatform.controller.measuringroom;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleNameDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleService;
import cn.psycloud.psyplatform.util.CommonHelper;
import lombok.var;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  量表管理
 */
@Controller
@RequestMapping("/measuringroom/scale")
public class ScaleController {
    @Autowired
    private ScaleService scaleService;

    /**
     *  量表管理页面
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/list")
    public String list(HttpServletRequest request) {
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01040103".equals(a.getFunctionCode()) || "01040103".equals(a.getParentCode())|| "01040102".equals(a.getFunctionCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false,  canDoTest = false, canExport = false, canSetOrder = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01040102".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("0104010301".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("0104010302".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //快速测试
            if ("0104010303".equals(privilege.getFunctionCode())) {
                canDoTest = true;
            }
            //导出量表
            if ("0104010305".equals(privilege.getFunctionCode())) {
                canExport = true;
            }
            //设置排序
            if ("0104010301".equals(privilege.getFunctionCode())) {
                canSetOrder = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canDoTest",canDoTest);
        request.setAttribute("canExport",canExport);
        request.setAttribute("canSetOrder",canSetOrder);
        return "measuringroom/scale/list";
    }

    /**
     *  查询量表集合：分页
     * @param dto 量表实体对象
     * @return BootstrapDatatables格式的对象
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByPaged(@RequestBody ScaleDto dto){
        return JSONUtil.toJsonStr(scaleService.getListByPaged(dto));
    }

    /**
     *  获取量表列表：DualList
     * @return 集合
     */
    @RequestMapping(value = "/get_list_for_dual",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForDual() {
        return scaleService.getListForDualList();
    }

    /**
     *  获取量表列表：select
     * @return 集合
     */
    @RequestMapping(value = "/get_for_select",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForSelect() {
        return scaleService.getListForSelect();
    }

    /**
     *  根据任务id获取量表列表：select
     * @param taskId 任务id
     * @return 集合
     */
    @RequestMapping(value = "/get_for_select_by_taskid",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByTaskIdForSelect(@RequestParam Integer taskId) {
        return scaleService.getListByTaskId(taskId);
    }

    /**
     *  量表维护页面
     * @return 视图
     */
    @GetMapping("/get_undone_scale")
    public String add(HttpServletRequest request) {
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01040103".equals(a.getFunctionCode()) || "01040103".equals(a.getParentCode())|| "01040102".equals(a.getFunctionCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01040102".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("0104010301".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("0104010302".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "measuringroom/scale/add";
    }

    /**
     *  获取推荐量表列表
     * @return 集合
     */
    @RequestMapping(value = "/get_is_recommend",method= RequestMethod.POST)
    @ResponseBody
    public Object getIsRecommendList(@RequestBody ScaleDto dto) {
        return scaleService.getRecommendList(dto);
    }

    /**
     *  查询具体分类下的量表集合
     * @param scaleTypeId 量表分类id
     * @return 集合
     */
    @RequestMapping(value = "/get_list_by_type",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByType(@RequestParam Integer scaleTypeId) {
        return scaleService.getListByType(scaleTypeId);
    }

    /**
     *  新增量表
     * @param entity 量表实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_scale",method=RequestMethod.POST)
    @ResponseBody
    public  Object insert(@RequestBody ScaleEntity entity){
        var result = new JsonResult<>();
        if(scaleService.addScale(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改量表
     * @param entity 量表实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update_scale",method=RequestMethod.POST)
    @ResponseBody
    public  Object update(@RequestBody ScaleEntity entity){
        var result = new JsonResult<>();
        if(scaleService.updateScale(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除（单个）
     * @param id 量表分类id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public Object delete(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(scaleService.delete(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(scaleService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
       验证量表名称是否存在
     */
    @RequestMapping(value="/verify_scale_name",method=RequestMethod.POST)
    @ResponseBody
    public Object verifyScaleName(@RequestBody ScaleNameDto dto) {
        return scaleService.isScaleNameExists(dto);
    }

    /**
     *  修改量表
     * @param scaleId 量表id
     * @param state 状态
     * @return 结果实体对象
     */
    @RequestMapping(value = "/done",method=RequestMethod.POST)
    @ResponseBody
    public  Object updateScaleDone(@RequestParam Integer scaleId, @RequestParam Integer state){
        var result = new JsonResult<>();
        if(scaleService.updateScaleDone(scaleId, state) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  设置量表排序
     * @param scaleId 量表id
     * @param sort 排序
     * @return 结果实体对象
     */
    @RequestMapping(value = "set_sort",method =RequestMethod.POST)
    @ResponseBody
    public Object setSort(@RequestParam Integer scaleId, @RequestParam Integer sort) {
        var result = new JsonResult<>();
        if(scaleService.setSort(scaleId, sort) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  导出量表（word）
     * @param scaleId 量表Id
     * @param response 响应
     */
    @GetMapping("/export")
    public void exportScale(@RequestParam Integer scaleId, HttpServletResponse response) {
        XWPFDocument document = scaleService.exportScale(scaleId);
        response.reset();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition","attachment;filename="+ CommonHelper.getCurrentDate() + ".docx");
        try {
            OutputStream outputStream = response.getOutputStream();
            document.write(outputStream);
            outputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

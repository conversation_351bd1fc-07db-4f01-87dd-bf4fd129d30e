package cn.psycloud.psyplatform.controller.measuringroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.TaskDto;
import cn.psycloud.psyplatform.entity.measuringroom.TaskEntity;
import cn.psycloud.psyplatform.service.measuringroom.TaskService;
import lombok.var;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  测评任务管理
 */
@Controller
@RequestMapping("/measuringroom/task")
public class TaskController {
    @Autowired
    private TaskService taskService;
    /**
     *  选择测评任务类型
     * @return 视图
     */
    @GetMapping("/choose_type")
    public String chooseType() {
        return "measuringroom/task/chooseType";
    }

    /**
     *  添加限定测试对象的测评任务
     * @return 视图
     */
    @GetMapping("/add_limited_task")
    public String addLimitedTask() {
        return "measuringroom/task/addLimitedTask";
    }

    /**
     *  添加非限定测试对象的测评任务
     * @return 视图
     */
    @GetMapping("/add_unlimited_task")
    public String addUnLimitedTask() {
        return "measuringroom/task/addUnLimitedTask";
    }

    /**
     *  验证名称是否重复
     * @param taskName 任务名称
     * @param hidTaskName 原任务名称
     * @return 结果
     */
    @RequestMapping(value="/check_name",method = RequestMethod.POST)
    @ResponseBody
    public Object checkName(@Param("taskName") String taskName, @Param("hidTaskName") String hidTaskName, @Param("taskKind") Integer taskKind) {
        int isExist = 0;
        if (!taskName.equals(hidTaskName))
            isExist = taskService.isTaskNameExist(taskName,taskKind);
        return isExist;
    }

    /**
     *  添加测评任务
     * @param dto 测评任务实体对象
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_task",method=RequestMethod.POST)
    @ResponseBody
    public Object addTask(@RequestBody TaskDto dto, HttpServletRequest request){
        var result = new JsonResult<>();
        if(taskService.addTask(dto, request)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.ScaleTaskCreateSuccess);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  测评任务管理页面
     * @param request 请求
     * @return 视图
     */
    @RequestMapping ("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("0104020201".equals(a.getFunctionCode()) || "01040201".equals(a.getFunctionCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01040201".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //删除权限
            if ("0104020201".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canDelete",canDelete);
        return "measuringroom/task/list";
    }

    /**
     *  测评任务的调查问卷记录页面
     * @return 视图
     */
    @GetMapping("/survey_record_list")
    public String surveyRecordList(){
        return "measuringroom/task/surveyRecordList";
    }

    /**
     *  测评任务列表：分页
     * @param dto 查询条件
     * @return 测评任务集合
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object list(@RequestBody TaskDto dto) {
        return taskService.getListByPaged(dto);
    }

    /**
     *  根据测评任务id查询测评对象
     * @param taskId 测评任务id
     * @return 测评对象集合
     */
    @RequestMapping(value = "/getUsersByTaskId",method= RequestMethod.POST)
    @ResponseBody
    public Object getUsersByTaskId(@RequestParam Integer taskId) {
        return taskService.getUsersByTaskId(taskId);
    }

    /**
     *  批量删除
     * @param ids  id集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(taskService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  根据任务ID查询任务详情
     * @param taskId 测评任务id
     * @return 任务实体对象
     */
    @RequestMapping(value = "/get_task_by_id",method = RequestMethod.POST)
    @ResponseBody
    public Object getTaskById(@RequestParam Integer taskId) {
        return taskService.getById(taskId);
    }

    /**
     *  修改测评任务
     * @return 视图
     */
    @GetMapping("/update")
    public String updateTask(){
        return "measuringroom/task/updateTask";
    }

    /**
     *  修改测评任务
     * @param entity 测评任务实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update_task",method=RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody TaskEntity entity){
        var result = new JsonResult<>();
        if(taskService.updateTask(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  我的测评任务
     * @return 视图
     */
    @GetMapping("/my_tasks")
    public String myTasks() {
        return "measuringroom/task/myTasks";
    }

    /**
     *  获取我的测评任务
     * @param dto 查询条件
     * @return 测评任务集合
     */
    @RequestMapping("/my_tasks")
    @ResponseBody
    public Object getMyTasksByPaged(@RequestBody TaskDto dto) {
        return taskService.getMyTasks(dto);
    }

    /**
     *  根据测评任务id查询测评记录id
     * @param taskId 测评任务id
     * @param scaleId 测评量表id
     * @return 记录id
     */
    @GetMapping("/get_record")
    @ResponseBody
    public Object getRecordIdByTaskId(@RequestParam Integer taskId, @RequestParam Integer scaleId) {
        return taskService.getRecordIdByTaskId(taskId,scaleId);
    }

    /**
     *  查询任务集合：select2
     * @return 集合
     */
    @RequestMapping("/get_for_select")
    @ResponseBody
    public Object getListForSelect(@RequestBody TaskDto dto) {
        return taskService.getListForSelect(dto);
    }

    /**
     *  判断用户是不是属于当前测评任务
     * @param taskId 任务id
     * @return 记录数
     */
    @RequestMapping("/is_task_valid")
    @ResponseBody
    public Object isTaskValid(@RequestParam Integer taskId){
        var result = new JsonResult<>();
        var taskDto = taskService.getById(taskId);
        if(taskDto.getTaskType() == 1 && taskService.isTaskValid(taskId) ==0){
            result.setResultCode(ResultCodeAndMsg.TaskInvalid);
        }
        if(taskService.isTaskEnded(taskId))
            result.setResultCode(ResultCodeAndMsg.TaskEnded);
        return result;
    }

    /**
     *  判断任务里的调查问卷是否完成
     * @param taskId 任务id
     * @return 是否完成
     */
    @RequestMapping("/is_task_survey_done")
    @ResponseBody
    public Object isTaskSurveyDone(@RequestParam Integer taskId,HttpServletRequest request){
        var result = new JsonResult<>();
        var user = (UserDto)request.getSession().getAttribute("user");
        if(taskService.isTaskSurveyDone(user.getUserId(),taskId)) {
            result.setResultCode(ResultCodeAndMsg.TaskSurveyDoneCode);
            result.setResultMsg(ResultCodeAndMsg.TaskSurveyDoneMsg);
        }
        return result;
    }

    /**
     *  获取用户的测评任务里未完成的量表
     * @param taskId 任务id
     * @param request 请求
     * @return 集合
     */
    @RequestMapping("/get_list_for_scale_undone")
    @ResponseBody
    public Object getListForScaleUnDone(@RequestParam Integer taskId, HttpServletRequest request) {
        var user = (UserDto)request.getSession().getAttribute("user");
        return taskService.getScaleListUndone(user.getUserId(), taskId);
    }
}

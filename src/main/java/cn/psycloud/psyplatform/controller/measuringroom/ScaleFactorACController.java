package cn.psycloud.psyplatform.controller.measuringroom;

import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleFactorACService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 *  因子异常条件
 */
@Controller
@RequestMapping("/measuringroom/factorac")
public class ScaleFactorACController {
    @Autowired
    private ScaleFactorACService scaleFactorACService;

    /**
     *  获取因子的异常条件集合
     * @param factorId 因子id
     * @return 异常条件集合
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object getListByPaged(@RequestParam Integer factorId) {
        return scaleFactorACService.getListByFactorId(factorId);
    }

    /**
     *  添加因子异常条件
     * @param entity 异常条件实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method= RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody ScaleFactorAbnormalConditionEntity entity){
        var result = new JsonResult<>();
        if(scaleFactorACService.add(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除因子异常条件
     * @param id 异常条件id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(scaleFactorACService.deleteById(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

package cn.psycloud.psyplatform.controller.measuringroom;

import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleFactorService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 *  因子管理
 */
@Controller
@RequestMapping("/measuringroom/scaleFactor")
public class ScaleFactorController {
    @Autowired
    private ScaleFactorService scaleFactorService;

    /**
     *  获取因子列表
     * @param scaleId 量表id
     * @return 因子集合
     */
    @RequestMapping("/list")
    @ResponseBody
    public Object getList(@RequestParam Integer scaleId) {
        return scaleFactorService.getFactorsByScaleId(scaleId);
    }

    /**
     *  获取因子列表：select
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/get_for_select",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForSelect(@RequestBody ScaleFactorDto dto) {
        return scaleFactorService.getListForSelect(dto);
    }

    /**
     *  添加因子
     * @param entity 因子实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method=RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody ScaleFactorEntity entity){
        var result = new JsonResult<>();
        if(scaleFactorService.addFactor(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改因子
     * @param entity 因子实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody ScaleFactorEntity entity){
        var result = new JsonResult<>();
        if(scaleFactorService.updateFactor(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除
     * @param factorId 因子id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public Object delete(@RequestParam Integer factorId){
        var result = new JsonResult<>();
        if(scaleFactorService.deleteById(factorId)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    @RequestMapping(value = "/sort",method=RequestMethod.POST)
    @ResponseBody
    public Object sort(@RequestParam Integer factorId, @RequestParam String flag){
        var result = new JsonResult<>();
        if(scaleFactorService.sort(factorId,flag)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 获取量表的因子总数
     * @param scaleId 量表id
     * @return 总数
     */
    @RequestMapping(value = "/get_factor_count", method = RequestMethod.POST)
    @ResponseBody
    public Object getFactorCount(@RequestParam Integer scaleId){
        return scaleFactorService.getFactorCount(scaleId);
    }
}

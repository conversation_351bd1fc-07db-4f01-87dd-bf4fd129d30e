package cn.psycloud.psyplatform.controller.consultationcase;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto;
import cn.psycloud.psyplatform.dto.consultationcase.ExportConsultationCaseDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.measuringroom.ExportTestRecordDto;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseEntity;
import cn.psycloud.psyplatform.service.consultationcase.ConsultationCaseService;
import com.alibaba.excel.EasyExcel;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/counselingroom/consultationcase")
@Controller
public class ConsultationCaseController {
    @Autowired
    private ConsultationCaseService consultationCaseService;
    @Value("${file.location}")
    String uploadPath;
    
    @GetMapping("/add")
    public String addConsultationCase() {
        return "/consultationcase/add";
    }

    @GetMapping("/update")
    public String updateConsultationCase() {
        return "/consultationcase/update";
    }

    @GetMapping("/list")
    public ModelAndView getConsultationCaseList(HttpServletRequest request) {
        var mv = new ModelAndView();
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010511".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //添加权限
            if ("01051101".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            if ("01051102".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            if ("01051103".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        mv.addObject("canAdd", canAdd);
        mv.addObject("canUpdate", canUpdate);
        mv.addObject("canDelete", canDelete);
        mv.setViewName("consultationcase/list");
        return mv;
    }

    /**
     *  我的个案页面
     * @return 视图
     */
    @GetMapping("/my_cases")
    public String getMyCases() {
        return "consultationcase/myCases";
    }

    /**
     * 新增个案
     * @param consultationCaseEntity 个案实体
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method= RequestMethod.POST)
    @ResponseBody
    public Object addConsultationCase(@RequestBody ConsultationCaseEntity consultationCaseEntity) {
        var result = new JsonResult<>();
        if (consultationCaseService.add(consultationCaseEntity) > 0) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 更新个案
     * @param consultationCaseEntity 个案实体
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method= RequestMethod.POST)
    @ResponseBody
    public Object updateConsultationCase(@RequestBody ConsultationCaseEntity consultationCaseEntity) {
        var result = new JsonResult<>();
        if (consultationCaseService.update(consultationCaseEntity) > 0) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 删除个案(批量删除)
     * @param ids 个案ID集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_del",method= RequestMethod.POST)
    @ResponseBody
    public Object batchDelConsultationCase(@RequestParam String ids) {
        var result = new JsonResult<>();
        if (consultationCaseService.batchDel(ids)) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 根据条件查询个案列表
     * @param consultationCaseDto 个案查询条件
     * @return 个案集合
     * */
    @RequestMapping(value = "/get_list_by_paged",method= RequestMethod.POST)
    @ResponseBody
    public Object getConsultationCaseList(@RequestBody ConsultationCaseDto consultationCaseDto) {
        return consultationCaseService.getListByPaged(consultationCaseDto);
    }

    /**
     * 获取我的个案集合
     * @param consultationCaseDto 个案查询条件
     * @return 个案集合
     */
    @RequestMapping(value = "/get_my_cases",method= RequestMethod.POST)
    @ResponseBody
    public Object getMyCases(@RequestBody ConsultationCaseDto consultationCaseDto) {
        return consultationCaseService.getMyCases(consultationCaseDto);
    }

    /**
     * 根据ID查询个案详情
     * @param id 个案ID
     * @return 个案实体
     */
    @GetMapping("/get")
    @ResponseBody
    public Object getById(@RequestParam Integer id) {
        return consultationCaseService.getById(id);
    }

    /**
     * 导出咨询个案Word文档
     * @param id 个案ID
     * @param response HTTP响应
     */
    @GetMapping("/export_word")
    public void exportWord(@RequestParam Integer id, HttpServletResponse response) {
        consultationCaseService.exportWordDocument(id, response);
    }

    /**
     * 导出咨询个案Excel文档
     * @param consultationCaseDto 个案查询条件
     */
    @PostMapping("/download")
    @ResponseBody
    public String exportExcel(@RequestBody ConsultationCaseDto consultationCaseDto) {
        List<ExportConsultationCaseDto> listCases = consultationCaseService.getExportList(consultationCaseDto);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHssmm");
        var fileName = sdf.format(new Date())+ ".xlsx";
        String savePath = uploadPath + "temp/"+ fileName;
        EasyExcel.write(savePath, ExportConsultationCaseDto.class)
                .sheet("咨询个案记录")
                .doWrite(listCases);
        return fileName;
    }

    /**
     * 咨询数据看板页面
     * @return 页面路径
     */
    @GetMapping("/stat")
    public String statPage() {
        return "consultationcase/stat";
    }

    /**
     * 获取咨询数据看板统计数据
     * @param consultationCaseDto 查询条件
     * @return 统计数据
     */
    @PostMapping("/get_dashboard_data")
    @ResponseBody
    public Object getDashboardData(@RequestBody ConsultationCaseDto consultationCaseDto) {
        return consultationCaseService.getDashboardData(consultationCaseDto);
    }
}

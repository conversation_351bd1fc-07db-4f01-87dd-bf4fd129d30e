package cn.psycloud.psyplatform.controller.consultationcase;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.consultationcase.ConsultationKeywordDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationKeywordEntity;
import cn.psycloud.psyplatform.service.consultationcase.ConsultationKeywordService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  咨询关键词管理
 */
@RequestMapping("/counselingroom/keywords")
@Controller
public class ConsultationKeywordController {
    @Autowired
    private ConsultationKeywordService consultationKeywordService;

    /**
     * 关键词管理页面
     * @return 视图
     */
    @GetMapping("/list")
    public String getKeywordList(HttpServletRequest request) {
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010514".equals(a.getFunctionCode()) || "010514".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01051401".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("01051402".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("01051403".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "consultationcase/keywords/list";
    }

    /**
     * 获取关键词列表（分页）
     * @param consultationKeywordDto 查询条件
     * @return 分页结果
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public Object getKeywordListByPaged(@RequestBody ConsultationKeywordDto consultationKeywordDto) {
        return consultationKeywordService.getListByPaged(consultationKeywordDto);
    }

    /**
     * 获取所有有效关键词列表（用于前端选择）
     * @return 关键词列表
     */
    @GetMapping("/all")
    @ResponseBody
    public Object getAllValidKeywords() {
        JsonResult<List<String>> result = new JsonResult<>();
        try {
            List<String> keywords = consultationKeywordService.getAllKeywords();
            result.setData(keywords);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("获取关键词列表失败");
        }
        return result;
    }

    /**
     * 新增关键词
     * @param consultationKeywordEntity 关键词实体
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public Object addKeyword(@RequestBody ConsultationKeywordEntity consultationKeywordEntity) {
        JsonResult<Object> result = new JsonResult<>();
        try {
            if (StrUtil.isEmpty(consultationKeywordEntity.getKeyword())) {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg("关键词内容不能为空");
                return result;
            }
            
            boolean success = consultationKeywordService.add(consultationKeywordEntity);
            if (success) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.FailureMsg);
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 更新关键词
     * @param consultationKeywordEntity 关键词实体
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public Object updateKeyword(@RequestBody ConsultationKeywordEntity consultationKeywordEntity) {
        JsonResult<Object> result = new JsonResult<>();
        try {
            if (StrUtil.isEmpty(consultationKeywordEntity.getKeyword())) {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg("关键词内容不能为空");
                return result;
            }
            
            boolean success = consultationKeywordService.update(consultationKeywordEntity);
            if (success) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.FailureMsg);
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 删除关键词
     * @param id 关键词ID
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public Object deleteKeyword(@RequestParam Integer id) {
        JsonResult<Object> result = new JsonResult<>();
        try {
            boolean success = consultationKeywordService.delete(id);
            if (success) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.FailureMsg);
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 批量删除关键词
     * @param ids 关键词ID列表（逗号分隔）
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_delete", method = RequestMethod.POST)
    @ResponseBody
    public Object batchDeleteKeywords(@RequestParam String ids) {
        JsonResult<Object> result = new JsonResult<>();
        try {
            if (StrUtil.isEmpty(ids)) {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg("请选择要删除的关键词");
                return result;
            }
            boolean success = consultationKeywordService.batchDel(ids);
            if (success) {
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
            } else {
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.FailureMsg);
            }
        } catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 检查关键词是否存在
     * @param keyword 关键词内容
     * @param id 当前记录ID（新增时为0或空）
     * @return 结果实体对象
     */
    @PostMapping("/check_exists")
    @ResponseBody
    public Object checkKeywordExists(@RequestParam String keyword, @RequestParam(required = false) Long id) {
        var result = new JsonResult<>();
        if (StrUtil.isEmpty(keyword)) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg("关键词内容不能为空");
            return result;
        }
        
        // 检查关键词是否已存在
        boolean isExist = consultationKeywordService.checkKeywordExistsExcludeId(keyword, id);
        
        if (isExist) {
            result.setResultCode(201); // 使用201表示存在重复
            result.setResultMsg("该关键词已存在");
        } else {
            result.setResultCode(200); // 使用200表示不存在重复
            result.setResultMsg("关键词可用");
        }
        return result;
    }
} 
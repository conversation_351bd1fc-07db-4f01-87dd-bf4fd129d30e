package cn.psycloud.psyplatform.controller.anteroom;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.anteroom.StructsEntity;
import cn.psycloud.psyplatform.service.anteroom.StructsService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  组织管理
 */
@Controller
@RequestMapping("/anteroom/structs")
public class StructsController {
    @Autowired
    private StructsService structsService;

    /**
     *  组织管理页面
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/index")
    public String index(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010301".equals(a.getFunctionCode()) || "010301".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01030101".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("01030102".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("01030103".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "anteroom/structs/index";
    }

    /**
     *  查询当前登录用户负责的组织集合
     * @param request 请求
     * @return 组织集合
     */
    @RequestMapping(value = "/index",method = RequestMethod.POST)
    @ResponseBody
    public Object list(HttpServletRequest request){
        UserDto user= (UserDto)request.getSession().getAttribute("user");
        return JSONUtil.toJsonStr(structsService.getListForZtree(user.getUserId(),true));
    }

    /**
     *  查询用户负责的组织集合
     * @param userId 用户id
     * @return 组织集合
     */
    @RequestMapping(value = "/get_structs",method = RequestMethod.POST)
    @ResponseBody
    public Object list(@RequestParam Integer userId){
        return structsService.getListForZtree(userId,true);
    }

    /**
     *  添加组织
     * @param entity 组织实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method=RequestMethod.POST)
    @ResponseBody
    public  Object insert(@RequestBody StructsEntity entity){
        var result = new JsonResult<>();
        if(structsService.addStruct(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改组织
     * @param entity 组织实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public  Object update(@RequestBody StructsEntity entity){
        var result = new JsonResult<>();
        if(structsService.updateStruct(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除组织
     * @param id 组织d
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public  Object delete(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(structsService.delStruct(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  移动组织
     * @param targetNode 目标节点
     * @param node 源节点
     * @return 结果实体对象
     */
    @RequestMapping(value = "/move",method=RequestMethod.POST)
    @ResponseBody
    public  Object delete(@RequestParam Integer targetNode,@RequestParam Integer node){
        var result = new JsonResult<>();
        if(structsService.moveStruct(targetNode,node) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  获取组织集合(下拉列表)：select
     * @return 组织集合
     */
    @RequestMapping(value = "/get_for_select",method=RequestMethod.POST)
    @ResponseBody
    public Object getStructsForSelect(){
        return structsService.getListForSelect();
    }

    /**
     *  根据组织名称和父id获取组织id
     * @param structName 组织名称
     * @param parentId 父id
     * @return 组织id
     */
    @RequestMapping(value = "/getByName",method=RequestMethod.GET)
    @ResponseBody
    public Object getIdByStructName(@RequestParam String structName, @RequestParam Integer parentId){
        return structsService.getStructIdByStructName(parentId,structName);
    }

    /**
     *  根据组织名称获取组织id
     * @param structName 组织名称
     * @return 组织id
     */
    @RequestMapping(value = "/getIdByName",method=RequestMethod.GET)
    @ResponseBody
    public Object getIdByStructName(@RequestParam String structName){
        return structsService.getIdByStructName(structName);
    }
}

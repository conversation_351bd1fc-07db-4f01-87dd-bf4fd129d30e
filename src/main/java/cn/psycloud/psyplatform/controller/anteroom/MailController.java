package cn.psycloud.psyplatform.controller.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.MailDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.service.anteroom.MailService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

/**
 *  站内消息
 */
@RequestMapping("/anteroom/mail")
@Controller
public class MailController {
    @Autowired
    private MailService mailService;
    /**
     *  站内消息管理页面
     * @return 视图
     */
    @RequestMapping ("/list")
    public String list(){
        return "anteroom/mail/list";
    }

    /**
     *  阅读站内消息页面
     * @param id 消息id
     * @return 视图
     */
    @GetMapping("/read")
    public ModelAndView read(@RequestParam Integer id){
        var mv = new ModelAndView();
        mv.addObject("mail",mailService.getById(id));
        mv.setViewName("anteroom/mail/read");
        return mv;
    }

    /**
     *  站内消息集合
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object insert(@RequestBody MailDto dto){
        return mailService.getListByPaged(dto);
    }

    /**
     *  批量删除
     * @param ids ID集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(mailService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  更改消息阅读状态
     * @param ids ID集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update_read_state",method=RequestMethod.POST)
    @ResponseBody
    public Object updateReadState(@RequestParam String ids){
        var result = new JsonResult<>();
        if(mailService.updateReadState(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

package cn.psycloud.psyplatform.controller.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import lombok.var;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  咨询师管理
 */
@Controller
@RequestMapping("/anteroom/counselor")
public class CounselorController {
    /**
     *  咨询师管理页面
     * @param request 请求
     * @return 视图
     */
    @RequestMapping ("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01030202".equals(a.getFunctionCode()) || "01030202".equals(a.getParentCode())))
                .collect(Collectors.toList());;
        boolean canAdd = false, canUpdate = false, canDelete = false, canResetPwd = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("0103020201".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("0103020202".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("0103020203".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //密码设置
            if ("0103020204".equals(privilege.getFunctionCode())) {
                canResetPwd = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canResetPwd",canResetPwd);
        return "anteroom/counselor/list";
    }

    /**
     *  添加咨询师页面
     * @return 视图
     */
    @GetMapping("/add")
    public String add(){
        return "anteroom/counselor/add";
    }

    /**
     *  修改咨询师页面
     * @return 视图
     */
    @GetMapping("/update")
    public String update(){
        return "anteroom/counselor/update";
    }
}

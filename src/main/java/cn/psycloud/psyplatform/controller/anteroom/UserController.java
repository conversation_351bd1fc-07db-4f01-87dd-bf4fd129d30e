package cn.psycloud.psyplatform.controller.anteroom;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.BatchAddDto;
import cn.psycloud.psyplatform.dto.anteroom.ImportUserDto;
import cn.psycloud.psyplatform.dto.anteroom.ModifyPwdDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.anteroom.WechatUserEntity;
import cn.psycloud.psyplatform.service.anteroom.UserService;
import com.alibaba.excel.EasyExcel;
import lombok.SneakyThrows;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 *  用户管理
 */
@Controller
@RequestMapping("/anteroom/user")
public class UserController {
    @Autowired
    private UserService userService;

    /**
     *  获取平台管理员集合
     * @param dto 用户实体对象
     * @return 平台管理员集合
     */
    @RequestMapping(value = "/get_admin_list",method= RequestMethod.POST)
    @ResponseBody
    public Object getAdminUsersByPaged(@RequestBody UserDto dto) {
        return userService.getUserListByPaged(dto,"admin", false);
    }

    /**
     *  获取咨询师集合
     * @param dto 用户实体对象
     * @return 咨询师集合
     */
    @RequestMapping(value = "/get_counselor_list",method= RequestMethod.POST)
    @ResponseBody
    public Object getCounselorListByPaged(@RequestBody UserDto dto) {
        return userService.getUserListByPaged(dto,"counselor", true);
    }

    /**
     *  根据条件查询来访者信息：分页
     * @param dto 用户实体对象
     * @return 来访者集合
     */
    @RequestMapping(value = "/get_visitor_list",method= RequestMethod.POST)
    @ResponseBody
    public Object getVisitorListByPaged(@RequestBody UserDto dto) {
        return userService.getUserListByPaged(dto, "visitor",true);
    }

    /**
     *  根据用户id查询用户信息
     * @param id 用户id
     * @return 用户实体
     */
    @RequestMapping(value = "/get",method = RequestMethod.POST)
    @ResponseBody
    public Object get(@RequestParam Integer id){
        return JSONUtil.toJsonStr(userService.getById(id));
    }

    /**
     *  修改密码
     * @param dto 修改密码实体
     * @return 结果实体对象
     */
    @RequestMapping(value = "/modify_pwd",method = RequestMethod.POST)
    @ResponseBody
    public  Object modifyPassword(@RequestBody ModifyPwdDto dto){
        var result = new JsonResult<>();
        if(userService.modifyPassword(dto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.ResetPwdSuccess);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  验证用户名是否存在
     * @return 结果实体对象
     */
    @RequestMapping(value = "/check_loginname",method = RequestMethod.POST)
    @ResponseBody
    public Object  isLoginNameExist(@RequestParam String loginName, @RequestParam String hidLoginName){
        int isExist = 0;
        if (!loginName.equals(hidLoginName))
            isExist = userService.isLoginNameExist(loginName);
        return isExist;
    }

    /**
     *  根据用户名查询用户信息
     * @return 结果实体对象
     */
    @RequestMapping(value = "/getByLoginName",method = RequestMethod.GET)
    @ResponseBody
    public Object getByLoginName(@RequestParam String loginName){
        return userService.checkUserInfo(loginName);
    }

    /**
     *  添加用户
     * @param dto 用户实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method = RequestMethod.POST)
    @ResponseBody
    public  Object addUser(@RequestBody UserDto dto){
        var result = new JsonResult<>();
        if(userService.addUser(dto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改用户
     * @param dto 用户实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method = RequestMethod.POST)
    @ResponseBody
    public  Object updateUser(@RequestBody UserDto dto){
        var result = new JsonResult<>();
        if(userService.updateUser(dto) ){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  测试之前修改用户信息
     * @param dto 用户实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "update_for_scale", method = RequestMethod.POST)
    @ResponseBody
    public Object updateForScale(@RequestBody UserDto dto){
        var result = new JsonResult<>();
        if(userService.updateForScale(dto) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除用户
     * @param userId 用户id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method = RequestMethod.POST)
    @ResponseBody
    public  Object deleteUser(@RequestParam Integer userId){
        var result = new JsonResult<>();
        if(userService.delUser(userId) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除用户（批量删除）
     * @param ids 用户id集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_del",method = RequestMethod.POST)
    @ResponseBody
    public  Object batchDelUser(@RequestParam String ids){
        var result = new JsonResult<>();
        if(userService.batchDelUser(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量更改角色
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_update_role",method = RequestMethod.POST)
    @ResponseBody
    public  Object batchUpdateRole(@RequestParam Integer roleId, @RequestParam String userIds){
        var result = new JsonResult<>();
        if(userService.batchUpdate(roleId, userIds)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量审核用户
     * @param ids 用户id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_check",method = RequestMethod.POST)
    @ResponseBody
    public  Object batchCheck(@RequestParam String ids){
        var result = new JsonResult<>();
        if(userService.batchCheck(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量开通：预览
     * @param dto 批量开通实体对象
     * @return 账号集合
     */
    @RequestMapping(value = "/preview",method= RequestMethod.POST)
    @ResponseBody
    public Object preview(@RequestBody BatchAddDto dto) {
        return userService.getPreviewUserList(dto);
    }

    /**
     *  批量开通
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_add",method = RequestMethod.POST)
    @ResponseBody
    public  Object batchAdd(@RequestBody List<BatchAddDto> previewUsers){
        var result = new JsonResult<>();
        if(userService.batchAdd(previewUsers)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量导入来访者信息：Excel
     * @param file excel文件
     * @return 结果实体对象
     */
    @SneakyThrows
    @RequestMapping(value = "/import",method = RequestMethod.POST)
    @ResponseBody
    public Object importVisitor(@RequestParam("file") MultipartFile file, @RequestParam Integer structId){
        List<ImportUserDto> list = EasyExcel.read(file.getInputStream())
                .head(ImportUserDto.class)
                .sheet()
                .doReadSync();
        var listUsers = userService.importVisitor(list, structId);
        var dtRes = new BSDatatableRes<ImportUserDto>();
        dtRes.setData(listUsers);
        dtRes.setRecordsTotal(listUsers.size());
        dtRes.setRecordsTotal(listUsers.size());
        return dtRes;
    }

    /**
     *  获取用户列表：select
     * @return 集合
     */
    @RequestMapping(value = "/getCounselorList_for_select",method = RequestMethod.POST)
    @ResponseBody
    public Object getCounselorListForSelect(){
        return userService.getCounselorListForSelect();
    }

    /**
     *  获取推荐咨询师列表
     * @return 集合
     */
    @RequestMapping(value = "/getRecommendCounselorList",method = RequestMethod.POST)
    @ResponseBody
    public Object getRecommendCounselorList(){
        return userService.getRecommendCounselorList();
    }

    /**
     *  更换头像
     * @param userId 用户id
     * @param headPic 用户头像
     * @return 结果实体对象
     */
    @RequestMapping(value = "/avatar",method=RequestMethod.POST)
    @ResponseBody
    public Object updateAvatar(@RequestParam Integer userId, @RequestParam String headPic){
        var result = new JsonResult<>();
        if(userService.updateAvatar(userId, headPic) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.UpdateAvatarSuccess);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  绑定手机号码
     * @param mobile 手机号码
     * @return 结果实体对象
     */
    @RequestMapping(value = "/bind_mobile",method=RequestMethod.POST)
    @ResponseBody
    public Object bindMobile(@RequestParam String mobile){
        var result = new JsonResult<>();
        if(userService.checkMobile(mobile) > 0){
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.CheckMobileExists);
        }
        else{
            if(userService.bindMobile(mobile) > 0){
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(ResultCodeAndMsg.BindMobileSuccess);
            }
            else{
                result.setResultCode(ResultCodeAndMsg.FailureCode);
                result.setResultMsg(ResultCodeAndMsg.FailureMsg);
            }
        }
        return result;
    }

    /**
     *  验证密码
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/verifypwd",method=RequestMethod.POST)
    @ResponseBody
    public Object verifyPwd(HttpServletRequest request, @RequestParam String originalPwd){
        var result = new JsonResult<>();
        var modifyPwdDto = new ModifyPwdDto();
        modifyPwdDto.setOriginalPwd(originalPwd);
        var userDto = (UserDto)request.getSession().getAttribute("user");
        modifyPwdDto.setUserId(userDto.getUserId());
        if(userService.verifyPassword(modifyPwdDto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改密码
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/modifypwd",method=RequestMethod.POST)
    @ResponseBody
    public Object modifyPwd(HttpServletRequest request, @RequestParam String newPwd){
        var result = new JsonResult<>();
        var modifyPwdDto = new ModifyPwdDto();
        modifyPwdDto.setNewPwd(newPwd);
        var userDto = (UserDto)request.getSession().getAttribute("user");
        modifyPwdDto.setUserId(userDto.getUserId());
        if(userService.modifyPassword(modifyPwdDto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  微信用户信息
     * @param entity 微信用户信息实体
     * @return 结果实体对象
     * */
    @RequestMapping(value = "/add_wechat_user",method = RequestMethod.POST)
    @ResponseBody
    public Object addWechatUser(@RequestBody WechatUserEntity entity){
        var result = new JsonResult<>();
        if(userService.addWechatUser(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

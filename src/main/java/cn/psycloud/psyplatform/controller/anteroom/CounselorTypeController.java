package cn.psycloud.psyplatform.controller.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import lombok.var;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  咨询师类型管理页面
 */
@Controller
@RequestMapping("/anteroom/counselortype")
public class CounselorTypeController {
    @RequestMapping ("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01030201".equals(a.getFunctionCode()) || "01030201".equals(a.getParentCode())))
                .collect(Collectors.toList());;
        boolean canAdd = false, canUpdate = false, canDelete = false, canGrant = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("0103020101".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("0103020102".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("0103020103".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //权限设置
            if ("0103020104".equals(privilege.getFunctionCode())) {
                canGrant = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canGrant",canGrant);
        return "anteroom/counselortype/list";
    }
}

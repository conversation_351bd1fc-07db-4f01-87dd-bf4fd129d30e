package cn.psycloud.psyplatform.controller.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import lombok.var;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/anteroom/visitor")
public class VisitorController {
    /**
     *  来访者管理页面
     * @param request 请求
     * @return 视图
     */
    @RequestMapping ("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01030301".equals(a.getFunctionCode()) || "01030303".equals(a.getParentCode())))
                .collect(Collectors.toList());;
        boolean canAdd = false, canUpdate = false, canDelete = false, canResetPwd = false, canUpdateRole = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01030301".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("0103030301".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("0103030302".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //密码设置
            if ("0103030303".equals(privilege.getFunctionCode())) {
                canResetPwd = true;
            }
            //更改角色
            if ("0103030304".equals(privilege.getFunctionCode())) {
                canUpdateRole = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canResetPwd",canResetPwd);
        request.setAttribute("canUpdateRole",canUpdateRole);
        return "anteroom/visitor/list";
    }

    /**
     *  添加来访者界面
     * @return 视图
     */
    @GetMapping("/add")
    public String add() {
        return "anteroom/visitor/add";
    }

    /**
     *  修改来访者界面
     * @return 视图
     */
    @GetMapping("/update")
    public String update() {
        return "anteroom/visitor/update";
    }

    /**
     *  导入来访者界面
     */
    @GetMapping("/import")
    public String importVisitor() {
        return "anteroom/visitor/import";
    }

    /**
     *  来访者注册审核
     * @return 视图
     */
    @GetMapping("/check")
    public String checkVisitor() {
        return "anteroom/visitor/check";
    }

    /**
     *  批量添加页面
     * @return 视图
     */
    @GetMapping("/batch_add")
    public String batchAdd() {
        return "anteroom/visitor/batchAdd";
    }
}

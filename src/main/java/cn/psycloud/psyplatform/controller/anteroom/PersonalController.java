package cn.psycloud.psyplatform.controller.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.service.anteroom.UserService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import static cn.hutool.core.date.DateUtil.ageOfNow;

/**
 *  个人中心
 */
@RequestMapping("/anteroom/personal")
@Controller
public class PersonalController {
    @Autowired
    private UserService userService;

    /**
     *  个人主页页面
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/user_center")
    public ModelAndView userCenter(HttpServletRequest request){
        var mv = new ModelAndView();
        var userDto = (UserDto)request.getSession().getAttribute("user");
        userDto = userService.getUserByName(userDto.getLoginName());
        userDto.setAge(ageOfNow(userDto.getBirth()));
        mv.addObject("user", userDto);
        mv.setViewName("anteroom/personal/userCenter");
        return mv;
    }

    /**
     *  编辑个人资料页面
     * @return 视图
     */
    @GetMapping("/edit_profile")
    public String editProfile(){
        return "anteroom/personal/editProfile";
    }

    /**
     *  账号安全页面
     * @return 视图
     */
    @GetMapping("/account_security")
    public String accountSecurity(){
        return "anteroom/personal/accountSecurity";
    }

    /**
     *  修改密码页面
     * @return 视图
     */
    @GetMapping("/modifypwd")
    public String modifyPwd() {
        return "anteroom/personal/modifyPwd";
    }
}

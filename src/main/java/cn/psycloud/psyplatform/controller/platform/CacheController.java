package cn.psycloud.psyplatform.controller.platform;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.service.anteroom.SysFunctionService;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import cn.psycloud.psyplatform.util.redis.JedisUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequestMapping("/platform/cache")
@Controller
public class CacheController {
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private SysFunctionService sysFunctionService;

    /**
     *  缓存设置
     * @return 视图
     */
    @GetMapping("/index")
    public ModelAndView index(HttpServletRequest request){
        var mv = new ModelAndView();
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("01020401".equals(a.getFunctionCode())))
                .collect(Collectors.toList());
        boolean canSet = false;
        for (SysFunctionDto privilege: privilegeList){
            //设置权限
            if ("01020401".equals(privilege.getFunctionCode())) {
                canSet = true;
            }
        }
        request.setAttribute("canSet",canSet);
        mv.setViewName("platform/cache/index");
        return mv;
    }

    /**
     *  更新平台参数缓存
     * @return 结果实体对象
     */
    @RequestMapping(value = "/set_sysconfig_cache",method= RequestMethod.POST)
    @ResponseBody
    public Object setSysconfigCache(){
        var result = new JsonResult<>();
        var res = JedisUtil.set("sysConfig", JSONUtil.toJsonStr(sysConfigService.get()), 0);
        log.error("更新平台参数缓存：{}",res);
        if("OK".equals(res)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  清除平台参数缓存
     * @return 结果实体对象
     */
    @RequestMapping(value = "/clear_sysconfig_cache",method=RequestMethod.POST)
    @ResponseBody
    public Object clearSysconfigCache(){
        var result = new JsonResult<>();
        var res = JedisUtil.del("sysConfig");
        log.error("清除平台参数缓存：{}",res);
        if(res > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  缓存平台功能
     * @return 结果实体对象
     */
    @RequestMapping(value = "/set_sysfunction_cache",method=RequestMethod.POST)
    @ResponseBody
    public Object setSysfunctionCache(){
        var result = new JsonResult<>();
        var sysFunctions = sysFunctionService.getGrantByRole(0);
        var res = JedisUtil.set("sysFunctions",JSONUtil.toJsonStr(sysFunctions),0);
        if("OK".equals(res)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    @RequestMapping(value = "/clear_sysfunction_cache",method=RequestMethod.POST)
    @ResponseBody
    public Object clearSysFunctionCache(){
        var result = new JsonResult<>();
        var res = JedisUtil.del("sysFunctions");
        if(res > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

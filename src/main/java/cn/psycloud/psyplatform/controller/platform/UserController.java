package cn.psycloud.psyplatform.controller.platform;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import lombok.var;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  平台管理员管理
 */
@Controller("AdminUser")
@RequestMapping("/platform/user")
public class UserController {
    /**
     *  平台用户管理页面
     * @return 视图
     */
    @GetMapping("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010203".equals(a.getFunctionCode()) || "010203".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canDelete = false, canResetPwd = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01020301".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //删除权限
            if ("01020303".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //密码设置
            if ("01020304".equals(privilege.getFunctionCode())) {
                canResetPwd = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canResetPwd",canResetPwd);
        return "platform/user/list";
    }
}

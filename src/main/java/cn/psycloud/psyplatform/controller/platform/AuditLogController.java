package cn.psycloud.psyplatform.controller.platform;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.platform.OptLogDto;
import cn.psycloud.psyplatform.dto.platform.UserLoginLogDto;
import cn.psycloud.psyplatform.entity.platform.UserLoginLogEntity;
import cn.psycloud.psyplatform.service.platform.OptLogService;
import cn.psycloud.psyplatform.service.platform.UserLoginLogService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  审计日志
 */
@RequestMapping("/platform/auditlog")
@Controller
public class AuditLogController {
    @Autowired
    private UserLoginLogService userLoginLogService;
    @Autowired
    private OptLogService optLogService;

    /**
     *  登录日志
     * @return 视图
     */
    @GetMapping("/loginlog_list")
    public String loginLogList(){
        return "platform/auditLog/loginLogList";
    }

    /**
     *  保存登录日志
     * @param entity 登录日志实体对象
     * @return 操作成功与否
     */
    @PostMapping("/add_loginlog")
    @ResponseBody
    public Object addLoginLog(@RequestBody UserLoginLogEntity entity){
        var result = new JsonResult<>();
        if(userLoginLogService.add(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return  result;
    }

    /**
     *  获取登录日志集合：分页
     * @param dto 条件
     * @return 集合
     */
    @RequestMapping(value = "/get_loginloglist", method= RequestMethod.POST)
    @ResponseBody
    public Object geLoginLogtListByPaged(@RequestBody UserLoginLogDto dto){
        return userLoginLogService.getListByPaged(dto);
    }

    /**
     *  操作日志
     * @return 视图
     */
    @GetMapping("/optlog_list")
    public String optLogList(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("0102060201".equals(a.getFunctionCode())))
                .collect(Collectors.toList());
        boolean canExport = false;
        for (SysFunctionDto privilege: privilegeList){
            //删除权限
            //导出权限
            if ("0102060201".equals(privilege.getFunctionCode())) {
                canExport = true;
            }
        }
        request.setAttribute("canExport",canExport);
        return "platform/auditLog/optLogList";
    }

    /**
     *  获取操作日志集合：分页
     * @param dto 条件
     * @return 集合
     */
    @RequestMapping(value = "/get_optloglist", method= RequestMethod.POST)
    @ResponseBody
    public Object getOptLogListByPaged(@RequestBody OptLogDto dto){
        return optLogService.getListByPaged(dto);
    }
}

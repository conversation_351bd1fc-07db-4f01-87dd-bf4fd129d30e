package cn.psycloud.psyplatform.controller.platform;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.RoleDto;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.anteroom.RoleEntity;
import cn.psycloud.psyplatform.service.anteroom.SysFunctionService;
import cn.psycloud.psyplatform.service.platform.RoleService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  平台角色
 */
@Controller
@RequestMapping("/platform/role")
public class RoleController {
    @Autowired
    private RoleService roleService;

    @Autowired
    private SysFunctionService sysFunctionService;

    /**
     *  平台角色管理页面
     * @param request 请求
     * @return 视图
     */
    @RequestMapping("/list")
    public String list(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010202".equals(a.getFunctionCode()) || "010202".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canAdd = false, canUpdate = false, canDelete = false, canGrant = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01020201".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("01020202".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("01020203".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //权限设置
            if ("01020204".equals(privilege.getFunctionCode())) {
                canGrant = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canGrant",canGrant);
        return "platform/role/list";
    }

    /**
     *  查询平台角色集合：分页
     * @param dto 平台角色实体对象
     * @return BootstrapDatatables格式的对象
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByPaged(@RequestBody RoleDto dto){
        return roleService.getListByPaged(dto);
    }

    /**
     *  获取所有功能的权限树
     * @return 返回ztree格式
     */
    @RequestMapping(value="/get_all_functions",method = RequestMethod.GET,produces = "text/html;charset=utf8")
    @ResponseBody
    public Object getAllFunctions(){
        return JSONUtil.toJsonStr(sysFunctionService.getListForZTree());
    }

    /**
     *  根据角色ID获取角色权限
     * @return 返回ztree格式
     */
    @RequestMapping(value = "/get_privilege", method=RequestMethod.POST,produces = "text/html;charset=utf8")
    @ResponseBody
    public Object getPrivilege(HttpServletRequest request){
        Integer roleId = Integer.parseInt(request.getParameter("roleId"));
        var result =  roleService.getRolePrivilegeForZTree(roleId)
                .stream()
                .filter(a->!"00".equals(a.getId()))
                .collect(Collectors.toList());
        return JSONUtil.toJsonStr(result);
    }

    /**
     *  角色授权
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add_privilege",method=RequestMethod.POST)
    @ResponseBody
    public Object addPrivilege(@RequestParam Integer roleId, @RequestParam String privileges){
        var result = new JsonResult<>();
        if(roleService.addGrant(roleId, privileges.split(","))){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  清空角色权限
     * @param roleId 角色id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/clear_privilege",method=RequestMethod.POST)
    @ResponseBody
    public Object clearGrant(@RequestParam Integer roleId){
        var result = new JsonResult<>();
        if(roleService.clearGrant(roleId) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  新增角色
     * @param entity 角色实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method=RequestMethod.POST)
    @ResponseBody
    public  Object insert(@RequestBody RoleEntity entity, HttpServletRequest request){
        var result = new JsonResult<>();
        var userDto = (UserDto)request.getSession().getAttribute("user");
        entity.setOperator(Integer.parseInt(userDto.getUserId().toString()));
        if(roleService.addRole(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改角色
     * @param entity 角色实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public  Object update(@RequestBody RoleEntity entity){
        var result = new JsonResult<>();
        if(roleService.updateRole(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除角色(单个)
     * @param roleId 角色id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public  Object delete(@RequestParam Integer roleId){
        var result = new JsonResult<>();
        if(roleService.del(roleId) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public  Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(roleService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  查询平台角色集合
     * @return 平台角色集合
     */
    @RequestMapping(value = "/get_for_select_p",method = RequestMethod.POST)
    @ResponseBody
    public Object getListForSelectP(){
        return roleService.getListForSelectP();
    }

    /**
     *  查询咨询师类型集合
     * @param flag 角色类型标识
     * @return 咨询师类型集合
     */
    @RequestMapping(value = "/get_for_select",method = RequestMethod.POST)
    @ResponseBody
    public Object getListForSelect(@RequestParam String flag){
        var roleDto = new RoleDto();
        roleDto.setFlag(flag);
        return roleService.getListForSelect(roleDto);
    }

    /**
     *  获取平台所有角色列表（除咨询师=2和家属=4）：select2
     * @return 平台角色集合
     */
    @RequestMapping(value = "/get_for_select_all",method = RequestMethod.POST)
    @ResponseBody
    public Object getListForSelectAll() {
        return roleService.getListForSelectAll();
    }
}

package cn.psycloud.psyplatform.controller.platform;

import cn.psycloud.psyplatform.dto.platform.DashboardDto;
import cn.psycloud.psyplatform.service.platform.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *  系统数据看板
 */
@RequestMapping("/platform/dashboard")
@Controller
public class DashboardController {
    @Autowired
    private DashboardService dashboardService;

    /**
     *  系统数据看板页面
     * @return 视图
     */
    @RequestMapping("/index")
    public String index(){
        return "platform/dashboard/index";
    }

    /**
     *  获取系统数据看板数据
     * @return 系统数据看板数据
     */
    @RequestMapping(value = "/get_dashboard_data",method = RequestMethod.POST)
    @ResponseBody
    public Object getDashboardData(@RequestBody DashboardDto dashboardDto){
        return dashboardService.getDashboardData(dashboardDto);
    }
}

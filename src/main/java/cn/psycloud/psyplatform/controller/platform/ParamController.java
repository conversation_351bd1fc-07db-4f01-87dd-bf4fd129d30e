package cn.psycloud.psyplatform.controller.platform;

import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.platform.SmsConfigEntity;
import cn.psycloud.psyplatform.entity.platform.SysConfigEntity;
import cn.psycloud.psyplatform.service.platform.SmsConfigService;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 *  平台参数
 */
@Controller
@RequestMapping("/platform/param")
public class ParamController {
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private SmsConfigService smsConfigService;

    /**
     *  平台参数页面
     * @return 视图
     */
    @RequestMapping("/index")
    public ModelAndView index(){
        var mv = new ModelAndView();
        mv.addObject("sysConfig",sysConfigService.get());
        mv.setViewName("platform/param/index");
        return mv;
    }

    /**
     *  保存系统参数配置
     * @param entity 系统参数配置实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/index",method = RequestMethod.POST)
    @ResponseBody
    public  Object index(@RequestBody SysConfigEntity entity){
        var result = new JsonResult<>();
        if(sysConfigService.update(entity)>0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  保存短信配置
     * @param entity 短信配置实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/save_sms_config", method = RequestMethod.POST)
    @ResponseBody
    public  Object saveSmsConfig(@RequestBody SmsConfigEntity entity){
        var result = new JsonResult<>();
        if(smsConfigService.saveConfig(entity)>0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

}

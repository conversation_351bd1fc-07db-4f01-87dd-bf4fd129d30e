package cn.psycloud.psyplatform.controller.archiveroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.archiveroom.ArchiveAdviceDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity;
import cn.psycloud.psyplatform.service.archiveroom.ArchiveAdviceService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  评语管理
 */
@RequestMapping("/archiveroom/archiveadvice")
@Controller
public class ArchiveAdviceController {
    @Autowired
    private ArchiveAdviceService archiveAdviceService;

    @GetMapping("/list")
    public String list(HttpServletRequest request) {
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions
                .stream()
                .filter(a->("010703".equals(a.getFunctionCode()) || "010703".equals(a.getParentCode())))
                .collect(Collectors.toList());;
        boolean canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("01070301".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //修改权限
            if ("01070302".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //删除权限
            if ("01070303".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        return "archiveroom/archiveAdvice/list";
    }

    /**
     *  查询评语集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByPaged(@RequestBody ArchiveAdviceDto dto){
        return archiveAdviceService.getListByPaged(dto);
    }

    /**
     *  添加评语
     * @param entity 实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method=RequestMethod.POST)
    @ResponseBody
    public Object add(@RequestBody AdviceEntity entity){
        var result = new JsonResult<>();
        if(archiveAdviceService.insert(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改评语
     * @param entity 实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/update",method=RequestMethod.POST)
    @ResponseBody
    public Object update(@RequestBody AdviceEntity entity){
        var result = new JsonResult<>();
        if(archiveAdviceService.update(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  删除
     * @param id 评语id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/delete",method=RequestMethod.POST)
    @ResponseBody
    public Object delete(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(archiveAdviceService.delete(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除
     * @param ids ID集合
     * @return 结果实体对象
     */
    @RequestMapping(value = "/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(archiveAdviceService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

package cn.psycloud.psyplatform.controller.archiveroom;

import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.archiveroom.ArchiveConditionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.service.archiveroom.ArchiveService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/archiveroom/archive")
@Controller
public class ArchiveController {
    @Autowired
    private ArchiveService archiveService;
    @Value("${file.location}")
    String uploadPath;

    /**
     *  档案生成
     * @return 视图
     */
    @GetMapping("/create")
    public String create(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions
                .stream()
                .filter(a->("010701".equals(a.getFunctionCode()) || "010701".equals(a.getParentCode())))
                .collect(Collectors.toList());;
        boolean canCreate = false, canDownload = false;
        for (SysFunctionDto privilege: privilegeList){
            //生成
            if ("01070101".equals(privilege.getFunctionCode())) {
                canCreate = true;
            }
            //下载
            if ("01070102".equals(privilege.getFunctionCode())) {
                canDownload = true;
            }
        }
        request.setAttribute("canCreate",canCreate);
        request.setAttribute("canDownload",canDownload);
        return "archiveroom/archive/create";
    }

    /**
     *  档案生成
     * @param dto 条件
     * @return 用户集合
     */
    @RequestMapping(value = "/create",method= RequestMethod.POST)
    @ResponseBody
    public Object create(HttpServletResponse response,HttpServletRequest request, @RequestBody ArchiveConditionDto dto){
        return JSONUtil.toJsonStr(archiveService.createArchive(response,request,dto));
    }

    /**
     *  批量导出测评报告(word)
     * @param zipFolderName 压缩文件所在的文件夹名
     * @return 文件路径
     */
    @GetMapping ("/batch_download_archive")
    @ResponseBody
    public Object batchDownloadArchive(@RequestParam String zipFolderName) {
        var result = new JsonResult<>();
        if(!"".equals(zipFolderName)){
            String sourcePath = uploadPath + "/archive/" + zipFolderName;
            ZipUtil.zip(sourcePath);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(zipFolderName+ ".zip");
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
        }
        return result;
    }

    /**
     *  档案查询
     * @return 视图
     */
    @GetMapping(value = "/get")
    @ResponseBody
    public Object getUserArchiveContent(@RequestParam Integer userId) {
        return archiveService.getArchiveContentByUserId(userId);
    }
}

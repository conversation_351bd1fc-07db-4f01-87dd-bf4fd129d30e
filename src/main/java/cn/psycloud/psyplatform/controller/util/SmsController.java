package cn.psycloud.psyplatform.controller.util;

import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.platform.SendSmsDto;
import cn.psycloud.psyplatform.dto.platform.SmsSendRecordDto;
import cn.psycloud.psyplatform.dto.platform.VerifySmsCodeDto;
import cn.psycloud.psyplatform.service.platform.SmsSendRecordService;
import cn.psycloud.psyplatform.util.RSAUtil;
import cn.psycloud.psyplatform.util.sms.aliyun.SendSmsService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 *  SMS短信
 */
@RequestMapping("/sms")
@Controller
public class SmsController {
    @Autowired
    private SendSmsService sendSmsService;
    @Autowired
    private SmsSendRecordService smsSendRecordService;
    /**
     *  发送短信
     * @param dto 短信实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/send",method= RequestMethod.POST)
    @ResponseBody
    public Object sendSms(@RequestBody SendSmsDto dto){
        var result = new JsonResult<>();
        try {
            dto.setPhoneNumber(RSAUtil.decrypt(dto.getPhoneNumber()));
            //验证码短信
            if(dto.getSmsType() == 1) {
                result = sendSmsService.sendSms(dto);
            }
            //通知短信
            if(dto.getSmsType() == 2){
                sendSmsService.sendNotify(dto);
            }
        }
        catch (Exception e) {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(e.getMessage());
        }
        return result;
    }

    /**
     *  短信验证
     * @return  结果实体对象
     */
    @RequestMapping(value = "/verify",method=RequestMethod.POST)
    @ResponseBody
    public Object verifySmsCode(@RequestParam String phoneNumber, @RequestParam String templateCode, @RequestParam String smsCode){
        var result = new JsonResult<>();
        var verifySmsCodeDto = new VerifySmsCodeDto();
        verifySmsCodeDto.setPhoneNumber(phoneNumber);
        verifySmsCodeDto.setTemplateCode(templateCode);
        verifySmsCodeDto.setSmsCode(smsCode);
        if(sendSmsService.verifySmsCode(verifySmsCodeDto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    @GetMapping("/list")
    public String getRecordList(){
        return "platform/sms/recordList";
    }

    /**
     *  查询短信发送记录集合：分页
     * @param dto 短信发送记录实体对象
     * @return BootstrapDatatables格式的对象
     */
    @RequestMapping(value = "/list",method= RequestMethod.POST)
    @ResponseBody
    public Object getListByPaged(@RequestBody SmsSendRecordDto dto){
        return smsSendRecordService.getListByPaged(dto);
    }
}

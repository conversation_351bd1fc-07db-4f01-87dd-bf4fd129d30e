package cn.psycloud.psyplatform.controller.util;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.CkeditorData;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.activityroom.ActivityClockingPicEntity;
import cn.psycloud.psyplatform.entity.activityroom.ActivityPicEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityClockingPicService;
import cn.psycloud.psyplatform.service.activityroom.ActivityPicService;
import cn.psycloud.psyplatform.util.FileHelper;
import lombok.var;
import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.geometry.Positions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.Date;

/**
 *  文件上传
 */
@Controller
@RequestMapping("/fileUpload")
public class FileUploadController {
    @Autowired
    private FileHelper fileHelper;
    @Autowired
    private ActivityClockingPicService activityClockingPicService;
    @Autowired
    private ActivityPicService activityPicService;
    @Value("${file.location}")
    String uploadPath;
    /**
     *  ckeditor 文件上传
     * @param file 文件
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/ckEditor",method = RequestMethod.POST)
    @ResponseBody
    public Object ckEditorFileUpload(@RequestPart("upload") MultipartFile file, HttpServletRequest request){
        var result = new CkeditorData();
        String fileName = fileHelper.fileUpload(file,"ckeditor",uploadPath, request);
        var path = "/static/upload/ckeditor/" +fileName;
        result.setUploaded(true);
        result.setUrl(path);
        return result;
    }

    /**
     *  常规文件上传
     * @param file 文件
     * @param fileType 上传的文件夹
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/general",method = RequestMethod.POST)
    @ResponseBody
    public Object generalFileUpload(@RequestParam("file") MultipartFile file, @RequestParam String fileType, HttpServletRequest request){
        var result = new JsonResult<>();
        var fileName = fileHelper.fileUpload(file,fileType, uploadPath, request);
        try {
            String sourcePath = uploadPath + "/"+fileType + "/" + fileName;
            String targetPath = uploadPath + "/"+fileType + "/thumbnail/" + fileName;
            switch (fileType) {
                case "avatar":
                    Thumbnails.of(sourcePath)
                            .sourceRegion(Positions.CENTER, 200, 200)
                            .size(128, 128)
                            .keepAspectRatio(false)
                            .toFile(targetPath);
                    break;
                case "music_cover":
                    Thumbnails.of(sourcePath)
                            .size(500,333)
                            .toFile(targetPath);
                    break;
                case "scale":
                case "camp_cover":
                case "activity_cover":
                case "caremap":
                case "course_cover":
                    Thumbnails.of(sourcePath)
                            .size(380,330)
                            .toFile(targetPath);
                    break;
                case "counseling":
                case "article":
                    String targetPath_large = uploadPath + "/"+fileType + "/thumbnail/large/" + fileName;
                    String targetPath_small = uploadPath + "/"+fileType + "/thumbnail/small/" + fileName;
                    Thumbnails.of(sourcePath)
                            .size(1000,600)
                            .outputQuality(0.5)
                            .toFile(targetPath_large);
                    Thumbnails.of(sourcePath)
                            .size(380,330)
                            .outputQuality(0.5)
                            .toFile(targetPath_small);
                    break;
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(fileName);
        return result;
    }

    /**
     *  上传签到图片
     * @param files 文件
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/clocking_pic",method = RequestMethod.POST)
    @ResponseBody
    public Object clockingFileUpload(@RequestParam("file") MultipartFile[] files, HttpServletRequest request,@RequestParam Integer structId){
        var result = new JsonResult<>();
        try{
            for (MultipartFile file : files) {
                var fileName = fileHelper.fileUpload(file,"clocking", uploadPath, request);
                String sourcePath = uploadPath + "/clocking/"+fileName;
                String mobilePath = uploadPath + "/clocking/mobile";
                File mobileDir = new File(mobilePath);
                if(!mobileDir.exists()){
                    mobileDir.mkdirs();
                }
                Thumbnails.of(sourcePath)
                        .width(480)           // 按宽度等比缩放
                        .outputQuality(0.6)   // 更高压缩比
                        .toFile(mobilePath + "/"+fileName);

                ActivityClockingPicEntity clockingPicEntity = new ActivityClockingPicEntity();
                clockingPicEntity.setStructId(structId);
                clockingPicEntity.setFileName(fileName);
                clockingPicEntity.setUploadTime(new Date());
                UserDto userDto = (UserDto) request.getSession().getAttribute("user");
                clockingPicEntity.setOperator(userDto.getUserId());
                activityClockingPicService.insert(clockingPicEntity);
            }
        }
        catch (Exception e){
            e.printStackTrace();
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        return result;
    }

    /**
     *  活动图片上传
     * @param files 文件
     * @param activityId 活动ID
     * @param request 请求
     * @return 结果实体对象
     */
    @RequestMapping(value = "/activity_pic",method = RequestMethod.POST)
    @ResponseBody
    public Object ActivityPicFileUpload(@RequestParam("file") MultipartFile[] files,@RequestParam Integer activityId, HttpServletRequest request){
        var result = new JsonResult<>();
        try{
            for (MultipartFile file : files) {
                var fileName = fileHelper.fileUpload(file,"activity", uploadPath, request);
                String sourcePath = uploadPath + "/activity/"+fileName;
                String mobilePath = uploadPath + "/activity/mobile";
                File mobileDir = new File(mobilePath);
                if(!mobileDir.exists()){
                    mobileDir.mkdirs();
                }
                Thumbnails.of(sourcePath)
                        .width(480)           // 按宽度等比缩放
                        .outputQuality(0.6)   // 更高压缩比
                        .toFile(mobilePath + "/"+fileName);

                ActivityPicEntity activityPicEntity = new ActivityPicEntity();
                activityPicEntity.setActivityId(activityId);
                activityPicEntity.setFileName(fileName);
                activityPicEntity.setUploadTime(new Date());
                UserDto userDto = (UserDto) request.getSession().getAttribute("user");
                activityPicEntity.setOperator(userDto.getUserId());
                activityPicService.uploadPic(activityPicEntity);
            }
        }
        catch (Exception e){
            e.printStackTrace();
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        return result;
    }
}

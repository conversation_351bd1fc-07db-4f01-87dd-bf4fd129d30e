package cn.psycloud.psyplatform.controller.comment;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.comment.CommentDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.comment.CommentEntity;
import cn.psycloud.psyplatform.service.comment.CommentService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/comment/comment")
@Controller
public class CommentController {
    @Autowired
    private CommentService commentService;

    /**
     *  发表评论
     * @param entity 评论实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/add",method= RequestMethod.POST)
    @ResponseBody
    public Object addComment(@RequestBody CommentEntity entity){
        var result = new JsonResult<>();
        if(commentService.addComment(entity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.CommentSuccess);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  批量删除评论
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/batch_del",method=RequestMethod.POST)
    @ResponseBody
    public Object batchDelComments(@RequestParam String ids){
        var result = new JsonResult<>();
        if(commentService.batchDelComments(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  评价管理
     * @param request 请求
     * @return 视图
     */
    @GetMapping("/list")
    public String getCommentList(HttpServletRequest request){
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("011201".equals(a.getFunctionCode()) || "011201".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canCheck = false,  canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //删除权限
            if ("01120101".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //审核权限
            if ("01120102".equals(privilege.getFunctionCode())) {
                canCheck = true;
            }
        }
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canCheck",canCheck);
        return "comment/list";
    }

    /**
     *  获取训练营评论集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @RequestMapping(value = "/get_list",method= RequestMethod.POST)
    @ResponseBody
    public Object getCommentListByPaged(@RequestBody CommentDto dto){
        return commentService.getCommentListByPaged(dto);
    }

    /**
     *  批量审核评论
     * @param ids id集合
     * @return 结果实体对象
     */
    @RequestMapping(value="/check",method=RequestMethod.POST)
    @ResponseBody
    public Object checkComments(@RequestParam String ids){
        var result = new JsonResult<>();
        if(commentService.checkComment(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

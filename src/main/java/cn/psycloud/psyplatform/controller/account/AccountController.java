package cn.psycloud.psyplatform.controller.account;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dto.anteroom.LoginDto;
import cn.psycloud.psyplatform.dto.anteroom.ModifyPwdDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.CommonData;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.anteroom.UserTokenEntity;
import cn.psycloud.psyplatform.enums.LoginWay;
import cn.psycloud.psyplatform.service.anteroom.StructsService;
import cn.psycloud.psyplatform.service.anteroom.UserSecurityService;
import cn.psycloud.psyplatform.service.anteroom.UserService;
import cn.psycloud.psyplatform.service.anteroom.UserTokenService;
import cn.psycloud.psyplatform.service.platform.UserLoginLogService;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.CookieHelper;
import cn.psycloud.psyplatform.util.RSAUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 *  登录账户
 */
@Controller
@RequestMapping("/account")
public class AccountController {
    @Autowired
    private UserService userService;
    @Autowired
    private UserTokenService userTokenService;
    @Autowired
    private StructsService structsService;
    @Autowired
    private UserLoginLogService userLoginLogService;
    @Autowired
    private UserSecurityService userSecurityService;
    @Value("${platform.auditlog}")
    private boolean isAuditLogEnabled;
    @Value("${platform.password-forced-expire.enabled}")
    private boolean isCheckPwdExpire;
    @Value("${spring.redis.isEnabled}")
    private boolean isRedisEnabled;
    @Value("${platform.login-attempt.enabled}")
    private boolean isLoginAttempt;
    /**
     * 登录页
     * @return 登录视图
     */
    @RequestMapping(value = "/login")
    public String login() {
        return "account/login";
    }

    /**
     * 账号密码登录
     * @param dto 登录实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ResponseBody
    public Object login(HttpServletResponse response,HttpServletRequest request, @RequestBody LoginDto dto) {
        var result = new JsonResult<>();
        //先判定账号是否已锁定
        if(isRedisEnabled && isLoginAttempt && userSecurityService.isAccountLocked(dto.getLoginName())){
            result.setResultCode(ResultCodeAndMsg.ACCOUNT_LOCKED_CODE);
            result.setResultMsg(ResultCodeAndMsg.ACCOUNT_LOCKED_MSG);
        }
        else{
            try {
                String decryptedPassword = RSAUtil.decrypt(dto.getPwd());
                dto.setPwd(decryptedPassword);
                var originalPwd = dto.getPwd();
                var userDto = userService.signIn(dto);
                if(userDto== null || userDto.getUserId() == null) { //登录失败
                    if(isRedisEnabled && isLoginAttempt){
                        var leftAttempt =  userSecurityService.incrementLoginAttempts(dto.getLoginName());
                        if(leftAttempt == 0){
                            result.setResultCode(ResultCodeAndMsg.ACCOUNT_LOCKED_CODE);
                            result.setResultMsg(ResultCodeAndMsg.ACCOUNT_LOCKED_MSG);
                        }
                        else{
                            result.setResultCode(ResultCodeAndMsg.LOGIN_ATTEMPT);
                            result.setData(leftAttempt);
                        }
                    }
                    else{
                        result.setResultCode(ResultCodeAndMsg.LoginPasswordOrUserNameErrorCode);
                        result.setResultMsg(ResultCodeAndMsg.LoginPasswordOrUserNameErrorMsg);
                    }
                }
                else {
                    if(isRedisEnabled && isLoginAttempt){
                        userSecurityService.clearLoginAttempts(dto.getLoginName());
                    }

                    if(isCheckPwdExpire && userService.checkPasswordExpiry(userDto.getUserId())){//检测密码是否过期
                        result.setResultCode(ResultCodeAndMsg.PASSWORD_EXPIRE_CODE);
                        result.setResultMsg(ResultCodeAndMsg.PASSWORD_EXPIRE_MSG);
                        result.setData(userDto.getUserId());
                    }
                    else{
                        request.getSession().setAttribute("user", userDto);

                        var tokenCookie = CookieHelper.getCookie(request, "token");
                        if(tokenCookie != null) {
                            userTokenService.deleteByToken(tokenCookie);
                            CookieHelper.removeCookie(request, response, "token");
                        }
                        String token = DigestUtil.md5Hex(CommonHelper.getGUID());
                        Integer day = dto.getIsRemember() == 1 ? 30 : CommonData.DEFAULT_COOKIE_EXPIRES;
                        CookieHelper.setCookie(response, "token", token , day);

                        userTokenService.deleteByLoginName(userDto.getLoginName());
                        var userToken = new UserTokenEntity();
                        userToken.setLoginName(userDto.getLoginName());
                        userToken.setToken(token);
                        userToken.setIpAddress(CommonHelper.getClinetIP(request));
                        userTokenService.insertToken(userToken);

                        var loginMap = new HashMap<String, String>();
                        loginMap.put("lastLoginDate", CommonHelper.getCurrentDate());
                        loginMap.put("lastLoginIp", CommonHelper.getClinetIP(request));
                        loginMap.put("userId", userDto.getUserId().toString());
                        userService.updateLoginDateAndIp(loginMap);

                        if(CommonData.DEFAULT_PASSWORD.equals(originalPwd)){
                            result.setResultCode(ResultCodeAndMsg.PASSWORD_NEED_CHANGE_CODE);
                            result.setResultMsg(ResultCodeAndMsg.PASSWORD_NEED_CHANGE_MSG);
                        }
                        else{
                            result.setResultCode(ResultCodeAndMsg.SuccessCode);
                            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
                        }
                        if(isAuditLogEnabled){
                            //保存登录日志
                            var loginLogEntity = CommonHelper.getUserLoginLog(request);
                            loginLogEntity.setLoginName(dto.getLoginName());
                            loginLogEntity.setState((result.getResultCode() ==100 ) ? 0:1);
                            loginLogEntity.setLoginWay(LoginWay.ACCOUNT.ordinal());//登录方式为账号密码登录
                            userLoginLogService.add(loginLogEntity);
                        }
                    }
                }
            }
            catch (Exception e) {
                result.setResultCode(ResultCodeAndMsg.LoginPasswordOrUserNameErrorCode);
                result.setResultMsg(ResultCodeAndMsg.LoginPasswordOrUserNameErrorMsg);
            }
        }
        return result;
    }

    /**
     *  注销登录
     * @return 结果实体对象
     */
    @RequestMapping(value = "/logout",method = RequestMethod.POST)
    @ResponseBody
    public  Object logout(HttpServletResponse response,HttpServletRequest request){
        var result = new JsonResult<>();
        request.getSession().invalidate();
        var tokenCookie = CookieHelper.getCookie(request,"token");
        userTokenService.deleteByToken(tokenCookie);
        CookieHelper.removeCookie(request,response,"token");
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        return result;
    }

    /**
     *  验证手机号码是否可用
     * @param mobile 手机号码
     * @return 结果实体对象
     */
    @RequestMapping(value = "/check_mobile",method=RequestMethod.POST)
    @ResponseBody
    public Object checkMobile(@RequestParam String mobile){
        var result = new JsonResult<>();
        if(userService.checkMobile(mobile) > 0){
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.CheckMobileExists);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.CheckMobileNoExists);
        }
        return result;
    }

    /**
     *  手机短信验证码登录
     * @param mobile 手机号码
     * @return 结果实体对象
     */
    @RequestMapping(value = "/smsLogin",method=RequestMethod.POST)
    @ResponseBody
    public Object smsLogin(HttpServletResponse response,HttpServletRequest request,@RequestParam String mobile){
        var result = new JsonResult<>();
        var userDto = userService.smsLogin(mobile);
        if(userDto== null || userDto.getUserId() == null) {
            result.setResultCode(ResultCodeAndMsg.LoginPasswordOrUserNameErrorCode);
            result.setResultMsg(ResultCodeAndMsg.LoginPasswordOrUserNameErrorMsg);
        }
        else {
            request.getSession().setAttribute("user", userDto);

            var tokenCookie = CookieHelper.getCookie(request, "token");
            if(tokenCookie != null) {
                userTokenService.deleteByToken(tokenCookie);
                CookieHelper.removeCookie(request, response, "token");
            }
            String token = DigestUtil.md5Hex(CommonHelper.getGUID());
            CookieHelper.setCookie(response, "token", token , 30);

            userTokenService.deleteByLoginName(userDto.getLoginName());
            var userToken = new UserTokenEntity();
            userToken.setLoginName(userDto.getLoginName());
            userToken.setToken(token);
            userToken.setIpAddress(CommonHelper.getClinetIP(request));
            userTokenService.insertToken(userToken);

            Map<String, String> loginMap = new HashMap<>();
            loginMap.put("lastLoginDate", CommonHelper.getCurrentDate());
            loginMap.put("lastLoginIp", CommonHelper.getClinetIP(request));
            loginMap.put("userId", userDto.getUserId().toString());
            userService.updateLoginDateAndIp(loginMap);

            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        if(isAuditLogEnabled){
            //保存登录日志
            var loginLogEntity = CommonHelper.getUserLoginLog(request);
            loginLogEntity.setLoginName(userDto.getLoginName());
            loginLogEntity.setState((result.getResultCode() ==100 ) ? 0:1);
            loginLogEntity.setLoginWay(LoginWay.MOBILE.ordinal());
            userLoginLogService.add(loginLogEntity);
        }
        return result;
    }

    @RequestMapping(value = "/mobileLogin",method=RequestMethod.POST)
    @ResponseBody
    public Object mobileLogin(HttpServletResponse response,HttpServletRequest request,@RequestParam String mobile){
        var result = new JsonResult<>();
        var userDto = userService.mobileLogin(mobile);
        if(userDto== null || userDto.getUserId() == null) {
            result.setResultCode(ResultCodeAndMsg.LoginPasswordOrUserNameErrorCode);
            result.setResultMsg(ResultCodeAndMsg.LoginPasswordOrUserNameErrorMsg);
        }
        else {
            request.getSession().setAttribute("user", userDto);
            var tokenCookie = CookieHelper.getCookie(request, "token");
            if(tokenCookie != null) {
                userTokenService.deleteByToken(tokenCookie);
                CookieHelper.removeCookie(request, response, "token");
            }
            String token = DigestUtil.md5Hex(CommonHelper.getGUID());
            CookieHelper.setCookie(response, "token", token , 30);

            userTokenService.deleteByLoginName(userDto.getLoginName());
            var userToken = new UserTokenEntity();
            userToken.setLoginName(userDto.getLoginName());
            userToken.setToken(token);
            userToken.setIpAddress(CommonHelper.getClinetIP(request));
            userTokenService.insertToken(userToken);

            Map<String, String> loginMap = new HashMap<>();
            loginMap.put("lastLoginDate", CommonHelper.getCurrentDate());
            loginMap.put("lastLoginIp", CommonHelper.getClinetIP(request));
            loginMap.put("userId", userDto.getUserId().toString());
            userService.updateLoginDateAndIp(loginMap);

            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        if(isAuditLogEnabled){
            //保存登录日志
            var loginLogEntity = CommonHelper.getUserLoginLog(request);
            loginLogEntity.setLoginName(userDto.getLoginName());
            loginLogEntity.setState((result.getResultCode() ==100 ) ? 0:1);
            loginLogEntity.setLoginWay(LoginWay.MOBILE.ordinal());
            userLoginLogService.add(loginLogEntity);
        }
        return result;
    }

    /**
     *  注册
     * @return 视图
     */
    @RequestMapping(value = "/signup")
    public String signUp(){
        return "account/signup";
    }

    /**
     *  注册
     * @param dto 用户实体对象
     * @return 结果实体对象
     */
    @RequestMapping(value = "/signup",method=RequestMethod.POST)
    @ResponseBody
    public Object signUp(@RequestBody UserDto dto){
        var result = new JsonResult<>();
        if(userService.addUser(dto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  获取组织
     * @return 集合
     */
    @RequestMapping(value = "/get_structs",method=RequestMethod.POST)
    @ResponseBody
    public Object getStructs(){
        return JSONUtil.toJsonStr(structsService.getListForZtree(0,false));
    }

    /**
     *  验证用户名是否存在
     * @param loginName 用户名
     * @return 是否存在
     */
    @RequestMapping(value = "/checkLoginName",method=RequestMethod.POST)
    @ResponseBody
    public Object checkLoginName(@RequestParam String loginName){
        return userService.isLoginNameExist(loginName);
    }

    /**
     *  根据用户id验证用户密码
     * @param originalPwd 密码
     * @param userId 用户id
     * @return 结果实体对象
     */
    @RequestMapping(value = "/verify_pwd_by_userId",method = RequestMethod.POST)
    @ResponseBody
    public Object verifyPwdByUserId(@RequestParam String originalPwd, @RequestParam Integer userId){
        var result = new JsonResult<>();
        var modifyPwdDto = new ModifyPwdDto();
        modifyPwdDto.setOriginalPwd(originalPwd);
        modifyPwdDto.setUserId(userId);
        if(userService.verifyPassword(modifyPwdDto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  修改密码
     * @return 结果实体对象
     */
    @RequestMapping(value = "/modifypwd",method=RequestMethod.POST)
    @ResponseBody
    public Object modifyPwd(@RequestParam String originalPwd,@RequestParam String newPwd, @RequestParam Integer userId){
        var result = new JsonResult<>();
        var modifyPwdDto = new ModifyPwdDto();
        modifyPwdDto.setOriginalPwd(originalPwd);
        modifyPwdDto.setNewPwd(newPwd);
        modifyPwdDto.setUserId(userId);
        if(userService.modifyPasswordByOriginalPwd(modifyPwdDto)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    @GetMapping("/getPublicKey")
    @ResponseBody
    public Object getPublicKey(){
        var result = new JsonResult<>();
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        result.setData(RSAUtil.getPublicKey());
        return result;
    }

    /**
     *  绑定微信用户
     * @param loginName 用户名
     * @param realName 真实姓名
     * @return 结果实体对象
     */
    @RequestMapping(value = "/bind_loginname",method=RequestMethod.POST)
    @ResponseBody
    public Object bindLoginName(@RequestParam String loginName, @RequestParam String realName, HttpServletResponse response, HttpServletRequest request){
        var result = new JsonResult<>();
        var userDto = userService.bindWechatUser(loginName,realName);
        if(userDto != null && userDto.getUserId() > 0){
            request.getSession().setAttribute("user", userDto);

            var tokenCookie = CookieHelper.getCookie(request, "token");
            if(tokenCookie != null) {
                userTokenService.deleteByToken(tokenCookie);
                CookieHelper.removeCookie(request, response, "token");
            }
            String token = DigestUtil.md5Hex(CommonHelper.getGUID());
            CookieHelper.setCookie(response, "token", token , 30);

            userTokenService.deleteByLoginName(userDto.getLoginName());
            var userToken = new UserTokenEntity();
            userToken.setLoginName(userDto.getLoginName());
            userToken.setToken(token);
            userToken.setIpAddress(CommonHelper.getClinetIP(request));
            userTokenService.insertToken(userToken);

            Map<String, String> loginMap = new HashMap<>();
            loginMap.put("lastLoginDate", CommonHelper.getCurrentDate());
            loginMap.put("lastLoginIp", CommonHelper.getClinetIP(request));
            loginMap.put("userId", userDto.getUserId().toString());
            userService.updateLoginDateAndIp(loginMap);
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}
package cn.psycloud.psyplatform.controller.app;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.service.activityroom.ActivityService;
import cn.psycloud.psyplatform.service.activityroom.SelfEvaluationService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

/**
 *  H5端活动页
 */
@RequestMapping("/app/activity")
@Controller
public class AppActivityController {
    @Autowired
    private ActivityService activityService;
    @Autowired
    private SelfEvaluationService selfEvaluationService;

    /**
     *  我的活动页
     * @return 视图
     */
    @GetMapping("/my_activities")
    public ModelAndView myActivity(HttpServletRequest request){
        var mv = new ModelAndView();
        UserDto userDto = (UserDto) request.getSession().getAttribute("user");
        var myActivities = activityService.getMyActivities(userDto.getUserId());
        mv.addObject("myActivities", myActivities);
        mv.setViewName("app/activity/myActivities");
        return mv;
    }

    /**
     *  活动详情页
     * @param activityId 活动ID
     * @return 视图
     */
    @GetMapping("/detail")
    public ModelAndView detail(@RequestParam("activityId") Integer activityId, HttpServletRequest request){
        var mv = new ModelAndView();
        var activityDto = activityService.getForDetail(activityId);
        mv.addObject("activity", activityDto);

        UserDto userDto = (UserDto) request.getSession().getAttribute("user");
        mv.addObject("roleId", userDto.getRole().getRoleId());
        mv.setViewName("app/activity/detail");
        return mv;
    }

    /**
     *  活动图片页
     * @param activityId 活动ID
     * @return 视图
     */
    @GetMapping("/gallery")
    public ModelAndView gallery(@RequestParam("activityId") Integer activityId){
        var mv = new ModelAndView();
        var activityDto = activityService.getById(activityId);
        mv.addObject("activity", activityDto);
        mv.setViewName("app/activity/gallery");
        return mv;
    }

    /**
     *  更新活动总评页
     * @return 视图
     */
    @GetMapping("/update_overall_evaluation")
    public ModelAndView updateOverallEvaluation(@RequestParam Integer activityId){
        var mv = new ModelAndView();
        var overallEvaluation = activityService.getOverallEvaluation(activityId);
        mv.addObject("overallEvaluation", overallEvaluation);
        mv.setViewName("app/activity/updateOverallEvaluation");
        return mv;
    }

    /**
     * 根据活动ID获取个人点评列表
     * @param activityId 活动ID
     * @return 视图
     */
    @GetMapping("/self_evaluation")
    public ModelAndView selfEvaluation(@RequestParam Integer activityId){
        var mv = new ModelAndView();
        var selfEvaluations = selfEvaluationService.getSelfEvaluationList(activityId);
        mv.addObject("selfEvaluations", selfEvaluations);
        mv.setViewName("app/activity/selfEvaluation");
        return mv;
    }
}

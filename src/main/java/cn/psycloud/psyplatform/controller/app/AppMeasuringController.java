package cn.psycloud.psyplatform.controller.app;

import cn.psycloud.psyplatform.dto.measuringroom.ScaleIDDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleTypeDto;
import cn.psycloud.psyplatform.service.measuringroom.ScaleService;
import cn.psycloud.psyplatform.service.measuringroom.ScaleTypeService;
import cn.psycloud.psyplatform.service.measuringroom.TaskService;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.survey.SurveyQuestionService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

@RequestMapping("/app/measuring")
@Controller
public class AppMeasuringController {
    @Autowired
    private ScaleTypeService scaleTypeService;
    @Autowired
    private ScaleService scaleService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private TestRecordService testRecordService;
    @Autowired
    private SurveyQuestionService surveyQuestionService;

    @GetMapping("/all")
    public ModelAndView all(){
        var mv = new ModelAndView();
        var scaleTypes = scaleTypeService.getList(new ScaleTypeDto());
        mv.addObject("scaleTypes",scaleTypes);
        mv.setViewName("app/measuring/all");
        return mv;
    }

    @GetMapping("/guide")
    public ModelAndView guide(@RequestParam Integer scaleId) {
        var mv = new ModelAndView();
        var scale = scaleService.getById(scaleId);
        mv.addObject("scale",scale);
        mv.setViewName("app/measuring/guide");
        return mv;
    }

    @GetMapping("/my_tasks")
    public String myTasks(){
        return "app/measuring/myTasks";
    }

    @GetMapping("/do_test")
    public String doTest(HttpServletRequest request){
        request.setAttribute("embu", ScaleIDDto.EMBU);
        request.setAttribute("aqy",ScaleIDDto.YDL_aqy);
        request.setAttribute("yys",ScaleIDDto.BKYYS);
        request.setAttribute("mbtis",ScaleIDDto.MBTIS);
        return "app/measuring/doTest";
    };

    @GetMapping("/report")
    public ModelAndView report(@RequestParam Integer recordId){
        var mv =new ModelAndView();
        var testRecordDto = testRecordService.getById(recordId);
        mv.addObject("testRecord", testRecordDto);
        mv.addObject("apmAndNineHouse", ScaleIDDto.APMAndNineHouse);
        mv.addObject("sds",ScaleIDDto.SDS);
        mv.addObject("pcm",ScaleIDDto.PCM);
        mv.addObject("epqc",ScaleIDDto.EPQC);
        mv.addObject("epqa",ScaleIDDto.EPQA);
        mv.addObject("zylx",ScaleIDDto.ZYLX);
        mv.addObject("nineHouse",ScaleIDDto.NineHouse);
        mv.addObject("sas",ScaleIDDto.SAS);
        mv.addObject("bdi",ScaleIDDto.BDI);
        mv.setViewName("app/measuring/report");
        return mv;
    }

    @GetMapping("/report_aqy")
    public String reportAqy(){
        return "app/measuring/report_aqy";
    }

    @GetMapping("/report_yys")
    public String reportYys(){
        return "app/measuring/report_yys";
    }

    @GetMapping("/my_records")
    public String myRecords(){
        return "app/measuring/myRecords";
    }

    @GetMapping("/scale")
    public ModelAndView scale(@RequestParam Integer taskId){
        var mv = new ModelAndView();
        var myTask = taskService.getById(taskId);
        var listQuestions = surveyQuestionService.getListBySurveyId(myTask.getSurveyId());
        mv.addObject("myTask",myTask);
        mv.addObject("isSurvey",myTask.getIsSurvey());
        mv.addObject("listQuestions", listQuestions);
        mv.setViewName("app/measuring/scale");
        return mv;
    }
}

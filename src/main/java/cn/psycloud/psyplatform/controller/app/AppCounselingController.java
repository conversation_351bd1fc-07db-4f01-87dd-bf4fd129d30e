package cn.psycloud.psyplatform.controller.app;

import cn.psycloud.psyplatform.service.anteroom.UserService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

@RequestMapping("/app/counseling")
@Controller
public class AppCounselingController {
    @Autowired
    private UserService userService;

    @GetMapping("/index")
    public ModelAndView index(){
        var mv = new ModelAndView();
        var counselors = userService.getRecommendCounselorList();
        mv.addObject("counselors",counselors);
        mv.setViewName("/app/counseling/index");
        return mv;
    }

    @GetMapping("/counselorinfo")
    public ModelAndView counselorInfo(@RequestParam Integer userId){
        var mv =new ModelAndView();
        var counselor = userService.getById(userId);
        mv.addObject("counselor",counselor);
        mv.setViewName("app/counseling/counselorinfo");
        return mv;
    }

    @GetMapping("/scheduling")
    public String scheduling(){
        return "app/counseling/scheduling";
    }

    @GetMapping("/order")
    public String order(){
        return "app/counseling/order";
    }

    @GetMapping("/order_record")
    public String orderRecord(){
        return "app/counseling/orderRecord";
    }

    @GetMapping("/handle")
    public String handle() {
        return "app/counseling/handle";
    }

    @GetMapping("/online")
    public String online(){
        return "app/counseling/online";
    }

    @GetMapping("/online_counseling")
    public String onlineCounseling(){
        return "app/counseling/onlineCounseling";
    }
}

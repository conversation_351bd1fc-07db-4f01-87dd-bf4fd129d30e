package cn.psycloud.psyplatform.controller.app;

import cn.psycloud.psyplatform.service.measuringroom.TaskService;
import cn.psycloud.psyplatform.service.survey.SurveyQuestionService;
import cn.psycloud.psyplatform.service.survey.SurveyService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

@RequestMapping("/app/survey")
@Controller
public class AppSurveyController {
    @Autowired
    private TaskService taskService;
    @Autowired
    private SurveyQuestionService surveyQuestionService;
    @Autowired
    private SurveyService surveyService;

    /**
     *  我的问卷调查任务
     * @return 视图
     */
    @GetMapping("/my_tasks")
    public String myTasks(){
        return "app/survey/myTasks";
    }

    /**
     *  我的问卷记录
     * @return 视图
     */
    @GetMapping("/my_records")
    public String myRecords(){ return "app/survey/myRecords"; }

    /**
     * 我的调查问卷列表
     * @return 视图
     */
    @GetMapping("/survey_list")
    public ModelAndView surveyList(@RequestParam Integer taskId){
        var mv = new ModelAndView();
        var myTask = taskService.getById(taskId);
        mv.addObject("myTask",myTask);
        mv.setViewName("app/survey/surveyList");
        return mv;
    }

    /**
     * 调查问卷作答
     * @param surveyId 问卷id
     * @return 视图
     */
    @GetMapping("/answer")
    public ModelAndView answer(@RequestParam Integer surveyId){
        var mv = new ModelAndView();
        var survey = surveyService.getById(surveyId);
        var listQuestions = surveyQuestionService.getListBySurveyId(surveyId);
        mv.addObject("survey",survey);
        mv.addObject("listQuestions", listQuestions);
        return mv;
    }
}

package cn.psycloud.psyplatform.controller.app;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequestMapping("/app/counseling")
@Controller
public class AppConsultationCaseController {
    @GetMapping("/add")
    public String add(){
        return "app/counseling/add";
    }

    @GetMapping("/update")
    public String update(){
        return "app/counseling/edit";
    }

    @GetMapping("/my_cases")
    public ModelAndView myCases(HttpServletRequest request){
        var mv = new ModelAndView();
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("010511".equals(a.getFunctionCode()) || "010511".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canView = false, canAdd = false, canUpdate = false, canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            if ("010511".equals(privilege.getFunctionCode())) {
                canView = true;
            }
            //添加权限
            if ("01051101".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            if ("01051102".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            if ("01051103".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        mv.addObject("canView", canView);
        mv.addObject("canAdd", canAdd);
        mv.addObject("canUpdate", canUpdate);
        mv.addObject("canDelete", canDelete);
        mv.setViewName("app/counseling/myCases");
        return mv;
    }
}

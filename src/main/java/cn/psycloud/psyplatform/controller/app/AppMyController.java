package cn.psycloud.psyplatform.controller.app;

import cn.psycloud.psyplatform.dto.anteroom.MailDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.service.anteroom.MailService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

@RequestMapping("/app/my")
@Controller
public class AppMyController {
    @Autowired
    private MailService mailService;

    @GetMapping("/index")
    public String index(){
        return "app/my/index";
    }

    @GetMapping("/edit_profile")
    public String editProfile(){
        return "app/my/editProfile";
    };

    @GetMapping("/mail")
    public ModelAndView mail(HttpServletRequest request){
        var mv = new ModelAndView();
        var userDto = (UserDto)request.getSession().getAttribute("user");
        var mailDto = new MailDto();
        mailDto.setToUser(userDto.getUserId());
        mv.addObject("mails",mailService.getList(mailDto));
        mv.setViewName("app/my/mail");
        return mv;
    };

    @GetMapping("/mail_detail")
    public ModelAndView mailDetail(@RequestParam Integer id){
        var mv = new ModelAndView();
        mv.addObject("mail",mailService.getById(id));
        mv.setViewName("app/my/mailDetail");
        return mv;
    };

    @GetMapping("/reset_password")
    public String resetPassword(){
        return "app/my/resetPassword";
    }

    @GetMapping("/upload_avatar")
    public String uploadAvatar(){
        return "app/my/uploadAvatar";
    }
}

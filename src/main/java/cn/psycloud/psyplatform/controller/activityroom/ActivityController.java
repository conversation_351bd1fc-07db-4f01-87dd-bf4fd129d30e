package cn.psycloud.psyplatform.controller.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.ActivityDto;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.activityroom.ActivityEntity;
import cn.psycloud.psyplatform.entity.activityroom.OverallEvaluationEntity;
import cn.psycloud.psyplatform.entity.activityroom.SelfEvaluationEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityService;
import cn.psycloud.psyplatform.service.activityroom.SelfEvaluationService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/activityroom/activity")
@Controller
public class ActivityController {
    @Autowired
    private ActivityService activityService;
    @Autowired
    private SelfEvaluationService selfEvaluationService;

    /**
     * 获取心理活动列表
     * @param request 请求
     * @return 视图
     */
    @RequestMapping(value = "/list")
    public String list(HttpServletRequest request) {
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("011302".equals(a.getFunctionCode()) || "011302".equals(a.getParentCode()) || "011301".equals(a.getFunctionCode())))
                .collect(Collectors.toList());
        boolean canAdd = false,
                canUpdate = false,
                canDelete = false,
                canDownloadQRCode = false,
                canViewPic = false,
                canViewSurveyRecord = false,
                canViewReport = false;
        for (SysFunctionDto privilege: privilegeList){
            //新增权限
            if ("011301".equals(privilege.getFunctionCode())) {
                canAdd = true;
            }
            //删除权限
            if ("01130201".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
            //修改权限
            if ("01130202".equals(privilege.getFunctionCode())) {
                canUpdate = true;
            }
            //下载二维码权限
            if ("01130203".equals(privilege.getFunctionCode())) {
                canDownloadQRCode = true;
            }
            if ("01130204".equals(privilege.getFunctionCode())) {
                canViewPic = true;
            }
            if ("01130205".equals(privilege.getFunctionCode())) {
                canViewSurveyRecord = true;
            }
            if ("01130206".equals(privilege.getFunctionCode())) {
                canViewReport = true;
            }
        }
        request.setAttribute("canAdd",canAdd);
        request.setAttribute("canUpdate",canUpdate);
        request.setAttribute("canDelete",canDelete);
        request.setAttribute("canDownloadQRCode",canDownloadQRCode);
        request.setAttribute("canViewPic",canViewPic);
        request.setAttribute("canViewSurveyRecord",canViewSurveyRecord);
        request.setAttribute("canViewReport",canViewReport);
        return "/activityroom/activity/list";
    }

    /**
     * 获取心理活动列表：分页
     * @param dto 查询条件
     * @return 心理活动集合
     */
    @PostMapping("/get_list_by_paged")
    @ResponseBody
    public Object getListByPaged(@RequestBody ActivityDto dto) {
        return activityService.getListByPaged(dto);
    }

    /**
     * 添加心理活动
     * @return 视图
     */
    @RequestMapping(value = "/add")
    public String addActivity(){
        return "/activityroom/activity/add";
    }

    /**
     * 添加心理活动
     * @param activityEntity 心理活动实体
     * @return 结果实体对象
     */
    @PostMapping("/add")
    @ResponseBody
    public Object addActivity(@RequestBody ActivityEntity activityEntity){
        var result = new JsonResult<>();
        if(activityService.add(activityEntity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 修改心理活动
     * @return 视图
     */
    @RequestMapping(value = "/update")
    public String updateActivity(){
        return "/activityroom/activity/update";
    }

    /**
     * 修改心理活动
     * @param activityEntity 心理活动实体
     * @return 结果实体对象
     */
    @PostMapping("/update")
    @ResponseBody
    public Object updateActivity(@RequestBody ActivityEntity activityEntity){
        var result = new JsonResult<>();
        if(activityService.update(activityEntity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 删除心理活动
     * @param ids id集合
     * @return 结果实体对象
     */
    @PostMapping("/batch_del")
    @ResponseBody
    public Object batchDel(@RequestParam String ids){
        var result = new JsonResult<>();
        if(activityService.batchDel(ids)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 根据ID获取心理活动详情
     * @param id 心理活动id
     * @return 心理活动实体
     */
    @GetMapping(value = "/get")
    @ResponseBody
    public Object getById(@RequestParam Integer id){
        return activityService.getById(id);
    }

    /**
     *  判断任务里的调查问卷是否完成
     * @param activityId 活动ID
     * @param surveyId 问卷ID
     * @param request 请求
     * @return 是否完成
     */
    @RequestMapping("/is_survey_done")
    @ResponseBody
    public Object isSurveyDone(@RequestParam Integer activityId,@RequestParam Integer surveyId,HttpServletRequest request){
        var result = new JsonResult<>();
        var user = (UserDto)request.getSession().getAttribute("user");
        if(activityService.isSurveyDone(user.getUserId(),activityId, surveyId)) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  更新活动总评
     * @param overallEvaluationEntity 总评实体
     * @return 是否成功
     */
    @RequestMapping(value = "/update_overall_evaluation",method = RequestMethod.POST)
    @ResponseBody
    public Object updateOverallEvaluation(@RequestBody OverallEvaluationEntity overallEvaluationEntity){
        var result = new JsonResult<>();
        if(activityService.updateOverallEvaluation(overallEvaluationEntity) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  获取活动总评
     * @param activityId 活动ID
     * @return 活动总评
     */
    @RequestMapping(value = "/get_overall_evaluation",method = RequestMethod.POST)
    @ResponseBody
    public Object getOverallEvaluation(@RequestParam Integer activityId){
        return activityService.getOverallEvaluation(activityId);
    }

    /**
     *  新增个人点评
     * @param entitys 个人点评实体
     * @return 是否成功
     */
    @PostMapping("/add_self_evaluation")
    @ResponseBody
    public Object addSelfEvaluation(@RequestBody List<SelfEvaluationEntity> entitys){
        var result = new JsonResult<>();
        if(selfEvaluationService.addSelfEvaluation(entitys)){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     *  活动报告页面
     * @return 活动报告
     */
    @GetMapping(value = "/report")
    public String activityReport(){
        return "/activityroom/activity/report";
    }

    /**
     *  获取活动报告
     * @param activityId 活动ID
     * @return 活动报告
     */
    @GetMapping(value = "/get_report")
    @ResponseBody
    public Object getActivityReport(@RequestParam Integer activityId){
        return activityService.getActivityReport(activityId);
    }

    /**
     *  查询咨询师的活动集合：select2
     * @return 活动集合
     */
    @RequestMapping(value = "/get_for_select",method= RequestMethod.POST)
    @ResponseBody
    public Object getListForSelect(HttpServletRequest request){
        var user = (UserDto)request.getSession().getAttribute("user");
        return activityService.getCounselorActivitiesForSelect(user.getUserId());
    }

    /**
     * 活动问卷结果统计页面
     * @param activityId 活动ID
     * @param surveyId 问卷ID
     * @return 视图
     */
    @GetMapping("/survey_stat")
    public String surveyStat(@RequestParam Integer activityId, @RequestParam Integer surveyId, Model model) {
        model.addAttribute("activityId", activityId);
        model.addAttribute("surveyId", surveyId);
        return "/activityroom/activity/survey_stat";
    }

    /**
     * 获取活动问卷结果统计数据
     * @param activityId 活动ID
     * @param surveyId 问卷ID
     * @return 统计数据
     */
    @PostMapping("/get_survey_stat")
    @ResponseBody
    public Object getActivitySurveyStat(@RequestParam Integer activityId, @RequestParam Integer surveyId) {
        return activityService.getActivitySurveyStat(activityId, surveyId);
    }

    /**
     * 活动数据看板页面
     * @return 视图
     */
    @RequestMapping(value = "/dashboard")
    public String dashboard() {
        return "/activityroom/activity/dashboard";
    }

    /**
     * 获取活动数据看板数据
     * @param dto 查询条件
     * @return 活动数据看板数据
     */
    @PostMapping("/get_dashboard_data")
    @ResponseBody
    public Object getDashboardData(@RequestBody ActivityDto dto) {
        return activityService.getActivityDashboard(dto);
    }
}

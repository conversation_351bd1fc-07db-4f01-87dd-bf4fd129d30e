package cn.psycloud.psyplatform.controller.activityroom;

import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.activityroom.ActivityPicEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityPicService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 活动图片管理
 */
@RequestMapping("/activityroom/activity_pic")
@Controller
public class ActivityPicController {
    @Autowired
    private ActivityPicService activityPicService;

    /**
     * 活动图片管理页面
     * @return 活动图片管理页面
     */
    @RequestMapping("/index")
    public String index() {
        return "activityroom/activity_pic/index";
    }

    /**
     * 获取活动图片列表
     * @param activityId 活动id
     * @return 活动图片列表
     */
    @PostMapping("/getPicList")
    @ResponseBody
    public Object getPicList(@RequestParam Integer activityId) {
        return activityPicService.getList(activityId);
    }

    /**
     * 上传活动图片
     * @param activityPicEntity 活动图片实体
     * @return 结果实体对象
     */
    @PostMapping("/uploadPic")
    @ResponseBody
    public Object uploadPic(@RequestBody ActivityPicEntity activityPicEntity) {
        var result = new JsonResult<>();
        if (activityPicService.uploadPic(activityPicEntity) > 0) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 删除活动图片
     * @param ids 活动图片集合ids
     * @return 结果实体对象
     */
    public Object batchDelPic(@RequestParam String ids) {
        var result = new JsonResult<>();
        if (activityPicService.batchDel(ids)) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }
}

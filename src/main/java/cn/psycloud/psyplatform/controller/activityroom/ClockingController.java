package cn.psycloud.psyplatform.controller.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.ActivityClockingPicDto;
import cn.psycloud.psyplatform.dto.activityroom.ClockingDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.entity.activityroom.ClockingEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityClockingPicService;
import cn.psycloud.psyplatform.service.activityroom.ActivityService;
import cn.psycloud.psyplatform.service.activityroom.ClockingService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

@RequestMapping("/activityroom/clocking")
@Controller
public class ClockingController {
    @Autowired
    private ClockingService clockingService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ActivityClockingPicService activityClockingPicService;

    /**
     * 添加签到签退记录
     * @param clockingEntity 签到签退实体
     * @return 结果实体对象
     */
    @PostMapping("/add_clocking_record")
    @ResponseBody
    public Object addClockingRecord(@RequestBody ClockingEntity clockingEntity){
        var result = new JsonResult<>();
        if(clockingService.addClockingRecord(clockingEntity) > 0){
            if(clockingEntity.getActionType() == 1){
                var picName = activityClockingPicService.getRandomPic();
                result.setResultCode(ResultCodeAndMsg.SuccessCode);
                result.setResultMsg(picName);
            }
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 查询签到签退记录是否存在
     * @param userId 用户ID
     * @param activityId 活动ID
     * @param actionType 操作类型：1-签到，2-签退
     * @return 是否签到签退
     */
    @GetMapping("/is_clocking_exists")
    @ResponseBody
    public Object isExists(@RequestParam Integer userId, @RequestParam Integer activityId, @RequestParam Integer actionType){
        return clockingService.isExists(userId, activityId, actionType);
    }

    /**
     * 签到签退记录列表页面
     * @param activityId 活动ID
     * @return 视图
     */
    @GetMapping("/list")
    public ModelAndView list(@RequestParam Integer activityId){
        var mv = new ModelAndView();
        var activity = activityService.getById(activityId);
        mv.addObject("activity", activity);
        mv.setViewName("activityroom/clocking/list");
        return mv;
    }

    /**
     * 查询签到签退记录列表
     * @param clockingDto 签到签退DTO
     * @return 签到签退实体列表
     */
    @PostMapping("/get_list_by_paged")
    @ResponseBody
    public Object getListByPaged(@RequestBody ClockingDto clockingDto){
        return clockingService.getListByPaged(clockingDto);
    }

    /**
     * 签到图片页面
     * @return 视图
     */
    @GetMapping("/clocking_pic")
    public String clockingPic(){
        return "activityroom/clocking/clockingPic";
    }

    /**
     * 查询签到图片列表
     * @return 签到图片列表
     */
    @PostMapping("/get_clocking_pic_list")
    @ResponseBody
    public Object getClockingPicList(@RequestBody ActivityClockingPicDto activityClockingPicDto){
        return activityClockingPicService.getList(activityClockingPicDto);
    }

    /**
     * 删除签到图片
     * @param id 签到图片ID
     * @return 结果实体对象
     */
    @PostMapping("/delete_pic")
    @ResponseBody
    public Object delete_pic(@RequestParam Integer id){
        var result = new JsonResult<>();
        if(activityClockingPicService.delete(id) > 0){
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 批量删除签到图片
     * @param ids 签到图片ID集合
     * @return 结果实体对象
     */
    @PostMapping("/batch_del_pic")
    @ResponseBody
    public Object batchDel(@RequestParam String ids) {
        var result = new JsonResult<>();
        if (activityClockingPicService.batchDel(ids)) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else{
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 获取随机图片
     * @return 图片名称
     */
    @GetMapping("/get_random_pic")
    @ResponseBody
    public Object getRandomPic(){
        return activityClockingPicService.getRandomPic();
    }
}

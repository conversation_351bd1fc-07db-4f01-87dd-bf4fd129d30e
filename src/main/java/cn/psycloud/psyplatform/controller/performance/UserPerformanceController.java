package cn.psycloud.psyplatform.controller.performance;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.JsonResult;
import cn.psycloud.psyplatform.dto.core.ResultCodeAndMsg;
import cn.psycloud.psyplatform.dto.performance.ImportUserPerformanceDto;
import cn.psycloud.psyplatform.dto.performance.UserPerformanceDto;
import cn.psycloud.psyplatform.service.performance.UserPerformanceService;
import com.alibaba.excel.EasyExcel;
import lombok.SneakyThrows;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/userPerformance")
@Controller
public class UserPerformanceController {
    @Autowired
    private UserPerformanceService userPerformanceService;

    @RequestMapping("/list")
    public String list(HttpServletRequest request) {
        var listFunctions = (List<SysFunctionDto>)request.getSession().getAttribute("listFunctions");
        var privilegeList = listFunctions.stream()
                .filter(a->("011402".equals(a.getFunctionCode()) || "011402".equals(a.getParentCode())))
                .collect(Collectors.toList());
        boolean canDelete = false;
        for (SysFunctionDto privilege: privilegeList){
            //删除权限
            if ("01140201".equals(privilege.getFunctionCode())) {
                canDelete = true;
            }
        }
        request.setAttribute("canDelete",canDelete);
        return "performance/list";
    }

    /**
     * 查询用户绩效信息列表
     * @param userPerformanceDto 用户绩效信息
     * @return 用户绩效信息列表
     */
    @PostMapping("/list")
    @ResponseBody
    public Object getListByPaged(@RequestBody UserPerformanceDto userPerformanceDto) {
        return userPerformanceService.getListByPaged(userPerformanceDto);
    }

    /**
     * 批量删除用户绩效信息
     * @param ids 主键ID列表
     * @return 影响行数
     */
    @PostMapping("/delete")
    @ResponseBody
    public Object delete(String ids) {
        var result = new JsonResult<>();
        if (userPerformanceService.batchDel(ids)) {
            result.setResultCode(ResultCodeAndMsg.SuccessCode);
            result.setResultMsg(ResultCodeAndMsg.SuccessMsg);
        }
        else {
            result.setResultCode(ResultCodeAndMsg.FailureCode);
            result.setResultMsg(ResultCodeAndMsg.FailureMsg);
        }
        return result;
    }

    /**
     * 导入用户绩效信息
     * @return 导入页面
     */
    @GetMapping("/import")
    public String importUserPerformance(){
        return "performance/import";
    }

    @SneakyThrows
    @RequestMapping(value = "/import",method = RequestMethod.POST)
    @ResponseBody
    public Object importUserPerformance(@RequestParam("file") MultipartFile file, HttpServletRequest request){
        List<ImportUserPerformanceDto> list = EasyExcel
                .read(file.getInputStream())
                .head(ImportUserPerformanceDto.class)
                .sheet()
                .doReadSync();
        var importResult = userPerformanceService.batchInsertUserPerformance(list);

        var result = new JsonResult<>();
        result.setResultCode(ResultCodeAndMsg.SuccessCode);
        result.setResultMsg(String.format("导入完成，成功：%d条，失败：%d条",
                importResult.getSuccessCount(), importResult.getFailCount()));
        result.setData(importResult);
        return result;
    }
}

package cn.psycloud.psyplatform.listener;

import cn.psycloud.psyplatform.dto.anteroom.ImportUserDto;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class ImportUserExcelListener extends AnalysisEventListener<ImportUserDto> {
    List<ImportUserDto> list = new ArrayList<>();
    /**
     *  一行一行去读取excle内容，把每行的内容封装到实体类对象中
     *  读取是从第二行开始读取，第一行默认为表头，不进行读取
     * @param dto 表格实体对象
     * @param analysisContext analysisContext
     */
    @Override
    public void invoke(ImportUserDto dto, AnalysisContext analysisContext) {
        list.add(dto);
    }

    /**
     *  读取excel表头信息，headMap即为表头信息
     * @param headMap map
     * @param context context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
    }

    /**
     *  读取完成后执行
     * @param analysisContext analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

}

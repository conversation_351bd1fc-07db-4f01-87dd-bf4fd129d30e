package cn.psycloud.psyplatform.serviceImpl.performance;

import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.performance.UserPerformanceDao;
import cn.psycloud.psyplatform.dto.anteroom.ImportUserDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ImportResultDto;
import cn.psycloud.psyplatform.dto.performance.ImportUserPerformanceDto;
import cn.psycloud.psyplatform.dto.performance.UserPerformanceDto;
import cn.psycloud.psyplatform.entity.performance.UserPerformanceEntity;
import cn.psycloud.psyplatform.service.performance.UserPerformanceService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class UserPerformanceServiceImpl implements UserPerformanceService {
    @Autowired
    private UserPerformanceDao userPerformanceDao;
    @Autowired
    private UserDao userDao;

    /**
     * 新增用户绩效信息
     * @param importUserPerformanceDto 用户绩效信息实体
     * @return 新增结果
     */
    private ImportUserDto insertUserPerformance(ImportUserPerformanceDto importUserPerformanceDto) {
        var importUserDto = new ImportUserDto();
        UserDto userDto = userDao.getUserByName(importUserPerformanceDto.getLoginName());
        if(userDto == null){
            importUserDto.setLoginName(importUserPerformanceDto.getLoginName());
            importUserDto.setRealName("");
            importUserDto.setState(0);
            importUserDto.setMsg("用户不存在");
        }
        else{
            var map = new HashMap<String, Object>();
            map.put("userId", userDto.getUserId());
            map.put("yearly", importUserPerformanceDto.getYearly());
            if(userPerformanceDao.isExist(map) > 0){
                userPerformanceDao.deleteByUserIdAndYearly(userDto.getUserId(), importUserPerformanceDto.getYearly());
            }
            var userPerformanceEntity = new UserPerformanceEntity();
            userPerformanceEntity.setUserId(userDto.getUserId());
            userPerformanceEntity.setYearly(importUserPerformanceDto.getYearly());
            userPerformanceEntity.setMonth01(importUserPerformanceDto.getMonth01());
            userPerformanceEntity.setMonth02(importUserPerformanceDto.getMonth02());
            userPerformanceEntity.setMonth03(importUserPerformanceDto.getMonth03());
            userPerformanceEntity.setMonth04(importUserPerformanceDto.getMonth04());
            userPerformanceEntity.setMonth05(importUserPerformanceDto.getMonth05());
            userPerformanceEntity.setMonth06(importUserPerformanceDto.getMonth06());
            userPerformanceEntity.setMonth07(importUserPerformanceDto.getMonth07());
            userPerformanceEntity.setMonth08(importUserPerformanceDto.getMonth08());
            userPerformanceEntity.setMonth09(importUserPerformanceDto.getMonth09());
            userPerformanceEntity.setMonth10(importUserPerformanceDto.getMonth10());
            userPerformanceEntity.setMonth11(importUserPerformanceDto.getMonth11());
            userPerformanceEntity.setMonth12(importUserPerformanceDto.getMonth12());
            if(userPerformanceDao.insertUserPerformance(userPerformanceEntity) > 0){
                importUserDto.setLoginName(userDto.getLoginName());
                importUserDto.setRealName(userDto.getRealName());
                importUserDto.setState(1);
                importUserDto.setMsg("导入成功");
            }
            else{
                importUserDto.setLoginName(userDto.getLoginName());
                importUserDto.setRealName(userDto.getRealName());
                importUserDto.setState(0);
                importUserDto.setMsg("导入失败");
            }
        }
        return importUserDto;
    }

    /**
     * 批量新增用户绩效信息
     * @param list 用户绩效信息实体列表
     * @return 执行是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDto batchInsertUserPerformance(List<ImportUserPerformanceDto> list) {
        List<ImportUserDto> users = new ArrayList<>();
        if(list == null || list.isEmpty()){
            return new ImportResultDto(users);
        }
        var successCount = 0;
        for (var importUserPerformance : list) {
            try {
                var importUserDto = insertUserPerformance(importUserPerformance);
                users.add(importUserDto);
                if(importUserDto.getState() == 1){
                    successCount++;
                    log.error("已成功导入绩效数据记录数量{}", successCount);
                }
            }
            catch (Exception e) {
                log.error("导入绩效数据失败", e);
                var errorDto = new ImportUserDto();
                errorDto.setState(0);
                errorDto.setMsg("导入失败：" + e.getMessage());
                users.add(errorDto);
            }
        }
        return new ImportResultDto(users);
    }

    /**
     * 根据主键ID删除用户绩效信息
     * @param id 主键ID
     * @return 删除结果
     */
    @Override
    public int deleteById(Integer id) {
        return userPerformanceDao.deleteById(id);
    }

    /**
     * 批量删除用户绩效信息
     * @param ids 主键ID列表
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean batchDel(String ids) {
        var isSuccess = true;
        try{
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                isSuccess = userPerformanceDao.deleteById(Integer.parseInt(id)) > 0 ;
            }
        }
        catch(Exception e){
            isSuccess = false;
        }
        return isSuccess;
    }

    /**
     * 查询用户绩效信息列表
     * @param userPerformanceDto 用户绩效信息
     * @return 用户绩效信息列表
     */
    @Override
    public BSDatatableRes<UserPerformanceDto> getListByPaged(UserPerformanceDto userPerformanceDto) {
        var childStructsIds = PermissonHelper.getChildStructIds(userPerformanceDto.getStructId());
        userPerformanceDto.setChildStructs(childStructsIds);
        var dtRes = new BSDatatableRes<UserPerformanceDto>();
        PageHelper.startPage(userPerformanceDto.getPageIndex() / userPerformanceDto.getPageSize() + 1, userPerformanceDto.getPageSize());
        var listPerformance = userPerformanceDao.getList(userPerformanceDto);
        var dto = new PageInfo<>(listPerformance);
        dtRes.setData(dto.getList());
        dtRes.setRecordsTotal((int)dto.getTotal());
        dtRes.setRecordsFiltered((int)dto.getTotal());
        return dtRes;
    }
}

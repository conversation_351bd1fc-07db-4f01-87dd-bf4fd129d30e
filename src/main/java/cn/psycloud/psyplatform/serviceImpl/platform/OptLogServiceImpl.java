package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.psycloud.psyplatform.dao.platform.OptLogDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.dto.platform.ExportOptLogDto;
import cn.psycloud.psyplatform.dto.platform.OptLogDto;
import cn.psycloud.psyplatform.entity.platform.OptLogEntity;
import cn.psycloud.psyplatform.service.platform.OptLogService;
import cn.psycloud.psyplatform.util.redis.JedisUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OptLogServiceImpl implements OptLogService {
    @Autowired
    private OptLogDao optLogDao;
    //是否启用redis缓存
    @Value("${spring.redis.isEnabled}")
    private boolean isRedisEnabled;
    /**
     *  操作日志
     * @param entity 实体类对象
     * @return 影响行数
     */
    @Override
    public int add(OptLogEntity entity){
        return optLogDao.add(entity);
    }

    /**
     *  获取操作日志集合：分页
     * @param dto 操作日志实体对象
     * @return 集合
     */
    @Override
    public BSDatatableRes<OptLogEntity> getListByPaged(OptLogDto dto){
        var dtRes = new BSDatatableRes<OptLogEntity>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<OptLogEntity> listAuditLogs;
        if(isRedisEnabled && JedisUtil.exists("auditLog")) {
            listAuditLogs = JSON.parseArray(JedisUtil.get("auditLog"), OptLogEntity.class);
            listAuditLogs = listAuditLogs.stream().sorted(Comparator.comparing(OptLogEntity::getOptDate).reversed()).collect(Collectors.toList());
        }
        else{
            listAuditLogs = optLogDao.getList(dto);
        }
        var optLogEntity = new PageInfo<>(listAuditLogs);
        dtRes.setData(optLogEntity.getList());
        dtRes.setRecordsTotal((int)optLogEntity.getTotal());
        dtRes.setRecordsFiltered((int)optLogEntity.getTotal());
        return dtRes;
    }

    /**
     *  导出操作日志
     * @param dto 条件
     * @return 集合
     */
    @Override
    public List<ExportOptLogDto> getExportList(OptLogDto dto){
        return optLogDao.getExportList(dto);
    }
}

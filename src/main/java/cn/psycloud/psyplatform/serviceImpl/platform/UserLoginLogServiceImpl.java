package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.psycloud.psyplatform.dao.platform.UserLoginLogDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.platform.UserLoginLogDto;
import cn.psycloud.psyplatform.entity.platform.UserLoginLogEntity;
import cn.psycloud.psyplatform.service.platform.UserLoginLogService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserLoginLogServiceImpl implements UserLoginLogService {
    @Autowired
    private UserLoginLogDao userLoginLogDao;
    /**
     *  保存登录日志
     * @param entity 登录信息实体类对象
     * @return 影响行数
     */
    @Override
    public int add(UserLoginLogEntity entity){
        return userLoginLogDao.add(entity);
    }

    /**
     *  获取登录日志集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<UserLoginLogEntity> getListByPaged(UserLoginLogDto dto){
        var dtRes = new BSDatatableRes<UserLoginLogEntity>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listLogs = userLoginLogDao.getList(dto);
        var logDto = new PageInfo<>(listLogs);
        dtRes.setData(logDto.getList());
        dtRes.setRecordsTotal((int) logDto.getTotal());
        dtRes.setRecordsFiltered((int) logDto.getTotal());
        return dtRes;
    }
}

package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.psycloud.psyplatform.dao.platform.DashboardDao;
import cn.psycloud.psyplatform.dto.platform.DashboardDto;
import cn.psycloud.psyplatform.service.platform.DashboardService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *  系统数据看板业务实现类
 */
@Service
public class DashboardServiceImpl implements DashboardService {
    @Autowired
    private DashboardDao dashboardDao;

    /**
     *  获取系统数据看板数据
     * @return 系统数据看板数据
     */
    @Override
    public DashboardDto getDashboardData(DashboardDto dashboardDto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dashboardDto.getStructId());
        dashboardDto.setChildStructs(childStructsIds);

        // 获取主要统计数据
        DashboardDto result = dashboardDao.getDashboardData(dashboardDto);
        
        // 获取咨询相关分布数据
        result.setConsultationsByForm(dashboardDao.getConsultationsByForm(dashboardDto));
        result.setConsultationsByField(dashboardDao.getConsultationsByField(dashboardDto));
        result.setConsultationsByRisk(dashboardDao.getConsultationsByRisk(dashboardDto));
        result.setConsultationsTrend(dashboardDao.getConsultationsTrend(dashboardDto));
        
        // 获取活动相关分布数据
        result.setActivitiesByType(dashboardDao.getActivitiesByType(dashboardDto));
        result.setActivitiesTrend(dashboardDao.getActivitiesTrend(dashboardDto));
        
        // 获取测评相关分布数据
        result.setTestsByWarningLevel(dashboardDao.getTestsByWarningLevel(dashboardDto));
        result.setTestsTrend(dashboardDao.getTestsTrend(dashboardDto));
        
        return result;
    }
}

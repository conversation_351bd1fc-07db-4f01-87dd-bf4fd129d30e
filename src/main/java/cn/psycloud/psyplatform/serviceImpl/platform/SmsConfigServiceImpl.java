package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.psycloud.psyplatform.dao.platform.SmsConfigDao;
import cn.psycloud.psyplatform.entity.platform.SmsConfigEntity;
import cn.psycloud.psyplatform.service.platform.SmsConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SmsConfigServiceImpl implements SmsConfigService {
    @Autowired
    private SmsConfigDao smsConfigDao;
    /**
     *  保存短信配置信息
     * @param entity 短信配置实体对象
     * @return 影响行数
     */
    public int saveConfig(SmsConfigEntity entity){
        return smsConfigDao.save(entity);
    }
}

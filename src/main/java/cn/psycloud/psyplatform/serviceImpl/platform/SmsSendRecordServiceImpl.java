package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.psycloud.psyplatform.dao.platform.SmsSendRecordDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.platform.SmsSendRecordDto;
import cn.psycloud.psyplatform.service.platform.SmsSendRecordService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SmsSendRecordServiceImpl implements SmsSendRecordService {
    @Autowired
    private SmsSendRecordDao smsSendRecordDao;
    /**
     * 查询发送记录集合：分页查询
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<SmsSendRecordDto> getListByPaged(SmsSendRecordDto dto){
        var dtRes = new BSDatatableRes<SmsSendRecordDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listRecords = smsSendRecordDao.getList(dto);
        var recordDto = new PageInfo<>(listRecords);
        dtRes.setData(recordDto.getList());
        dtRes.setRecordsTotal((int) recordDto.getTotal());
        dtRes.setRecordsFiltered((int) recordDto.getTotal());
        return dtRes;
    }
}

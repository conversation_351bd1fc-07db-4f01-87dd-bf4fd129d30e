package cn.psycloud.psyplatform.serviceImpl.platform;

import cn.hutool.json.JSONUtil;
import cn.psycloud.psyplatform.dao.anteroom.SysFunctionDao;
import cn.psycloud.psyplatform.dao.platform.RoleDao;
import cn.psycloud.psyplatform.dto.anteroom.RoleDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.core.ZtreeData;
import cn.psycloud.psyplatform.entity.anteroom.RoleEntity;
import cn.psycloud.psyplatform.service.platform.RoleService;
import cn.psycloud.psyplatform.util.redis.JedisUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class RoleServiceImpl implements RoleService {
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private SysFunctionDao sysFunctionDao;

    /**
     *  新增
     * @param entity 平台角色实体对象
     * @return 影响行数
     */
    @Override
    public int addRole(RoleEntity entity){
        return roleDao.insert(entity);
    }

    /**
     *  更新
     * @param entity 平台角色实体对象
     * @return 影响行数
     */
    @Override
    public int updateRole(RoleEntity entity){
        return roleDao.update(entity);
    }

    /**
     *  删除
     * @param id 角色id
     * @return 影响行数
     */
    @Override
    public int del(Integer id){
        Integer isSuccess = 0;
        int[] idArray = { 1, 2, 3};//系统内置的角色不允许删除
        if(!Arrays.asList(idArray).contains(id)){
            if (roleDao.delete(id) > 0) {
                isSuccess = 1;
            }
        }
        return isSuccess;
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public  boolean batchDel(String ids){
        boolean isSuccess = false;
        int[] idArray = { 1, 2, 3};//系统内置的角色不允许删除
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            if(!Arrays.asList(idArray).contains(id)){
                isSuccess = del(id) >0 ;
            }
        }
        return isSuccess;
    }

    /**
     *  查询平台角色集合
     * @param dto 实体对象
     * @return 角色集合
     */
    @Override
    public List<RoleDto> getList(RoleDto dto){
        return roleDao.getList(dto);
    }

    /**
     *  查询平台角色集合：分页
     * @param dto 实体对象
     * @return 角色集合
     */
    @Override
    public BSDatatableRes<RoleDto> getListByPaged(RoleDto dto){
        var dtRes = new BSDatatableRes<RoleDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listRoles = getList(dto);
        var roleDto = new PageInfo<RoleDto>(listRoles);
        dtRes.setData(roleDto.getList());
        dtRes.setRecordsTotal((int) roleDto.getTotal());
        dtRes.setRecordsFiltered((int) roleDto.getTotal());
        return dtRes;
    }

    /**
     *  根据角色ID获取角色权限返回ztree格式
     * @param roleId 角色ID
     * @return 平台功能集合
     */
    @Override
    public List<ZtreeData> getRolePrivilegeForZTree(Integer roleId){
        return sysFunctionDao.getGrantForZTree(roleId);
    }

    /**
     *  角色授权
     * @param roleId 角色id
     * @param grants 权限集合
     * @return 执行是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  Boolean addGrant(Integer roleId, String[] grants){
        boolean isSuccess = false;
        clearGrant(roleId);
        for(String grant:grants){
            Map<String,String> map = new HashMap<>();
            map.put("roleId",roleId.toString());
            map.put("funCode",grant);
            isSuccess = roleDao.addGrant(map) > 0;
        }
        var sysFunctions = sysFunctionDao.getGrantByRole(0);
        JedisUtil.set("sysFunctions", JSONUtil.toJsonStr(sysFunctions),0);
        return  isSuccess;
    }

    /**
     *  清空角色权限
     * @param roleId 角色id
     * @return 影响行数
     */
    @Override
    public int clearGrant(Integer roleId){
        return roleDao.clearGrant(roleId);
    }

    /**
     *  将数据格式转换成select2格式
     * @param listRoles
     * @return
     */
    private List<Object> getSelect2Data(List<RoleDto> listRoles) {
        var lsNode = new ArrayList<Object>();
        for (RoleDto role : listRoles) {
            var select2Data = new Select2Data();
            select2Data.setId(role.getRoleId());
            select2Data.setText(role.getRoleName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  查询平台角色集合：转换成select所需格式
     * @return 平台角色集合
     */
    @Override
    public List<Object> getListForSelectP(){
        var roleDto = new RoleDto();
        roleDto.setFlag("p");
        List<Integer> unRoleIds=new ArrayList<>();
        Collections.addAll(unRoleIds,2,3,4);
        var listRoles = roleDao.getList(roleDto).stream().filter(role->! unRoleIds.contains(role.getRoleId())).collect(Collectors.toList());
        return getSelect2Data(listRoles);
    }

    /**
     *  查询咨询师角色集合：转换成select所需格式
     * @param dto 角色实体对象
     * @return 咨询师角色集合
     */
    public  List<Object> getListForSelect(RoleDto dto){
        var listRoles =roleDao.getList(dto);
        return getSelect2Data(listRoles);
    }

    /**
     *  查询角色集合(除了咨询师=2和家属=4)：转换成select所需格式
     * @return 咨询师角色集合
     */
    public List<Object> getListForSelectAll() {
        List<Integer> unRoleIds=new ArrayList<>();
        Collections.addAll(unRoleIds,2,4);
        var listRoles = roleDao.getList(new RoleDto()).stream().filter(role->! unRoleIds.contains(role.getRoleId())).collect(Collectors.toList());
        return getSelect2Data(listRoles);
    }
}
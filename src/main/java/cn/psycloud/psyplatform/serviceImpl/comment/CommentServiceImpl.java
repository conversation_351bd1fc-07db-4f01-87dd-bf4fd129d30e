package cn.psycloud.psyplatform.serviceImpl.comment;

import cn.psycloud.psyplatform.dao.comment.CommentDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.comment.CommentDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.comment.CommentEntity;
import cn.psycloud.psyplatform.service.comment.CommentService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class CommentServiceImpl implements CommentService {
    @Autowired
    private CommentDao commentDao;
    /**
     *  发表评论
     * @param entity 评论实体对象
     * @return  影响行数
     */
    @Override
    public int addComment(CommentEntity entity) {
        var currentUser = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setUserId(currentUser.getUserId());
        entity.setCommentDate(new Date());
        return commentDao.addComment(entity);
    }

    /**
     *  删除评论
     * @param id 评论id
     * @return 影响行数
     */
    @Override
    public int deleteComment(Integer id) {
        return commentDao.deleteComment(id);
    }

    /**
     *  批量删除评论
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDelComments(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = deleteComment(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  审核评论
     * @param ids 评论id集合
     * @return 影响行数
     */
    @Override
    public boolean checkComment(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = commentDao.checkComment(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  获取训练营评论集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<CommentDto> getCommentListByPaged(CommentDto dto){
        var dtRes = new BSDatatableRes<CommentDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listComments= commentDao.getComments(dto);
        var commentDto = new PageInfo<>(listComments);
        dtRes.setData(commentDto.getList());
        dtRes.setRecordsTotal((int) commentDto.getTotal());
        dtRes.setRecordsFiltered((int) commentDto.getTotal());
        return dtRes;
    }
}

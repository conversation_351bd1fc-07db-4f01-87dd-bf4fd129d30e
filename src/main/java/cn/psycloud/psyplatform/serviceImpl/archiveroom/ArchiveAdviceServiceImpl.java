package cn.psycloud.psyplatform.serviceImpl.archiveroom;

import cn.psycloud.psyplatform.dao.archiveroom.ArchiveAdviceDao;
import cn.psycloud.psyplatform.dto.archiveroom.ArchiveAdviceDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity;
import cn.psycloud.psyplatform.service.archiveroom.ArchiveAdviceService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ArchiveAdviceServiceImpl implements ArchiveAdviceService {
    @Autowired
    private ArchiveAdviceDao archiveAdviceDao;

    /**
     *  添加评语
     * @param entity 评语实体对象
     * @return 影响行数
     */
    @Override
    public int insert(AdviceEntity entity){
        return archiveAdviceDao.insert(entity);
    }

    /**
     *  修改评语
     * @param entity 评语实体对象
     * @return 影响行数
     */
    @Override
    public int update(AdviceEntity entity){
        return archiveAdviceDao.update(entity);
    }

    /**
     *  删除评语
     * @param id 评语Id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return archiveAdviceDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids ID集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  根据条件查询评语集合
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<AdviceEntity> getListByPaged(ArchiveAdviceDto dto){
        var dtRes = new BSDatatableRes<AdviceEntity>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listAdvices = archiveAdviceDao.getList(dto);
        var archiveAdviceDto = new PageInfo<>(listAdvices);
        dtRes.setData(archiveAdviceDto.getList());
        dtRes.setRecordsTotal((int)archiveAdviceDto.getTotal());
        dtRes.setRecordsFiltered((int)archiveAdviceDto.getTotal());
        return dtRes;
    }
}

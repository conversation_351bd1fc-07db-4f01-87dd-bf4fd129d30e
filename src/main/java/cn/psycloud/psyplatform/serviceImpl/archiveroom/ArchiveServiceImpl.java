package cn.psycloud.psyplatform.serviceImpl.archiveroom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.activityroom.SelfEvaluationDao;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.archiveroom.ArchiveAdviceDao;
import cn.psycloud.psyplatform.dao.consultationcase.ConsultationCaseDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.dao.performance.UserPerformanceDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.archiveroom.ArchiveConditionDto;
import cn.psycloud.psyplatform.dto.archiveroom.ArchiveDto;
import cn.psycloud.psyplatform.dto.archiveroom.MeasuringRecordDto;
import cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.service.archiveroom.ArchiveService;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.POIWordHelper;
import cn.psycloud.psyplatform.util.PermissonHelper;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Service
public class ArchiveServiceImpl implements ArchiveService {
    @Autowired
    private UserDao userDao;
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private ArchiveAdviceDao archiveAdviceDao;
    @Autowired
    private UserPerformanceDao userPerformanceDao;
    @Autowired
    private ConsultationCaseDao consultationCaseDao;
    @Autowired
    private SelfEvaluationDao selfEvaluationDao;
    @Autowired
    private POIWordHelper poiWordHelper;
    @Value("${file.location}")
    String uploadPath;
    /**
     *  生成档案
     * @param dto 条件
     * @return 用户集合
     */
    @Override
    public Map<String, Object> createArchive(HttpServletResponse response, HttpServletRequest request, ArchiveConditionDto dto){
        var resultMap = new HashMap<String,Object>();
        BSDatatableRes<UserDto> users = getArchiveUsers(dto);
        if(users.getData() != null && users.getData().size() > 0){
            var condition = dto.getContentCondition();
            boolean isMeasuringContains = StrUtil.contains(condition,"condition_measuring");
            boolean isCounselingContains = StrUtil.contains(condition,"condition_counseling");
            boolean isAdviceContains = StrUtil.contains(condition,"condition_advice");
            String folderName = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
            for(UserDto user: users.getData()){
                var map = new HashMap<String,Object>();
                map.put("user",user);
                map.put("isMeasuringContains",isMeasuringContains);
                map.put("isCounselingContains",isCounselingContains);
                map.put("isAdviceContains",isAdviceContains);
                if(dto.getAdvice() != null){
                    map.put("adviceId", dto.getAdvice().getId());
                }
                ArchiveDto archiveDto = getArchiveContent(map);
                //生成word
                Map<String, Object> archiveMap = new HashMap<>();
                archiveMap.put("createDate", DateUtil.format(new Date(), "yyyy-MM-dd"));
                archiveMap.put("realName",user.getRealName());
                archiveMap.put("sex",user.getSex());
                archiveMap.put("nation",user.getNation());
                archiveMap.put("birth",user.getBirth());
                archiveMap.put("nativePlace",user.getNativePlace());
                archiveMap.put("iDcardNo",user.getIDCardNo());
                archiveMap.put("structFullName",user.getStructFullName());
                archiveMap.put("address",user.getAddressProvince()+user.getAddressCity()+user.getAddressDist()+user.getAddressDetail());
                archiveMap.put("mobile",user.getMobile());
                List<Map<String,Object>> testRecordsMap =new ArrayList<>();
                //测评记录
                if(archiveDto.getMeasuringRecords() != null && archiveDto.getMeasuringRecords().size() > 0 ){
                    for(MeasuringRecordDto record: archiveDto.getMeasuringRecords()){
                        var testRecordMap = new HashMap<String,Object>();
                        testRecordMap.put("scaleName",record.getScaleName());
                        testRecordMap.put("testDate",DateUtil.format(record.getStartTime(),"yyyy-MM-dd HH:mm:ss"));
                        testRecordMap.put("interpretation", "<div style=\"font-family: 等线\">" +record.getInterpretation() + "</div>");
                        testRecordsMap.add(testRecordMap);
                    }
                }

                archiveMap.put("testRecords",testRecordsMap);
                String advice = "";
                if(archiveDto.getAdvice() != null){
                    advice = archiveDto.getAdvice().getAdviceContent();
                }
                archiveMap.put("advice",advice);

                String templatePath = CommonHelper.getResourcesFilePath("static/template/心理档案.docx");
                String fileName = user.getUserId().toString()+user.getRealName()+ ".docx";
                String fileAbPath=String.format( uploadPath +"archive/%s/%s", folderName,fileName);
                resultMap.put("folderName",folderName);
                poiWordHelper.createArchive(response, request, templatePath, fileAbPath, archiveMap);
            }
        }
        resultMap.put("users",users);
        return resultMap;
    }

    /**
     *  根据条件获取需要生成档案的用户集合
     * @param dto 条件
     * @return 用户集合
     */
    private BSDatatableRes<UserDto> getArchiveUsers(ArchiveConditionDto dto){
        var userDto = dto.getUserDto();
        userDto.setChildStructs(PermissonHelper.getChildStructIds(userDto.getStructId()));
        var users = userDao.getUserListForArchive(userDto);
        BSDatatableRes<UserDto> dtRes = new BSDatatableRes<>();
        dtRes.setData(users);
        dtRes.setRecordsTotal(users.size());
        dtRes.setRecordsFiltered(users.size());
        return dtRes;
    }

    /**
     *  根据条件获取档案内容
     * @param map 条件集合
     * @return 档案实体对象
     */
    private ArchiveDto getArchiveContent(Map<String,Object> map){
        ArchiveDto archiveDto = new ArchiveDto();
        //用户基本信息
        var user = (UserDto)map.get("user");
        archiveDto.setUser(user);
        //测评记录
        if(Boolean.parseBoolean(map.get("isMeasuringContains").toString())){
            var listTestRecords = testRecordDao.getListForArchive(user.getUserId());
            archiveDto.setMeasuringRecords(listTestRecords);
        }
        //预约咨询记录
        if(Boolean.parseBoolean(map.get("isCounselingContains").toString())){

        }
        //评语记录
        if(Boolean.parseBoolean(map.get("isAdviceContains").toString())){
            var adviceContent =archiveAdviceDao.getById(Integer.parseInt(map.get("adviceId").toString()));
            archiveDto.setAdvice(adviceContent);
        }
        return archiveDto;
    }

    /**
     *  根据用户ID获取档案内容
     * @param userId 用户ID
     * @return 档案实体对象
     */
    @Override
    public ArchiveDto getArchiveContentByUserId(Integer userId) {
        var archiveDto = new ArchiveDto();
        var userDtoCondition = new UserDto();
        userDtoCondition.setUserId(userId);
        //用户基本信息
        var userDto = userDao.get(userDtoCondition);
        archiveDto.setUser(userDto);
        //测评记录
        var listTestRecords = testRecordDao.getListForArchive(userId);
        if (listTestRecords != null && listTestRecords.size() > 0) {
            archiveDto.setMeasuringRecords(listTestRecords);
        }
        //绩效数据
        var userPerformance = userPerformanceDao.getByUserId(userId);
        if (userPerformance != null && userPerformance.size() > 0) {
            archiveDto.setUserPerformance(userPerformance);
        }
        //心理咨询个案
        var consultationCaseDto = new ConsultationCaseDto();
        consultationCaseDto.setUserId(userId);
        var consultationCases = consultationCaseDao.getList(consultationCaseDto);
        if (consultationCases != null && consultationCases.size() > 0) {
            archiveDto.setConsultationCases(consultationCases);
        }
        //个人活动评价
        var selfEvaluations = selfEvaluationDao.getMySelfEvaluationListByUerId(userId);
        if (selfEvaluations != null && selfEvaluations.size() > 0) {
            archiveDto.setSelfEvaluations(selfEvaluations);
        }
        return archiveDto;
    }
}

package cn.psycloud.psyplatform.serviceImpl.activityroom;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.dao.activityroom.ActivityDao;
import cn.psycloud.psyplatform.dao.activityroom.SelfEvaluationDao;
import cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao;
import cn.psycloud.psyplatform.dao.survey.SurveyItemDao;
import cn.psycloud.psyplatform.dao.survey.SurveyRecordDao;
import cn.psycloud.psyplatform.dao.survey.SurveyDao;
import cn.psycloud.psyplatform.dto.activityroom.*;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.survey.*;
import cn.psycloud.psyplatform.entity.survey.SurveyRecordEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyItemEntity;
import cn.psycloud.psyplatform.entity.activityroom.ActivityEntity;
import cn.psycloud.psyplatform.entity.activityroom.OverallEvaluationEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityService;
import cn.psycloud.psyplatform.service.activityroom.ClockingService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.poi.xwpf.usermodel.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 心理活动服务实现类
 */
@Service
public class ActivityServiceImpl implements ActivityService {
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private ClockingService clockingService;
    @Autowired
    private SurveyQuestionDao surveyQuestionDao;
    @Autowired
    private SelfEvaluationDao selfEvaluationDao;
    @Autowired
    private SurveyRecordDao surveyRecordDao;
    @Autowired
    private SurveyDao surveyDao;
    @Autowired
    private SurveyItemDao surveyItemDao;
    @Value("${file.location}")
    String uploadPath;

    /**
     * 添加心理活动
     * @param activityEntity 心理活动实体
     * @return 活动ID
     */
    @Override
    public int add(ActivityEntity activityEntity){
        activityEntity.setAddDate(new Date());
        UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        activityEntity.setOperator(userDto.getUserId());
        return activityDao.add(activityEntity);
    }

    /**
     * 修改心理活动
     * @param activityEntity 心理活动实体
     * @return 是否成功
     */
    @Override
    public int update(ActivityEntity activityEntity){
        return activityDao.update(activityEntity);
    }

    /**
     * 删除心理活动
     * @param id 心理活动id
     * @return 是否成功
     */
    @Override
    public int delete(Integer id){
        return activityDao.delete(id);
    }

    /**
     * 批量删除心理活动
     * @param ids 心理活动id
     * @return 是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     * 根据条件查询心理活动列表：分页
     * @param activityDto 心理活动查询条件
     * @return 心理活动列表
     */
    @Override
    public BSDatatableRes<ActivityDto> getListByPaged(ActivityDto activityDto){
        var dtRes = new BSDatatableRes<ActivityDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(activityDto.getStructId());
        activityDto.setChildStructs(childStructsIds);
        PageHelper.startPage(activityDto.getPageIndex()/activityDto.getPageSize()+1,activityDto.getPageSize());
        var listCourses = activityDao.getList(activityDto);
        var dto = new PageInfo<>(listCourses);
        dtRes.setData(listCourses);
        dtRes.setRecordsTotal((int) dto.getTotal());
        dtRes.setRecordsFiltered((int) dto.getTotal());
        return dtRes;
    }

    /**
     * 根据条件查询心理活动列表
     * @param activityDto 心理活动查询条件
     * @return 心理活动列表
     */
    @Override
    public List<ActivityDto> getList(ActivityDto activityDto){
        return activityDao.getList(activityDto);
    }

    /**
     * 根据id查询心理活动
     * @param id 心理活动id
     * @return 心理活动详情
     */
    @Override
    public ActivityDto getById(Integer id){
        return activityDao.getById(id);
    }

    /**
     * 根据id查询心理活动详情
     * @param id 心理活动id
     * @return 心理活动详情
     */
    @Override
    public ActivityDto getForDetail(Integer id){
        var activityDto = getById(id);
        UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        var activityState = clockingService.getUserActivityStatus(userDto.getUserId(),id);
        activityDto.setActivityStateForUser(activityState);
        var isSurveyDone = 0;
        if(isSurveyDone(userDto.getUserId(),id,activityDto.getSurveyId())){
            isSurveyDone = 1;
        }
        else{
            //获取问卷
            var surveyId = activityDto.getSurveyId();
            var surveyDto = new SurveyDto();
            surveyDto.setId(surveyId);
            surveyDto.setSurveyName(activityDto.getSurveyName());
            var surveyQuestions = surveyQuestionDao.getListBySurveyId(surveyId);
            surveyDto.setListQuestions(surveyQuestions);
            activityDto.setSurvey(surveyDto);
        }
        activityDto.setIsSurveyDone(isSurveyDone);
        return activityDto;
    }

    /**
     * 判断调查问卷是否已做
     * @param userId 用户ID
     * @param activityId 活动ID
     * @param surveyId 调查问卷ID
     * @return 是否已做
     */
    @Override
    public boolean isSurveyDone(Integer userId, Integer activityId, Integer surveyId){
        var map = new HashMap<String,Integer>();
        map.put("userId",userId);
        map.put("activityId",activityId);
        map.put("surveyId",surveyId);
        return activityDao.isSurveyDone(map) > 0;
    }

    /**
     * 获取题目类型文字描述
     * @param qType 题目类型
     * @return 类型描述
     */
    private String getQuestionTypeText(Integer qType) {
        if (qType == null) {
            return "未知";
        }
        switch (qType) {
            case 1:
                return "单选题";
            case 2:
                return "多选题";
            case 3:
                return "填空题";
            case 4:
                return "排序题";
            case 5:
                return "评分题";
            default:
                return "其他";
        }
    }

    /**
     * 导出活动问卷作答记录
     * @param dto 导出条件
     * @return 导出数据
     */
    @Override
    public List<LinkedHashMap<String,Object>> exportSurveyResult(ExportActivitySurveyRecordDto dto){
        List<LinkedHashMap<String,Object>> questions =surveyQuestionDao.getListForExport(dto.getSurveyId() == null ? 0 : dto.getSurveyId());
        StringBuilder questionBuilder = new StringBuilder();
        
        // 根据导出类型决定使用哪种字段
        // exportType为null或2时，按选项内容导出（原有功能）；为1时，按选项序号导出（新增功能）
        boolean exportById = dto.getExportType() != null && dto.getExportType() == 1;
        
        if (exportById) {
            // 按选项序号导出（新增功能）
            for(LinkedHashMap<String,Object> questionMap: questions){
                String qTypeText = getQuestionTypeText((Integer) questionMap.get("q_type"));
                String qContent = (String) questionMap.get("q_content");
                // 移除HTML标签
                qContent = qContent.replaceAll("<[^>]*>", "").trim();
                String columnHeader = String.format("第%s题（%s）、%s", questionMap.get("q_number"), qTypeText, qContent);
                
                questionBuilder.append(String.format("max(case when psq.q_number ='%s' then " +
                    "CASE " +
                    "WHEN psq.q_type = 3 THEN psr.item_id " +  // 填空题显示内容
                    "WHEN psq.q_type = 2 THEN " +  // 多选题转换为序号
                        "(SELECT GROUP_CONCAT(psi2.item_no ORDER BY FIND_IN_SET(psi2.item_content, REPLACE(psr.item_id, '|', ',')) SEPARATOR '|') " +
                        "FROM psycloud_survey_item psi2 " +
                        "WHERE psi2.q_id = psq.id AND FIND_IN_SET(psi2.item_content, REPLACE(psr.item_id, '|', ',')) > 0) " +
                    "WHEN psq.q_type = 4 THEN " +  // 排序题：暂时显示去掉前缀的内容，建议后续在应用层处理序号转换
                        "REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(" +
                        "psr.item_id, '1.', ''), '2.', ''), '3.', ''), '4.', ''), '5.', ''), '6.', ''), '7.', ''), '8.', ''), '9.', ''), '10.', '') " +
                    "ELSE CASE WHEN psi.item_no IS NULL OR psi.item_no = 0 THEN psr.item_id ELSE psi.item_no END " +  // 单选题、评分题显示选项序号
                    "END " +
                    "else 0 end) as '%s',",
                    questionMap.get("q_number"), columnHeader));
            }
            String qNoStr = StrUtil.removeSuffix(questionBuilder.toString(),",");
            dto.setSql(qNoStr);
            List<LinkedHashMap<String, Object>> result = activityDao.getExportSurveyTestResultByText(dto);
            
            // 后处理排序题：将选项内容转换为序号
            return postProcessSortQuestions(result, questions, dto.getSurveyId());
        } else {
            // 按选项内容导出（原有功能，保持不变）
            for(LinkedHashMap<String,Object> questionMap: questions){
                String qTypeText = getQuestionTypeText((Integer) questionMap.get("q_type"));
                String qContent = (String) questionMap.get("q_content");
                // 移除HTML标签
                qContent = qContent.replaceAll("<[^>]*>", "").trim();
                String columnHeader = String.format("第%s题（%s）、%s", questionMap.get("q_number"), qTypeText, qContent);
                
                questionBuilder.append(String.format("max(case when psq.q_number ='%s' then psr.item_id else 0 end) as '%s',",
                    questionMap.get("q_number"), columnHeader));
            }
            String qNoStr = StrUtil.removeSuffix(questionBuilder.toString(),",");
            dto.setSql(qNoStr);
            return activityDao.getExportSurveyTestResult(dto);
        }
    }

    /**
     * 获取我的活动
     * @param userId 用户ID
     * @return 我的活动列表
     */
    @Override
    public List<ActivityDto> getMyActivities(Integer userId){
        var activitys =  activityDao.getMyActivities(userId);
        for (ActivityDto activity : activitys) {
            var map = new HashMap<String,Object>();
            map.put("userId",userId);
            map.put("activityId",activity.getId());
            var tags = selfEvaluationDao.getMySelfEvaluationList(map);
            activity.setTags(tags);
        }
        return activitys;
    }

    /**
     * 更新活动总评
     * @param overallEvaluationEntity 总评实体
     * @return 是否成功
     */
    @Override
    public int updateOverallEvaluation(OverallEvaluationEntity overallEvaluationEntity){
        return activityDao.updateOverallEvaluation(overallEvaluationEntity);
    }

    /**
     * 获取活动总评
     * @param activityId 活动ID
     * @return 活动总评
     */
    @Override
    public OverallEvaluationDto getOverallEvaluation(Integer activityId){
        return activityDao.getOverallEvaluation(activityId);
    }

    /**
     * 获取活动报告
     * @param activityId 活动ID
     * @return 活动报告
     */
    @Override
    public ActivityReportDto getActivityReport(Integer activityId){
        var activityReport = activityDao.getActivityReport(activityId);
        var selfEvaluations = selfEvaluationDao.getSelfEvaluationList(activityId);
        var participants = activityDao.getActivityParticipants(activityId);
        activityReport.setSelfEvaluations(selfEvaluations);
        activityReport.setParticipants(participants);
        return activityReport;
    }

    /**
     * 获取咨询师的活动集合：select
     * @param userId 咨询师id
     * @return 活动集合
     */
    @Override
    public List<Object> getCounselorActivitiesForSelect(Integer userId){
        var activities =  activityDao.getCounselorActivitiesForSelect(userId);
        var lsNode = new ArrayList<>();
        for (ActivityForSelectDto dto : activities) {
            var select2Data = new Select2Data();
            select2Data.setId(dto.getId());
            select2Data.setText(dto.getActivityName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     * 获取活动问卷结果统计
     * @param activityId 活动ID
     * @param surveyId 问卷ID
     * @return 统计结果
     */
    @Override
    public ActivitySurveyStatDto getActivitySurveyStat(Integer activityId, Integer surveyId) {
        var activitySurveyStatDto = new ActivitySurveyStatDto();

        // 获取活动信息
        var activity = activityDao.getById(activityId);
        activitySurveyStatDto.setActivityId(activityId);
        activitySurveyStatDto.setActivityName(activity.getActivityName());

        // 获取问卷信息
        var survey = surveyDao.getById(surveyId);
        activitySurveyStatDto.setSurveyId(surveyId);
        activitySurveyStatDto.setSurveyName(survey.getSurveyName());
        activitySurveyStatDto.setSurvey(survey);

        // 获取活动的问卷作答记录
        var activitySurveyRecordDto = new ActivitySurveyRecordDto();
        activitySurveyRecordDto.setActivityId(activityId);
        var surveyRecords = surveyRecordDao.getActivitySurveyRecordList(activitySurveyRecordDto);
        activitySurveyStatDto.setSurveyRecords(new ArrayList<>());

        // 转换为SurveyRecordEntity列表
        for (var record : surveyRecords) {
            var surveyRecord = new SurveyRecordEntity();
            surveyRecord.setId(record.getId());
            surveyRecord.setUserId(record.getUserId());
            surveyRecord.setSurveyId(record.getSurveyId());
            surveyRecord.setRecordDate(record.getRecordDate());
            surveyRecord.setIsDone(record.getIsDone());
            surveyRecord.setIsValid(1);
            activitySurveyStatDto.getSurveyRecords().add(surveyRecord);
        }

        int totalCount = activitySurveyStatDto.getSurveyRecords().size();
        activitySurveyStatDto.setTotalCount(totalCount);

        int completedCount = 0;
        int notCompletedCount = 0;
        for (var surveyRecord : activitySurveyStatDto.getSurveyRecords()) {
            if (surveyRecord.getIsDone() == 1) {
                completedCount++;
            } else {
                notCompletedCount++;
            }
        }

        activitySurveyStatDto.setDoneCount(completedCount);
        activitySurveyStatDto.setUnDoneCount(notCompletedCount);
        activitySurveyStatDto.setDoneRate(totalCount > 0 ? ((double) completedCount / totalCount) * 100 : 0.0);
        activitySurveyStatDto.setUnDoneRate(totalCount > 0 ? ((double) notCompletedCount / totalCount) * 100 : 0.0);

        // 获取问卷题目
        var surveyQuestions = surveyQuestionDao.getListBySurveyId(surveyId);
        System.out.println("获取到的问卷题目数量: " + (surveyQuestions != null ? surveyQuestions.size() : 0));
        if (surveyQuestions != null && !surveyQuestions.isEmpty()) {
            for (var question : surveyQuestions) {
                System.out.println("题目 " + question.getQNumber() + ": " + question.getQContent() + ", 选项数量: " +
                    (question.getListItems() != null ? question.getListItems().size() : 0));
            }
        }

        // 获取问卷结果数据
        var surveyResults = activityDao.getActivitySurveyResults(activityId, surveyId);
        System.out.println("获取到的问卷结果数量: " + (surveyResults != null ? surveyResults.size() : 0));
        activitySurveyStatDto.setSurveyResults(surveyResults);

        // 统计选项选择情况
        activitySurveyStatDto.setSurveyResultStat(countOptionSelections(surveyQuestions, surveyResults, completedCount));

        // 计算评分题总体平均分
        activitySurveyStatDto.setRatingOverallAverage(calculateRatingOverallAverage(surveyQuestions, surveyResults));

        return activitySurveyStatDto;
    }

    /**
     * 统计选项选择情况
     */
    private List<SurveyResultStatDto> countOptionSelections(List<SurveyQuestionDto> surveyQuestions, List<SurveyResultDto> surveyResults, Integer totalCount) {
        // 使用流式处理统计数据，但仅针对非填空题
        Map<Integer, Map<String, Long>> countsByQuestionAndItem = surveyResults.stream()
                .flatMap(srd -> {
                    // 查找对应的问题以确定其类型
                    Optional<SurveyQuestionDto> questionOpt = surveyQuestions.stream()
                            .filter(qwo -> qwo.getQNumber().equals(srd.getQNumber()))
                            .findFirst();

                    int qType = questionOpt.map(SurveyQuestionDto::getQType).orElse(1); // 默认为单选题

                    if (qType == 2) { // 如果是多选题
                        // 分割itemContent
                        return Arrays.stream(srd.getItemId().split("\\|"))
                                .map(String::trim)
                                .map(itemContent -> new AbstractMap.SimpleEntry<>(srd.getQNumber(), itemContent));
                    } else if (qType == 4) { // 如果是排序题
                        // 分割排序结果，提取选项内容
                        return Arrays.stream(srd.getItemId().split("\\|"))
                                .map(String::trim)
                                .filter(item -> item.contains("."))
                                .map(item -> item.substring(item.indexOf(".") + 1))
                                .map(itemContent -> new AbstractMap.SimpleEntry<>(srd.getQNumber(), itemContent));
                    } else if (qType == 5) { // 如果是评分题
                        // 对于评分题，itemId存储的是分数值，需要匹配到对应的选项内容
                        // 例如：itemId="3" 需要匹配到 itemContent="3分" 或 "3"
                        return Stream.of(new AbstractMap.SimpleEntry<>(srd.getQNumber(), srd.getItemId()));
                    } else if (qType != 3) { // 如果不是填空题
                        // 对于单选题，直接返回单个条目
                        return Stream.of(new AbstractMap.SimpleEntry<>(srd.getQNumber(), srd.getItemId()));
                    } else {
                        // 对于填空题，跳过统计
                        return Stream.empty();
                    }
                })
                .collect(Collectors.groupingBy(
                        entry -> entry.getKey(),
                        Collectors.groupingBy(
                                entry -> entry.getValue(),
                                Collectors.counting()
                        )
                ));

        // 构建结果统计
        return surveyQuestions.stream()
                .map(question -> {
                    Integer qNumber = question.getQNumber();
                    Integer qType = question.getQType();
                    List<SurveyItemEntity> allItems = question.getListItems();

                    SurveyResultStatDto statDto = new SurveyResultStatDto();
                    statDto.setQNumber(qNumber);
                    statDto.setQContent(question.getQContent());
                    statDto.setQType(qType);

                    if (qType != 3 && qType != 4) { // 如果不是填空题和排序题
                        // 对于选择题（单选、多选、评分题），构建选项统计信息
                        List<SurveyResultCountDto> listResultCounts = allItems.stream()
                                .map(item -> {
                                    SurveyResultCountDto countDto = new SurveyResultCountDto();
                                    countDto.setQuestionNumber(qNumber);
                                    countDto.setItemContent(item.getItemContent());

                                    // 获取统计数据，对于评分题需要特殊处理
                                    long selCount = 0;
                                    if (qType == 5) { // 评分题特殊处理
                                        // 评分题的itemId是分数值，需要匹配选项内容
                                        // 先尝试直接匹配itemContent
                                        selCount = countsByQuestionAndItem.getOrDefault(qNumber, Collections.emptyMap())
                                                .getOrDefault(item.getItemContent(), 0L);
                                        
                                        // 如果直接匹配失败，尝试提取数字进行匹配
                                        if (selCount == 0) {
                                            String itemContentNum = item.getItemContent().replaceAll("[^0-9]", "");
                                            if (!itemContentNum.isEmpty()) {
                                                selCount = countsByQuestionAndItem.getOrDefault(qNumber, Collections.emptyMap())
                                                        .getOrDefault(itemContentNum, 0L);
                                            }
                                        }
                                    } else {
                                        // 其他题型直接匹配
                                        selCount = countsByQuestionAndItem.getOrDefault(qNumber, Collections.emptyMap())
                                                .getOrDefault(item.getItemContent(), 0L);
                                    }
                                    
                                    countDto.setSelCount((int) selCount);

                                    // 计算选择项数量占比，如果总选择次数为0，则占比为0
                                    double selRate = totalCount > 0 ? ((double) selCount / totalCount) : 0.0;
                                    countDto.setSelRate(formatPercentage(selRate));

                                    return countDto;
                                })
                                .collect(Collectors.toList());

                        statDto.setListResultCounts(listResultCounts);
                        
                        // 如果是评分题，额外计算平均分
                        if (qType == 5) {
                            double averageRating = calculateRatingAverage(surveyResults, qNumber);
                            statDto.setRatingAverage(averageRating);
                        }
                    } else if (qType == 4) { // 排序题特殊处理
                        // 对于排序题，统计每个选项的平均排名
                        List<SurveyResultCountDto> listResultCounts = calculateSortingStats(surveyResults, qNumber, allItems);
                        statDto.setListResultCounts(listResultCounts);
                    } else {
                        // 对于填空题，不进行统计
                        statDto.setListResultCounts(new ArrayList<>());
                    }

                    return statDto;
                })
                .filter(statDto -> !statDto.getListResultCounts().isEmpty()) // 过滤掉填空题
                .collect(Collectors.toList());
    }

    /**
     * 计算排序题的统计信息
     */
    private List<SurveyResultCountDto> calculateSortingStats(List<SurveyResultDto> surveyResults, Integer qNumber, List<SurveyItemEntity> allItems) {
        // 统计每个选项的排名信息
        Map<String, List<Integer>> rankingMap = new HashMap<>();

        // 初始化排名映射
        for (SurveyItemEntity item : allItems) {
            rankingMap.put(item.getItemContent(), new ArrayList<>());
        }

        // 收集排名数据
        for (SurveyResultDto result : surveyResults) {
            if (result.getQNumber().equals(qNumber)) {
                String[] sortedItems = result.getItemId().split("\\|");
                for (String sortedItem : sortedItems) {
                    if (sortedItem.contains(".")) {
                        String[] parts = sortedItem.split("\\.", 2);
                        if (parts.length == 2) {
                            try {
                                int rank = Integer.parseInt(parts[0]);
                                String itemContent = parts[1].trim();
                                if (rankingMap.containsKey(itemContent)) {
                                    rankingMap.get(itemContent).add(rank);
                                }
                            } catch (NumberFormatException e) {
                                // 忽略格式错误的数据
                            }
                        }
                    }
                }
            }
        }

        // 计算平均排名并生成统计结果
        return allItems.stream()
                .map(item -> {
                    SurveyResultCountDto countDto = new SurveyResultCountDto();
                    countDto.setQuestionNumber(qNumber);
                    countDto.setItemContent(item.getItemContent());

                    List<Integer> ranks = rankingMap.get(item.getItemContent());
                    if (!ranks.isEmpty()) {
                        double avgRank = ranks.stream().mapToInt(Integer::intValue).average().orElse(0.0);
                        countDto.setSelCount(ranks.size()); // 选择次数
                        countDto.setSelRate(String.format("%.2f", avgRank)); // 平均排名
                    } else {
                        countDto.setSelCount(0);
                        countDto.setSelRate("0.00");
                    }

                    return countDto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(double rate) {
        return String.format("%.2f%%", rate * 100);
    }

    /**
     * 计算单个评分题的平均分
     */
    private double calculateRatingAverage(List<SurveyResultDto> surveyResults, Integer qNumber) {
        List<Double> ratings = new ArrayList<>();
        
        for (SurveyResultDto result : surveyResults) {
            if (result.getQNumber().equals(qNumber)) {
                try {
                    double rating = Double.parseDouble(result.getItemId());
                    ratings.add(rating);
                } catch (NumberFormatException e) {
                    // 忽略无效的评分值
                }
            }
        }
        
        if (ratings.isEmpty()) {
            return 0.0;
        }
        
        return ratings.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
    }

    /**
     * 计算所有评分题的总体平均分
     */
    private Double calculateRatingOverallAverage(List<SurveyQuestionDto> surveyQuestions, List<SurveyResultDto> surveyResults) {
        List<Double> allRatings = new ArrayList<>();
        
        // 找到所有评分题
        List<Integer> ratingQuestionNumbers = surveyQuestions.stream()
                .filter(q -> q.getQType() == 5)
                .map(SurveyQuestionDto::getQNumber)
                .collect(Collectors.toList());
        
        // 收集所有评分题的评分值
        for (SurveyResultDto result : surveyResults) {
            if (ratingQuestionNumbers.contains(result.getQNumber())) {
                try {
                    double rating = Double.parseDouble(result.getItemId());
                    allRatings.add(rating);
                } catch (NumberFormatException e) {
                    // 忽略无效的评分值
                }
            }
        }
        
        if (allRatings.isEmpty()) {
            return null;
        }
        
        return allRatings.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
    }

    /**
     * 后处理排序题：将选项内容转换为序号
     */
    private List<LinkedHashMap<String, Object>> postProcessSortQuestions(
            List<LinkedHashMap<String, Object>> result, 
            List<LinkedHashMap<String,Object>> questions,
            Integer surveyId) {
        
        // 收集所有排序题的问题编号
        Set<Integer> sortQuestionNumbers = new HashSet<>();
        for (LinkedHashMap<String,Object> q : questions) {
            Object qType = q.get("q_type");
            if (qType != null && (qType.equals(4) || qType.equals("4") || (qType instanceof Integer && ((Integer)qType) == 4))) { // 排序题
                Object qNumber = q.get("q_number");
                if (qNumber != null) {
                    sortQuestionNumbers.add((Integer) qNumber);
                }
            }
        }
        
        if (sortQuestionNumbers.isEmpty()) {
            return result;
        }
        
        // 处理每行数据
        for (LinkedHashMap<String, Object> row : result) {
            for (Integer qNumber : sortQuestionNumbers) {
                String columnName = findSortQuestionColumn(row, qNumber);
                if (columnName != null) {
                    Object value = row.get(columnName);
                    if (value != null && !value.toString().trim().isEmpty()) {
                        String convertedValue = convertSortQuestionToNumbers(value.toString(), qNumber, questions, surveyId);
                        row.put(columnName, convertedValue);
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 查找排序题对应的列名
     */
    private String findSortQuestionColumn(LinkedHashMap<String, Object> row, Integer qNumber) {
        for (String key : row.keySet()) {
            if (key.contains("第" + qNumber + "题")) {
                return key;
            }
        }
        return null;
    }
    
    /**
     * 将排序题的选项内容转换为序号
     */
    private String convertSortQuestionToNumbers(String sortResult, Integer questionNumber, List<LinkedHashMap<String,Object>> questions, Integer surveyId) {
        if (sortResult == null || sortResult.trim().isEmpty()) {
            return "";
        }
        
        try {
            // 找到对应的问题
            LinkedHashMap<String,Object> currentQuestion = null;
            for (LinkedHashMap<String,Object> q : questions) {
                Object qNumber = q.get("q_number");
                if (qNumber != null && qNumber.equals(questionNumber)) {
                    currentQuestion = q;
                    break;
                }
            }
            
            if (currentQuestion == null) {
                return sortResult; // 如果找不到问题，返回原始内容
            }
            
            Object questionIdObj = currentQuestion.get("id");
            Integer questionId = null;
            
            if (questionIdObj == null) {
                // 如果questions中没有ID，通过surveyId查询所有问题来找到对应的ID
                if (surveyId != null) {
                    try {
                        var allQuestions = surveyQuestionDao.getListBySurveyId(surveyId);
                        for (var q : allQuestions) {
                            if (q.getQNumber().equals(questionNumber)) {
                                questionId = q.getId();
                                break;
                            }
                        }
                    } catch (Exception e) {
                        // 查询失败，忽略异常
                    }
                }
                
                if (questionId == null) {
                    return sortResult; // 如果问题ID为空，返回原始内容
                }
            } else {
                questionId = (Integer) questionIdObj;
            }
            
            var surveyItems = surveyItemDao.getListByQId(questionId);
            
            if (surveyItems == null || surveyItems.isEmpty()) {
                return sortResult; // 如果没有选项，返回原始内容
            }
            
            // 建立选项内容到序号的映射
            Map<String, Integer> contentToNumberMap = new HashMap<>();
            for (var item : surveyItems) {
                if (item != null && item.getItemContent() != null && item.getItemNo() != null) {
                    contentToNumberMap.put(item.getItemContent(), item.getItemNo());
                }
            }
            
            // 分割排序结果并转换为序号
            String[] sortItems = sortResult.split("\\|");
            StringBuilder result = new StringBuilder();
            
            for (String item : sortItems) {
                String cleanItem = item.trim();
                if (!cleanItem.isEmpty()) {
                    Integer itemNumber = contentToNumberMap.get(cleanItem);
                    
                    if (result.length() > 0) {
                        result.append("|");
                    }
                    // 如果找到对应的序号就使用序号，否则使用原始内容
                    result.append(itemNumber != null ? itemNumber : cleanItem);
                }
            }
            
            return result.toString();
        } catch (Exception e) {
            // 如果出现异常，返回原始内容
            return sortResult;
        }
    }

    /**
     * 获取活动数据看板数据
     * @param dto 查询条件
     * @return 活动数据看板数据
     */
    @Override
    public ActivityDashboardDto getActivityDashboard(ActivityDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);

        // 获取基础统计数据
        ActivityDashboardDto result = activityDao.getActivityDashboardBasicStats(dto);
        if (result == null) {
            result = new ActivityDashboardDto();
            result.setActivityCount(0);
            result.setTotalClockInCount(0);
            result.setTotalClockOutCount(0);
            result.setSurveyCompletedCount(0);
        }

        // 获取活动类型统计数据
        List<ActivityTypeStatDto> typeStats = activityDao.getActivityTypeStats(dto);
        for (ActivityTypeStatDto stat : typeStats) {
            // 格式化时长显示
            if (stat.getTotalDuration() != null) {
                long hours = stat.getTotalDuration() / 60;
                long minutes = stat.getTotalDuration() % 60;
                stat.setTotalDurationFormatted(hours + "小时" + minutes + "分钟");
            }
        }
        result.setActivityTypeStats(typeStats);

        // 获取咨询师统计数据
        List<CounselorStatDto> counselorStats = activityDao.getCounselorStats(dto);
        for (CounselorStatDto stat : counselorStats) {
            // 格式化时长显示
            if (stat.getTotalDuration() != null) {
                long hours = stat.getTotalDuration() / 60;
                long minutes = stat.getTotalDuration() % 60;
                stat.setTotalDurationFormatted(hours + "小时" + minutes + "分钟");
            }
        }
        result.setCounselorStats(counselorStats);

        return result;
    }

    /**
     * 批量导出活动问卷作答记录为Word文档
     * @param dto 导出条件
     * @return 压缩包文件名
     */
    @Override
    public String exportSurveyToWord(ExportActivitySurveyRecordDto dto) {
        try {
            // 获取问卷作答记录数据 - 使用现有的导出方法获取基础数据
            List<LinkedHashMap<String,Object>> exportData = this.exportSurveyResult(dto);
            
            if (exportData == null || exportData.isEmpty()) {
                return "";
            }

            // 创建临时文件夹
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String folderName = "survey_word_" + sdf.format(new Date());
            String tempFolderPath = uploadPath + "/temp/" + folderName;
            File tempFolder = new File(tempFolderPath);
            if (!tempFolder.exists()) {
                tempFolder.mkdirs();
            }
            
            // 为每条记录生成Word文档
            for (int i = 0; i < exportData.size(); i++) {
                LinkedHashMap<String, Object> record = exportData.get(i);
                
                // 生成Word文档 - 记录id_用户名格式
                String recordId = record.get("记录id") != null ? 
                    record.get("记录id").toString() : 
                    String.valueOf(i + 1);
                String userName = record.get("用户名") != null ? 
                    record.get("用户名").toString().replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_") : 
                    "用户" + (i + 1);
                String wordFileName = String.format("%s_%s.docx", recordId, userName);
                String wordFilePath = tempFolderPath + "/" + wordFileName;
                
                // 创建Word文档内容
                createWordDocument(wordFilePath, record);
            }

            // 压缩文件夹
            String zipFileName = folderName + ".zip";
            String zipFilePath = uploadPath + "/temp/" + zipFileName;
            ZipUtil.zip(tempFolderPath, zipFilePath);
            
            // 删除临时文件夹
            deleteDirectory(new File(tempFolderPath));
            
            return zipFileName;
            
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 创建Word文档
     */
    private void createWordDocument(String outputPath, LinkedHashMap<String, Object> data) {
        try {
            // 创建Word文档
            XWPFDocument document = new XWPFDocument();
            
            // 获取问卷名称作为标题
            String surveyName = data.get("问卷名称") != null ? 
                data.get("问卷名称").toString() : "问卷作答记录";
            
            // 创建标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText(surveyName);
            titleRun.setBold(true);
            titleRun.setFontSize(16);
            
            // 空行
            document.createParagraph();
            
            // 提取并显示基本信息
            String userName = "";
            String realName = "";
            String recordDate = "";
            String department = "";
            
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (key.equals("用户名")) {
                    userName = value != null ? value.toString() : "";
                } else if (key.equals("姓名")) {
                    realName = value != null ? value.toString() : "";
                } else if (key.equals("作答时间")) {
                    recordDate = value != null ? value.toString() : "";
                } else if (key.equals("部门")) {
                    department = value != null ? value.toString() : "";
                }
            }
            
            // 显示基本信息
            addInfoLine(document, "用户名：" + userName);
            addInfoLine(document, "姓名：" + realName);
            addInfoLine(document, "部门：" + department);
            addInfoLine(document, "作答时间：" + recordDate);
            
            // 空行
            document.createParagraph();
            
            // 作答详情标题
            XWPFParagraph detailTitleParagraph = document.createParagraph();
            XWPFRun detailTitleRun = detailTitleParagraph.createRun();
            detailTitleRun.setText("作答详情：");
            detailTitleRun.setBold(true);
            detailTitleRun.setFontSize(12);
            
            // 空行
            document.createParagraph();
            
            // 处理题目和答案
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (key.startsWith("第") && key.contains("题")) {
                    // 题目
                    XWPFParagraph questionParagraph = document.createParagraph();
                    XWPFRun questionRun = questionParagraph.createRun();
                    questionRun.setText(key);
                    questionRun.setBold(true);
                    
                    // 答案
                    XWPFParagraph answerParagraph = document.createParagraph();
                    XWPFRun answerRun = answerParagraph.createRun();
                    answerRun.setText("答案：" + (value != null ? value.toString() : "未作答"));
                    answerRun.setColor("0000FF"); // 蓝色
                    
                    // 空行
                    document.createParagraph();
                }
            }
            
            // 保存文档
            FileOutputStream out = new FileOutputStream(outputPath);
            document.write(out);
            out.close();
            document.close();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 添加信息行
     */
    private void addInfoLine(XWPFDocument document, String text) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setFontSize(11);
    }



    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}

package cn.psycloud.psyplatform.serviceImpl.activityroom;

import cn.psycloud.psyplatform.dao.activityroom.ActivityPicDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivityPicEntity;
import cn.psycloud.psyplatform.service.activityroom.ActivityPicService;
import cn.psycloud.psyplatform.util.SessionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;

@Service
public class ActivityPicServiceImpl implements ActivityPicService {
    @Autowired
    private ActivityPicDao activityPicDao;

    /**
     * 添加活动图片
     * @param activityPicEntity 活动图片实体
     * @return 是否成功
     */
    @Override
    public int uploadPic(ActivityPicEntity activityPicEntity){
        activityPicEntity.setUploadTime(new Date());
        UserDto userDto = (UserDto)SessionUtil.getSession().getAttribute("user");
        activityPicEntity.setOperator(userDto.getUserId());
        return activityPicDao.uploadPic(activityPicEntity);
    }

    /**
     * 删除活动图片
     * @param id 活动图片id
     * @return 是否成功
     */
    @Override
    public int delPic(Integer id){
        return activityPicDao.delPic(id);
    }

    /**
     * 批量删除活动图片
     * @param ids 活动图片id集合
     * @return 是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        String[] idArray = ids.split(",");
        for (String id : idArray){
            isSuccess = activityPicDao.delPic(Integer.parseInt(id)) > 0;
        }
        return isSuccess;
    }

    /**
     * 获取活动图片列表
     * @param activityId 活动id
     * @return 活动图片列表
     */
    @Override
    public List<ActivityPicEntity> getList(Integer activityId){
        return activityPicDao.getList(activityId);
    }
}

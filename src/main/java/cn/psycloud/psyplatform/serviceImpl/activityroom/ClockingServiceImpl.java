package cn.psycloud.psyplatform.serviceImpl.activityroom;

import cn.psycloud.psyplatform.dao.activityroom.ClockingDao;
import cn.psycloud.psyplatform.dto.activityroom.ClockingDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.activityroom.ClockingEntity;
import cn.psycloud.psyplatform.service.activityroom.ClockingService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;

@Service
public class ClockingServiceImpl implements ClockingService {
    @Autowired
    private ClockingDao clockingDao;

    /**
     * 添加签到签退记录
     * @param clockingEntity 签到签退实体
     * @return 影响行数
     */
    @Override
    public int addClockingRecord(ClockingEntity clockingEntity) {
        UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        clockingEntity.setUserId(userDto.getUserId());
        clockingEntity.setClockingTime(new Date());
        return clockingDao.insert(clockingEntity);
    }

    /**
     * 查询签到签退记录是否存在
     * @param userId 用户ID
     * @param activityId 活动ID
     * @param actionType 操作类型：1-签到，2-签退
     * @return 是否签到签退
     */
    @Override
    public boolean isExists(Integer userId, Integer activityId, Integer actionType) {
        var map = new HashMap<String, Integer>();
        map.put("userId", userId);
        map.put("activityId", activityId);
        map.put("actionType", actionType);
        return clockingDao.isExists(map) > 0;
    }

    /**
     * 查询签到签退记录列表
     * @param clockingDto 签到签退DTO
     * @return 签到签退实体列表
     */
    @Override
    public BSDatatableRes<ClockingDto> getListByPaged(ClockingDto clockingDto){
        var dtRes = new BSDatatableRes<ClockingDto>();
        PageHelper.startPage(clockingDto.getPageIndex() / clockingDto.getPageSize() + 1, clockingDto.getPageSize());
        var clockingList = clockingDao.getList(clockingDto);
        var dto = new PageInfo<>(clockingList);
        dtRes.setData(clockingList);
        dtRes.setRecordsTotal((int) dto.getTotal());
        dtRes.setRecordsFiltered((int) dto.getTotal());
        return dtRes;
    }

    /**
     * 查询当前用户的活动状态
     * @param userId 用户ID
     * @param activityId 活动ID
     * @return 活动状态
     */
    @Override
    public Integer getUserActivityStatus(Integer userId, Integer activityId) {
        var map = new HashMap<String, Integer>();
        map.put("userId", userId);
        map.put("activityId", activityId);
        return clockingDao.getUserActivityStatus(map);
    }
}

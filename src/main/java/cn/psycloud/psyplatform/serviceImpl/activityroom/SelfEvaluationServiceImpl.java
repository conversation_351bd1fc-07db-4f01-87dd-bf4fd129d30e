package cn.psycloud.psyplatform.serviceImpl.activityroom;

import cn.psycloud.psyplatform.dao.activityroom.SelfEvaluationDao;
import cn.psycloud.psyplatform.dto.activityroom.SelfEvaluationDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.entity.activityroom.SelfEvaluationEntity;
import cn.psycloud.psyplatform.service.activityroom.SelfEvaluationService;
import cn.psycloud.psyplatform.util.SessionUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class SelfEvaluationServiceImpl implements SelfEvaluationService {
    @Autowired
    private SelfEvaluationDao selfEvaluationDao;

    /**
     * 插入个人点评
     * @param SelfEvaluations 个人点评实体集合
     * @return 插入行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addSelfEvaluation(List<SelfEvaluationEntity> SelfEvaluations) {
        boolean isSuccess = true;
        try {
            for (SelfEvaluationEntity entity : SelfEvaluations) {
                UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
                entity.setOperator(userDto.getUserId());
                entity.setOperateDate(new Date());

                var map = new HashMap<String, Object>();
                map.put("activityId", entity.getActivityId());
                map.put("userId", entity.getUserId());
                selfEvaluationDao.delete(map);
                if (selfEvaluationDao.insert(entity) <= 0) {
                    isSuccess = false;
                }
            }
        }
        catch (Exception e) {
            isSuccess = false;
            log.error("插入个人点评失败：{}", e.getMessage());
        }
        return isSuccess;
    }

    /**
     * 根据活动ID获取个人点评列表
     * @param activityId 活动ID
     * @return 个人点评列表
     */
    @Override
    public List<SelfEvaluationDto> getSelfEvaluationList(Integer activityId){
        return selfEvaluationDao.getSelfEvaluationList(activityId);
    }

    /**
     * 根据活动ID和用户ID获取个人点评
     * @param activityId 活动ID
     * @param userId 用户ID
     * @return 个人点评
     */
    @Override
    public String getMySelfEvaluationList(Integer activityId, Integer userId){
        var map = new HashMap<String, Object>();
        map.put("activityId", activityId);
        map.put("userId", userId);
        return selfEvaluationDao.getMySelfEvaluationList(map);
    }
}

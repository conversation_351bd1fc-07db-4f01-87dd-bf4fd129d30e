package cn.psycloud.psyplatform.serviceImpl.measuringroom.stat;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorDao;
import cn.psycloud.psyplatform.dto.measuringroom.TaskStatDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestScoreService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *  测评统计基类
 */
@Service
public class BaseStat {
    public String[] labels;
    public String recordIds;
    public Integer scaleId;

    @Autowired
    protected TestRecordService testRecordService;
    @Autowired
    protected TestScoreService testScoreService;
    @Autowired
    protected ScaleFactorDao scaleFactorDao;

    public void init(Integer scaleId){
        this.scaleId = scaleId;
    }

    public String draw(TaskStatDto dto){
        init(scaleId);
        return stat(dto);
    }

    public String stat(TaskStatDto dto){
        var k = new int[labels.length];
        for (int i = 0; i < k.length; i++) {
            k[i] = 0;
        }
        List<TestScoreDto> listTestScores;
        var arrayRecordIds = recordIds.split(",");
        for (int i = 0; i < arrayRecordIds.length; i++) {
            var recordId = Integer.parseInt(arrayRecordIds[i]);
            var testScoreDto = new TestScoreDto();
            testScoreDto.setRecordId(recordId);
            listTestScores = testScoreService.getTestScoreList(testScoreDto);
            for(TestScoreDto testScore: listTestScores) {
                k[testScore.getScore().intValue() - 1]++;
            }
        }
        return "";
    }
}

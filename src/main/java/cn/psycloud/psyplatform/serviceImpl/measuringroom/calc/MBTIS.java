package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * MBTI标准版（48题）
 */
@Service
public class MBTIS extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Override
    public void compute() {
        computeCommonFactor(true);
        var feature = "";
        if (listFactorScores.get(0).compareTo(listFactorScores.get(1)) > 0)
            feature += "E";
        else
            feature += "I";
        if (listFactorScores.get(2).compareTo(listFactorScores.get(3)) > 0)
            feature += "S";
        else
            feature += "N";
        if (listFactorScores.get(4).compareTo(listFactorScores.get(5)) > 0)
            feature += "T";
        else
            feature += "F";
        if (listFactorScores.get(6).compareTo(listFactorScores.get(7))> 0)
            feature += "P";
        else
            feature += "J";
        interpretation = "您的职业性格类型是：" + feature + "<br/>";
        var ex = getExplain(feature);
        interpretation += ex;
        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(HtmlUtil.cleanHtmlTag(interpretation.replace("<br/>","\r")));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }

    private String getExplain(String typeName) {
        return calcDao.getMBTIExplain(typeName);
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleAnswerDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.DuallistData;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleNameDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleService;
import cn.psycloud.psyplatform.util.POIWordHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
public class ScaleServiceImpl implements ScaleService {
    @Autowired
    private ScaleDao scaleDao;
    @Autowired
    private ScaleAnswerDao scaleAnswerDao;
    @Autowired
    private POIWordHelper poiWordHelper;

    /**
     * 根据量表ID查询量表详细信息
     * @param scaleId 量表id
     * @return 量表实体对象
     */
    @Override
    public ScaleDto getById(Integer scaleId) {
        return scaleDao.getById(scaleId);
    }

    /**
     *  根据条件查询量表集合
     * @param dto 量表实体对象
     * @return 量表集合
     */
    @Override
    public List<ScaleDto> getList(ScaleDto dto){
        return scaleDao.getList(dto);
    }

    /**
     *  查询量表集合：分页
     * @param dto 量表实体对象
     * @return 量表集合
     */
    @Override
    public BSDatatableRes<ScaleDto> getListByPaged(ScaleDto dto) {
        var dtRes = new BSDatatableRes<ScaleDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listScales = scaleDao.getList(dto);
        var scaleDto = new PageInfo<>(listScales);
        dtRes.setData(scaleDto.getList());
        dtRes.setRecordsTotal((int) scaleDto.getTotal());
        dtRes.setRecordsFiltered((int) scaleDto.getTotal());
        return dtRes;
    }

    /**
     *  查询量表集合：DualListBox格式
     * @return 集合
     */
    @Override
    public List<Object> getListForDualList() {
        var dto = new ScaleDto();
        dto.setIsDone(1);
        var listScales = scaleDao.getList(dto);
        var lsNode = new ArrayList<>();
        for (ScaleDto scale : listScales)
        {
            var duallistData = new DuallistData();
            duallistData.setId(scale.getId());
            duallistData.setName(scale.getScaleName());
            lsNode.add(duallistData);
        }
        return lsNode;
    }

    /**
     *  查询量表集合：select格式
     * @return 集合
     */
    @Override
    public List<Object> getListForSelect() {
        var listScales = scaleDao.getListForSelect();
        return getSelect2Data(listScales);
    }

    /**
     *  将数据格式转换成select2格式
     * @param listScales 量表集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<ScaleEntity> listScales) {
        var lsNode = new ArrayList<>();
        for (ScaleEntity entity : listScales) {
            var select2Data = new Select2Data();
            select2Data.setId(entity.getId());
            select2Data.setText(entity.getScaleName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  获取推荐量表集合
     * @return 量表集合
     */
    @Override
    public List<ScaleDto> getRecommendList(ScaleDto dto) {
        return scaleDao.getList(dto);
    }

    /**
     *  新增
     * @param entity 量表实体对象
     * @return 影响行数
     */
    @Override
    public int addScale(ScaleEntity entity) {
        entity.setCreateDate(new Date());
        return scaleDao.insert(entity);
    }

    /**
     *  更新
     * @param entity 量表实体对象
     * @return 影响行数
     */
    @Override
    public int updateScale(ScaleEntity entity) {
        return scaleDao.update(entity);
    }

    /**
     *  删除
     * @param id 量表id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id) {
        return scaleDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  验证量表名称是否存在
     * @param dto 量表实体对象
     * @return 是否存在
     */
    @Override
    public boolean isScaleNameExists(ScaleNameDto dto) {
        if (dto.getOriginalScaleName().equals(dto.getNewScaleName())) return false;
        return scaleDao.getCountByName(dto.getNewScaleName()) > 0;
    }

    /**
     *  更改量表状态
     * @param scaleId 量表id
     * @param state 状态
     * @return 影响行数
     */
    @Override
    public int updateScaleDone(Integer scaleId, Integer state) {
        var map = new HashMap<String,Integer>();
        map.put("scaleId",scaleId);
        map.put("state", state);
        return scaleDao.updateScaleDone(map);
    }

    /**
     *  获取量表类别下的量表集合
     * @param scaleTypeId 量表分类Id
     * @return 量表集合
     */
    @Override
    public List<ScaleDto> getListByType(Integer scaleTypeId) {
        var dto = new ScaleDto();
        dto.setIsDone(1);
        dto.setIsRecommend(1);
        dto.setScaleTypeId(scaleTypeId);
        return scaleDao.getList(dto);
    }

    /**
     *  根据测评任务查询量表集合
     * @param taskId 任务id
     * @return 集合
     */
    @Override
    public List<Object> getListByTaskId(Integer taskId) {
        var listScales = scaleDao.getListByTaskId(taskId);
        return getSelect2Data(listScales);
    }

    /**
     * 手机端首页
     * @return 量表集合
     */
    @Override
    public List<ScaleDto> getListForIndex() {
        return scaleDao.getListForIndex();
    }

    /**
     * 设置量表排序
     * @param scaleId 量表id
     * @param sort  排序
     * @return 影响行数
     */
    @Override
    public int setSort(Integer scaleId, Integer sort) {
        var map = new HashMap<String, Integer>();
        map.put("scaleId", scaleId);
        map.put("sort", sort);
        return scaleDao.setSort(map);
    }

    /**
     *  量表导出：word
     * @param scaleId 量表id
     * @return XWPFDocument对象
     */
    public XWPFDocument exportScale(Integer scaleId) {
        var scale = scaleDao.getScaleForExport(scaleId);
        poiWordHelper.createParagraph(1);
        poiWordHelper.createRun(scale.getScaleName(),true,false,"000000",20, "");
        poiWordHelper.createParagraph(0);
        poiWordHelper.createRun("指导语：",false,false,"",14,"");
        poiWordHelper.createParagraph(0);
        poiWordHelper.addTextIndent(2);
        poiWordHelper.createRun(HtmlUtil.cleanHtmlTag(scale.getScaleGuide()),false,false,"",14,"");
        poiWordHelper.createEmpty(1);

        var questions = scale.getListQuestions();
        if(questions != null) {
            for(ScaleQuestionEntity question: questions) {
                poiWordHelper.createParagraph(0);
                poiWordHelper.createRun(question.getQNumber() + "." +HtmlUtil.cleanHtmlTag(question.getQContent()),false,false,"",14,"");

                StringBuilder answerStr = new StringBuilder();
                var answers = scaleAnswerDao.getListByQId(question.getId());
                for(ScaleAnswerEntity answer: answers) {
                    var itemNo = new char[] { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L' };
                    answerStr.append(itemNo[answer.getANo() - 1]).append(".").append(answer.getAContent());
                    poiWordHelper.createParagraph(0);
                    poiWordHelper.createRun(itemNo[answer.getANo() - 1] + "."+ answer.getAContent(),false,false,"",14,"");
                }
                poiWordHelper.createEmpty(1);
            }
        }
        return poiWordHelper.document;
    }
}

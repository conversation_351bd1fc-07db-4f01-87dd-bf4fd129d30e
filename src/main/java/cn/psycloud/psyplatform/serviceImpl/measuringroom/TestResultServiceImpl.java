package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultCheckQuesEmbuDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultVO;
import cn.psycloud.psyplatform.entity.measuringroom.TestResultEntity;
import cn.psycloud.psyplatform.service.measuringroom.TestResultService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class TestResultServiceImpl implements TestResultService {
    @Autowired
    private TestResultDao testResultDao;
    /**
     *  保存答案
     * @return 执行是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveResult(TestResultVO vo) {
        var isSuccess = false;
        var listResults = vo.getListResults();
        var recordId = vo.getRecordId();
        try {
            // 先删除已存在的记录
            int deleteCount = testResultDao.deleteResultByRecordId(recordId);
            log.error("删除记录数: {}, recordId: {}", deleteCount, recordId);
            
            // 批量保存新记录
            for (TestResultEntity result : listResults) {
                if (result != null) {
                    int saveCount = testResultDao.saveResult(result);
                    if (saveCount <= 0) {
                        throw new RuntimeException("保存选项失败，recordId: " + recordId);
                    }
                }
            }
            isSuccess = true;
        } catch (Exception e) {
            log.error("保存选项时出错，recordId: {}, 错误信息: {}", recordId, e.getMessage(), e);
            throw new RuntimeException("保存选项失败", e);
        }
        return isSuccess;
    }

    /**
     *  保存答案：父母养育方式问卷(EMBU)
     * @return 执行是否成功
     */
    @Override
    public boolean saveResultEMBU(TestResultVO vo) {
        var isSuccess = false;
        var listResults = vo.getListEmbuResults();
        var recordId = vo.getRecordId();
        testResultDao.deleteResultByRecordId(recordId);
        var result = new TestResultEntity();
        for(TestResultCheckQuesEmbuDto dto: listResults){
            int qType = dto.getQType();
            result.setRecordId(recordId);
            result.setQNo(dto.getId() + 1);
            result.setQType(qType);
            String answer = String.valueOf(Integer.parseInt(dto.getFa_answer() == null ? "1" : dto.getFa_answer()) * 10 + Integer.parseInt(dto.getMo_answer() == null ? "1": dto.getMo_answer()));
            result.setANo(answer);
            isSuccess = testResultDao.saveResult(result) > 0;
        }
        return isSuccess;
    }

    /**
     *  根据条件查询答案记录
     * @param recordId 记录Id
     * @return 选项集合
     */
    @Override
    public List<TestResultDto> getList(Integer recordId) {
        return testResultDao.getList(recordId);
    }
}

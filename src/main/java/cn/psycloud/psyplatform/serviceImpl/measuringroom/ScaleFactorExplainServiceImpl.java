package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorExplainDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleFactorExplainService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ScaleFactorExplainServiceImpl implements ScaleFactorExplainService {
    @Autowired
    private ScaleFactorExplainDao scaleFactorExplainDao;

    /**
     *  删除因子解释
     * @param id 因子解释Id
     * @return 影响行数
     */
    @Override
    public int deleteById(int id) {
        return scaleFactorExplainDao.delete(id);
    }

    /**
     *  添加因子解释
     * @param entity 因子解释实体对象
     * @return 影响行数
     */
    @Override
    public int addExplain(ScaleFactorExplainEntity entity) {
        return scaleFactorExplainDao.insert(entity);
    }

    /**
     *  修改因子解释
     * @param entity 因子解释实体对象
     * @return 影响行数
     */
    @Override
    public int updateExplain(ScaleFactorExplainEntity entity) {
        return scaleFactorExplainDao.update(entity);
    }

    /**
     *  根据id查询因子结果解释
     * @param factorId 因子id
     * @return 结果解释集合
     */
    @Override
    public BSDatatableRes<ScaleFactorExplainEntity> getListById(Integer factorId) {
        var explains = scaleFactorExplainDao.getListById(factorId);
        var dtRes = new BSDatatableRes<ScaleFactorExplainEntity>();
        dtRes.setData(explains);
        dtRes.setRecordsFiltered(explains.size());
        dtRes.setRecordsTotal(explains.size());
        return dtRes;
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleAnswerDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleAService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ScaleAServiceImpl implements ScaleAService {
    @Autowired
    private ScaleAnswerDao scaleAnswerDao;

    /**
     *  查询条目的答案集合：返回Bootstrap Datatables格式
     * @param qId 题目id
     * @return 答案集合
     */
    @Override
    public BSDatatableRes<ScaleAnswerEntity> getAnswersByQId(Integer qId) {
        var dtRes = new BSDatatableRes<ScaleAnswerEntity>();
        var answers = scaleAnswerDao.getListByQId(qId);
        dtRes.setData(answers);
        dtRes.setRecordsTotal(answers.size());
        dtRes.setRecordsFiltered(answers.size());
        return dtRes;
    }

    /**
     *  添加答案（单个条目）
     * @param entity 答案实体对象
     * @return 影响行数
     */
    @Override
    public int addAnswer(ScaleAnswerEntity entity) {
        return scaleAnswerDao.insert(entity);
    }

    /**
     *  添加答案（批量）
     * @param entity 答案实体对象
     * @param qIds 题目id集合
     * @return 是否成功
     */
    @Override
    public boolean batchAddAnswer(ScaleAnswerEntity entity, String qIds) {
        var isSuccess = false;
        var array_ids = qIds.split(",");
        for (String arrayId : array_ids) {
            var qId = Integer.parseInt(arrayId);
            entity.setQId(qId);
            if(entity.getId() == 0){
                //如果存在则删除
                scaleAnswerDao.deleteByAid(entity.getId());
                //计算答案序号
                var count = scaleAnswerDao.getAnswerCount(qId);
                entity.setANo(count + 1);
                isSuccess = addAnswer(entity) > 0;
            }
            else{
                isSuccess = scaleAnswerDao.update(entity) > 0;
            }
        }
        return isSuccess;
    }

    /**
     *  删除答案
     * @param aId 答案id
     * @return 影响行数
     */
    @Override
    public int delete(Long aId) {
        return scaleAnswerDao.deleteByAid(aId);
    }
}

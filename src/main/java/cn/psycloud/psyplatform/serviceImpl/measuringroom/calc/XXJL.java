package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.dto.measuringroom.CheckAbnormalDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class XXJL extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Override
    public void compute(){
        var totalScore = computeTotalScore().intValue();
        var standardScore = getStandardScore(totalScore, age);
        var listFactorExplains = scaleFactorExplainDao.getListById(getTotalScoreFactorId());
        var factorId = listFactorExplains.get(0).getFactorId();
        var listAbnormalConditions = scaleFactorAbnormalConditionDao.getListByFactorId(factorId)
                .stream()
                .sorted(Comparator.comparing(ScaleFactorAbnormalConditionEntity::getScoreValue).reversed())
                .collect(Collectors.toList());
        BigDecimal conditionValue = BigDecimal.valueOf(0);
        CheckAbnormalDto checkAbnormalDto = new CheckAbnormalDto();
        checkAbnormalDto.setFactorId(factorId);
        checkAbnormalDto.setAge(age);
        checkAbnormalDto.setSex(sex);
        checkAbnormalDto.setFactorScore(BigDecimal.valueOf(standardScore));
        checkAbnormalDto.setConditionValue(conditionValue);
        checkAbnormalDto.setListAbnormalConditions(listAbnormalConditions);
        isAbnormal = checkIsAbnormal(checkAbnormalDto);
        var testScoreEntity = new TestScoreEntity();
        testScoreEntity.setRecordId(recordId);
        testScoreEntity.setFactorId(factorId);
        testScoreEntity.setOriginalScore(BigDecimal.valueOf(totalScore));
        testScoreEntity.setScore(BigDecimal.valueOf(standardScore));
        testScoreEntity.setIsAbnormal(isAbnormal ? 1 : 0);
        testScoreEntity.setAbnormalValue(checkAbnormalDto.getConditionValue());
        saveScore(testScoreEntity);
        interpretation = getFactorExplain();
    }

    private int getStandardScore(int totalScore, int age) {
        if (age <= 8) age = 9;
        if (age >= 18) age = 17;
        List<LinkedHashMap<String,Object>> listMap = calcDao.getXxjlNorm(age);
        var map = listMap.get(0);
        return Integer.parseInt(map.get(String.valueOf(totalScore)).toString());
    }
}

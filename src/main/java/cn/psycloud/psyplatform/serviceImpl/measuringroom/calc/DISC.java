package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.stream.Collectors;

@Service
public class DISC extends BaseCalc{
    @Autowired
    private TestResultDao testResultDao;
    @Override
    public void compute() {
        var listResults = testResultDao.getDiscList(recordId);
        var listFactors = getFactorList().stream().sorted(Comparator.comparing(ScaleFactorDto::getFactorNo)).collect(Collectors.toList());
        var score = new int[4][2];
        var factor = new String[listFactors.size()][2];
        for(TestResultDto result: listResults) {
            if (result.getFactorId()== 466) {
                score[0][0] = score[0][0] + 1;
                score[0][1] = 0;
                factor[0][0] = listFactors.get(0).getFactorName();
            }
            if (result.getFactorId()== 467) {
                score[1][0] = score[1][0] + 1;
                score[1][1] = 1;
                factor[1][0] = listFactors.get(1).getFactorName();
            }
            if (result.getFactorId() == 468) {
                score[2][0] = score[2][0] + 1;
                score[2][1] = 2;
                factor[2][0] = listFactors.get(2).getFactorName();
            }
            if (result.getFactorId() == 469) {
                score[3][0] = score[3][0] + 1;
                score[3][1] = 3;
                factor[3][0] = listFactors.get(3).getFactorName();
            }
        }
        var m = 0;
        for(ScaleFactorDto scaleFactorDto: listFactors){
            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(scaleFactorDto.getId());
            testScoreEntity.setOriginalScore(BigDecimal.valueOf(score[m][0]));
            testScoreEntity.setScore(BigDecimal.valueOf(score[m][0]));
            testScoreEntity.setIsAbnormal(0);
            testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
            saveScore(testScoreEntity);
            m++;
        }
        StringBuilder positiveFactor = new StringBuilder();
        StringBuilder factorNameStr = new StringBuilder();
        //结果解释
        for (int i = 0; i < 4; i++) {
            if (score[i][0] > 10) {
                positiveFactor.append(listFactors.get(score[i][1]).getId()).append(",");
                factorNameStr.append(factor[i][0]).append(",");
            }
        }
        var responseText = new StringBuilder();
        responseText.append(String.format("您的性格类型是：%s<br><br>",factorNameStr.substring(0, factorNameStr.length() - 1)));
        var listExplains = scaleFactorExplainDao.getListByIds(positiveFactor.substring(0, positiveFactor.length() - 1));
        for (ScaleFactorExplainEntity listExplain : listExplains) {
            responseText.append(listExplain.getFactorName()).append(listExplain.getInterpretation()).append("<br>");
        }
        interpretation = responseText.toString();

        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(HtmlUtil.cleanHtmlTag((interpretation.replace("<br>", "\r"))));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

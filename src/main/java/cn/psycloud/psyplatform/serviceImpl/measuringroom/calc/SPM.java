package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

/**
 *  瑞文标准推理
 */
@Service
public class SPM extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Override
    public void compute(){
        computeCommonFactor(false);
        int grade = 0;
        String level = "";
        var totalScore = computeTotalScore();
        if (age > 17 && age < 20) age = 17;
        else if (age >= 20) age = ((int) age / 10) * 10;
        var listNormMap= getSpmNorm(age);
        for(LinkedHashMap<String,Object> map: listNormMap){
            var level5 = BigDecimal.valueOf(Long.parseLong(map.get("level5").toString()));
            var level25 = BigDecimal.valueOf(Long.parseLong(map.get("level25").toString()));
            var level75 = BigDecimal.valueOf(Long.parseLong(map.get("level75").toString()));
            var level95 = BigDecimal.valueOf(Long.parseLong(map.get("level95").toString()));
            if (totalScore.compareTo(level5) < 0)
                grade = 5;
            if (totalScore.compareTo(level5) >= 0 && totalScore.compareTo(level25) < 0)
                grade = 4;
            if (totalScore.compareTo(level25) >= 0 && totalScore.compareTo(level75) < 0)
                grade = 3;
            if (totalScore.compareTo(level75) >= 0 && totalScore.compareTo(level95) < 0)
                grade = 2;
            if (totalScore.compareTo(level95) >= 0)
                grade = 1;
            switch (grade) {
                case 1:
                    level = "测量结果反映出你的智力处于很高的水平。";
                    break;
                case 2:
                    level = "测量结果反映出你的智力水平良好。";
                    break;
                case 3:
                    level = "测量结果反映出你的智力处于中等水平。";
                    break;
                case 4:
                    level = "测量结果反映出你的智力水平不是很高。";
                    break;
                case 5:
                    level = "测量结果反映出你的智力有些缺陷。";
                    break;
            }
        }
        var factorId = getTotalScoreFactorId();
        var testScoreEntity = new TestScoreEntity();
        testScoreEntity.setRecordId(recordId);
        testScoreEntity.setFactorId(factorId);
        testScoreEntity.setOriginalScore(totalScore);
        testScoreEntity.setScore(totalScore);
        testScoreEntity.setIsAbnormal(0);
        testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
        saveScore(testScoreEntity);

        interpretation =String.format("得分：%.2f。智力级别：%d,%s",totalScore,grade,level);
        var testRecordExplain = new TestRecordExplainEntity();
        testRecordExplain.setRecordId(recordId);
        testRecordExplain.setFactorId(factorId);
        testRecordExplain.setInterpretation(interpretation);
        testRecordDao.saveTestRecordExplain(testRecordExplain);
    }

    private List<LinkedHashMap<String,Object>> getSpmNorm(int age) {
        return calcDao.getSpmNorm(age);
    }
}

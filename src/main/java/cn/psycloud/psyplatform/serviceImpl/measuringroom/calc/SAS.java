package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.CheckAbnormalDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.stream.Collectors;

/**
 * 焦虑自评量表（SAS）
 */
@Service
public class SAS extends BaseCalc{
    @Override
    public void compute() {
        var totalScore = computeTotalScore();
        var standardScore = totalScore.multiply(BigDecimal.valueOf(1.25));
        var listFactors = getFactorList();
        var listSingleFactors = listFactors.stream().filter(r->r.getFactorType() == 1).collect(Collectors.toList());;
        for (ScaleFactorDto factor: listSingleFactors){
            //异常判定规则
            var listAbnormalConditions = scaleFactorAbnormalConditionDao
                    .getListByFactorId(factor.getId())
                    .stream()
                    .sorted(Comparator.comparing(ScaleFactorAbnormalConditionEntity::getScoreValue).reversed())
                    .collect(Collectors.toList());
            //判断是否正常
            BigDecimal conditionValue = BigDecimal.valueOf(0);
            CheckAbnormalDto checkAbnormalDto = new CheckAbnormalDto();
            checkAbnormalDto.setFactorId(factor.getId());
            checkAbnormalDto.setAge(age);
            checkAbnormalDto.setSex(sex);
            checkAbnormalDto.setFactorScore(standardScore);
            checkAbnormalDto.setConditionValue(conditionValue);
            checkAbnormalDto.setListAbnormalConditions(listAbnormalConditions);
            isAbnormal = checkIsAbnormal(checkAbnormalDto);

            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(factor.getId());
            testScoreEntity.setOriginalScore(totalScore);
            testScoreEntity.setScore(standardScore);
            testScoreEntity.setIsAbnormal(isAbnormal ? 1 : 0);
            testScoreEntity.setAbnormalValue(checkAbnormalDto.getConditionValue());
            saveScore(testScoreEntity);
        }
        interpretation = getFactorExplain();
    }
}

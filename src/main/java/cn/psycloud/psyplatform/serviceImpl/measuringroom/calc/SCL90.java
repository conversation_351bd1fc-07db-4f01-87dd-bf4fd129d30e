package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
public class SCL90 extends BaseCalc{
    private BigDecimal negativeScore = new BigDecimal(0);
    private BigDecimal positiveScore = new BigDecimal(0);
    private int positiveCount = 0, negativeCount = 0;

    @Autowired
    private TestResultDao testResultDao;

    /**
     *  计算scl-90量表结果中阳性项目数
     * @param recordId 测评记录id
     */
    private void calcScl90PositiveAndNegativeCount(Integer recordId) {
        positiveCount = 0;
        negativeCount = 0;
        var listTestResults = testResultDao.getList(recordId);
        for(TestResultDto dto: listTestResults) {
            if(dto.getAScore().compareTo(new BigDecimal(2)) >= 0) {
                positiveCount++;
                positiveScore = positiveScore.add(dto.getAScore());
            }
            if(dto.getAScore().compareTo(new BigDecimal(1)) == 0) {
                negativeCount++;
                negativeScore = negativeScore.add(dto.getAScore());
            }
        }
    }

    /**
     *  计算阳性症状均分
     * @return 阳性症状均分
     */
    private BigDecimal calcPositiveAvgScore() {
        BigDecimal totalScore= computeTotalScore();
        return positiveCount == 0 ? new BigDecimal(0) : totalScore.subtract(new BigDecimal(negativeCount)).divide(new BigDecimal(positiveCount), 2, RoundingMode.HALF_UP);
    }

    @Override
    public void compute() {
        var reponseText = new StringBuilder();
        computeCommonFactor(true);
        reponseText.append(getFactorExplain());
        var avgTotalScore = computeFactorAvgScore();
        String avgTotalExplain = "";
        if (avgTotalScore.compareTo(new BigDecimal(1)) >= 0 && avgTotalScore.compareTo(new BigDecimal("1.5")) < 0)
            avgTotalExplain = "表明被试自我感觉没有量表所列的症状";
        if (avgTotalScore.compareTo(new BigDecimal("1.5")) >= 0 && avgTotalScore.compareTo(new BigDecimal("2.5")) < 0)
            avgTotalExplain = "表明被试感觉有点症状，但发生得并不频繁";
        if (avgTotalScore.compareTo(new BigDecimal("2.5")) >=0 && avgTotalScore.compareTo(new BigDecimal("3.5")) < 0)
            avgTotalExplain = "表明被试感觉有症状，其严重程度为轻到中度";
        if (avgTotalScore.compareTo(new BigDecimal("3.5")) >= 0 && avgTotalScore.compareTo(new BigDecimal("4.5")) < 0)
            avgTotalExplain = "表明被试感觉有症状，其程度为中到严重";
        if (avgTotalScore.compareTo(new BigDecimal("4.5")) >=0 && avgTotalScore.compareTo(new BigDecimal(5)) < 0)
            avgTotalExplain = "表明被试感觉有症状，且症状的频度和强度都十分严重";
        calcScl90PositiveAndNegativeCount(recordId);
        var positiveAvgScore = calcPositiveAvgScore();
        reponseText.append(String.format("<dt class=\"pb10\"></dt><dd class=\"pb10\">总均分：%.2f，%s。阳性项目数：%d，阴性项目数：%d。阳性症状均分：%.2f，表示被试在“有症状”项目中的平均得分。<dd/>",
                avgTotalScore,
                avgTotalExplain,
                positiveCount,
                negativeCount,
                positiveAvgScore
        ));
        interpretation = reponseText.toString();
        var testRecordExplain = new TestRecordExplainEntity();
        testRecordExplain.setRecordId(recordId);
        testRecordExplain.setFactorId(0);
        testRecordExplain.setInterpretation(String.format("总均分：%.2f，%s。阳性项目数：%d，阴性项目数：%d。阳性症状均分：%.2f，表示被试在“有症状”项目中的平均得分。",
                avgTotalScore,
                avgTotalExplain,
                positiveCount,
                negativeCount,
                positiveAvgScore
        ));
        testRecordDao.saveTestRecordExplain(testRecordExplain);
    }
}

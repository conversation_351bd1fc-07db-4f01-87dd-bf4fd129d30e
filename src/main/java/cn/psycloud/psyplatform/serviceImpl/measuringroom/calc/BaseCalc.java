package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.anteroom.MailDao;
import cn.psycloud.psyplatform.dao.measuringroom.*;
import cn.psycloud.psyplatform.dto.measuringroom.CheckAbnormalDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.entity.anteroom.MailEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateUtil.ageOfNow;

/**
 *  计算量表得分基类
 */
@Slf4j
@Service
public class BaseCalc {
    protected int recordId, scaleId, userId, age, state, qCount;
    protected String sex, interpretation;
    protected boolean isAbnormal =false;
    protected boolean hasAbnormal = false;
    protected List<BigDecimal> listOriginalScores;
    protected List<BigDecimal> listFactorScores;
    protected List<Integer> listFactorIds;
    protected TestRecordDto testRecordDto;

    @Autowired
    protected TestRecordDao testRecordDao;
    @Autowired
    protected TestScoreDao testScoreDao;
    @Autowired
    protected ScaleFactorDao scaleFactorDao;
    @Autowired
    protected ScaleFactorAbnormalConditionDao scaleFactorAbnormalConditionDao;
    @Autowired
    protected ScaleFactorExplainDao scaleFactorExplainDao;
    @Autowired
    protected SysConfigService sysConfigService;
    @Autowired
    private MailDao mailDao;

    /**
     *  计算量表得分
     */
    @Transactional(rollbackFor = Exception.class)
    public void compute(Integer recordId)
    {
        try{
            init(recordId);
            compute();
            updateDone();
            updateTestCount();
            if(hasAbnormal) {
                var sysConfigDto = sysConfigService.get();
                //发送站内消息
                if (sysConfigDto.getIsAbnormalNotify() == 1) {
                    abnormalNotify();
                }
            }
        }
        catch (Exception e){
            log.error("量表计分出现异常：{}",e.getMessage());
        }
    }

    public void compute(){

    }

    /**
     *  初始化方法
     * @param recordId 测评记录Id
     */
    public void init(Integer recordId) {
        this.recordId = recordId;
        testScoreDao.deleteByRecordId(recordId);
        testRecordDao.deleteTestRecordExplain(recordId);
        this.testRecordDto = testRecordDao.getById(recordId);
        this.scaleId = testRecordDto.getScaleId();
        this.userId = testRecordDto.getUser().getUserId();
        this.age = ageOfNow(testRecordDto.getUser().getBirth());
        this.sex = testRecordDto.getUser().getSex();
        this.qCount = testRecordDto.getQCount();
    }

    /**
     *  更新测评完成状态
     */
    public void updateDone()
    {
        var map = new HashMap<String, Object>();
        map.put("recordId",recordId);
        map.put("state",1);
        map.put("interpretation",interpretation);
        map.put("endTime",new Date());
        testRecordDao.updateTestRecord(map);
    }

    /**
     *  更新量表测试次数
     */
    public void updateTestCount() {
        testRecordDao.updateTestCount(scaleId);
    }

    /**
     *  异常通知
     */
    public void abnormalNotify() {
        var map = testRecordDao.getAbnormalNotifyInfo(recordId);
        if(map != null) {
            var mailEntity = new MailEntity();
            mailEntity.setMsgTitle("测试异常消息");
            mailEntity.setMsgContent(String.format("被试者[%s]在量表[%s]上测试异常，如需了解详情，请前往心理异常筛选里查看。",map.get("realName"),map.get("scaleName")));
            mailEntity.setToUser(Integer.parseInt(map.get("userId").toString()));
            mailEntity.setFromUser(1);
            mailEntity.setIsRead(0);
            mailEntity.setSendDate(new Date());
            mailDao.sendMsg(mailEntity);
        }
    }

    /**
     *  获取因子集合
     * @return 因子集合
     */
    public List<ScaleFactorDto> getFactorList() {
        return scaleFactorDao.getFactorsByScaleId(scaleId);
    }

    /**
     *  计算普通因子得分
     * @param isSave 是否保存结果
     */
    public void computeCommonFactor(boolean isSave) {
        //原始分
        listOriginalScores = new ArrayList<>();
        //因子分
        listFactorScores = new ArrayList<>();
        //因子ID
        listFactorIds = new ArrayList<>();
        var listSingleFactors = getFactorList();
        for (ScaleFactorDto factor: listSingleFactors) {
            var originalScore = BigDecimal.valueOf(0);
            if(factor.getFactorType() == 1){
                //计算原始分
                originalScore = testScoreDao.computeFactorScore(recordId,factor.getQIds().split(","));
            }
            if(factor.getFactorType() == 2 || factor.getFactorType() == 3){
                originalScore = testScoreDao.computeMultiFactorScore(recordId, factor.getFactorIds().split(","));
            }
            BigDecimal factorScore = originalScore;
            //判断计分方式
            int formula = factor.getFormulaId();
            switch (formula) {
                case 2: // 平均分
                    factorScore = originalScore.divide(BigDecimal.valueOf(factor.getQIds().split(",").length), 2,RoundingMode.HALF_UP);
                    break;
                case 3: // Z分
                    factorScore = (originalScore.subtract(factor.getAvgScore())).divide(factor.getStandardScore(), 2,RoundingMode.HALF_UP);
                    break;
                case 4: // T分
                    factorScore = (originalScore.subtract(factor.getAvgScore())).divide(factor.getStandardScore(), 2,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(10)).add(BigDecimal.valueOf(50));
                    break;
            }
            listOriginalScores.add(originalScore);
            listFactorScores.add(factorScore);
            listFactorIds.add(factor.getId());
            if (isSave) {
                //异常判定规则
                var listAbnormalConditions = scaleFactorAbnormalConditionDao
                        .getListByFactorId(factor.getId())
                        .stream()
                        .sorted(Comparator.comparing(ScaleFactorAbnormalConditionEntity::getScoreValue).reversed())
                        .collect(Collectors.toList());
                var checkAbnormalDto = new CheckAbnormalDto();
                BigDecimal conditionValue = new BigDecimal(0);
                if(listAbnormalConditions.size() > 0){
                    //判断是否正常
                    checkAbnormalDto.setFactorId(factor.getId());
                    checkAbnormalDto.setAge(age);
                    checkAbnormalDto.setSex(sex);
                    checkAbnormalDto.setFactorScore(factorScore);
                    checkAbnormalDto.setConditionValue(conditionValue);
                    checkAbnormalDto.setListAbnormalConditions(listAbnormalConditions);
                    isAbnormal = checkIsAbnormal(checkAbnormalDto);
                }
                if(isAbnormal) hasAbnormal = true;
                //保存因子得分
                var testScoreEntity = new TestScoreEntity();
                testScoreEntity.setRecordId(recordId);
                testScoreEntity.setFactorId(factor.getId());
                testScoreEntity.setOriginalScore(originalScore);
                testScoreEntity.setScore(factorScore);
                testScoreEntity.setIsAbnormal(isAbnormal ? 1 : 0);
                testScoreEntity.setAbnormalValue(checkAbnormalDto.getConditionValue());
                saveScore(testScoreEntity);
            }
        }
    }

    /**
     *  计算复合因子
     */
    public void computeHyperFactor() {
        var listFactors = getFactorList();
        var listComplexFactors = listFactors
                .stream()
                .filter(a->a.getFactorType() == 2)
                .sorted(Comparator.comparing(ScaleFactorDto::getId))
                .collect(Collectors.toList());
        for (ScaleFactorDto factor: listComplexFactors) {
            String formula = factor.getCompute();
            //从普通因子中匹配
            for (int i = 0; i < listFactorIds.size(); i++) {
                String t = "{" + listFactorIds.get(i) + "}";
                formula = formula.replace(t, listFactorScores.get(i).toString());
            }
            //公式被替换成一个计算表达式,让数据库去计算
            String sql = String.format("select %s", formula);
            BigDecimal originalScore = testScoreDao.getComplexFactorScore(sql);
            BigDecimal factorScore = originalScore;
            //判断计分方式
            int formulaId = factor.getFormulaId();
            switch (formulaId) {
                case 2: //平均分
                    factorScore = originalScore.divide(BigDecimal.valueOf(factor.getCompute().split(",").length), 2,RoundingMode.HALF_UP);
                    break;
                case 3: // Z分
                    factorScore = (originalScore.subtract(factor.getAvgScore())).divide(factor.getStandardScore(),2,RoundingMode.HALF_UP);
                    break;
                case 4: //T分
                    factorScore = (originalScore.subtract(factor.getAvgScore())).divide(factor.getStandardScore(), 2,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(10)).add(BigDecimal.valueOf(50));
                    break;
            }
            //异常判定规则
            var listAbnormalConditions = scaleFactorAbnormalConditionDao
                    .getListByFactorId(factor.getId())
                    .stream()
                    .sorted(Comparator.comparing(ScaleFactorAbnormalConditionEntity::getScoreValue).reversed())
                    .collect(Collectors.toList());
            //判断是否正常
            BigDecimal conditionValue = new BigDecimal(0);
            var checkAbnormalDto = new CheckAbnormalDto();
            checkAbnormalDto.setFactorId(factor.getId());
            checkAbnormalDto.setAge(age);
            checkAbnormalDto.setSex(sex);
            checkAbnormalDto.setFactorScore(factorScore);
            checkAbnormalDto.setConditionValue(conditionValue);
            checkAbnormalDto.setListAbnormalConditions(listAbnormalConditions);
            isAbnormal = checkIsAbnormal(checkAbnormalDto);
            if(isAbnormal) hasAbnormal = true;
            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(factor.getId());
            testScoreEntity.setOriginalScore(originalScore);
            testScoreEntity.setScore(factorScore);
            testScoreEntity.setIsAbnormal(isAbnormal ? 1 : 0);
            testScoreEntity.setAbnormalValue(conditionValue);
            saveScore(testScoreEntity);

            listFactorIds.add(factor.getId());
            listOriginalScores.add(originalScore);
            listFactorScores.add(factorScore);
        }
    }

    /**
     *  计算总分
     * @return 总分
     */
    public BigDecimal computeTotalScore() {
        return testScoreDao.computeFactorScore(recordId,null);
    }

    /**
     *  计算总均分
     * @return 总均分
     */
    public BigDecimal computeFactorAvgScore() {
        return computeTotalScore().divide(BigDecimal.valueOf(qCount), 2, RoundingMode.HALF_UP);
    }

    /**
     *  获取因子结果解释
     * @return 因子结果解释
     */
    public String getFactorExplain() {
        var responseText = new StringBuilder();
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        var listTestScores = testScoreDao.getTestScoreList(testScoreDto);
        var listFactorExplains = scaleFactorExplainDao.getListByScaleId(scaleId);
        for (TestScoreDto testScore: listTestScores){
            String exp = "";
            var score = testScore.getScore();
            var explains = listFactorExplains.stream().filter(fe -> Objects.equals(fe.getFactorId(), testScore.getFactor().getId())).collect(Collectors.toList());
            var warningLevel = 0;
            for(ScaleFactorExplainEntity factorExplain: explains){
                BigDecimal startValue = factorExplain.getStartValue();
                BigDecimal endValue = factorExplain.getEndValue();
                if(score.compareTo(startValue) >= 0 && score.compareTo(endValue) <= 0) {
                    exp = factorExplain.getInterpretation();
                    warningLevel = factorExplain.getWarningLevel() == null ? 0 :factorExplain.getWarningLevel();
                    break;
                }
            }

            var map = new HashMap<String, Integer>();
            map.put("recordId", recordId);
            map.put("factorId", testScore.getFactor().getId());
            map.put("warningLevel", warningLevel);
            testScoreDao.updateWarningLevel(map);

            var testRecordExplainEntity = new TestRecordExplainEntity();
            testRecordExplainEntity.setRecordId(recordId);
            testRecordExplainEntity.setFactorId(testScore.getFactor().getId());

            boolean isAbnormal = testScore.getIsAbnormal() > 0;
            if (testScore.getFactor().getIsLie() == 1) {
                if (isAbnormal) {
                    state = 2;
                    responseText.setLength(0);
                    responseText.append(String.format("测谎得分：%.2f，掩饰性高，结果不可信。",testScore.getOriginalScore()));
                    testRecordExplainEntity.setInterpretation(String.format("测谎得分：%.2f，掩饰性高，结果不可信。",testScore.getOriginalScore()));
                    testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
                    break;
                }
            }
            var originalScore = testScore.getOriginalScore().compareTo(score) == 0 ? testScore.getOriginalScore() : score;
            var abnormal = isAbnormal ? "（<span class=\"text-danger\">异常</span>）": "";
            testRecordExplainEntity.setInterpretation(String.format("因子[%s]得分%.2f%s 。%s",
                    testScore.getFactor().getFactorName(),
                    originalScore,
                    abnormal,
                    exp
                    )
            );
            testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
            responseText.append(String.format("<dt class=\"pb10\">%s</dt><dd class=\"pb10\">因子\"%s\"得分%.2f %s。%s<dd/>",
                    testScore.getFactor().getFactorName(),
                    testScore.getFactor().getFactorName(),
                    originalScore,
                    abnormal,
                    exp
                    ));
        }
        return responseText.toString();
    }

    /**
     *  获取总分因子ID
     * @return 总分因子ID
     */
    public int getTotalScoreFactorId(){
        return  getFactorList().get(0).getId();
    }

    /**
     *  检测是否正常
     * @param factorScore 因子分
     * @param conditionValue 异常值
     * @param unCondition 异常条件
     * @return 是否异常
     */
    public boolean check(BigDecimal factorScore, BigDecimal conditionValue, int unCondition)
    {
        boolean abnormal = false;
        switch (unCondition) {
            case 1:
                abnormal = factorScore.compareTo(conditionValue) > 0; //大于
                break;
            case 2:
                abnormal = factorScore.compareTo(conditionValue) >= 0; //大于等于
                break;
            case 3:
                abnormal = factorScore.compareTo(conditionValue) < 0; //小于
                break;
            case 4:
                abnormal = factorScore.compareTo(conditionValue) <= 0; //小于等于
                break;
            case 5:
                abnormal = factorScore.compareTo(conditionValue) == 0; //等于
                break;
        }
        return abnormal;
    }

    /**
     *  判断是否异常
     * @param dto 判断是否异常实体对象
     * @return 是否异常
     */
    public boolean checkIsAbnormal(CheckAbnormalDto dto) {
        boolean abnormal = false;
        //读出异常判定规则
        if (dto.getListAbnormalConditions().size()> 0) {
            //遍历每一条规则
            for (ScaleFactorAbnormalConditionEntity abnormalCondition: dto.getListAbnormalConditions()) {
                if (abnormalCondition.getFactorId() == dto.getFactorId()) {
                    if (abnormalCondition.getAgeCondition() != 0) //年龄限制{
                        if (!check(BigDecimal.valueOf(age), BigDecimal.valueOf(abnormalCondition.getAgeValue()), Integer.parseInt(abnormalCondition.getAgeCondition().toString()))){
                            continue; //年龄不符,PASS
                        }
                }
                //性别限制
                if (abnormalCondition.getSexCondition() != null && !"".equals(abnormalCondition.getSexCondition()) ) {
                    if (!sex.equals(abnormalCondition.getSexCondition())){
                        continue; //性别不符,PASS
                    }
                }
                dto.setConditionValue(abnormalCondition.getScoreValue());
                abnormal = check(dto.getFactorScore(), dto.getConditionValue(), abnormalCondition.getScoreCondition());
                if (abnormal) { //发现异常即退出判定
                    break;
                }
            }
        }
        return abnormal;
    }

    /// <summary>
    /// 保存结果
    /// </summary>
    /// <param name="model"></param>
    public void saveScore(TestScoreEntity entity) {
        var map = new HashMap<String, Integer>();
        map.put("recordId", entity.getRecordId());
        map.put("factorId", entity.getFactorId());
        testScoreDao.deleteByRecordIdAndFactorId(map);
        testScoreDao.addTestScore(entity);
    }
}

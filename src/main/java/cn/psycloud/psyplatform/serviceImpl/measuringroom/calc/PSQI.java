package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.dto.measuringroom.CheckAbnormalDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.stream.Collectors;

/**
 * 匹兹堡睡眠质量指数(PSQI )
 */
@Service
public class PSQI extends BaseCalc{
    @Autowired
    private TestResultDao testResultDao;
    @Override
    public void compute(){
        var listFactors = getFactorList();
        int sleep_time = 0;
        BigDecimal score =BigDecimal.valueOf(0);
        BigDecimal totalScore = BigDecimal.valueOf(0);
        var responseText = new StringBuilder();
        for(ScaleFactorDto scaleFactorDto: listFactors){
            if (scaleFactorDto.getFactorType() == 1) {
                //region 睡眠质量
                //睡眠质量：根据条目6的应答计分“较好”计1分，“较差”计2分，“很差”计3分。
                if ("睡眠质量".equals(scaleFactorDto.getFactorName())){
                    score = testScoreDao.computeFactorScore(recordId, new String[]{"15"});
                }
                //endregion
                //region 入睡时间
                /* 入睡时间：
                 * 1. 条目2的计分为“≤15分”计0分，“16～30分”计1分，“31~60”计2分，“≥  60分”计3分。
                 * 2. 条目5a的计分为“无”计0分，“<1周/次”计1分，“1~2周/次”计2分，“≥3周/次”计3分。
                 * 3. 累加条目2和5a的计分，若累加分为“0”计0分，“1~2”计1分，“3~4”计2分，“5~6”计3分
                 */
                if ("入睡时间".equals(scaleFactorDto.getFactorName()) ) {
                    BigDecimal score_2 = testScoreDao.computeFactorScore(recordId, new String[]{"2"});
                    BigDecimal score_5a = testScoreDao.computeFactorScore(recordId, new String[]{"5"});
                    BigDecimal score_2_5a = score_2.add(score_5a);
                    if (score_2_5a.compareTo(BigDecimal.valueOf(0)) == 0)
                        score = BigDecimal.valueOf(0);
                    if (score_2_5a.compareTo(BigDecimal.valueOf(1)) >= 0 && score_2_5a.compareTo(BigDecimal.valueOf(2))<= 0)
                        score = BigDecimal.valueOf(1);
                    if (score_2_5a.compareTo(BigDecimal.valueOf(3)) >= 0 && score_2_5a.compareTo(BigDecimal.valueOf(4)) <= 0)
                        score = BigDecimal.valueOf(2);
                    if (score_2_5a.compareTo(BigDecimal.valueOf(5)) >= 0 && score_2_5a.compareTo(BigDecimal.valueOf(6)) <= 0)
                        score = BigDecimal.valueOf(3);
                }
                //endregion
                //region 睡眠时间
                //睡眠时间：根据条目4的应答计分，“>7小时”计0分，“6~7”计1分，“5~6”计2分，“<5小时”计3分。
                if ("睡眠时间".equals(scaleFactorDto.getFactorName())) {
                    sleep_time = 0;
                    var _resultList = testResultDao.getList(recordId);
                    if (_resultList.size() == 0) return;
                    for (int i = 0; i < _resultList.size(); i++){
                        if (i == 3)
                            sleep_time = Integer.parseInt(_resultList.get(0).getANos());
                    }
                    if (sleep_time > 7) {
                        score = BigDecimal.valueOf(0);
                    }
                    if (sleep_time >= 6 && sleep_time <= 7) {
                        score = BigDecimal.valueOf(1);
                    }
                    if (sleep_time == 5) {
                        score = BigDecimal.valueOf(2);
                    }
                    if (sleep_time < 5) {
                        score = BigDecimal.valueOf(3);
                    }
                }
                //endregion
                //region 睡眠效率
                /* 睡眠效率：
                 1. 床上时间 = 条目3（起床时间）- 条目1（上床时间）  
                 2. 睡眠效率 = 条目4（睡眠时间）/ 床上时间 × 100%   
                 3. 成分D计分位，睡眠效率 > 85%计0分，75~84% 计1分，65~74%计2分，< 65% 计3分。
                 */
                if ("睡眠效率".equals(scaleFactorDto.getFactorName())) {
                    int sleep_end = 0;//起床时间 ,
                    int sleep_start = 0;//上床时间
                    var _resultList = testResultDao.getList(recordId);
                    if (_resultList.size() == 0) return;
                    for (int i = 0; i < _resultList.size(); i++) {
                        if (i == 0) sleep_start = Integer.parseInt(_resultList.get(i).getANos());
                        if (i == 2) sleep_end = Integer.parseInt(_resultList.get(i).getANos());
                    }
                    int time_in_bed = 24 - sleep_start + sleep_end; //床上时间
                    double sleepRate = (double) sleep_time / time_in_bed;
                    if (sleepRate > 0.85) score = BigDecimal.valueOf(0);
                    if (sleepRate >= 0.75 && sleepRate <= 0.84) score = BigDecimal.valueOf(1);
                    if (sleepRate >= 0.65 && sleepRate <= 0.74) score = BigDecimal.valueOf(2);
                    if (sleepRate < 0.65) score = BigDecimal.valueOf(3);
                }
                //endregion
                //region 睡眠障碍
                    /* 睡眠障碍：
                     根据条目5b至5j的计分为“无”计0分，“<1周/次”计1分，“1~2周/次”计2分，“≥3周/次”计3分。
                     累加条目5b至5j的计分，若累加分为“0”则成分E计0分，“1~9”计1分，“10~18”计2分，“19~27”计3分。
                     */
                if ("睡眠障碍".equals(scaleFactorDto.getFactorName()) ) {
                    String qids = "5,6,7,8,9,10,11,12,13,14";
                    BigDecimal score_5b_5j = testScoreDao.computeFactorScore(recordId, StrUtil.splitToArray(qids,','));
                    if (score_5b_5j.compareTo(BigDecimal.valueOf(0)) == 0)
                        score = BigDecimal.valueOf(0);
                    if (score_5b_5j.compareTo(BigDecimal.valueOf(1)) >= 0 && score_5b_5j.compareTo(BigDecimal.valueOf(9)) <= 0)
                        score = BigDecimal.valueOf(1);
                    if (score_5b_5j.compareTo(BigDecimal.valueOf(10)) >= 0 && score_5b_5j.compareTo(BigDecimal.valueOf(18)) <= 0)
                        score = BigDecimal.valueOf(2);
                    if (score_5b_5j.compareTo(BigDecimal.valueOf(19)) >= 0 && score_5b_5j.compareTo(BigDecimal.valueOf(27)) <= 0)
                        score = BigDecimal.valueOf(3);
                }
                //endregion
                //region 催眠药物
                //催眠药物：根据条目7的应答计分，“无”计0分，“<1周/次”计1分，“1~2周/次”计2分，“≥3周/次”计3分。
                if ("催眠药物".equals(scaleFactorDto.getFactorName()))
                    score = testScoreDao.computeFactorScore(recordId, new String[]{"16"});
                //endregion
                //region 日间功能障碍
                if ("日间功能障碍".equals(scaleFactorDto.getFactorName())) {
                    BigDecimal score_8 = testScoreDao.computeFactorScore(recordId, new String[]{"17"});
                    BigDecimal score_9 = testScoreDao.computeFactorScore(recordId, new String[]{"18"});
                    BigDecimal score_8_9 = score_8.add(score_9);
                    if (score_8_9.compareTo(BigDecimal.valueOf(0)) == 0)
                        score = BigDecimal.valueOf(0);
                    if (score_8_9.compareTo(BigDecimal.valueOf(1)) >= 0 && score_8_9.compareTo(BigDecimal.valueOf(2)) <= 0)
                        score = BigDecimal.valueOf(1);
                    if (score_8_9.compareTo(BigDecimal.valueOf(3)) >= 0  && score_8_9.compareTo(BigDecimal.valueOf(4)) <= 0)
                        score = BigDecimal.valueOf(2);
                    if (score_8_9.compareTo(BigDecimal.valueOf(5)) >= 0 && score_8_9.compareTo(BigDecimal.valueOf(6)) <= 0)
                        score = BigDecimal.valueOf(3);
                }
                //endregion

                var testScoreEntity = new TestScoreEntity();
                testScoreEntity.setRecordId(recordId);
                testScoreEntity.setFactorId(scaleFactorDto.getId());
                testScoreEntity.setOriginalScore(score);
                testScoreEntity.setScore(score);
                testScoreEntity.setIsAbnormal(0);
                testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
                saveScore(testScoreEntity);

                totalScore = totalScore.add(score);
            }
            if (scaleFactorDto.getFactorType() == 2) {
                var listFactorExplains = scaleFactorExplainDao.getListById(scaleFactorDto.getId());
                String exp = "";
                for(ScaleFactorExplainEntity scaleFactorExplainEntity: listFactorExplains) {
                    BigDecimal startValue = scaleFactorExplainEntity.getStartValue();
                    BigDecimal endValue = scaleFactorExplainEntity.getEndValue();
                    if (totalScore.compareTo(startValue) >= 0 && totalScore.compareTo(endValue) <= 0) {
                        exp = scaleFactorExplainEntity.getInterpretation();
                        break;
                    }
                }
                //异常判定规则
                var listAbnormalConditions = scaleFactorAbnormalConditionDao.getListByFactorId(scaleFactorDto.getId())
                        .stream()
                        .sorted(Comparator.comparing(ScaleFactorAbnormalConditionEntity::getScoreValue).reversed())
                        .collect(Collectors.toList());
                //判断是否正常
                BigDecimal conditionValue = BigDecimal.valueOf(0);
                CheckAbnormalDto checkAbnormalDto = new CheckAbnormalDto();
                checkAbnormalDto.setFactorId(scaleFactorDto.getId());
                checkAbnormalDto.setAge(age);
                checkAbnormalDto.setSex(sex);
                checkAbnormalDto.setFactorScore(totalScore);
                checkAbnormalDto.setConditionValue(conditionValue);
                checkAbnormalDto.setListAbnormalConditions(listAbnormalConditions);
                isAbnormal = checkIsAbnormal(checkAbnormalDto);
                responseText.append(String.format("<dt class=\"pb10\">%s</dt><dd class=\"pb10\">因子\"%s\"得分%.2f %s。%s<dd/>",
                        scaleFactorDto.getFactorName(),
                        scaleFactorDto.getFactorName(),
                        totalScore,
                        isAbnormal ? "<span class=\"text-danger\">(异常)</span>" : "",
                        exp
                ));
                var testScoreEntity = new TestScoreEntity();
                testScoreEntity.setRecordId(recordId);
                testScoreEntity.setFactorId(scaleFactorDto.getId());
                testScoreEntity.setOriginalScore(totalScore);
                testScoreEntity.setScore(totalScore);
                testScoreEntity.setIsAbnormal(isAbnormal ? 1 : 0);
                testScoreEntity.setAbnormalValue(totalScore);
                saveScore(testScoreEntity);

                var testRecordExplainEntity = new TestRecordExplainEntity();
                testRecordExplainEntity.setRecordId(recordId);
                testRecordExplainEntity.setFactorId(scaleFactorDto.getId());
                testRecordExplainEntity.setInterpretation(String.format("因子%s得分%.2f%s。%s",
                        scaleFactorDto.getFactorName(),
                        totalScore,
                        isAbnormal ? "（异常）" : "",
                        exp
                ));
                testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
            }
        }
        interpretation = responseText.toString();
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 *  包容力测试
 */
@Service
public class BRL extends BaseCalc{
    @Override
    public void compute() {
        var totalScore = computeTotalScore();
        int grade = 0;
        if (age >= 14 && age <= 16) {
            if (totalScore.compareTo(BigDecimal.valueOf(10)) <= 0) {
                grade = 1;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(11)) >=0 && totalScore.compareTo(BigDecimal.valueOf(12)) <= 0) {
                grade = 2;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(13)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(29)) <= 0) {
                grade = 3;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(30)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(62)) <= 0) {
                grade = 4;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(63)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(144)) <= 0) {
                grade = 5;
            }
        }
        if (age >= 17 && age <= 21) {
            if (totalScore.compareTo(BigDecimal.valueOf(13)) <= 0) {
                grade = 1;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(14)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(16)) <= 0) {
                grade = 2;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(17)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(30)) <= 0) {
                grade = 3;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(31)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(49)) <= 0) {
                grade = 4;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(50)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(144)) <= 0) {
                grade = 5;
            }
        }
        if (age >= 22 && age <= 30) {
            if (totalScore.compareTo(BigDecimal.valueOf(9)) <= 0) {
                grade = 1;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(10)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(15)) <= 0) {
                grade = 2;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(16)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(32)) <= 0) {
                grade = 3;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(33)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(48)) <= 0) {
                grade = 4;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(49)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(144)) <= 0) {
                grade = 5;
            }
        }
        if (age >= 31) {
            if (totalScore.compareTo(BigDecimal.valueOf(15)) <= 0) {
                grade = 1;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(16)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(31)) <= 0) {
                grade = 2;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(32)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(50)) <= 0) {
                grade = 3;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(51)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(60)) <= 0) {
                grade = 4;
            }
            if (totalScore.compareTo(BigDecimal.valueOf(61)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(144)) <= 0) {
                grade = 5;
            }
        }
        switch (grade) {
            case 1:
                interpretation = String.format("您的得分：%.2f。包容力非常强。不在乎别人的意见和自己不同，能够容忍偏激和善变的意见。",totalScore);
                break;
            case 2:
                interpretation = String.format("您的得分：%.2f。包容力强。能理解和自己想法不同的意见。心中没有偏见，愿意敞开心胸接受新潮、新思想。同年龄层次中比你缺乏包容力的人很多。",totalScore);
                break;
            case 3:
                interpretation = String.format("您的得分：%.2f。包容力普通（尚可）。包容力于平均水准中还算可以。",totalScore);
                break;
            case 4:
                interpretation = String.format("您的得分：%.2f。包容力普通（稍低）。偶尔无法接纳不同声音，对新趋势和新思想持怀疑的态度。",totalScore);
                break;
            case 5:
                interpretation = String.format("您的得分：%.2f。包容力很弱。没有什么包容力，排斥和自己不同的意见，希望所有的人和自己的想法一致。",totalScore);
                break;
        }
        var factor = getFactorList();
        var testScoreEntity = new TestScoreEntity();
        testScoreEntity.setRecordId(recordId);
        testScoreEntity.setFactorId(factor.get(0).getId());
        testScoreEntity.setOriginalScore(totalScore);
        testScoreEntity.setScore(totalScore);
        testScoreEntity.setIsAbnormal(0);
        testScoreEntity.setAbnormalValue(new BigDecimal(0));
        saveScore(testScoreEntity);

        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(factor.get(0).getId());
        testRecordExplainEntity.setInterpretation(interpretation);
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.stat;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleAnswerDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.measuringroom.*;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestResultService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TestStat {
    @Autowired
    private TestRecordService testRecordService;
    @Autowired
    private TestResultService testResultService;
    @Autowired
    private ScaleQuestionDao scaleQuestionDao;
    @Autowired
    private ScaleAnswerDao scaleAnswerDao;
    @Autowired
    private General general;
    @Autowired
    private Scl90Stat scl90Stat;

    public TaskStatDto get(TestRecordDto dto){
        var stat = new TaskStatDto();
        var listTestRecords = testRecordService.getList(dto);
        var totalCount = listTestRecords.size();
        if (totalCount > 0) {
            int doneCount = 0, undoCount = 0, invalidCount = 0, abnormalCount = 0;
            int qCount,aMaxNo = 0;
            var scaleId = listTestRecords.get(0).getScaleId();
            qCount = scaleQuestionDao.getQuestionCount(scaleId);
            stat.setQCount(qCount);
            aMaxNo = scaleAnswerDao.getMaxANo(scaleId);
            stat.setAMaxNo(aMaxNo);
            int[][] aFreq= new int[qCount][aMaxNo];
            for (int i = 0; i < qCount; i++) {
                for (int j = 0; j < aMaxNo; j++) {
                    aFreq[i][j] = 0;
                }
            }
            stat.setAFreq(aFreq);
            String recordIds = "";
            var sexList = new ArrayList<TaskStatCnt>();
            var educationList = new ArrayList<TaskStatCnt>();
            var marriageList = new ArrayList<TaskStatCnt>();
            var religionList = new ArrayList<TaskStatCnt>();
            //异常用户清单
            var abnormalUsers = new ArrayList<UserDto>();
            for(TestRecordDto testRecord: listTestRecords) {
                stat.setScaleName(testRecord.getScaleName());
                stat.setScaleIntro(testRecord.getScaleIntro());
                var state = testRecord.getState();
                //region 统计人数
                //已做完的
                if (state == 1 || state == 2)
                    doneCount++;
                //未做
                if (state == 0)
                    undoCount++;
                //异常人数
                boolean isAbnormal = testRecordService.isAbnormal(testRecord.getId()) > 0;
                if (isAbnormal) {
                    var abnormalUser = new UserDto();
                    abnormalUser.setStructFullName(testRecord.getStructFullName());
                    abnormalUser.setLoginName(testRecord.getLoginName());
                    abnormalUser.setRealName(testRecord.getRealName());
                    abnormalUsers.add(abnormalUser);
                    abnormalCount++;
                }
                invalidCount = totalCount - doneCount;

                var sexCnt = new TaskStatCnt();
                var educationCnt = new TaskStatCnt();
                var marriageCnt= new TaskStatCnt();
                var religionCnt = new TaskStatCnt();

                sexCnt.setId(testRecord.getSex() == null ? "未知" :testRecord.getSex());
                sexCnt.setCnt((long)1);

                educationCnt.setId(testRecord.getEducation() == null ? "未知" :testRecord.getEducation());
                educationCnt.setCnt((long)1);

                marriageCnt.setId(testRecord.getMarriage() == null ? "未知" :testRecord.getMarriage());
                marriageCnt.setCnt((long)1);

                religionCnt.setId(testRecord.getReligion() == null ? "未知" :testRecord.getReligion());
                religionCnt.setCnt((long)1);

                sexList.add(sexCnt);
                educationList.add(educationCnt);
                marriageList.add(marriageCnt);
                religionList.add(religionCnt);
                //endregion
                //region 各项目选择频度
                var listResults = testResultService.getList(testRecord.getId());
                for(TestResultDto result: listResults) {
                    if (result.getQType() == 1){//单选题{
                        int q = result.getQNo();
                        int a = Integer.parseInt(result.getANos());
                        if (q >= 1 && q <= qCount && a >= 1 && a <= aMaxNo) {
                            aFreq[q-1][a-1]++;
                            stat.setAFreq(aFreq);
                        }
                    }
                }
                //endregion
                recordIds += testRecord.getId().toString() + ",";
            }
            stat.setTotalCount(totalCount);
            stat.setDoneCount(doneCount);
            stat.setUnDoCount(undoCount);
            stat.setInvalidCount(invalidCount);
            stat.setAbnormalCount(abnormalCount);

            // 数量分布统计
            var newSexlist = mapToList(sexList.stream().collect(Collectors.groupingBy(TaskStatCnt :: getId, Collectors.counting())));
            var newEducationlist = mapToList(educationList.stream().collect(Collectors.groupingBy(TaskStatCnt :: getId, Collectors.counting())));
            var newMarriagelist = mapToList(marriageList.stream().collect(Collectors.groupingBy(TaskStatCnt :: getId, Collectors.counting())));
            var newReligionlist = mapToList(religionList.stream().collect(Collectors.groupingBy(TaskStatCnt :: getId, Collectors.counting())));

            stat.setListSexFreq(newSexlist);
            stat.setListEducationFreq(newEducationlist);
            stat.setListMarriageFreq(newMarriagelist);
            stat.setListReligionFreq(newReligionlist);
            stat.setAbnormalUsers(abnormalUsers);

            //测评结果分布
            BaseStat sc;
            switch (scaleId) {
                case ScaleIDDto.SCL90:
                    sc = scl90Stat;
                    break;
                default:
                    sc = general;
                    break;
            }
            sc.recordIds = recordIds;
            stat.setStatResultDistribution(!"".equals(recordIds) ?sc.stat(stat):"");
        }
        return stat;
    }

    private List<TaskStatCnt> mapToList(Map<String,Long> map){
        return map.entrySet()
                .stream()
                .sorted(Comparator.comparing(e -> e.getKey()))
                .map(e -> new TaskStatCnt(e.getKey(), e.getValue()))
                .collect(Collectors.toList());
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestScoreDao;
import cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ExportTestScoreDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.service.measuringroom.TestScoreService;
import cn.psycloud.psyplatform.serviceImpl.measuringroom.calc.InitCalc;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class TestScoreServiceImpl implements TestScoreService {
    @Autowired
    private TestScoreDao testScoreDao;
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private ScaleFactorDao scaleFactorDao;
    @Autowired
    private ScaleQuestionDao scaleQuestionDao;
    @Autowired
    private InitCalc initCalc;
    @Autowired
    private SurveyQuestionDao surveyQuestionDao;
    /**
     *  根据测评记录ID删除测评得分情况
     * @param recordId 测评记录id
     * @return 影响行数
     */
    @Override
    public int delTestScoreByRecordId(Integer recordId) {
        return testScoreDao.deleteByRecordId(recordId);
    }

    /**
     *  计算量表得分情况
     * @param recordId 测评记录id
     * @param scaleId 量表id
     */
    @Override
    public void calc(Integer recordId, Integer scaleId){
        initCalc.calc(recordId, scaleId);
    }

    /**
     *  获取测评报告
     * @param recordId 测评记录id
     * @return 测评得分集合
     */
    @Override
    public List<TestScoreDto> getReport(Integer recordId) {
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        return testScoreDao.getTestScoreList(testScoreDto);
    }

    /**
     *  查询测评得分情况集合：分页
     * @param dto 测评得分实体对象
     * @return 测评得分集合
     */
    @Override
    public BSDatatableRes<TestScoreDto> getTestScoreListByPaged(TestScoreDto dto) {
        var dtRes = new BSDatatableRes<TestScoreDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listTestScores = testScoreDao.getTestScoreList(dto);
        var testScoreDto = new PageInfo<>(listTestScores);
        dtRes.setData(testScoreDto.getList());
        dtRes.setRecordsTotal((int)testScoreDto.getTotal());
        dtRes.setRecordsFiltered((int)testScoreDto.getTotal());
        return dtRes;
    }

    /**
     *  查询测评得分情况集合
     * @param dto 测评得分实体对象
     * @return 测评得分集合
     */
    @Override
    public List<TestScoreDto> getTestScoreList(TestScoreDto dto) {
        var testRecordDto = testRecordDao.getById(dto.getRecordId());
        var currentUser = (UserDto)SessionUtil.getSession().getAttribute("user");
        if(!Objects.equals(currentUser.getUserId(), testRecordDto.getUser().getUserId())){
            var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
            dto.setChildStructs(childStructsIds);
        }
        var listTestScores = testScoreDao.getTestScoreList(dto);
        for(var testScore: listTestScores){
            testScore.getTestRecord().getScale().setListCharts(testRecordDto.getScale().getListCharts());
        }
        return listTestScores;
    }

    /**
     *  导出测评得分情况
     * @ dto  条件
     */
    @Override
    public List<LinkedHashMap<String,Object>> exportTestScore(ExportTestScoreDto dto) {
        if((dto.getScaleId() != null && dto.getScaleId() != 0) || (dto.getScaleIds()!=null && !"".equals(dto.getScaleIds()))) {
            var scaleFactorDto = new ScaleFactorDto();
            scaleFactorDto.setScaleId(dto.getScaleId() == null ? 0:dto.getScaleId());
            scaleFactorDto.setScaleIds(dto.getScaleIds() == null?"":dto.getScaleIds());
            List<LinkedHashMap<String,Object>> factors = scaleFactorDao.getListForExport(scaleFactorDto);
            StringBuilder factorBuilder = new StringBuilder();
            for(LinkedHashMap<String,Object> factorMap: factors) {
                factorBuilder.append(String.format("max(case when pf.factor_name ='%s' then pts.score else 0 end) as %s,",factorMap.get("factor_name"),factorMap.get("factor_name")));
            }
            String factorStr = StrUtil.removeSuffix(factorBuilder.toString(),",");
            var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
            dto.setChildStructs(childStructsIds);
            dto.setSql(factorStr);
            return testScoreDao.getExportTestScore(dto);
        }
        else
            return new ArrayList<>();
    }

    /**
     *  导出测评选项
     * @param dto 集合
     * @return 集合
     */
    @Override
    public List<LinkedHashMap<String,Object>> exportTestResult(ExportTestScoreDto dto) {
        List<LinkedHashMap<String,Object>> questions = scaleQuestionDao.getListForExport(dto.getScaleId()==null?0:dto.getScaleId());
        StringBuilder questionBuilder = new StringBuilder();
        for(LinkedHashMap<String,Object> questionMap: questions) {
            questionBuilder.append(String.format("max(case when ptr.q_no ='%s' then ptr.a_no else 0 end) as 第%s题,",questionMap.get("q_number"),questionMap.get("q_number")));
        }
        String qNoStr = StrUtil.removeSuffix(questionBuilder.toString(),",");
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        dto.setSql(qNoStr);
        return testScoreDao.getExportTestResult(dto);
    }

    /**
     *  导出测评选项得分
     * @param dto 条件
     * @return 集合
     */
    @Override
    public List<LinkedHashMap<String,Object>> exportTestResultScore(ExportTestScoreDto dto){
        List<LinkedHashMap<String,Object>> questions = scaleQuestionDao.getListForExport(dto.getScaleId()==null?0:dto.getScaleId());
        StringBuilder questionBuilder = new StringBuilder();
        for(LinkedHashMap<String,Object> questionMap: questions) {
            questionBuilder.append(String.format("max(case when ptr.q_no ='%s' then pa.a_score else 0 end) as 第%s题,",questionMap.get("q_number"),questionMap.get("q_number")));
        }
        String qNoStr = StrUtil.removeSuffix(questionBuilder.toString(),",");
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        dto.setSql(qNoStr);
        return testScoreDao.getExportTestResultScore(dto);
    }

    /**
     *  导出调查问卷结果数据
     * @param dto 条件
     * @return 集合
     */
    @Override
    public List<LinkedHashMap<String,Object>> exportSurveyResutl(ExportTestScoreDto dto){
        List<LinkedHashMap<String,Object>> questions =surveyQuestionDao.getListForExport(dto.getSurveyId() == null ? 0 : dto.getSurveyId());
        StringBuilder questionBuilder = new StringBuilder();
        for(LinkedHashMap<String,Object> questionMap: questions){
            questionBuilder.append(String.format("max(case when psq.q_number ='%s' then psr.item_id else 0 end) as 第%s题,",questionMap.get("q_number"),questionMap.get("q_number")));
        }
        String qNoStr = StrUtil.removeSuffix(questionBuilder.toString(),",");
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        dto.setSql(qNoStr);
        return testScoreDao.getExportSurveyTestResult(dto);
    }

    /**
     *  测评统计获取测评得分情况
     * @param factorId 因子id
     * @param recordIds 测评记录id集合
     * @return 测评得分集合
     */
    @Override
    public List<TestScoreDto> getTestScoreListForStat(Integer factorId, String recordIds) {
        var map = new HashMap<String,Object>();
        map.put("factorId",factorId);
        map.put("recordIds",StrUtil.removeSuffix(recordIds,","));
        return testScoreDao.getTestScoreListForStat(map);
    }

    /**
     *  异常结果导出报告
     * @param dto 测评得分实体对象
     * @return 测评记录集合
     */
    @Override
    public List<TestRecordDto> getRecordListByTestScore(TestScoreDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testScoreDao.getRecordListByTestScore(dto);
    }
}

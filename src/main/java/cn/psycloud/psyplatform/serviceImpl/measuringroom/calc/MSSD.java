package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 *  精神症状自我诊断
 */
@Service
public class MSSD extends BaseCalc{
    @Override
    public void compute(){
        var totalScore = BigDecimal.valueOf(0);
        var reponseText = new StringBuilder();
        computeCommonFactor(true);
        for (int i = 0; i < listFactorScores.size() - 2; i++) {
            totalScore = totalScore.add(listFactorScores.get(i));
        }
        totalScore = totalScore.multiply(BigDecimal.valueOf(3));
        String exp = "";
        BigDecimal abnormalVal = BigDecimal.valueOf(61);
        if (totalScore.compareTo(abnormalVal) >= 0){//异常条件{
            isAbnormal =true;
            reponseText.append(String.format("<dt>总分</dt><dd>精神症状指数：%.2f(<span class=\"text-danger\">异常</span>),您的精神症状指数得分过高，存在一定的心理状况。<dd/>", totalScore));
        }
        else {
            isAbnormal = false;
            reponseText.append(String.format("<dt>总分</dt><dd>精神症状指数：%.2f(正常)，您的精神症状指数得分正常，心理状况良好。dd/>", totalScore));
        }
        interpretation = reponseText.toString();
        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(String.format("精神症状指数：%.2f(%s)，%s",
                totalScore,
                isAbnormal? "异常" : "正常",
                exp
        ));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

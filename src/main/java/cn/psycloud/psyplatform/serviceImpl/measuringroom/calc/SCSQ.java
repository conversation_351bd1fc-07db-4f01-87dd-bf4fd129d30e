package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;

/**
 *  简易应对方式
 */
@Service
public class SCSQ extends BaseCalc{
    @Override
    public void compute(){
        var listFactors = getFactorList();
        var listFactorStandardScore = new ArrayList<BigDecimal>();
        for(ScaleFactorDto factor: listFactors) {
            //计算原始分
            var originalScore = testScoreDao.computeFactorScore(recordId, factor.getQIds().split(","));
            BigDecimal factorAvgScore = originalScore.divide(BigDecimal.valueOf(factor.getQIds().split(",").length),2, RoundingMode.HALF_UP);
            var factorStandardScore = factorAvgScore.subtract(factor.getAvgScore()).divide(factor.getStandardScore(), 2,RoundingMode.HALF_UP);
            listFactorStandardScore.add(factorStandardScore);

            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(factor.getId());
            testScoreEntity.setOriginalScore(originalScore);
            testScoreEntity.setScore(factorStandardScore);
            testScoreEntity.setIsAbnormal(0);
            testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
            saveScore(testScoreEntity);
        }
        var pos = listFactorStandardScore.get(0);
        var neg = listFactorStandardScore.get(1);
        var rate = pos.subtract(neg);
        var factorExplain = rate.compareTo(BigDecimal.valueOf(0)) > 0 ? "当面临压力时，你多会采用\"知难而进\"，把压力看作是一种挑战去解决，而不是把压力看作是一种负担。这种应对方式有助于缓解你的精神紧张，帮助你最终成功地解决问题，从而调整良好的心理状态，保护心理健康。" : "当面对压力、挫折和应急事件时，你会采用抱怨、忍耐、逃避等消极的应对方式。事实上，短时间的消极应对不会太影响个体，长时间采用这中可能可能会使人丧失斗志。在这方面，你可以尝试积极心态、自我调整、包括音乐、画画、运动、阅读和向朋友或领导倾诉，积极解决问题和反思总结，参加集体活动，获得心理支持等积极应对方式来进行自我调整。";
        interpretation = String.format("您的应对倾向得分为%.2f（积极应对标准分 - 消极应对标准分）。应对倾向值大于0，说明被试者在应激状态时主要采用积极的应对方式；反之。说明被试者在应激状态时更习惯采用消极的应对方式。<br/>%s",
                rate,
                factorExplain
        );
        var testRecordExplain = new TestRecordExplainEntity();
        testRecordExplain.setRecordId(recordId);
        testRecordExplain.setFactorId(0);
        testRecordExplain.setInterpretation(interpretation);
        testRecordDao.saveTestRecordExplain(testRecordExplain);
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 *  压力应对方式量表
 */
@Service
public class PCM extends BaseCalc{
    @Override
    public void compute() {
        var score = new double[6][2];
        int i = 0;
        for (i = 0; i < 6; i++) {
            score[i][0] = 0;
            score[i][1] = i;
        }
        computeCommonFactor(true);
        var listFactors = getFactorList();
        var factor = new String[6];
        int a = 0;
        for(ScaleFactorDto scaleFactorDto: listFactors) {
            factor[a] = scaleFactorDto.getFactorName();
            a++;
        }
        int m = 0;
        for(BigDecimal factorScore: listFactorScores) {
            score[m][0] = factorScore.doubleValue();
            m++;
        }
        //排序
        for (i = 0; i < 6; i++) {
            for (int j = 5; j > i; j--) {
                if (score[j][0] > score[j - 1][0]) {
                    for (int k = 0; k < 2; k++) {
                        double t = score[j][k];
                        score[j][k] = score[j - 1][k];
                        score[j - 1][k] = t;
                    }
                }
            }
        }
        var responseText = new StringBuilder();
        responseText.append("<table class=\"table table-striped\"><thead><tr><th>#</th><th>应对方式</th><th>得分</th></tr></thead><tbody>");
        for (i = 0; i < 6; i++) {
            responseText.append(String.format("<tr><td>%s</td><td>%s</td><td>%.2f</td></tr>",
                    i + 1,
                    factor[(int)score[i][1]],
                    score[i][0]
            ));
        }
        responseText.append("</tbody><tfoot>应对方式分数最高的代表被试者最常用的应付倾向。</tfoot></table>");
        interpretation = responseText.toString();
        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(String.format("应付倾向排序为：%s、%s、%s、%s、%s、%s",
            factor[(int)score[0][1]],
            factor[(int) score[1][1]],
            factor[(int) score[2][1]],
            factor[(int)score[3][1]],
            factor[(int)score[4][1]],
            factor[(int)score[5][1]]
        ));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.stat;

import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleIDDto;
import cn.psycloud.psyplatform.dto.measuringroom.TaskStatDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class Scl90Stat extends BaseStat{
    @Autowired
    private CalcDao calcDao;

    /**
     *  获取scl90常模
     * @return 常模集合
     */
    private List<LinkedHashMap<String,Object>> getScl90Norm(){
        return calcDao.getSclNorm();
    }

    private Object[][] listToArray(List<LinkedHashMap<String, Object>> list) {
        int size = list.size();
        Object[][] array = new Object[size][2];
        for (int i = 0; i < size; i++) {
            array[i] = list.get(i).values().toArray();
        }
        return array;
    }

    @Override
    public String stat(TaskStatDto dto){
        // 获取常模
        var norm = new double[11][2];
        var avg = new double[11];
        var sd = new double[11];

        List<LinkedHashMap<String,Object>> listNorms = getScl90Norm();
        Object[][] cm = listToArray(listNorms);
        for (int i = 1; i <= 10; i++) {
            if("java.lang.Double".equals(cm[i - 1][0].getClass().getName())){
                norm[i][0] = (double) cm[i - 1][0];
                norm[i][1] = (double) cm[i - 1][1];
            }
            if("java.lang.BigDecimal".equals(cm[i - 1][0].getClass().getName())){
                norm[i][0] = ((BigDecimal)cm[i - 1][0]).doubleValue();
                norm[i][1] = ((BigDecimal)cm[i - 1][1]).doubleValue();
            }

        }
        if("java.lang.Double".equals(cm[10][0].getClass().getName())){
            norm[0][0] = (double) cm[10][0];
            norm[0][1] = (double) cm[10][1];
        }
        if("java.lang.BigDecimal".equals(cm[10][0].getClass().getName())){
            norm[0][0] = ((BigDecimal)cm[10][0]).doubleValue();
            norm[0][1] = ((BigDecimal)cm[10][1]).doubleValue();
        }
        //计算方差
        var gid = new int[11];
        var scaleFactorDto = new ScaleFactorDto();
        scaleFactorDto.setScaleId(ScaleIDDto.SCL90);
        List<ScaleFactorDto> listFactors = scaleFactorDao.getFactorsByScaleId(ScaleIDDto.SCL90);
        int k = 0;
        for(ScaleFactorDto scaleFactor: listFactors) {
            gid[k] = scaleFactor.getId();
            k++;
        }
        for (int i = 0; i < 11; i++) {
            var listTestScores = testScoreService.getTestScoreListForStat(gid[i],recordIds);
            double sum = 0;
            for(TestScoreDto ts: listTestScores) {
                sum += ts.getScore().doubleValue();//因子总分
            }
            avg[i] = sum / dto.getDoneCount();//因子平均分
            double s = 0;
            for(TestScoreDto ts: listTestScores) {
                double t = ts.getScore().doubleValue() - avg[i];
                s += t * t;
            }
            sd[i] = Math.sqrt(s / dto.getDoneCount());
        }
        String[] fnstr = { "躯体化", "强迫症状", "人际关系敏感", "抑郁", "焦虑", "敌对", "恐怖", "偏执", "精神病性", "其他","[总分]" };
        dto.setStatResultDistribution("常模比较与方差分析");
        var sb = new StringBuilder();
        sb.append(String.format("<table class='table table-striped'><thead class='white-bg'><tr><th rowspan='2' class='text-center'>序号</th><th rowspan='2' class='text-center'>总分或因子分</th><th colspan='2' class='text-center'>统计数据(%d人)</th><th rowspan='2' class='text-center'>与常模比较</th></tr> <tr><th class='text-center'>平均数(M)</th><th class='text-center'>标准差(SD)</th></tr> </thead>", dto.getDoneCount()));
        sb.append("<tbody>");
        for (int i = 0; i <= 10; i++) {
            sb.append(String.format("<tr><td class='text-center'>%d</td><td class='text-center'>%s</td>", i, fnstr[i]));
            sb.append(String.format("<td class='text-center'>%.2f</td><td class='text-center'>%.2f</td><td class='text-center'>",
                    BigDecimal.valueOf(Math.round(avg[i])),
                    BigDecimal.valueOf(Math.round(sd[i])))
            );
            if (avg[i] > norm[i][0] + norm[i][1]) {
                sb.append("偏高");
            }
            else if (avg[i] < norm[i][0] - norm[i][1]){
                sb.append("偏低");
            }
            else{
                sb.append("正常");
            }
            sb.append("</td></tr>");
        }
        sb.append("</tbody></table><span class='help-block'>该数据只统计当前范围。</span>");
        return sb.toString();
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  安全员测试
 */
@Service
public class AQY extends BaseCalc{
    @Override
    public void compute(){
        computeCommonFactor(true);
        interpretation = getExplain();
    }

    private String getExplain() {
        var responseText1 = new StringBuilder();
        var responseText2 = new StringBuilder();

        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        var listTestScores = testScoreDao.getTestScoreList(testScoreDto);
        var listFactorExplains = scaleFactorExplainDao.getListByScaleId(scaleId);
        var testRecordExplainEntity = new TestRecordExplainEntity();
        String str1 = "", str2 = "", str3 = "", str4 = "";
        for(TestScoreDto testScore: listTestScores) {
            String exp = "";
            var score = testScore.getScore();
            var explains = listFactorExplains.stream().filter(r-> Objects.equals(r.getFactorId(), testScore.getFactor().getId())).collect(Collectors.toList());
            for(ScaleFactorExplainEntity scaleFactorExplainEntity: explains) {
                BigDecimal startValue = scaleFactorExplainEntity.getStartValue();
                BigDecimal endValue = scaleFactorExplainEntity.getEndValue();
                if (score.compareTo(startValue) >= 0 && score.compareTo(endValue) <= 0) {
                    exp = scaleFactorExplainEntity.getInterpretation();
                    break;
                }
            }
            testRecordExplainEntity.setRecordId(recordId);
            testRecordExplainEntity.setFactorId(testScore.getFactor().getId());

            boolean isAbnormal = testScore.getIsAbnormal() > 0;
            testRecordExplainEntity.setInterpretation(String.format("因子[%s]得分%.2f%s。%s",
                    testScore.getFactor().getFactorName(),
                    testScore.getOriginalScore().compareTo(score) == 0 ? testScore.getOriginalScore() : score,
                    isAbnormal ? "(异常)" : "",
                    exp
            ));
            testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
            var factorNo = testScore.getFactor().getFactorNo();
            if (factorNo < 5) {
                str1 += String.format("<tr><td class=\"text-left text-nowrap\">%s</td><td class=\"text-left\">%.2f</td><td class=\"text-left\">%s</td></tr>",
                        testScore.getFactor().getFactorName(),
                        score,
                        exp
                );
            }
            else if(factorNo >= 5 && factorNo <= 12) {
                str2 += String.format("<tr><td class=\"text-left text-nowrap\">%s</td><td class=\"text-left\">%.2f</td><td class=\"text-left\">%s</td></tr>",
                        testScore.getFactor().getFactorName(),
                        score,
                        exp
                );
            }
            if (factorNo == 12)
                str3 += exp;
            if (factorNo == 13)
                str4 += exp;
        }
        responseText1.append(String.format("<table class=\"table table-striped\"><thead><tr><th class=\"text-left\">因子名称</th><th class=\"text-left\">得分</th><th class=\"text-left text-nowrap\">结果及建议</th></tr></thead><tbody>%s</tbody></table>", str1));
        responseText2.append(String.format("<table class=\"table table-striped\"><thead><tr><th class=\"text-left\">因子名称</th><th class=\"text-left\">得分</th><th class=\"text-left text-nowrap\">结果及建议</th></tr></thead><tbody>%s</tbody></table>",str2));
        return responseText1 + "|" + responseText2 + "|"+ str3 + "|" + str4;
    }
}

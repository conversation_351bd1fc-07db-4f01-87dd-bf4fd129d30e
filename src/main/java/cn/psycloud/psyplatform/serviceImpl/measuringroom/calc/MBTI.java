package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 *  MBTI测试
 */
@Service
public class MBTI extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Override
    public void compute() {
        var listFactors = getFactorList();
        int[] featureScore = new int[8];
        for(ScaleFactorDto scaleFactorDto: listFactors) {
            var score = getScore(scaleFactorDto);
            if ("E".equals(scaleFactorDto.getFactorEn())) {
                featureScore[0] = score;
            }
            if ("I".equals(scaleFactorDto.getFactorEn())) {
                featureScore[1] = score;
            }
            if ("S".equals(scaleFactorDto.getFactorEn())) {
                featureScore[2] = score;
            }
            if ("N".equals(scaleFactorDto.getFactorEn())) {
                featureScore[3] = score;
            }
            if ("T".equals(scaleFactorDto.getFactorEn())) {
                featureScore[4] = score;
            }
            if ("F".equals(scaleFactorDto.getFactorEn())) {
                featureScore[5] = score;
            }
            if ("J".equals(scaleFactorDto.getFactorEn())) {
                featureScore[6] = score;
            }
            if ("P".equals(scaleFactorDto.getFactorEn())) {
                featureScore[7] = score;
            }
            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(scaleFactorDto.getId());
            testScoreEntity.setOriginalScore(BigDecimal.valueOf(score));
            testScoreEntity.setScore(BigDecimal.valueOf(score));
            testScoreEntity.setIsAbnormal(0);
            testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
            saveScore(testScoreEntity);
        }

        var feature = "";
        if (featureScore[0] > featureScore[1])
            feature += "E";
        else
            feature += "I";
        if (featureScore[2] > featureScore[3])
            feature += "S";
        else
            feature += "N";
        if (featureScore[4] > featureScore[5])
            feature += "T";
        else
            feature += "F";
        if (featureScore[6] > featureScore[7])
            feature += "J";
        else
            feature += "P";
        interpretation = "您的职业性格类型是：" + feature + "<br/>";
        var ex = getExplain(feature);
        interpretation += ex;

        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(HtmlUtil.cleanHtmlTag(interpretation.replace("<br/>","\r")));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }

    private String getExplain(String typeName) {
        return calcDao.getMBTIExplain(typeName);
    }

    private int getScore(ScaleFactorDto dto){
        var mbtiScore = 0;
        var factorName = dto.getFactorName();
        var map = new HashMap<String,Object>();
        map.put("recordId",recordId);
        map.put("factorName",factorName);
        var qIds = calcDao.getQidsByRecordIdAndFactorName(map);
        map.put("qIds",qIds);
        List<LinkedHashMap<String,Object>> listTestResultMap = calcDao.getTestResultForMBTI(map);
        for(LinkedHashMap<String,Object> resultMap: listTestResultMap) {
            var qNo = Integer.parseInt(resultMap.get("q_no").toString());
            var aNo = Integer.parseInt(resultMap.get("a_no").toString());
            //region 外向（E）
            if ("外向".equals(factorName)) {
                int[] e_array_a = {4,8,14,19,23,34,62,67,77};
                int[] e_array_b = {12,18,22,26,27,35,42,48,54,60,66,72};
                if(ArrayUtil.contains(e_array_a,qNo) && aNo ==1) {
                    mbtiScore++;
                }
                if(ArrayUtil.contains(e_array_b,qNo) && aNo ==2) {
                    mbtiScore++;
                }
            }
            //endregion
            //region 内向（I）
            if ("内向".equals(factorName)) {
                int[] e_array_a = { 4, 8, 14, 19, 23, 34, 62, 67, 77 };
                int[] e_array_b = { 12, 18, 22, 26, 27, 35, 42, 48, 54, 60, 66, 72 };
                if(ArrayUtil.contains(e_array_a,qNo) && aNo ==2) {
                    mbtiScore++;
                }
                if(ArrayUtil.contains(e_array_b,qNo) && aNo ==1) {
                    mbtiScore++;
                }
            }
            //endregion
            //region 实感（S）
            if ("实感".equals(factorName)) {
                int[] e_array_a = { 3, 13, 32, 40, 47, 53, 58, 61, 73, 82, 86, 90, 93 };
                int[] e_array_b = { 5, 15, 24, 29, 37, 44, 50, 55, 63, 74, 79, 83, 87 };
                if(ArrayUtil.contains(e_array_a,qNo) && aNo ==1) {
                    mbtiScore++;
                }
                if(ArrayUtil.contains(e_array_b,qNo) && aNo ==2) {
                    mbtiScore++;
                }
            }
            //endregion
            //region 直觉（N）
            if ("直觉".equals(factorName)) {
                int[] e_array_a = { 3, 13, 32, 40, 47, 53, 58, 61, 73, 82, 86, 90, 93 };
                int[] e_array_b = { 5, 15, 24, 29, 37, 44, 50, 55, 63, 74, 79, 83, 87 };
                if(ArrayUtil.contains(e_array_a,qNo) && aNo ==2) {
                    mbtiScore++;
                }
                if(ArrayUtil.contains(e_array_b,qNo) && aNo ==1) {
                    mbtiScore++;
                }
            }
            //endregion
            //region 思考（T）
            if ("思考".equals(factorName)) {
                int[] e_array_a = { 31, 39, 46, 52, 57, 69, 75, 78, 81, 85, 89, 92 };
                int[] e_array_b = { 6, 16, 30, 38, 45, 51, 56, 64, 80, 84, 88, 91 };
                if(ArrayUtil.contains(e_array_a,qNo) && aNo ==1) {
                    mbtiScore++;
                }
                if(ArrayUtil.contains(e_array_b,qNo) && aNo ==2){
                    mbtiScore++;
                }
            }
            //endregion
            //region 情感（F）
            if ("情感".equals(factorName)) {
                int[] e_array_a = { 31, 39, 46, 52, 57, 69, 75, 78, 81, 85, 89, 92 };
                int[] e_array_b = { 6, 16, 30, 38, 45, 51, 56, 64, 80, 84, 88, 91 };
                if(ArrayUtil.contains(e_array_a,qNo) && aNo ==2) {
                    mbtiScore++;
                }
                if(ArrayUtil.contains(e_array_b,qNo) && aNo ==1){
                    mbtiScore++;
                }
            }
            //endregion
            //region 判断（J）
            if ("判断".equals(factorName)) {
                int[] e_array_a = { 1, 9, 10, 20, 28, 36, 43, 49, 59, 68, 70 };
                int[] e_array_b = { 2, 7, 11, 17, 21, 25, 33, 41, 65, 71, 76 };
                if(ArrayUtil.contains(e_array_a,qNo) && aNo ==1) {
                    mbtiScore++;
                }
                if(ArrayUtil.contains(e_array_b,qNo) && aNo ==2) {
                    mbtiScore++;
                }
            }
            //endregion
            //region 认知（P）
            if ("认知".equals(factorName)) {
                int[] e_array_a = { 1, 9, 10, 20, 28, 36, 43, 49, 59, 68, 70 };
                int[] e_array_b = { 2, 7, 11, 17, 21, 25, 33, 41, 65, 71, 76 };
                if(ArrayUtil.contains(e_array_a,qNo) && aNo ==2) {
                    mbtiScore++;
                }
                if(ArrayUtil.contains(e_array_b,qNo) && aNo ==1) {
                    mbtiScore++;
                }
            }
            //endregion
        }
        return mbtiScore;
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class EMBU extends BaseCalc{
    @Autowired
    private TestResultDao testResultDao;
    @Override
    public void compute() {
        //计算因子分
        var listFactors = getFactorList();
        for(ScaleFactorDto scaleFactorDto: listFactors){
            var flag = !scaleFactorDto.getFactorName().contains("父亲") ? "mother" : "father";
            var score =compute(scaleFactorDto,flag);

            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(scaleFactorDto.getId());
            testScoreEntity.setOriginalScore(score);
            testScoreEntity.setScore(score);
            testScoreEntity.setIsAbnormal(0);
            testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
            saveScore(testScoreEntity);
        }
        //结果解释
        interpretation = getFactorExplain();
    }

    private BigDecimal compute(ScaleFactorDto scaleFactorDto, String flag) {
        BigDecimal score = new BigDecimal(0);
        List<LinkedHashMap<String, Object>> scoreMap = testScoreDao.computeEmbuFactorScore(recordId, scaleFactorDto.getQIds().split(","));
        for(LinkedHashMap<String, Object> map: scoreMap) {
            int aNo = 0;
            if("father".equals(flag)) {
                aNo = Integer.parseInt(map.get("a_no").toString()) /10 ;
            }
            if("mother".equals(flag)) {
                aNo = Integer.parseInt(map.get("a_no").toString()) % 10;
            }
            var scoreHashMap = new HashMap<String ,Object>();
            scoreHashMap.put("qId",map.get("q_id"));
            scoreHashMap.put("aNo",aNo);
            score =score.add(testResultDao.getAnswerScore(scoreHashMap));
        }
        return score;
    }
}

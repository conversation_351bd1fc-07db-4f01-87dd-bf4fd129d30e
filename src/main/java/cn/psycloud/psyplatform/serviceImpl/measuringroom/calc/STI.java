package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 斯特劳里气质类型
 */
@Service
public class STI extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Override
    public void compute(){
        computeCommonFactor(true);
        int a = listOriginalScores.get(0).intValue(), b = listOriginalScores.get(1).intValue(), c = listOriginalScores.get(2).intValue();
        int explainId = 0;
        if (a > 0 && b < 0 && c < 0) explainId = 1;
        else if (a > 0 && b > 0 && c > 0) explainId = 2;
        else if (a > 0 && b > 0 && c < 0) explainId = 3;
        else if (a < 0 && b < 0 && c < 0) explainId = 4;
        else if (a <= 0 && b >= 0 && c >= 0) explainId = 5;
        else if (a <= 0 && b >= 0 && c <= 0) explainId = 6;
        else explainId = 7;

        var listExplainMap = getStiExplain(explainId);
        var map = listExplainMap.get(0);
        String _type = map.get("type").toString();
        String exp = map.get("interpretation").toString();
        interpretation = String.format("<dt>兴奋强度(E)</dt><dd>得分：%d</dd><dt>抑制强度(I)</dt><dd>得分：%d</dd><dt>神经过程灵活性(M)</dt><dd>得分：%d</dd><dt>最终结果</dt><dd>%s</dd>", a, b, c, _type);
        interpretation += "<dt>结果解释</dt><dd>" + exp + "</dd>";

        var testRecordExplain = new TestRecordExplainEntity();
        testRecordExplain.setRecordId(recordId);
        testRecordExplain.setFactorId(0);
        testRecordExplain.setInterpretation(HtmlUtil.cleanHtmlTag(interpretation));
        testRecordDao.saveTestRecordExplain(testRecordExplain);
    }

    private List<LinkedHashMap<String,Object>> getStiExplain(int id) {
        return calcDao.getStiExplain(id);
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 职业人格测验
 */
@Service
public class ZYLX extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Override
    public void compute() {
        computeCommonFactor(true);
        List<BigDecimal> sc = listOriginalScores;
        //分数解释
        char[] fn = { 'R', 'I', 'A', 'S', 'E', 'C' };

        var listMap = getZylxExplain();
        var listExplainMap = listMap.stream().limit(6).collect(Collectors.toList());
        var responseText = new StringBuilder("<ul>");
        int i = 0;
        for(LinkedHashMap<String,Object> map: listExplainMap){
            responseText.append(String.format("<li>%s（%s）：<br /><br />",map.get("type_name"),map.get("type")));
            responseText.append(String.format("【共同特点】%s<br />",map.get("te_dian")));
            responseText.append(String.format("【性格特点】%s<br />",map.get("xing_ge")));
            responseText.append(String.format("【职业建议】%s<br />",map.get("zhi_ye")));
            responseText.append(String.format("【您的得分】最高分为24分，您的得分为<span>%.2f</span><br/><br/></li>",sc.get(i++)));
        }

        int[] _fn = { 0, 1, 2, 3, 4, 5 };
        BigDecimal[] scClone = sc.toArray(new BigDecimal[sc.size()]);
        //取出三个大的
        for (int j = 1; j < 6; j++) {
            for (int k = j; k < 6; k++) {
                if (scClone[k].compareTo(scClone[k - 1]) > 0) {
                    int t = scClone[k].intValue();
                    scClone[k] = scClone[k - 1];
                    scClone[k - 1] = BigDecimal.valueOf(t);
                    t = _fn[k];
                    _fn[k] = _fn[k - 1];
                    _fn[k - 1] = t;
                }
            }
        }
        String ss = "";
        for (int j = 0; j < 3; j++) {
            ss += fn[_fn[j]];
        }
        final String sss = ss;
        var listZhiyeMap = listMap.stream().filter(r->r.get("type").equals(sss)).limit(1).collect(Collectors.toList());
        if(listZhiyeMap.size() > 0) {
            var zhiyeMap = listZhiyeMap.get(0);
            if (!zhiyeMap.isEmpty()) {
                responseText.append(String.format("<li>【测评结果】您在 %s 得分较高，建议职业类型：%s</li>",sss,zhiyeMap.get("zhi_ye")));
            }
        }
        responseText.append("</ul>");
        interpretation = responseText.toString();
        var testRecordExplain = new TestRecordExplainEntity();
        testRecordExplain.setRecordId(recordId);
        testRecordExplain.setFactorId(0);
        testRecordExplain.setInterpretation(HtmlUtil.cleanHtmlTag(interpretation));
        testRecordDao.saveTestRecordExplain(testRecordExplain);
    }

    private List<LinkedHashMap<String,Object>> getZylxExplain() {
        return calcDao.getZylxExplain();
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class MBTIB extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Override
    public void compute() {
        var listFactors = getFactorList();
        int[] featureScore = new int[8];
        for(ScaleFactorDto scaleFactorDto: listFactors) {
            var score = getScore(scaleFactorDto);
            if ("E".equals(scaleFactorDto.getFactorEn())) {
                featureScore[0] = score;
            }
            if ("I".equals(scaleFactorDto.getFactorEn())) {
                featureScore[1] = score;
            }
            if ("S".equals(scaleFactorDto.getFactorEn())) {
                featureScore[2] = score;
            }
            if ("N".equals(scaleFactorDto.getFactorEn())) {
                featureScore[3] = score;
            }
            if ("T".equals(scaleFactorDto.getFactorEn())) {
                featureScore[4] = score;
            }
            if ("F".equals(scaleFactorDto.getFactorEn())) {
                featureScore[5] = score;
            }
            if ("J".equals(scaleFactorDto.getFactorEn())) {
                featureScore[6] = score;
            }
            if ("P".equals(scaleFactorDto.getFactorEn())) {
                featureScore[7] = score;
            }
            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(scaleFactorDto.getId());
            testScoreEntity.setOriginalScore(BigDecimal.valueOf(score));
            testScoreEntity.setScore(BigDecimal.valueOf(score));
            testScoreEntity.setIsAbnormal(0);
            testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
            saveScore(testScoreEntity);
        }
        var feature = "";
        if (featureScore[0] > featureScore[1])
            feature += "E";
        else
            feature += "I";
        if (featureScore[2] > featureScore[3])
            feature += "S";
        else
            feature += "N";
        if (featureScore[4] > featureScore[5])
            feature += "T";
        else
            feature += "F";
        if (featureScore[6] > featureScore[7])
            feature += "J";
        else
            feature += "P";
        interpretation = "您的职业性格类型是：" + feature + "<br/>";
        var ex = getExplain(feature);
        interpretation += ex;

        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(HtmlUtil.cleanHtmlTag(interpretation.replace("<br/>","\r")));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }

    private String getExplain(String typeName) {
         return calcDao.getMBTIExplain(typeName);
    }

    private int getScore(ScaleFactorDto dto){
        var score = 0;
        var factorName = dto.getFactorName();
        var map = new HashMap<String,Object>();
        map.put("recordId",recordId);
        map.put("factorName",factorName);
        var qIds = calcDao.getQidsByRecordIdAndFactorName(map);
        map.put("qIds",qIds);
        List<LinkedHashMap<String,Object>> listTestResultMap = calcDao.getTestResultForMBTI(map);
        for(LinkedHashMap<String,Object> resultMap: listTestResultMap) {
            var qNo = Integer.parseInt(resultMap.get("q_no").toString());
            var aNo = Integer.parseInt(resultMap.get("a_no").toString());
            //region 外向（E）
            if ("外向".equals(factorName)) {
                int[] e_array = { 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45 };
                if(ArrayUtil.contains(e_array,qNo) && aNo ==1) score++;
            }
            //endregion
            //region 内向（I）
            if ("内向".equals(factorName)) {
                int[] e_array = { 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45 };
                if(ArrayUtil.contains(e_array,qNo) && aNo ==2) score++;
            }
            //endregion
            //region 实感（S）
            if ("实感".equals(factorName)) {
                int[] e_array = { 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46 };
                if(ArrayUtil.contains(e_array,qNo) && aNo ==1) score++;
            }
            //endregion
            //region 直觉（N）
            if ("直觉".equals(factorName)) {
                int[] e_array = { 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46 };
                if(ArrayUtil.contains(e_array,qNo) && aNo ==2) score++;
            }
            //endregion
            //region 思考（T）
            if ("思考".equals(factorName)) {
                int[] e_array = { 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 47 };
                if(ArrayUtil.contains(e_array,qNo) && aNo ==1) score++;
            }
            //endregion
            //region 情感（F）
            if ("情感".equals(factorName)) {
                int[] e_array = { 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 47 };
                if(ArrayUtil.contains(e_array,qNo) && aNo ==2) score++;
            }
            //endregion
            //region 判断（J）
            if ("判断".equals(factorName)) {
                int[] e_array = { 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48 };
                if(ArrayUtil.contains(e_array,qNo) && aNo ==1) score++;
            }
            //endregion
            //region 认知（P）
            if ("认知".equals(factorName)) {
                int[] e_array = { 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48 };
                if(ArrayUtil.contains(e_array,qNo) && aNo ==2) score++;
            }
            //endregion
        }
        return score;
    }
}

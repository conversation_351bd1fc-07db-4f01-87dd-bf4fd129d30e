package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  RCCP通用职业匹配测试量表
 */
@Service
public class RCCP extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Override
    public void compute(){
        computeCommonFactor(true);
        //获取因子分
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        var listFactorScores = testScoreDao.getTestScoreList(testScoreDto);
        //对因子得分进行排序
        var list = listFactorScores.stream().sorted(Comparator.comparing(TestScoreDto:: getScore)).collect(Collectors.toList());
        String resultStr =list.get(list.size() - 1).getFactor().getFactorEn()+list.get(list.size() - 2).getFactor().getFactorEn();
        var listExplainMap = getRccpExplain();
        var responseText = new StringBuilder("<ul class='list-unstyled'>");
        for(LinkedHashMap<String,Object> explainMap: listExplainMap) {
            responseText.append(String.format("<li class=\"mb-2\">%s(%s)：%s，如：%s</li>",
                    explainMap.get("type_name"),
                    explainMap.get("type"),
                    explainMap.get("work"),
                    explainMap.get("zhiye")
            ));
        }
        responseText.append(String.format("<li>您属于 %s 类型。</li></ul>",resultStr));
        interpretation = responseText.toString();

        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(String.format("您属于%s类型。",resultStr));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }

    private List<LinkedHashMap<String,Object>> getRccpExplain() {
        return calcDao.getRccpExplain();
    }
}

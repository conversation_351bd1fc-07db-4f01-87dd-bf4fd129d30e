package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

/**
 *  明尼苏达人格
 */
@Service
public class MMPI extends BaseCalc{
    @Autowired
    private CalcDao calcDao;
    @Autowired
    private TestResultDao testResultDao;
    @Override
    public void compute() {
        var responseText = new StringBuilder();
        var listResults = testResultDao.getList(recordId);
        int[] answer = new int[listResults.size()];
        int k = 0;
        for(TestResultDto testResult: listResults) {
            answer[k] = Integer.parseInt(testResult.getANos());
            k++;
        }
        //各分量表题号
        String[] lb = new String[46];
        var listLbMap = calcDao.getMmpiLB();
        int a = 0;
        for(LinkedHashMap<String, Object> map: listLbMap){
            lb[a] = map.get("question").toString();
            a++;
        }
        //常模
        var listNormMap = calcDao.getMmpiNorm("男".equals(sex) ? 0 :1);

        Object[][] cm = listToArray(listNormMap);
        int[] score = new int[24];
        for (int i = 0; i< score.length; i++) {
            score[i] = 0;
        }
        //Q量表
        for (int j : answer) {
            if (j == 3) score[0]++;
        }
        if (score[0] > 30) {
            responseText.append(String.format("<dt>【无法回答(Q)】</dt><dd>该因子得分为%d,本测验结果不可信；</dd><br/>", score[0]));
        }
        else {
            responseText.append(String.format("<dt>【无法回答(Q)】</dt><dd>该因子得分为%d,属于正常水平；</dd><br/>", score[0]));
        }
        //L量表
        score[1] =cal(lb[0], answer, 2, score[1]);
        if (score[1]> 10) {
            responseText.append(String.format("<dt>【谎言(L)】</dt><dd>该因子得分为%d,分数高表示自己有崇高的道德品质，而在现实中很少人有这样的信念与生活准则；</dd><br/>", score[1]));
        }
        else {
            responseText.append(String.format("<dt>【谎言(L)】</dt><dd>该因子得分为%d,测试者比较诚实，有富于自我批判的精神；</dd><br/>", score[1]));
        }
        //测伪
        if (score[0] > 30 || score[1] > 10) {
            interpretation = String.format("<dd>Q量表：%d分(>30)，L量表：%d分(>10)，掩饰性高，结果不可信。</dd><br/>",score[0],score[1]);
        }

        //F-Pd量表
        for (int i = 2; i <= 7; i++) {
            score[i] = cal(lb[i * 2 - 2 - 1], answer, 1,  score[i]);
            cal(lb[i * 2 - 1 - 1], answer, 2,  score[i]);
        }

        //Mf量表
        score[8] = cal(lb["男".equals(sex) ? 13 : 15], answer, 1,  score[8]);
        score[8] =cal(lb["男".equals(sex)? 14 : 16], answer, 2,  score[8]);

        //Pa-A量表
        for (int i = 9; i <= 14; i++) {
            score[i] = cal(lb[i * 2 - 1], answer, 1, score[i]);
            score[i] =cal(lb[i * 2 + 1 - 1], answer, 2,  score[i]);
        }

        //R量表
        score[15] = cal(lb[29], answer, 2, score[15]);

        //MAS-Cn量表
        for (int i = 16; i <= 23; i++) {
            score[i] = cal(lb[i * 2 - 1 - 1], answer, 1, score[i]);
            score[i] = cal(lb[i * 2 - 1], answer, 2, score[i]);
        }
        int[] sc = score.clone();

        //Hs、Pd、Pt、Sc、Ma加K分
        score[4] += score[3] / 2;
        score[7] += score[3] * 2 / 5;
        score[10] += score[3];
        score[11] += score[3];
        score[12] += score[3] / 5;

        //通过常模计算标准分
        //Q量表未换算
        //L-Si量表
        double[] score2 = new double[score.length];
        for (int i = 1; i <= 13; i++) {
            score2[i] = ((double) score[i] - Double.parseDouble(String.valueOf(cm[0][i])))*10/Double.parseDouble(String.valueOf(cm[1][i])) +50;
        }
        //MAS量表
        score2[16] = 50 + (10 * ((double)score[16] - Double.parseDouble(String.valueOf(cm[0][14])))) / Double.parseDouble(String.valueOf(cm[1][14]));

        //Dy-Re量表
        for (int i = 18; i <= 20; i++) {
            score2[i] = 50 +  (10 * ((double)score[i] - Double.parseDouble(String.valueOf(cm[0][i - 2 - 1])))) / Double.parseDouble(String.valueOf(cm[1][i - 2 - 1]));
        }

        //Cn量表
        score2[23] = 50 + (10 * ((double)score[23] - Double.parseDouble(String.valueOf(cm[0][18])))) / Double.parseDouble(String.valueOf(cm[1][18]));

        for (int i = 0; i < 24; i++) {
            score[i] = (int)score2[i];
        }
        //伪装
        if (score[2] > 60) {
            responseText.append(String.format("<dt>【伪装(F)】</dt><dd>该因子的得分为%d,分数高表示受试者不认真，理解错误，表现出一组互相无关的症状，或在伪装疾病。</dd><br/>",score[2]));
        }
        if (score[2] <= 60) {
            responseText.append(String.format("<dt>【伪装(F)】</dt><dd>该因子的得分为%d,属于正常水平；</dd><br/>",score[2]));
        }
        //K量表
        if (score[3] > 70) {
            responseText.append(String.format("<dt>【防御(K)】</dt><dd>该因子的得分为%d,分数超过70，可能是受试者不合作；</dd><br/>",score[3]));
        }
        if (score[3] <= 70 && score[3] > 55) {
            responseText.append(String.format("<dt>【防御(K)】</dt><dd>该因子的得分为%d,分数超过55,被认为是中等或高等社会经济地位的模式；</dd><br/>",score[3]));
        }
        if (score[3] <= 55) {
            responseText.append(String.format("<dt>【防御(K)】</dt><dd>该因子的得分为%d,分数较低测试者可能过分坦率、自我批评或有装坏的企图；</dd><br/>",score[3]));
        }
        //Hs量表
        if (score[4] > 60) {
            responseText.append(String.format("<dt>【疑病(Hs)】</dt><dd>该因子得分为%d,高分数一般是不愉快、自我中心、敌意、需求、同情、诉苦及企图博得同情的表现；</dd><br/>",score[4]));
        }
        if (score[4] <= 60 && score[4] > 35) {
            responseText.append(String.format("<dt>【疑病(Hs)】</dt><dd>该因子得分为%d,属于正常水平；</dd><br/>",score[4]));
        }
        if (score[4] <= 35) {
            responseText.append(String.format("<dt>【疑病(Hs)】</dt><dd>该因子得分为%d,得分较低；</dd><br/>",score[4]));
        }
        if (score[5] > 60) {
            responseText.append(String.format("<dt>【抑郁(D)】</dt><dd>该因子得分为%d,分数高反应了情绪抑郁，缺乏自信，处处感到不适。这个量表是临床病人经常提高的许多量表之一。高分数者表现为易怒、胆小、依赖、悲观、苦恼、嗜睡、过份控制及自罪；</dd><br/>",score[5]));
        }
        if (score[5] <= 60 && score[5] > 35) {
            responseText.append(String.format("<dt>【抑郁(D)】</dt><dd>该因子得分为%d, 属于正常水平；</dd><br/>",score[5]));
        }
        if (score[5] <= 35) {
            responseText.append(String.format("<dt>【抑郁(D)】</dt><dd>该因子得分为%d, 得分较低；</dd><br/>",score[5]));
        }
        if (score[6] > 60) {
            responseText.append(String.format("<dt>【癔病(Hy)】</dt><dd>该因子得分为%d,分数高表现出依赖性神经症的防御，用否认和压抑来处理外来的压力。</dd><br/>",score[6]));
        }
        if (score[6] <= 60 && score[6] > 35) {
            responseText.append(String.format("<dt>【癔病(Hy)】</dt><dd>该因子得分为%d,属于正常水平；</dd><br/>",score[6]));
        }
        if (score[6] <= 35) {
            responseText.append(String.format("<dt>【癔病(Hy)】</dt><dd>该因子得分为%d,得分较低；</dd><br/>",score[6]));
        }
        if (score[7] > 60) {
            responseText.append(String.format("<dt>【精神病态(Pd)】</dt><dd>该因子得分为%d，高分数者表现个性上的障碍：外露，善交际，可爱。但却是虚伪、做作的。爱享受、好出风头、表现判断力差、不可信任、不成熟、故意的、好攻击，爱寻衅。他们在婚姻及家庭关系中、经常处理不好、并违反法律。高分数者反映出持久的性格问题并且极难治疗；</dd><br/>",score[7]));
        }
        if (score[7] <= 60 && score[7] > 35) {
            responseText.append(String.format("<dt>【精神病态(Pd)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[7]));
        }
        if (score[7] <= 35) {
            responseText.append(String.format("<dt>【精神病态(Pd)】</dt><dd>该因子得分为%d，得分较低；</dd><br/>",score[7]));
        }
        if (score[8] > 60) {
            if ("男".equals(sex)) {
                responseText.append(String.format("<dt>【男性女性化(Mf-m)】</dt><dd>该因子得分为%d，高分数的男人表现敏感、爱美、被动、女性化。他们缺乏对异性的追逐；</dd><br/>",score[8]));
            }
            else {
                responseText.append(String.format("<dt>【女性男性化(Mf-f)】</dt><dd>该因子得分为%d，高分数的妇女被看作男性化，粗鲁、好攻击、自信、缺乏情感、不敏感；</dd><br/>",score[8]));
            }
        }
        if (score[8] <= 60 && score[8] > 35) {
            if ("男".equals(sex)) {
                responseText.append(String.format("<dt>【男性女性化(Mf-m)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[8]));
            }
            else {
                responseText.append(String.format("<dt>【女性男性化(Mf-f)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[8]));
            }
        }
        if (score[8] <= 35) {
            if ("男".equals(sex)) {
                responseText.append(String.format("<dt>【男性女性化(Mf-m)】</dt><dd>该因子得分为%d，低分数的男人好攻击、粗鲁、爱冒险、粗心大意、好实践及兴趣狭窄；</dd><br/>",score[8]));
            }
            else {
                responseText.append(String.format("<dt>【女性男性化(Mf-f)】</dt><dd>该因子得分为%d，低分数的妇女被看作被动、屈服、诉苦、吹毛求疵、理想主义（不现实）、敏感；</dd><br/>",score[8]));
            }
        }
        if (score[9] > 60) {
            responseText.append(String.format("<dt>【妄想(Pa)】</dt><dd>该因子得分为%d，分数过分高，经常与多疑、孤独、精明、警惕、烦恼、过份敏感有关。他们的行为表现是敌意的和好争论的，同时不太服从心理治疗；</dd><br/>",score[9]));
        }
        if (score[9] <= 60 && score[9] > 35) {
            responseText.append(String.format("<dt>【妄想(Pa)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[9]));
        }
        if (score[9] <= 35) {
            responseText.append(String.format("<dt>【妄想(Pa)】</dt><dd>该因子得分为%d，得分较低；</dd><br/>",score[9]));
        }
        if (score[10] > 60) {
            responseText.append(String.format("<dt>【精神衰弱(Pt)】</dt><dd>该因子得分为%d，高分数者表现紧张、焦虑、反复思考、强迫思想、恐怖的、刻版的。他们经常自责、自罪、感到不如人和不安；</dd><br/>",score[10]));
        }
        if (score[10] <= 60 && score[10] > 35) {
            responseText.append(String.format("<dt>【精神衰弱(Pt)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[10]));
        }
        if (score[10] <= 35) {
            responseText.append(String.format("<dt>【精神衰弱(Pt)】</dt><dd>该因子得分为%d，得分较低；</dd><br/>",score[10]));
        }
        if (score[11] > 70) {
            responseText.append(String.format("<dt>【精神分裂症(Sc)】</dt><dd>该因子得分为%d，高分数，表现出异乎寻常或分裂的生活方式。他们是退缩的、胆小的、感觉不充分、紧张的、混乱的以及心情易变的。可有不寻常或奇怪的思想，判断力差及怪僻（不稳定）的情绪；</dd><br/>",score[11] ));
        }
        if (score[11] <= 70 && score[11] > 35) {
            responseText.append(String.format("<dt>【精神分裂症(Sc)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[11] ));
        }
        if (score[11] <= 35) {
            responseText.append(String.format("<dt>【精神分裂症(Sc)】</dt><dd>该因子得分为%d，得分较低；</dd><br/>",score[11]));
        }
        if (score[12] > 70) {
            responseText.append(String.format("<dt>【轻躁狂(Ma)】</dt><dd>该因子得分为%d，高分数者被看作为善交际、外露、冲动、精力过度充沛、乐观、无拘无束的道德观、轻浮、纵酒、夸张、易怒、绝对乐观及有现实的打算、过高地估计自己，有些造作、表现性急、易怒；</dd><br/>",score[12]));
        }
        if (score[12] <= 70 && score[12] > 35) {
            responseText.append(String.format("<dt>【轻躁狂(Ma)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[12]));
        }
        if (score[12] <= 35) {
            responseText.append(String.format("<dt>【轻躁狂(Ma)】</dt><dd>该因子得分为%d，得分较低；</dd><br/>",score[12]));
        }
        if (score[13] > 60) {
            responseText.append(String.format("<dt>【社会内向(Si)】</dt><dd>该因子得分为%d，高分数者表现内向，胆小、退缩、不善交际、屈服、过份自我控制，懒散、习俗、紧张、固执及表现自罪；</dd><br/>",score[13]));
        }
        if (score[13] <= 60 && score[13] > 35) {
            responseText.append(String.format("<dt>【社会内向(Si)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[13]));
        }
        if (score[13] <= 35) {
            responseText.append(String.format("<dt>【社会内向(Si)】</dt><dd>该因子得分为%d，低分数者表现外向。爱社交、富于表情、好攻击、健谈、冲动、不受拘束、任性、做作，在社会关系中不真诚；</dd><br/>",score[13]));
        }
        if (score[16] > 60) {
            responseText.append(String.format("<dt>【外显性焦虑(MAS)】</dt><dd>该因子得分为%d，分数高，表示受试者在应激状态下容易表现出强烈的情绪不安，如焦虑、紧张、神经过敏以及明显的生理变化，如出汗过多，脉搏加速等。视周围环境存在某种危险，感到有超过自己控制能力的外在压力，强调现在比强调将来多。对简单的工作完成较好，而复杂的工作完成较差。常诉说有较多的躯体疾病，大部分时间处于激动状态，难以集中注意力。缺乏自信，对周围人的反应过份敏感，常感到自己不幸或没意思；</dd><br/>",score[16]));
        }
        if (score[16] <= 60 && score[16] > 35) {
            responseText.append(String.format("<dt>【外显性焦虑(MAS)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[16]));
        }
        if (score[16] <= 35) {
            responseText.append(String.format("<dt>【外显性焦虑(MAS)】</dt><dd>该因子得分为%d，低分，在应激状态下没有强烈的情绪不安，心态保持平衡和沉着，对事物感到自信和有把握。一般说来，身体没有太多的躯体疾病；</dd><br/>",score[16]));
        }
        if (score[18] > 60) {
            responseText.append(String.format("<dt>【依赖性(Dy)】</dt><dd>该因子得分为%d，高分者，提示其心理适应不良，依赖性强。常感到被人误解，情绪烦躁不安，不愉快，身体不舒服。在社交场合感到缺乏自信，害羞和窘迫，对周围事物反应过分敏感；</dd><br/>",score[18]));
        }
        if (score[18] <= 60 && score[18] > 35) {
            responseText.append(String.format("<dt>【依赖性(Dy)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[18]));
        }
        if (score[18] <= 35) {
            responseText.append(String.format("<dt>【依赖性(Dy)】</dt><dd>该因子得分为%d，低分，表示有良好的心理适应能力，否认依赖性强。感到被别人理解，愉快，身体无不适感。在社交场合能轻松自如，有信心。对周围反应不过分敏感；</dd><br/>",score[18]));
        }
        if (score[19] > 60) {
            responseText.append(String.format("<dt>【支配性(Do)】</dt><dd>该因子得分为%d，高分，表示在人际交往中能力较强，不易受人影响。支配性强的人，在领导者或负责人中较普遍，他们常以沉着、自信、直截了当的方式办事。乐观，明智，办事高效率，喜欢做目的性明确的工作，能胜任困难的任务，有恒心。在道德问题上有责任感。能面对现实；</dd><br/>",score[19]));
        }
        if (score[19] <= 60 && score[19] > 35) {
            responseText.append(String.format("<dt>【支配性(Do)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[19]));
        }
        if (score[19] <= 35) {
            responseText.append(String.format("<dt>【支配性(Do)】</dt><dd>该因子得分为%d，低分，提示在面对面的人际交往中显得软弱，不果断，常不能维护自己的权利和坚持自己的观点，容易受别人的思想影响而放弃已见。缺乏自信，易退却，对周围事或人不愿承担责任，不能面对现实。；</dd><br/>",score[19]));
        }
        if (score[20] > 60) {
            responseText.append(String.format("<dt>【社会责任感(Re)】</dt><dd>该因子得分为%d，分数高，提示对自己的行为后果能负责任，可靠，可信任，有整体观念和对集体有责任感。有固定的价值观，对伦理道德等问题较关心，有强烈的正义感，能高标准要求自己，厌恶特权和不公正，过分强调自己所担负的责任，自信；</dd><br/>",score[20] ));
        }
        if (score[20] <= 60 && score[20] > 35) {
            responseText.append(String.format("<dt>【社会责任感(Re)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[20]));
        }
        if (score[20] <= 35) {
            responseText.append(String.format("<dt>【社会责任感(Re)】</dt><dd>该因子得分为%d，低分，表示不愿为自己的行为后果负责任，缺乏可靠性、可信赖感和对集体的责任感。价值观易受人们影响而改变，如若是青年人，则可能会否定父辈们的价值观；如是年长者，可能否定最近形式的价值观和接受新的宗教或政治观点的影响；</dd><br/>",score[20]));
        }
        if (score[23] > 60) {
            responseText.append(String.format("<dt>【控制力(Cn)】</dt><dd>该因子得分为%d，高分，表示不愿将问题行为显露给他人，希望能在不被送住院的情况下处理问题。可能对医生隐瞒病情或问题，冷漠，希望表达更多的情绪，对劝说不耐烦，意识到自己的弱点，内心对别人的批评较敏感，对权威有反抗倾向，愿意以不同方式应付环境；</dd><br/>",score[23]));
        }
        if (score[23] <= 60 && score[23] > 35) {
            responseText.append(String.format("<dt>【控制力(Cn)】</dt><dd>该因子得分为%d，属于正常水平；</dd><br/>",score[23]));
        }
        if (score[23] <= 35) {
            responseText.append(String.format("<dt>【控制力(Cn)】</dt><dd>该因子得分为%d，低分，不能控制有问题的行为，被送住院是必要的，不能从别人的劝说中取得经验，对宗教信仰虔诚。不能正确自我评价；</dd><br/>",score[23] ));
        }
        //计算正常异常
        for (int i = 1; i < 24; i++) {
            if (score[i] > 60 && i != 8 && i != 13){//Mf与Si因子不作判断
                isAbnormal = true;
                break;
            }
        }

        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(HtmlUtil.cleanHtmlTag(responseText.toString().replace("<br/>","\r")));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);

        responseText.append("<dt>注意：以下为总体解释，并非针对个人得分情况。</dt>");
        var listExplainMap = calcDao.getMmpiExplain();
        for(LinkedHashMap<String,Object> explainMap: listExplainMap) {
            responseText.append(String.format("<dt>【%s（%s）】<dt><dd>%s</dd>",
                    explainMap.get("type_name"),
                    explainMap.get("type"),
                    explainMap.get("interpretation")
            ));
        }

        for (int i = 0; i < score.length; i++) {
            if (score[i] > 60 && i != 8 && i != 13)//Mf与Si因子不作判断
            {
                isAbnormal = true;
            }
            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(291 + i);
            testScoreEntity.setOriginalScore(BigDecimal.valueOf(sc[i]));
            testScoreEntity.setScore(BigDecimal.valueOf(score[i]));
            testScoreEntity.setIsAbnormal(isAbnormal ? 1 : 0);
            testScoreEntity.setAbnormalValue(BigDecimal.valueOf( score[i]));
            saveScore(testScoreEntity);
        }
        interpretation = responseText.toString();
    }

    private int cal(String qs, int[] answer, int _answer, Integer sc) {
        String[] t = qs.split("\\|");
        for(String s: t) {
            if (answer[Integer.parseInt(s)] == _answer) sc++;
        }
        return sc;
    }

    private Object[][] listToArray(List<LinkedHashMap<String, Object>> list) {
        int size = list.size();
        Object[][] array = new Object[size][19];
        for (int i = 0; i < size; i++) {
            array[i] = list.get(i).values().toArray();
        }
        return array;
    }
}

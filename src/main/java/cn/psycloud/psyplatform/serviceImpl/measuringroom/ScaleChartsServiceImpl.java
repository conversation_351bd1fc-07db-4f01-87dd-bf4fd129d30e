package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleChartsDao;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleChartsEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleChartsService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class ScaleChartsServiceImpl implements ScaleChartsService {
    @Autowired
    private ScaleChartsDao scaleChartsDao;

    /**
     * 保存测评报告图表类型
     * @param scaleId 量表id
     * @param factorType 因子类型
     * @param charts 图表类型字符串集合
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(Integer scaleId, Integer factorType, String charts){
        boolean flag=false;
        try{
            // 删除该量表该因子类型的所有图表配置
            scaleChartsDao.deleteByScaleIdAndFactorType(scaleId, factorType);
            for (String chart:charts.split(",")){
                var scaleChartsEntity=new ScaleChartsEntity();
                scaleChartsEntity.setScaleId(scaleId);
                scaleChartsEntity.setFactorType(factorType);
                scaleChartsEntity.setChartType(chart);
                scaleChartsDao.insert(scaleChartsEntity);
                flag=true;
            }
        }
        catch(Exception e){
            log.error("保存量表的测评报告图表类型时出错：{}",e.getMessage());
        }
        return flag;
    }
}

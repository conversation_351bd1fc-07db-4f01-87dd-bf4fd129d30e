package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HtmlUtil;
import cn.psycloud.psyplatform.dao.anteroom.MailDao;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.measuringroom.TaskDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao;
import cn.psycloud.psyplatform.dao.survey.SurveyRecordDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleUndoneDto;
import cn.psycloud.psyplatform.dto.measuringroom.TaskDto;
import cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyStatDto;
import cn.psycloud.psyplatform.dto.survey.SurveyQuestionDto;
import cn.psycloud.psyplatform.dto.survey.SurveyResultCountDto;
import cn.psycloud.psyplatform.dto.survey.SurveyResultDto;
import cn.psycloud.psyplatform.dto.survey.SurveyResultStatDto;
import cn.psycloud.psyplatform.entity.anteroom.MailEntity;
import cn.psycloud.psyplatform.entity.measuringroom.*;
import cn.psycloud.psyplatform.entity.survey.SurveyItemEntity;
import cn.psycloud.psyplatform.service.measuringroom.TaskService;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.POIWordHelper;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class TaskServiceImpl implements TaskService {
    @Autowired
    private TaskDao taskDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private MailDao mailDao;
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private SurveyRecordDao surveyRecordDao;
    private List<UserDto> users;
    @Autowired
    private SurveyQuestionDao surveyQuestionDao;
    @Value("${file.location}")
    String uploadPath;
    @Autowired
    private POIWordHelper poiWordHelper;

    /**
     *  获取测评任务集合：分页
     * @param dto 查询条件
     * @return 测评任务集合
     */
    @Override
    public BSDatatableRes<TaskDto> getListByPaged(TaskDto dto) {
        var user = (UserDto)SessionUtil.getSession().getAttribute("user");
        List<Integer> childs = new ArrayList<>();
        var dataPrivilege = PermissonHelper.getDataPrivilege(user);
        if(dataPrivilege == 0) childs.add(0);
        if(dataPrivilege == 1) {
            childs = PermissonHelper.getChildStructIds(user.getStructId());
        }
        dto.setChildStructs(childs);
        var dtRes = new BSDatatableRes<TaskDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize() +1,dto.getPageSize());
        var lisTasks = taskDao.getList(dto);
        var taskDto = new PageInfo<>(lisTasks);
        dtRes.setData(taskDto.getList());
        dtRes.setRecordsTotal((int)taskDto.getTotal());
        dtRes.setRecordsFiltered((int)taskDto.getTotal());
        return dtRes;
    }

    /**
     *  验证任务名称是否重复
     * @param taskName 任务名称
     * @return 是否存在
     */
    @Override
    public int isTaskNameExist(String taskName, Integer taskKind) {
        var map = new HashMap<String, Object>();
        map.put("taskName", taskName);
        map.put("taskKind", taskKind);
        return taskDao.isExist(map);
    }

    /**
     *  添加测评任务
     * @param dto 测评任务实体对象
     * @return 是否成功
     */
    @Override
    public boolean addTask(TaskDto dto,HttpServletRequest request) {
        boolean isSuccess = false;
        var taskEntity = new TaskEntity();
        taskEntity.setId(0);
        taskEntity.setTaskName(dto.getTaskName());
        taskEntity.setStartTime(dto.getStartTime());
        taskEntity.setEndTime(dto.getEndTime());
        taskEntity.setResultViewRule(dto.getResultViewRule());
        var currentUser = (UserDto) SessionUtil.getSession().getAttribute("user");
        taskEntity.setPersonInCharge(currentUser.getUserId());
        taskEntity.setTaskType(dto.getTaskType());
        taskEntity.setIsLimitTestCount(dto.getIsLimitTestCount());
        taskEntity.setLimitTestCount(dto.getLimitTestCount());
        taskEntity.setIsSurvey(dto.getIsSurvey());
        taskEntity.setSurveyId(dto.getSurveyId());
        taskEntity.setTaskKind(dto.getTaskKind());
        taskEntity.setShowBackground(dto.getShowBackground());
        taskEntity.setBackgroundUrl(dto.getBackgroundUrl());
        taskDao.addTask(taskEntity);
        var taskId = taskEntity.getId();
        if (taskId > 0) {
            var isTaskUserSuccess = true;
            //限定测试对象的任务
            if (dto.getTaskType() == 1) {
                isTaskUserSuccess = addTaskUser(taskId, dto, request);
                if(!"".equals(dto.getStructIds())){
                    addTaskStruct(taskId, dto.getStructIds());
                }
            }
            var isTaskScaleSuccess = false;
            var isTaskSurveySuccess = false;
            //量表测评任务
            if(taskEntity.getTaskKind() == 1){
                isTaskScaleSuccess = addTaskScale(taskId, dto.getScaleIds());
                isSuccess = isTaskScaleSuccess && isTaskUserSuccess && addTaskRecord(taskId, dto);
            }
            //调查问卷任务
            if(taskEntity.getTaskKind() == 2){
                isTaskSurveySuccess = addTaskSurvey(taskId, dto.getSurveyIds());
                isSuccess = isTaskSurveySuccess && isTaskUserSuccess && addTaskSurveyRecord(taskId, dto);
            }
        }
        return isSuccess;
    }

    /**
     *  添加测评任务的测评记录
     * @param taskId 任务id
     * @param dto 测评任务实体对象
     * @return 是否成功
     */
    private boolean addTaskRecord(Integer taskId, TaskDto dto) {
        var isScuuess = false;
        try{
            String[] scaleIds = dto.getScaleIds().split(",");
            if(users != null && users.size() > 0) {
                for(UserDto user: users) {
                    for(String scaleId: scaleIds) {
                        var testRcordEntity = new TestRecordEntity();
                        testRcordEntity.setUserId(user.getUserId());
                        testRcordEntity.setScaleId(Integer.parseInt(scaleId));
                        testRcordEntity.setState(0);
                        testRcordEntity.setStartTime(dto.getStartTime());
                        testRcordEntity.setEndTime(dto.getEndTime());
                        testRcordEntity.setId(0);
                        testRecordDao.addTestRecord(testRcordEntity);
                        var recordId = testRcordEntity.getId();
                        var taskRecordEntity = new TaskRecordEntity();
                        taskRecordEntity.setTaskId(taskId);
                        taskRecordEntity.setRecordId(recordId);
                        taskDao.addTaskRecord(taskRecordEntity);
                        isScuuess = true;
                    }
                }
            }
            isScuuess = true;
        }
        catch (Exception e) {
            log.error("添加测评任务记录时发生错误：{}",e.getMessage());
        }
        return isScuuess;
    }

    /**
     * 添加问卷调查任务记录
     * @param taskId 任务id
     * @param dto 问卷调查任务实体对象
     * @return 是否成功
     */
    private boolean addTaskSurveyRecord(Integer taskId, TaskDto dto) {
        var isScuuess = false;
        try{
            String[] surveyIds = dto.getSurveyIds().split(",");
            if(users != null && users.size() > 0) {
                for(UserDto user: users) {
                    for(String surveyId: surveyIds) {
                        var surveyRecordEntity = new TaskSurveyRecordEntity();
                        surveyRecordEntity.setSurveyId(Integer.parseInt(surveyId));
                        surveyRecordEntity.setUserId(user.getUserId());
                        surveyRecordEntity.setRecordDate(dto.getStartTime());
                        surveyRecordEntity.setId(0);
                        surveyRecordDao.addRecord(surveyRecordEntity);
                        var recordId = surveyRecordEntity.getId();
                        var taskRecordEntity = new TaskRecordEntity();
                        taskRecordEntity.setTaskId(taskId);
                        taskRecordEntity.setRecordId(recordId);
                        taskDao.addTaskRecord(taskRecordEntity);
                        isScuuess = true;
                    }
                }
            }
            isScuuess = true;
        }
        catch (Exception e) {
            log.error("添加问卷调查任务记录时发生错误：{}",e.getMessage());
        }
        return isScuuess;
    }

    /**
     *  添加任务包含的组织
     * @param taskId 任务id
     * @param structIds 任务包含的组织id集合
     */
    private void addTaskStruct(Integer taskId, String structIds)
    {
        var arrayStructIds = structIds.split(",");
        for (String arrayStructId : arrayStructIds) {
            var structId = Integer.parseInt(arrayStructId);
            var map = new HashMap<String,Integer>();
            map.put("taskId",taskId);
            map.put("structId",structId);
            taskDao.addTaskStruct(map);
        }
    }

    private List<Integer> getChildStructs(String childStr) {
        var childStructs = new ArrayList<Integer>();
        String[] arrayIds = childStr.split(",");
        //获取所有子组织的ID集合
        for (String arrayId : arrayIds) {
            var structId = Integer.parseInt(arrayId);
            childStructs.add(structId);
        }
        return  childStructs;
    }

    /// <summary>
    /// 添加测评对象
    /// </summary>
    /// <param name="structIds"></param>
    /// <returns></returns>
    private boolean addTaskUser(Integer taskId, TaskDto dto, HttpServletRequest request)
    {
        boolean isSuccess = false;
        var userDto = new UserDto();
        userDto.setRoleId(dto.getRoleId());
        userDto.setLoginName(dto.getLoginName());
        userDto.setRealName(dto.getRealName());
        if (dto.getStructIds() != null && !"".equals(dto.getStructIds())) {
            userDto.setChildStructs(getChildStructs(dto.getStructIds()));
        }
        users = userDao.getUserList(userDto);
        for(UserDto user : users) {
            var taskUser = new TaskUserEntity();
            taskUser.setTaskId(taskId);
            taskUser.setUserId(user.getUserId());
            addUser(taskUser);
            sendTaskMsg(taskUser.getUserId(),  request);
            isSuccess = true;
        }
        return isSuccess;
    }

    /**
     *  添加测评对象
     * @param entity 测评任务里的对象实体
     * @return 影响行数
     */
    private int addUser(TaskUserEntity entity) {
        return taskDao.addTaskUser(entity);
    }

    /**
     *  发送站内消息
     * @param toUser 接收人
     * @param request 发送人
     * @return 影响行数
     */
    private int sendTaskMsg(int toUser, HttpServletRequest request)
    {
        var mail = new MailEntity();
        mail.setMsgTitle("新的测评任务消息");
        String redirectUrl;
        var currentUrl = request.getRequestURL();
        if(currentUrl.toString().toLowerCase().contains("app")){
            redirectUrl = "/app/measuring/my_tasks";
        }
        else
            redirectUrl = "/measuringroom/task/my_tasks/";
        var msgContent = MessageFormat.format("您有新的测评任务，<a href=\"{0}\" class=\"text-primary\">前往查看</a>",redirectUrl);
        mail.setMsgContent(msgContent);
        mail.setToUser(toUser);
        mail.setFromUser(1);
        mail.setSendDate(new Date());
        return mailDao.sendMsg(mail);
    }

    /**
     *  添加测评量表
     * @param taskId 任务id
     * @param scaleIds 量表集合
     * @return 是否成功
     */
    private boolean addTaskScale(Integer taskId, String scaleIds) {
        var isSuccess = false;
        String[] arrayScaleIds = scaleIds.split(",");
        for (String arrayScaleId : arrayScaleIds) {
            var scaleId = Integer.parseInt(arrayScaleId);
            var taskScale = new TaskScaleEntity();
            taskScale.setTaskId(taskId);
            taskScale.setScaleId(scaleId);
            isSuccess = taskDao.addTaskScale(taskScale) > 0;
        }
        return isSuccess;
    }

    /**
     * 添加问卷调查任务里的问卷
     * @param taskId 任务id
     * @param surveyIds 问卷集合
     * @return 是否成功
     */
    private boolean addTaskSurvey(Integer taskId, String surveyIds){
        var isSuccess = false;
        String[] arraySurveyIds = surveyIds.split(",");
        for (String arraySurveyId : arraySurveyIds) {
            var surveyId = Integer.parseInt(arraySurveyId);
            var taskSurvey = new TaskSurveyEntity();
            taskSurvey.setTaskId(taskId);
            taskSurvey.setSurveyId(surveyId);
            isSuccess = taskDao.addTaskSurvey(taskSurvey) > 0;
        }
        return isSuccess;
    }

    /**
     *  修改测评任务基本信息
     * @param entity 测评任务实体对象
     * @return 影响行数
     */
    @Override
    public int updateTask(TaskEntity entity) {
        return taskDao.updateTask(entity);
    }

    /**
     *  删除测评任务
     * @param taskId 测评任务id
     * @return 影响行数
     */
    @Override
    public int delTask(Integer taskId) {
        return taskDao.deleteById(taskId);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        var isSuccess = false;
        var array_ids = ids.split(",");
        for (String arrayId : array_ids) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delTask(id) > 0;
        }
        return isSuccess;
    }

    /**
     *  根据测评任务id查询测评对象
     * @param taskId 测评任务id
     * @return 用户集合
     */
    @Override
    public List<UserDto> getUsersByTaskId(Integer taskId) {
        return taskDao.getUsersByTaskId(taskId);
    }

    /**
     *  根据任务ID查询任务详情
     * @param taskId 测评任务id
     * @return 测评任务实体丢向
     */
    @Override
    public TaskDto getById(Integer taskId) {
        return taskDao.getById(taskId);
    }

    /**
     *  获取我的测评任务
     * @param dto 查询条件
     * @return 测评任务集合
     */
    @Override
    public BSDatatableRes<TaskDto> getMyTasks(TaskDto dto) {
        var user = (UserDto)SessionUtil.getSession().getAttribute("user");
        dto.setVisitorId(user.getUserId());
        var dtRes = new BSDatatableRes<TaskDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listTasks = taskDao.getMyTasks(dto);
        var taskDto = new PageInfo<>(listTasks);
        dtRes.setData(taskDto.getList());
        dtRes.setRecordsTotal((int)taskDto.getTotal());
        dtRes.setRecordsFiltered((int)taskDto.getTotal());
        return dtRes;
    }

    /**
     *  判断任务中的量表是否已经做过
     * @param taskId 测评任务id
     * @param scaleId 量表id
     * @return 测评记录实体对象
     */
    @Override
    public TestRecordEntity isScaleDone(Integer taskId, Integer scaleId) {
        var currentUser = (UserDto)SessionUtil.getSession().getAttribute("user");
        var userId = currentUser.getUserId();
        var map = new HashMap<String,Integer>();
        map.put("taskId",taskId);
        map.put("scaleId",scaleId);
        map.put("userId",userId);
        return taskDao.isScaleDone(map);
    }

    /**
     *  获取记录Id
     * @param taskId 测评任务id
     * @param scaleId 量表id
     * @return 测评记录id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer getRecordIdByTaskId(Integer taskId, Integer scaleId) {
        Integer recordId = 0;
        try{
            var currentUser = (UserDto)SessionUtil.getSession().getAttribute("user");
            var userId = currentUser.getUserId();
            var map = new HashMap<String,Integer>();
            map.put("taskId", taskId);
            map.put("userId", userId);
            map.put("scaleId",scaleId);
            recordId = taskDao.getRecordIdByTaskId(map);
            if (recordId == null) {
                var taskUserEntity = BeanUtil.fillBeanWithMap(map, new TaskUserEntity(),false);
                taskDao.addTaskUser(taskUserEntity);
                var testRecordEntity = new TestRecordEntity();
                testRecordEntity.setId(0);
                testRecordEntity.setUserId(userId);
                testRecordEntity.setScaleId(scaleId);
                testRecordEntity.setState(0);
                testRecordEntity.setStartTime(new Date());
                testRecordDao.addTestRecord(testRecordEntity);
                recordId = testRecordEntity.getId();
                var taskRecordEntity = new TaskRecordEntity();
                taskRecordEntity.setTaskId(taskId);
                taskRecordEntity.setRecordId(recordId);
                taskDao.addTaskRecord(taskRecordEntity);
            }
        }
        catch (Exception e) {
            log.error("通过测评任务id获取记录id时发生错误：{}",e.getMessage());
        }
        return recordId;
    }

    /**
     *  根据测评记录ID查询结果查看规则
     * @param recordId 记录id
     * @return 记录Id
     */
    @Override
    public Integer getResultViewRuleByRecordId(Integer recordId) {
        return taskDao.getResultViewRuleByRecordId(recordId);
    }

    /**
     *  查询测评任务集合：转换成select所需格式
     * @return 集合
     */
    @Override
    public List<Object> getListForSelect(TaskDto taskDto) {
        var user = (UserDto)SessionUtil.getSession().getAttribute("user");
        List<Integer> childs = new ArrayList<>();
        var dataPrivilege = PermissonHelper.getDataPrivilege(user);
        if(dataPrivilege == 0) childs.add(0);
        if(dataPrivilege == 1) {
            childs = PermissonHelper.getChildStructIds(user.getStructId());
        }
        taskDto.setChildStructs(childs);
        var listTasks =taskDao.getListForSelect(taskDto);
        return getSelect2Data(listTasks);
    }

    /**
     *  将数据格式转换成select2格式
     * @param listTasks 任务集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<TaskEntity> listTasks) {
        var lsNode = new ArrayList<>();
        for (TaskEntity entity : listTasks) {
            var select2Data = new Select2Data();
            select2Data.setId(entity.getId());
            select2Data.setText(entity.getTaskName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  判断用户是不是属于当前测评任务
     * @param taskId 任务id
     * @return 记录数
     */
    @Override
    public int isTaskValid(Integer taskId){
        var map = new HashMap<String, Integer>();
        var user = (UserDto)SessionUtil.getSession().getAttribute("user");
        map.put("userId",user.getUserId());
        map.put("taskId",taskId);
        return taskDao.isTaskValid(map);
    }

    /**
     *  判断测评任务时间是否已结束
     */
    @Override
    public boolean isTaskEnded(Integer taskId){
        var taskDto = getById(taskId);
        var endTime = taskDto.getEndTime();
        var currentTime = new Date();
        return currentTime.compareTo(endTime) > 0;
    }

    /**
     *  判断测评任务里的调查问卷是否完成
     * @param taskId 任务id
     * @return 是否完成
     */
    @Override
    public boolean isTaskSurveyDone(Integer userId,Integer taskId){
        Map<String,Integer> map = new HashMap<>();
        map.put("userId",userId);
        map.put("taskId",taskId);
        return taskDao.isTaskSurveyDone(map) == 1 ;
    }

    /**
     *  判断问卷调查任务里的问卷是否完成
     * @param userId 用户id
     * @param taskId 任务id
     * @param surveyId 问卷id
     * @return 问卷作答记录实体对象
     */
    @Override
    public TaskSurveyRecordEntity isSurveyOfTaskDone(Integer userId,Integer taskId,Integer surveyId){
        Map<String,Integer> map = new HashMap<>();
        map.put("userId",userId);
        map.put("taskId",taskId);
        map.put("surveyId",surveyId);
        var taskSurveyRecordEntity = taskDao.isSurveyOfTaskDone(map);
        if(taskSurveyRecordEntity == null){
            taskSurveyRecordEntity = new TaskSurveyRecordEntity();
            var taskUserEntity = BeanUtil.fillBeanWithMap(map, new TaskUserEntity(),false);
            taskDao.addTaskUser(taskUserEntity);
            taskSurveyRecordEntity.setId(0);
            taskSurveyRecordEntity.setUserId(userId);
            taskSurveyRecordEntity.setSurveyId(surveyId);
            taskSurveyRecordEntity.setIsDone(0);
            taskSurveyRecordEntity.setRecordDate(new Date());
            surveyRecordDao.addRecord(taskSurveyRecordEntity);
            var recordId = taskSurveyRecordEntity.getId();
            var taskRecordEntity = new TaskRecordEntity();
            taskRecordEntity.setTaskId(taskId);
            taskRecordEntity.setRecordId(recordId);
            taskDao.addTaskRecord(taskRecordEntity);
        };
        return taskSurveyRecordEntity;
    }

    /**
     *  查询用户的测评任务里面未完成的量表
     * @param userId 用户id
     * @param taskId 任务id
     * @return 未完成量表集合
     */
    @Override
    public List<ScaleUndoneDto> getScaleListUndone(Integer userId, Integer taskId){
        Map<String,Integer> map = new HashMap<>();
        map.put("userId",userId);
        map.put("taskId",taskId);
        return taskDao.getScaleListUndone(map);
    }

    /**
     * 调查问卷结果统计
     * @param entity 调查问卷任务和问卷关联实体类
     * @return 实体对象
     */
    @Override
    public TaskSurveyStatDto getTaskSurveyStat(TaskSurveyEntity entity){
        var taskSurveyStatDto = taskDao.getTaskSurveyStat(entity);
        int totalCount= taskSurveyStatDto.getSurveyRecords().size();
        taskSurveyStatDto.setTotalCount(totalCount);
        int completedCount = 0;
        int notCompletedCount = 0;
        for(TaskSurveyRecordEntity taskSurveyRecord: taskSurveyStatDto.getSurveyRecords()){
            if(taskSurveyRecord.getIsDone() == 1){
                completedCount++;
            }
            if(taskSurveyRecord.getIsDone() == 0){
                notCompletedCount++;
            }
        }
        taskSurveyStatDto.setDoneCount(completedCount);
        taskSurveyStatDto.setUnDoneCount(notCompletedCount);
        taskSurveyStatDto.setDoneRate(((double) completedCount / totalCount) * 100);
        taskSurveyStatDto.setUnDoneRate(((double) notCompletedCount / totalCount) * 100);

        List<SurveyQuestionDto> surveyQuestions =  surveyQuestionDao.getListBySurveyId(taskSurveyStatDto.getSurveyId());

        taskSurveyStatDto.setSurveyResultStat(countOptionSelections(surveyQuestions,taskSurveyStatDto.getSurveyResults(),completedCount));
        return taskSurveyStatDto;
    }

    public List<SurveyResultStatDto> countOptionSelections(List<SurveyQuestionDto> surveyQuestions,List<SurveyResultDto> surveyResults,Integer totalCount) {
        // 使用流式处理统计数据，但仅针对非填空题
        Map<Integer, Map<String, Long>> countsByQuestionAndItem = surveyResults.stream()
                .flatMap(srd -> {
                    // 查找对应的问题以确定其类型
                    Optional<SurveyQuestionDto> questionOpt = surveyQuestions.stream()
                            .filter(qwo -> qwo.getQNumber().equals(srd.getQNumber()))
                            .findFirst();

                    int qType = questionOpt.map(SurveyQuestionDto::getQType).orElse(1); // 默认为单选题

                    if (qType == 2) { // 如果是多选题
                        // 分割itemContent
                        return Arrays.stream(srd.getItemId().split("\\|"))
                                .map(String::trim)
                                .map(itemContent -> new AbstractMap.SimpleEntry<>(srd.getQNumber(), itemContent));
                    } else if (qType == 4) { // 如果是排序题
                        // 分割排序结果，提取选项内容
                        return Arrays.stream(srd.getItemId().split("\\|"))
                                .map(String::trim)
                                .filter(item -> item.contains("."))
                                .map(item -> item.substring(item.indexOf(".") + 1))
                                .map(itemContent -> new AbstractMap.SimpleEntry<>(srd.getQNumber(), itemContent));
                    } else if (qType != 3) { // 如果不是填空题
                        // 对于单选题，直接返回单个条目
                        return Stream.of(new AbstractMap.SimpleEntry<>(srd.getQNumber(), srd.getItemId()));
                    } else {
                        // 对于填空题，跳过统计
                        return Stream.empty();
                    }
                })
                .collect(Collectors.groupingBy(
                        entry -> entry.getKey(),
                        Collectors.groupingBy(
                                entry -> entry.getValue(),
                                Collectors.counting()
                        )
                ));

        // 构建最终的结果列表
        List<SurveyResultStatDto> resultStats = surveyQuestions.stream()
                .map(qwo -> {
                    Integer qNumber = qwo.getQNumber();
                    String qContent = qwo.getQContent();
                    int qType = qwo.getQType();
                    List<SurveyItemEntity> allItems = qwo.getListItems();

                    SurveyResultStatDto statDto = new SurveyResultStatDto();
                    statDto.setQNumber(qNumber);
                    statDto.setQContent(qContent);
                    statDto.setQType(qType);

                    if (qType != 3) { // 如果不是填空题
                        // 对于选择题（单选、多选或排序），构建选项统计信息
                        List<SurveyResultCountDto> listResultCounts = allItems.stream()
                                .map(item -> {
                                    SurveyResultCountDto countDto = new SurveyResultCountDto();
                                    countDto.setQuestionNumber(qNumber);
                                    countDto.setItemContent(item.getItemContent());

                                    // 如果该选项有统计数据，则使用统计数据；否则设置数量为0
                                    long selCount = countsByQuestionAndItem.getOrDefault(qNumber, Collections.emptyMap())
                                            .getOrDefault(item.getItemContent(), 0L);
                                    countDto.setSelCount((int) selCount);

                                    // 计算选择项数量占比，如果总选择次数为0，则占比为0
                                    double selRate = totalCount > 0 ? ((double) selCount / totalCount) : 0.0;
                                    countDto.setSelRate(formatPercentage(selRate));

                                    return countDto;
                                })
                                .collect(Collectors.toList());

                        statDto.setListResultCounts(listResultCounts);
                    } else if (qType == 4) { // 排序题特殊处理
                        // 对于排序题，统计每个选项的平均排名
                        List<SurveyResultCountDto> listResultCounts = calculateSortingStats(surveyResults, qNumber, allItems);
                        statDto.setListResultCounts(listResultCounts);
                    } else {
                        // 对于填空题，可以选择跳过或者添加一个特殊的标识符来表示没有统计数据
                        statDto.setListResultCounts(Collections.emptyList());
                    }

                    return statDto;
                })
                .collect(Collectors.toList());

        return resultStats;
    }

    /**
     * 计算排序题的统计信息
     */
    private List<SurveyResultCountDto> calculateSortingStats(List<SurveyResultDto> surveyResults, Integer qNumber, List<SurveyItemEntity> allItems) {
        // 统计每个选项的排名信息
        Map<String, List<Integer>> rankingMap = new HashMap<>();

        // 初始化排名映射
        for (SurveyItemEntity item : allItems) {
            rankingMap.put(item.getItemContent(), new ArrayList<>());
        }

        // 收集排名数据
        for (SurveyResultDto result : surveyResults) {
            if (result.getQNumber().equals(qNumber)) {
                String[] sortedItems = result.getItemId().split("\\|");
                for (String sortedItem : sortedItems) {
                    if (sortedItem.contains(".")) {
                        String[] parts = sortedItem.split("\\.", 2);
                        if (parts.length == 2) {
                            try {
                                int rank = Integer.parseInt(parts[0]);
                                String itemContent = parts[1].trim();
                                if (rankingMap.containsKey(itemContent)) {
                                    rankingMap.get(itemContent).add(rank);
                                }
                            } catch (NumberFormatException e) {
                                // 忽略格式错误的数据
                            }
                        }
                    }
                }
            }
        }

        // 计算平均排名并生成统计结果
        return allItems.stream()
                .map(item -> {
                    SurveyResultCountDto countDto = new SurveyResultCountDto();
                    countDto.setQuestionNumber(qNumber);
                    countDto.setItemContent(item.getItemContent());

                    List<Integer> ranks = rankingMap.get(item.getItemContent());
                    if (!ranks.isEmpty()) {
                        double avgRank = ranks.stream().mapToInt(Integer::intValue).average().orElse(0.0);
                        countDto.setSelCount(ranks.size()); // 选择次数
                        countDto.setSelRate(String.format("%.2f", avgRank)); // 平均排名
                    } else {
                        countDto.setSelCount(0);
                        countDto.setSelRate("0.00");
                    }

                    return countDto;
                })
                .collect(Collectors.toList());
    }

    private static String formatPercentage(double rate) {
        // 先将比率乘以100并保留一位小数
        String formattedRate = String.format("%.1f", rate * 100);

        // 检查是否以".0"结尾，如果是，则移除".0"只保留整数部分
        if (formattedRate.endsWith(".0")) {
            formattedRate = formattedRate.substring(0, formattedRate.length() - 2);
        }
        // 添加百分号
        return formattedRate + "%";
    }

    /**
     *  生成问卷调查任务的结果统计报告
     * @param response
     * @param request
     * @param taskSurveyEntity
     * @return
     */
    @Override
    public String createSurveyStatReport(HttpServletResponse response, HttpServletRequest request,TaskSurveyEntity taskSurveyEntity) {
        String filePath = "";
        var taskSurveyStatDto = getTaskSurveyStat(taskSurveyEntity);
        Map<String, Object> reportMap = new HashMap<>();
        reportMap.put("surveyName",taskSurveyStatDto.getSurveyName());
        reportMap.put("totalCount",taskSurveyStatDto.getTotalCount());
        reportMap.put("doneCount",taskSurveyStatDto.getDoneCount());
        reportMap.put("unDoneCount",taskSurveyStatDto.getUnDoneCount());
        reportMap.put("doneRate",String.format("%.2f%%", taskSurveyStatDto.getDoneRate()));
        reportMap.put("unDoneRate",String.format("%.2f%%", taskSurveyStatDto.getUnDoneRate()));

        List<Map<String,Object>> surveyQuestionsMap =new ArrayList<>();
        for (SurveyResultStatDto surveyResultStatDto : taskSurveyStatDto.getSurveyResultStat()) {
            var surveyQuestionMap = new HashMap<String, Object>();
            surveyQuestionMap.put("qNumber", surveyResultStatDto.getQNumber());
            surveyQuestionMap.put("qContent", HtmlUtil.cleanHtmlTag(surveyResultStatDto.getQContent()));

            List<Map<String, Object>> surveyItemsMap = new ArrayList<>();
            for (SurveyResultCountDto surveyResultCountDto : surveyResultStatDto.getListResultCounts()) {
                var surveyItemMap = new HashMap<String, Object>();
                surveyItemMap.put("itemContent", surveyResultCountDto.getItemContent());
                surveyItemMap.put("selCount", surveyResultCountDto.getSelCount());
                surveyItemMap.put("selRate", surveyResultCountDto.getSelRate());
                surveyItemsMap.add(surveyItemMap);
            }
            // 将 surveyItemsMap 添加到 surveyQuestionMap 中，而不是 reportMap
            surveyQuestionMap.put("listResultCounts", surveyItemsMap);

            surveyQuestionsMap.add(surveyQuestionMap);
        }
        reportMap.put("surveyResultStat", surveyQuestionsMap);
        String templatePath = CommonHelper.getResourcesFilePath("static/template/survey_report.docx");
        String folderName = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        String fileName = taskSurveyStatDto.getSurveyName()+ ".docx";
        filePath = String.format("%s/%s",folderName, fileName);
        String fileAbPath=String.format( uploadPath +"survey/%s/%s", folderName,fileName);
        poiWordHelper.createSurveyReport(response, request, templatePath, fileAbPath, reportMap);
        return filePath;
    }

    /**
     * 根据测评记录ID获取背景图片信息
     * @param recordId 测评记录ID
     * @return 背景图片信息
     */
    @Override
    public Map<String, Object> getBackgroundByRecordId(Integer recordId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 根据recordId获取taskId
            Integer taskId = taskDao.getTaskIdByRecordId(recordId);
            if (taskId != null) {
                // 获取任务信息
                TaskDto task = taskDao.getById(taskId);
                if (task != null && task.getShowBackground() != null && task.getShowBackground() == 1
                    && task.getBackgroundUrl() != null && !task.getBackgroundUrl().isEmpty()) {
                    result.put("showBackground", true);
                    result.put("backgroundUrl", "/static/upload/task_background/" + task.getBackgroundUrl());
                } else {
                    result.put("showBackground", false);
                }
            } else {
                result.put("showBackground", false);
            }
        } catch (Exception e) {
            result.put("showBackground", false);
        }
        return result;
    }
}

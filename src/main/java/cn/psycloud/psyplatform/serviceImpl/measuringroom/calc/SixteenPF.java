package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 *  卡特尔16PF
 */
@Service
public class SixteenPF extends BaseCalc {
    @Autowired
    private CalcDao calcDao;

    @Override
    public void compute() {
        int _sex = "男".equals(sex) ? 1 : 0;
        var score = new int[24];
        var sc = new double[24];
        var listFactors = getFactorList();
        int k = 0;
        for (ScaleFactorDto factor : listFactors) {
            if (k > 16) break;
            //计算因子的问题答案积分和(原始分)
            score[k] = testScoreDao.computeFactorScore(recordId, factor.getQIds().split(",")).intValue();
            k++;
        }
        //根据原始分计算出标准分（16PF）
        var standardScoreMap = get16pfNorm(_sex);
        int m = 0;
        for (LinkedHashMap<String, Object> map : standardScoreMap) {
            if(m < 16){
                for(Map.Entry<String,Object> entry:map.entrySet()) {
                    if(entry.getKey().equals(String.valueOf(score[m]))){
                        sc[m] = Double.parseDouble(entry.getValue().toString());
                    }
                }
            }
            m++;
        }
        /*
        计算次级人格因素和特殊公式
        //A B C E F G H I L M N  O  Q1 Q2 Q3 Q4
        //0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15
        dim xx(3),yy(3)
        '适应与焦虑型：[(38+L*2+O*3+Q4*4)-(C*2+H*2+Q3*2)]/10
        '内向与外向型：[(A*2+E*3+F*4+H*5)-(Q2*2+11)]/10
        '感情用事与安祥机警型：[(77+C*2+E*2+F*2+N*2)-(A*4+I*6+M*2)]/10
        '怯懦与果断型：[(E*4+M*3+Q1*4+Q2*4)-(A*3+G*2)]/10

        xx(0)=((38+sc(8)*2+sc(11)*3+sc(15)*4)-(sc(2)*2+sc(6)*2+sc(14)*2))/10
        xx(1)=((sc(0)*2+sc(3)*3+sc(4)*4+sc(6)*5)-(sc(13)+11))/10
        xx(2)=((77+sc(2)*2+sc(3)*2+sc(4)*2+sc(10)*2)-(sc(0)*4+sc(7)*6+sc(9)*2))/10
        xx(3)=((sc(3)*4+sc(9)*3+sc(12)*4+sc(13)*4)-(sc(0)*3+sc(5)*2))/10
        '心理健康因素：C+F+(11-O)+(11-Q4) 12
        '专业而有成就着的人格因素：Q3*2+G*2+C*2+E+N+Q2+Q1 67
        '创造能力者强的人格因素：(11-A)*2+B*2+E+(11-F)*2+H+I*2+M+(11-N)+Q1+Q2*2 88
        '在新的环境中有成长能力的人格因素：B+G+Q3+(11-F) 27
        yy(0)=sc(2)+sc(4)+(11-sc(11))+(11-sc(15))
        yy(1)=sc(14)*2+sc(5)*2+sc(3)+sc(10)+sc(13)+sc(12)
        yy(2)=(11-sc(0))*2+sc(1)*2+sc(3)+(11-sc(4))*2+sc(6)+sc(7)*2+sc(9)+(11-sc(10))+sc(12)+sc(13)*2
        yy(3)=sc(1)+sc(5)+sc(14)+(11-sc(4))
        */
        sc[16] = ((38 + sc[8] * 2 + sc[11] * 3 + sc[15] * 4) - (sc[2] * 2 + sc[6] * 2 + sc[14] * 2)) / 10.0;
        sc[17] = ((sc[0] * 2 + sc[3] * 3 + sc[4] * 4 + sc[6] * 5) - (sc[13] + 11)) / 10.0;
        sc[18] = ((77 + sc[2] * 2 + sc[3] * 2 + sc[4] * 2 + sc[10] * 2) - (sc[0] * 4 + sc[7] * 6 + sc[9] * 2)) / 10.0;
        sc[19] = ((sc[3] * 4 + sc[9] * 3 + sc[12] * 4 + sc[13] * 4) - (sc[0] * 3 + sc[5] * 2)) / 10.0;

        sc[20] = sc[2] + sc[4] + (11 - sc[11]) + (11 - sc[15]);
        sc[21] = sc[14] * 2 + sc[5] * 2 + sc[3] + sc[10] + sc[13] + sc[12];
        sc[22] = (11 - sc[0]) * 2 + sc[1] * 2 + sc[3] + (11 - sc[4]) * 2 + sc[6] + sc[7] * 2 + sc[9] + (11 - sc[10]) + sc[12] + sc[13] * 2;
        sc[23] = sc[1] + sc[5] + sc[14] + (11 - sc[4]);

        //保存结果
        int j = 0;
        for (ScaleFactorDto _factor : listFactors) {
            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(_factor.getId());
            testScoreEntity.setOriginalScore(BigDecimal.valueOf(score[j]));
            testScoreEntity.setScore(BigDecimal.valueOf(sc[j]));
            testScoreEntity.setIsAbnormal(0);
            testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
            saveScore(testScoreEntity);
            j++;
        }
        //结果解释
        var exId = new int[24];
        for (int i = 0; i <= 19; i++) {
            if (sc[i] < 4) exId[i] = i * 3 + 1;
            else if (sc[i] >= 4 && sc[i] <= 7) exId[i] = i * 3 + 2;
            else exId[i] = i * 3 + 3;
        }
        exId[20] = sc[20] < 12 ? 61 : 62;
        exId[21] = sc[21] < 67 ? 63 : 64;
        exId[22] = sc[22] < 88 ? 65 : 66;
        exId[23] = sc[23] < 27 ? 67 : 68;
        String ex = "";
        for (int id : exId) {
            ex += id + ",";
        }
        var explainMap = get16pfExplain(StrUtil.removeSuffix(ex, ","));
        var responseText = new StringBuilder();
        int n = 0;
        for (Map<String, Object> map : explainMap) {
            if (n < exId.length) {
                var _type = map.get("type");
                String substring = _type.toString().substring(0, _type.toString().length() - 1).toUpperCase();
                responseText.append(String.format("<dt>%s（%s）</dt><dd>【结果解释】%s<br/>%s</dd>",
                        map.get("type_name"),
                        substring,
                        map.get("interpretation"),
                        map.get("occupation") == "" ? "" : String.format("【适合职业】%s",map.get("occupation") == null ? "无" : map.get("occupation") )
                ));
                var testRecordExplainEntity = new TestRecordExplainEntity();
                testRecordExplainEntity.setRecordId(recordId);
                testRecordExplainEntity.setFactorId(Integer.parseInt(map.get("factor_id").toString()));
                testRecordExplainEntity.setInterpretation(String.format("[%s{%s}]%s%s",
                        map.get("type_name"),
                        substring,
                        map.get("interpretation"),
                        map.get("occupation") == "" ? "" : String.format("【适合职业】%s",map.get("occupation") == null ? "无" : map.get("occupation") )
                ));
                testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
            }
            n++;
        }
        interpretation = responseText.toString();
    }

    private List<LinkedHashMap<String, Object>> get16pfNorm(Integer sex) {
        return calcDao.get16pfNorm(sex);
    }

    private List<Map<String, Object>> get16pfExplain(String ids) {
        return calcDao.get16pfExplain(ids);
    }
}
package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao;
import cn.psycloud.psyplatform.dao.measuringroom.TaskDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao;
import cn.psycloud.psyplatform.dto.anteroom.ImportUserDto;
import cn.psycloud.psyplatform.dto.measuringroom.ImportResultDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultVO;
import cn.psycloud.psyplatform.dto.survey.SurveyQuestionDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestResultEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyResultEntity;
import cn.psycloud.psyplatform.service.measuringroom.ImportTestResultService;
import cn.psycloud.psyplatform.service.measuringroom.TestResultService;
import cn.psycloud.psyplatform.service.survey.SurveyRecordService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;

@Slf4j
@Service
public class ImportTestResultServiceImpl implements ImportTestResultService {
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private TestResultService testResultService;
    @Autowired
    private ScaleQuestionDao scaleQuestionDao;
    @Autowired
    private TaskDao taskDao;
    @Autowired
    private SurveyQuestionDao surveyQuestionDao;
    @Autowired
    private SurveyRecordService surveyRecordService;
    @Autowired
    private TestScoreServiceImpl testScoreService;
    @Autowired
    private UserDao userDao;

    /**
     * 根据任务id和用户id获取记录id
     * @param taskId 任务id
     * @param userId 用户id
     * @param scaleId 量表id
     * @return 记录id
     */
    private int getRecordIdByTaskIdAndUserId(Integer taskId, Integer userId, Integer scaleId) {
        HashMap<String, Integer> map = new HashMap<>();
        map.put("taskId", taskId);
        map.put("userId", userId);
        map.put("scaleId", scaleId);
        return testRecordDao.getRecordIdByTaskIdAndUserId(map);
    }

    /**
     *  导入测试结果
     * @param taskId 任务id
     * @param scaleId 量表id
     * @param listTestResults 测试结果集合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ImportResultDto importTestResultFromExcel(Integer taskId, Integer scaleId, List<Object> listTestResults){
        List<ImportUserDto> users = new ArrayList<>();
        if(listTestResults == null || listTestResults.isEmpty()){
            return new ImportResultDto(users);
        }
        var successCount = 0;
        for (Object listTestResult : listTestResults) {
            try {
                var importUserDto = importTestResult(taskId, scaleId, listTestResult);
                users.add(importUserDto);
                if(importUserDto.getState() == 1){
                    successCount++;
                    log.error("已成功导入测评记录数量{}", successCount);
                }
            } catch (Exception e) {
                log.error("导入测试结果失败", e);
                var errorDto = new ImportUserDto();
                errorDto.setState(0);
                errorDto.setMsg("导入失败：" + e.getMessage());
                users.add(errorDto);
            }
        }
        
        return new ImportResultDto(users);
    }

    /**
     *  导入测试结果
     *  @param taskId 任务id
     * @param scaleId 量表id
     * @param testResult 测试结果
     */
    private ImportUserDto importTestResult(Integer taskId, Integer scaleId, Object testResult) {
        var importUserDto = new ImportUserDto();
        LinkedHashMap<String,Object> rowData = (LinkedHashMap<String,Object>) testResult;
        // 获取用户名（第一列）
        String loginName = rowData.get(0).toString();
        var userDto = userDao.getUserByName(loginName);
        if(userDto != null){
            var map = new HashMap<String,Integer>();
            map.put("taskId",taskId);
            map.put("scaleId",scaleId);
            map.put("userId",userDto.getUserId());
            TestRecordEntity testRecordEntity = taskDao.isScaleDone(map);
            if(testRecordEntity.getId() > 0 && testRecordEntity.getState() == 1){
                importUserDto.setLoginName(loginName);
                importUserDto.setRealName(userDto.getRealName());
                importUserDto.setState(0);
                importUserDto.setMsg("已存在该用户的测评记录！");
            }
            else {
                var testResultVO = new TestResultVO();
                testResultVO.setScaleId(scaleId);
                var recordId = getRecordIdByTaskIdAndUserId(taskId, userDto.getUserId(), scaleId);
                testResultVO.setRecordId(recordId);
                var testResults = new ArrayList<TestResultEntity>();

                // 获取量表题目集合
                List<ScaleQuestionEntity> listScaleQuestions = scaleQuestionDao.getListByScaleId(scaleId);

                int startIndex = 3;
                for(int i = 0; i < listScaleQuestions.size(); i++){
                    var testResultEntity = new TestResultEntity();
                    ScaleQuestionEntity question = listScaleQuestions.get(i);
                    testResultEntity.setQNo(question.getQNumber());
                    testResultEntity.setQType(question.getQType());
                    testResultEntity.setANo(rowData.get(startIndex).toString());
                    testResultEntity.setRecordId(recordId);
                    testResults.add(testResultEntity);
                    startIndex++;
                }
                testResultVO.setListResults(testResults);
                // 保存量表测试结果
                testResultService.saveResult(testResultVO);
                testScoreService.calc(recordId,scaleId);

                String testEndTime = rowData.get(1).toString();
                String duration = rowData.get(2).toString().replace("秒", "");

                // 计算作答开始时间
                Date endTime = DateUtil.parse(testEndTime, "yyyy/M/d H:mm:ss");
                int durationSeconds = Integer.parseInt(duration);
                Date startTime = DateUtil.offsetSecond(endTime, -durationSeconds);
                var testTimeMap = new HashMap<String,Object>();
                testTimeMap.put("startTime", startTime);
                testTimeMap.put("endTime", endTime);
                testTimeMap.put("recordId", recordId);
                testRecordDao.updateTestTime(testTimeMap);

                //获取测评任务信息
                var taskDto = taskDao.getById(taskId);
                if(taskDto.getIsSurvey() == 1){ //启用调查问卷
                    var taskSurveyEnity = new TaskSurveyRecordEntity();
                    taskSurveyEnity.setUserId(userDto.getUserId());
                    taskSurveyEnity.setSurveyId(taskDto.getSurveyId());
                    taskSurveyEnity.setTaskId(taskId);

                    // 调查问卷题目集合
                    List<SurveyQuestionDto> listSurveyQuestions = surveyQuestionDao.getListBySurveyId(taskDto.getSurveyId());
                    List<SurveyResultEntity> surveyResultEntities = new ArrayList<>();
                    // 遍历Excel数据读取调查问卷题目
                    for (var i = 0; i < listSurveyQuestions.size(); i++) {
                        var surveyResultEntity = new SurveyResultEntity();
                        var qId = listSurveyQuestions.get(i).getId();
                        surveyResultEntity.setQId(qId);
                        int qIndex = startIndex + i;
                        surveyResultEntity.setItemId(rowData.get(qIndex) == null ? "" : rowData.get(qIndex).toString());
                        surveyResultEntities.add(surveyResultEntity);
                    }
                    taskSurveyEnity.setListAnswers(surveyResultEntities);
                    surveyRecordService.addRecord(taskSurveyEnity);

                    var surveyRecordId = surveyRecordService.getSurveyRecordId(taskId, userDto.getUserId(),taskDto.getSurveyId());
                    surveyRecordService.updateSurveyRecordDate(surveyRecordId, endTime);
                    importUserDto.setLoginName(loginName);
                    importUserDto.setRealName(userDto.getRealName());
                    importUserDto.setState(1);
                    importUserDto.setMsg("导入成功！");
                }
            }
        }
        else {
            importUserDto.setLoginName(loginName);
            importUserDto.setRealName("");
            importUserDto.setState(0);
            importUserDto.setMsg("不存在该用户！");
        }
        return importUserDto;
    }
}

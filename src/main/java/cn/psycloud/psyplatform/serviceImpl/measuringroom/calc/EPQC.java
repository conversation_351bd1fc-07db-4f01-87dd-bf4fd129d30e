package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 艾森克人格问卷（少年式）
 */
@Service
public class EPQC extends BaseCalc{
    @Autowired
    private CalcDao calcDao;

    @Override
    public void compute() {
        computeCommonFactor(true);  //计算因子分
        //结果解释
        int[] exId = new int[4];
        if ("男".equals(sex)) {
            // 维度E
            if (listOriginalScores.get(0).compareTo(BigDecimal.valueOf( 13.43 - 3.15)) < 0) {
                exId[0] = 1;
            }
            else if (listOriginalScores.get(0).compareTo(BigDecimal.valueOf(13.43 + 3.15)) <= 0) {
                exId[0] = 2;
            }
            else {
                exId[0] = 3;
            }
            //维度N
            if (listOriginalScores.get(1).compareTo(BigDecimal.valueOf(6.31 - 3.86)) < 0) {
                exId[1] = 4;
            }
            else if (listOriginalScores.get(1).compareTo(BigDecimal.valueOf(6.31 + 3.86)) <= 0) {
                exId[1] = 5;
            }
            else {
                exId[1] = 6;
            }
            //维度P
            if (listOriginalScores.get(2).compareTo(BigDecimal.valueOf(3.99 - 2.77)) < 0) {
                exId[2] = 7;
            }
            else if (listOriginalScores.get(2).compareTo(BigDecimal.valueOf(3.99 + 2.77)) <= 0) {
                exId[2] = 8;
            }
            else {
                exId[2] = 9;
            }
            //维度L
            if (listOriginalScores.get(3).compareTo(BigDecimal.valueOf(11.66 - 3.83)) < 0) {
                exId[3] = 10;
            }
            else if (listOriginalScores.get(3).compareTo(BigDecimal.valueOf(11.66 + 3.83)) <= 0) {
                exId[3] = 11;
            }
            else {
                exId[3] = 12;
            }
        }
        else
        {
            // 维度E
            if (listOriginalScores.get(0).compareTo(BigDecimal.valueOf(12.64 - 3.62)) < 0) {
                exId[0] = 1;
            }
            else if (listOriginalScores.get(0).compareTo(BigDecimal.valueOf(12.64 + 3.62)) <= 0) {
                exId[0] = 2;
            }
            else {
                exId[0] = 3;
            }
            //维度N
            if (listOriginalScores.get(1).compareTo(BigDecimal.valueOf(7.04 - 3.56)) < 0) {
                exId[1] = 4;
            }
            else if (listOriginalScores.get(1).compareTo(BigDecimal.valueOf(7.04 + 3.56)) <= 0) {
                exId[1] = 5;
            }
            else {
                exId[1] = 6;
            }
            //维度P
            if (listOriginalScores.get(2).compareTo(BigDecimal.valueOf(3.35 - 2.28)) < 0) {
                exId[2] = 7;
            }
            else if (listOriginalScores.get(2).compareTo(BigDecimal.valueOf(3.35 + 2.28)) <= 0) {
                exId[2] = 8;
            }
            else {
                exId[2] = 9;
            }
            //维度L
            if (listOriginalScores.get(3).compareTo(BigDecimal.valueOf(11.31 - 3.56)) < 0) {
                exId[3] = 10;
            }
            else if (listOriginalScores.get(3).compareTo(BigDecimal.valueOf(11.31 + 3.56)) <= 0) {
                exId[3] = 11;
            }
            else {
                exId[3] = 12;
            }
        }
        String ex = "";
        for (int j : exId) {
            ex += j + ",";
        }

        var explainMap = getEPQCExplain(StrUtil.removeSuffix(ex,","));
        var responseText = new StringBuilder();
        int i =0;
        for(LinkedHashMap<String, Object> map: explainMap){
            if(i < exId.length) {
                String level = map.get("grade").toString();
                responseText.append(String.format("<dt>%s（%s）</dt><dd>【解释】%s <br />【建议】%s</dd>",
                        map.get("dimension"),
                        level.substring(0,1).toUpperCase(),
                        map.get("interpretation"),
                        map.get("advice")
                        ));
                var testRecordExplainEntity = new TestRecordExplainEntity();
                testRecordExplainEntity.setRecordId(recordId);
                testRecordExplainEntity.setFactorId(Integer.parseInt(map.get("factor_id").toString()));
                testRecordExplainEntity.setInterpretation(String.format("\"[%s（%s）]%s \\r【建议】%s",
                        map.get("dimension"),
                        level.substring(0,1).toUpperCase(),
                        map.get("interpretation"),
                        map.get("advice")
                        ));
                testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
            }
            i++;
        }
        interpretation = responseText.toString();
    }

    private List<LinkedHashMap<String, Object>> getEPQCExplain(String ids) {
        return calcDao.getEPQCExplain(ids);
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class APMAndNineHouse extends BaseCalc{
    @Override
    public void compute() {
        computeCommonFactor(true);
        var responseText = new StringBuilder();
        var testRecordExplainEntity = new TestRecordExplainEntity();
        //结果解释
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        var listTestScores = testScoreDao.getTestScoreList(testScoreDto);
        var listFactorExplains = scaleFactorExplainDao.getListById(535);
        var listTestScores2 = listTestScores.stream().filter(s->s.getFactor().getId() == 535).collect(Collectors.toList());
        for (int i = 0; i < listTestScores2.size(); i++) {
            String exp = "";
            var testScore = listTestScores.get(i);
            var score = listTestScores.get(i).getScore();
            var explains = listFactorExplains.stream().filter(s-> Objects.equals(s.getFactorId(), testScore.getFactorId())).collect(Collectors.toList());
            for(ScaleFactorExplainEntity explainEntity: explains) {
                BigDecimal startValue = explainEntity.getStartValue();
                BigDecimal endValue = explainEntity.getEndValue();
                if(score.compareTo(startValue) > 0 && score.compareTo(endValue) < 0){
                    exp = explainEntity.getInterpretation();
                    break;
                }
            }
            testRecordExplainEntity.setRecordId(recordId);
            testRecordExplainEntity.setFactorId(listTestScores.get(i).getFactor().getId());
            testRecordExplainEntity.setInterpretation(String.format("[%s]得分%.2f。%s",listTestScores.get(i).getFactor().getFactorName(),listTestScores.get(i).getOriginalScore(),exp));
            testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
            responseText.append(testRecordExplainEntity.getInterpretation()).append("<br/>");
        }

        listTestScores.remove(0);
        listTestScores = listTestScores.stream().sorted(Comparator.comparing(TestScoreDto::getOriginalScore).reversed()).collect(Collectors.toList());

        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(listTestScores.get(0).getFactor().getId());

        var scaleFactorExplain = new ScaleFactorExplainEntity();
        scaleFactorExplain = scaleFactorExplainDao.getListById(listTestScores.get(0).getFactor().getId()).get(0);
        testRecordExplainEntity.setInterpretation(String.format("您属于%s<br/>%s",listTestScores.get(0).getFactor().getFactorName(),scaleFactorExplain.getInterpretation()));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
        responseText.append(testRecordExplainEntity.getInterpretation());
        interpretation = responseText.toString();
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 *  九型人格
 */
@Service
public class NineHouse extends BaseCalc{
    @Autowired
    private TestResultDao testResultDao;
    @Override
    public void compute() {
        var listResults = testResultDao.getNineHouseList(recordId);
        var listFactors = getFactorList().stream().sorted(Comparator.comparing(ScaleFactorDto::getFactorNo)).collect(Collectors.toList());
        var score = new int[9][2];
        for(TestResultDto result: listResults) {
            if (result.getFactorId() == 457) {
                score[0][0] = score[0][0] + 1;
                score[0][1] = 0;
            }
            if (result.getFactorId() == 458) {
                score[1][0] = score[1][0] + 1;
                score[1][1] = 1;
            }
            if (result.getFactorId() == 459) {
                score[2][0] = score[2][0] + 1;
                score[2][1] = 2;
            }
            if (result.getFactorId() == 460) {
                score[3][0] = score[3][0] + 1;
                score[3][1] = 3;
            }
            if (result.getFactorId() == 461) {
                score[4][0] = score[4][0] + 1;
                score[4][1] = 4;
            }
            if (result.getFactorId() == 462) {
                score[5][0] = score[5][0] + 1;
                score[5][1] = 5;
            }
            if (result.getFactorId() == 463) {
                score[6][0] = score[6][0] + 1;
                score[6][1] = 6;
            }
            if (result.getFactorId() == 464) {
                score[7][0] = score[7][0] + 1;
                score[7][1] = 7;
            }
            if (result.getFactorId() == 465) {
                score[8][0] = score[8][0] + 1;
                score[8][1] = 8;
            }
        }
        var m = 0;
        for(ScaleFactorDto scaleFactorDto: listFactors) {
            var testScoreEntity = new TestScoreEntity();
            testScoreEntity.setRecordId(recordId);
            testScoreEntity.setFactorId(scaleFactorDto.getId());
            testScoreEntity.setOriginalScore(BigDecimal.valueOf(score[m][0]));
            testScoreEntity.setScore(BigDecimal.valueOf(score[m][0]));
            testScoreEntity.setIsAbnormal(0);
            testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
            saveScore(testScoreEntity);
            m++;
        }
        //结果解释
        for (var i = 0; i < 9; i++) {
            for (int j = 8; j > i; j--) {
                if (score[j][0] > score[j - 1][0]) {
                    for (int n = 0; n < 2; n++) {
                        int t = score[j][n];
                        score[j][n] = score[j - 1][n];
                        score[j - 1][n] = t;
                    }
                }
            }
        }
        var listExplains = scaleFactorExplainDao.getListById(listFactors.get(score[0][1]).getId());
        String responseText = String.format("您的九型人格属于：%s。<br>%s", listFactors.get(score[0][1]).getFactorName(), listExplains.get(0).getInterpretation());
        var map = new HashMap<String,Integer>();
        map.put("recordId",recordId);
        map.put("factorId",listFactors.get(score[0][1]).getId());
        testRecordDao.saveNineHouseRecord(map);
        interpretation = responseText;
        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(String.format("您的九型人格属于：%s。<br/>%s",
                listFactors.get(score[0][1]).getFactorName(),
                listExplains.get(0).getInterpretation()
        ));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

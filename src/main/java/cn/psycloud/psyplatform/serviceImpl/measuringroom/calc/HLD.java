package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.stream.Collectors;

/**
 *  霍兰德职业
 */
@Service
public class HLD extends BaseCalc{
    @Override
    public void compute() {
        //计算因子分
        var listFactors = getFactorList();
        if (listFactors.size() == 0) return;
        computeCommonFactor(true);
        //获取因子分
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        var listFactorScores = testScoreDao.getTestScoreList(testScoreDto);
        //对因子得分进行排序
        var list = listFactorScores.stream().sorted(Comparator.comparing(TestScoreDto:: getOriginalScore)).collect(Collectors.toList());
        var responseText = new StringBuilder();
        responseText.append(String.format("被试者得分前三的特征为：%s(%.2f分)、%s(%.2f分)、%s(%.2f分)。因此，被试者属于%s型",
                list.get(list.size() - 1).getFactor().getFactorName(),
                list.get(list.size() - 1).getOriginalScore(),
                list.get(list.size() - 2).getFactor().getFactorName(),
                list.get(list.size() - 2).getOriginalScore(),
                list.get(list.size() - 3).getFactor().getFactorName(),
                list.get(list.size() - 3).getOriginalScore(),
                list.get(list.size() - 1).getFactor().getFactorEn() + list.get(list.size() - 2).getFactor().getFactorEn() + list.get(list.size() - 3).getFactor().getFactorEn()
        ));
        interpretation = responseText.toString();
        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(interpretation);
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

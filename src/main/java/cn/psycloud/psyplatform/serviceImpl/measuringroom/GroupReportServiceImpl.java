package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.anteroom.StructsDao;
import cn.psycloud.psyplatform.dao.measuringroom.GroupReportDao;
import cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto;
import cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto;
import cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto.*;
import cn.psycloud.psyplatform.service.measuringroom.GroupReportService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 团体测评报告业务服务实现类
 */
@Service
public class GroupReportServiceImpl implements GroupReportService {
    
    @Autowired
    private GroupReportDao groupReportDao;
    
    @Autowired
    private StructsDao structsDao;
    
    @Override
    public GroupReportResponseDto generateGroupReport(GroupReportRequestDto request) {
        // 参数验证
        if (request == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        if (request.getScaleId() == null) {
            throw new IllegalArgumentException("量表ID不能为空");
        }
        
        GroupReportResponseDto response = new GroupReportResponseDto();

        request.setChildStructs(PermissonHelper.getChildStructIds(request.getStructId()));
        
        // 1. 获取测评工具介绍
        ScaleInfoDto scaleInfo = groupReportDao.getScaleInfo(request.getScaleId());
        response.setScaleInfo(scaleInfo);
        
        // 2. 获取测评总体情况
        OverallSituationDto overallSituation = generateOverallSituation(request);
        response.setOverallSituation(overallSituation);
        
        // 3. 获取测评情况分析
        AnalysisDto analysis = generateAnalysis(request);
        response.setAnalysis(analysis);
        
        return response;
    }

    /**
     * 生成测评总体情况
     */
    private OverallSituationDto generateOverallSituation(GroupReportRequestDto request) {
        OverallSituationDto overallSituation = groupReportDao.getOverallSituation(request);
        
        if (overallSituation == null) {
            overallSituation = new OverallSituationDto();
            overallSituation.setTotalCount(0);
            overallSituation.setCompletedCount(0);
            overallSituation.setUncompletedCount(0);
        }
        
        // 按部门统计完成情况
        List<CompletionStatDto> departmentStats = groupReportDao.getDepartmentCompletionStats(request);
        overallSituation.setDepartmentStats(departmentStats);
        
        // 按性别统计完成情况
        List<CompletionStatDto> genderStats = groupReportDao.getGenderCompletionStats(request);
        overallSituation.setGenderStats(genderStats);
        
        // 按年龄段统计完成情况
        List<CompletionStatDto> ageStats = groupReportDao.getAgeCompletionStats(request);
        overallSituation.setAgeStats(ageStats);
        
        return overallSituation;
    }
    
    /**
     * 生成测评情况分析
     */
    private AnalysisDto generateAnalysis(GroupReportRequestDto request) {
        AnalysisDto analysis = new AnalysisDto();
        
        // 按组织统计预警等级分布
        List<OrganizationWarningStatDto> organizationWarningStats = groupReportDao.getOrganizationWarningStats(request);
        analysis.setOrganizationWarningStats(organizationWarningStats);
        
        // 按性别统计预警等级分布
        List<GenderWarningStatDto> genderWarningStats = groupReportDao.getGenderWarningStats(request);
        analysis.setGenderWarningStats(genderWarningStats);
        
        // 按年龄段统计预警等级分布
        List<AgeWarningStatDto> ageWarningStats = groupReportDao.getAgeWarningStats(request);
        analysis.setAgeWarningStats(ageWarningStats);
        
        // 按因子统计预警等级分布
        List<FactorWarningStatDto> factorWarningStats = groupReportDao.getFactorWarningStats(request);
        analysis.setFactorWarningStats(factorWarningStats);
        
        // 各组织红码橙码统计
        List<CriticalWarningStatDto> criticalWarningStats = groupReportDao.getCriticalWarningStats(request);
        analysis.setCriticalWarningStats(criticalWarningStats);
        
        return analysis;
    }
} 
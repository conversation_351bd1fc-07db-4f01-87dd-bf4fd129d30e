package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleAnswerDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.DuallistData;
import cn.psycloud.psyplatform.dto.measuringroom.ImportScaleQDto;
import cn.psycloud.psyplatform.dto.measuringroom.NineHouseStatDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleQuestionDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleQService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class ScaleQServiceImpl implements ScaleQService {
    @Autowired
    private ScaleQuestionDao scaleQuestionDao;
    @Autowired
    private ScaleAnswerDao scaleAnswerDao;

    /**
     *  查询量表条目集合：分页
     * @param dto 查询条件
     * @return 题目集合
     */
    public BSDatatableRes<ScaleQuestionEntity> getListByScaleId(ScaleQuestionDto dto) {
        var dtRes = new BSDatatableRes<ScaleQuestionEntity>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listQuestions = scaleQuestionDao.getListByScaleId(dto.getScaleId());
        PageInfo<ScaleQuestionEntity> scaleQuestionDto = new PageInfo<>(listQuestions);
        dtRes.setData(scaleQuestionDto.getList());
        dtRes.setRecordsTotal((int)scaleQuestionDto.getTotal());
        dtRes.setRecordsFiltered((int)scaleQuestionDto.getTotal());
        return dtRes;
    }

    /**
     *  查询量表条目集合：DualListBox形式
     * @param scaleId 量表Id
     * @return 题目集合
     */
    @Override
    public List<DuallistData> getQListForDualList(Integer scaleId) {
        var listQuestions = scaleQuestionDao.getListByScaleIdForDuallist(scaleId);
        var lsNode = new ArrayList<DuallistData>();
        for (ScaleQuestionEntity question : listQuestions)
        {
            var duallistData = new DuallistData();
            duallistData.setId(question.getQNumber());
            duallistData.setName("第" + question.getQNumber().toString() + "题");
            lsNode.add(duallistData);
        }
        return lsNode;
    }

    private void deleteByQid(Integer qId) {
        //根据题目id查询量表id和题目序号
        var map = scaleQuestionDao.getById(qId);
        //根据题目id删除题目
        scaleQuestionDao.delete(qId);
        //根据题目id删除答案
        scaleAnswerDao.deleteByQid(qId);
        //更新删除题目后的题目序号
        scaleQuestionDao.updateQno(map);
    }

    /**
     *  删除条目
     * @param qId 题目id
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean delete(Integer qId) {
        boolean isSuccess = false;
        try{
            deleteByQid(qId);
            isSuccess = true;
        }
        catch (Exception e) {
            log.error("删除条目发生错误："+e.getMessage());
        }
        return isSuccess;
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        try {
            var arrayIds = ids.split(",");
            for (String arrayId : arrayIds) {
                var id = Integer.parseInt(arrayId);
                 deleteByQid(id) ;
            }
            isSuccess = true;
        }
        catch (Exception e) {
            log.error("删除条目发生错误："+e.getMessage());
        }
        return isSuccess;
    }

    /**
     *  添加题目
     * @param entity 题目实体对象
     * @return 题目id
     */
    @Override
    public int addQuestion(ScaleQuestionEntity entity) {
        entity.setId(0);
        var questionCount = scaleQuestionDao.getQuestionCount(entity.getScaleId());
        entity.setQNumber(questionCount+1);
        return scaleQuestionDao.add(entity);
    }

    /**
     *  修改题目
     * @param entity 题目实体对象
     * @return 影响行数
     */
    @Override
    public int updateQuestion(ScaleQuestionEntity entity) {
        return scaleQuestionDao.update(entity);
    }

    /**
     *  excel导入题目和答案
     * @param list excel表格数据
     * @param scaleId 量表id
     */
    @Override
    public void importQuestion(List<ImportScaleQDto> list, Integer scaleId) {
        for(ImportScaleQDto dto: list) {
            var question = new ScaleQuestionEntity();
            question.setId(0);
            question.setQNumber(Integer.parseInt(dto.getQNo()));
            question.setQContent(dto.getQContent());
            var qType = 1;
            switch (dto.getQType()){
                case "单选":
                    qType = 1;
                    break;
                case "多选":
                    qType = 2;
                    break;
                case "填空":
                    qType = 3;
                    break;
                case "评分单选":
                    qType = 4;
                    break;
                case "迫选":
                    qType = 5;
                    break;
            }
            question.setQType(qType);
            question.setScaleId(scaleId);
            scaleQuestionDao.add(question);
            var questionId = question.getId();
            //导入答案
            var answers = dto.getAnswers().split("\\|");
            var scores = dto.getScores().split("\\|");
            for (int i=0; i < answers.length; i++) {
                var answer = new ScaleAnswerEntity();
                answer.setAContent(answers[i]);
                answer.setAScore((new BigDecimal(scores[i])));
                answer.setQId(questionId);
                answer.setANo(i + 1);
                scaleAnswerDao.insert(answer);
            }
        }
    }

    /**
     *  题目排序
     * @param qId 题目id
     * @param flag 排序标识
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean sort(Integer qId,String flag) {
        boolean isSuccess = false;
        try{
            var map= scaleQuestionDao.getById(qId);
            var scaleId = map.get("scale_id");
            var qNo = map.get("q_number");
            var sortMap = new HashMap<String, Integer>();
            sortMap.put("scaleId",scaleId);
            sortMap.put("qNo",qNo);
            //上移
            if ("up".equals(flag)) {
                if(qNo == 1) return  false;
                sortMap.put("id",qId);
                scaleQuestionDao.moveUpOther(sortMap); //移动其它
                scaleQuestionDao.moveUpSelf(sortMap);
                isSuccess = true;
            }
            //下移
            if ("down".equals(flag)) {
                var maxSort = scaleQuestionDao.getQuestionCount(scaleId);
                if(maxSort == 0) return false;
                if(maxSort == qNo) return  false;
                sortMap.put("id",qId);
                scaleQuestionDao.moveDownOther(sortMap);
                scaleQuestionDao.moveDownSelf(sortMap);
                isSuccess = true;
            }
        }
        catch (Exception e) {
            log.error("题目排序出现错误：" + e.getMessage());
        }
        return isSuccess;
    }
}

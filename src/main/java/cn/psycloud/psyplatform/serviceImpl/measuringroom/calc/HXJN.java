package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.stereotype.Service;

@Service
public class HXJN extends BaseCalc{
    @Override
    public void compute() {
        computeCommonFactor(true);
        //获取因子分
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        var listFactorScores =  testScoreDao.getTestScoreList(testScoreDto);
        var score = new int[10][2];
        var factor = new String[listFactorScores.size()];
        int n = 0;
        for (TestScoreDto factorScore: listFactorScores) {
            score[n][0] = factorScore.getOriginalScore().intValue();
            score[n][1] = n;
            factor[n] = factorScore.getFactor().getFactorName();
            n++;
        }
        //排序
        for (var i = 0; i < 10; i++) {
            for (int j = 9; j > i; j--) {
                if (score[j][0] > score[j - 1][0]) {
                    for (int k = 0; k < 2; k++) {
                        int t = score[j][k];
                        score[j][k] = score[j - 1][k];
                        score[j - 1][k] = t;
                    }
                }
            }
        }
        var responseText = new StringBuilder("<div class='table-responsive'><table class='table table-striped'><thead><tr><th>核心技能</th><th>总分</th><th>排序</th></tr></thead><tbody>");
        for (var i = 0; i < 10; i++) {
            responseText.append(String.format("<tr><td>%s</td><td>%d</td><td>%d</td></tr>",
                    factor[score[i][1]],
                    score[i][0],
                    i + 1
            ));
        }
        responseText.append("</tbody></table></div>");
        interpretation = responseText.toString();
        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        testRecordExplainEntity.setInterpretation(String.format("被试者的核心技能排序依次是：%s、%s、%s、%s、%s、%s、%s、%s、%s、%s",
                factor[score[0][1]],
                factor[score[1][1]],
                factor[score[2][1]],
                factor[score[3][1]],
                factor[score[4][1]],
                factor[score[5][1]],
                factor[score[6][1]],
                factor[score[7][1]],
                factor[score[8][1]],
                factor[score[9][1]]
        ));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

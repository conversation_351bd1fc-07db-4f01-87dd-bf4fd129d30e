package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  贝康育婴师问卷
 */
@Service
public class BKYYS extends BaseCalc{
    @Override
    public void compute() {
        computeCommonFactor(true);
        interpretation = getExplain();
    }
    private String getExplain() {
        var responseText = new StringBuilder();
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        var listTestScores = testScoreDao.getTestScoreList(testScoreDto);
        var listFactorExplains = scaleFactorExplainDao.getListByScaleId(scaleId);
        var testRecordExplainEntity = new TestRecordExplainEntity();
        String str1 = "", str2 = "", str3 = "", ne_explain="";
        for(TestScoreDto testScore: listTestScores) {
            String exp ="";
            var score = testScore.getScore();
            var explains = listFactorExplains.stream().filter(r-> Objects.equals(r.getFactorId(), testScore.getFactor().getId())).collect(Collectors.toList());
            for(ScaleFactorExplainEntity _explain: explains) {
                BigDecimal startValue = _explain.getStartValue();
                BigDecimal endValue = _explain.getEndValue();
                if (score.compareTo(startValue) >=0 && score.compareTo(endValue) <=0) {
                    exp = _explain.getInterpretation();
                    break;
                }
            }
            testRecordExplainEntity.setRecordId(recordId);
            testRecordExplainEntity.setFactorId(testScore.getFactor().getId());

            boolean isAbnormal = testScore.getIsAbnormal() > 0;
            testRecordExplainEntity.setInterpretation(String.format("因子[%s]得分%.2f%s。%s",
                    testScore.getFactor().getFactorName(),
                    testScore.getOriginalScore().compareTo(score) ==0 ? testScore.getOriginalScore() :score,
                    isAbnormal?"（异常）":"",
                    exp
                    ));
            testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
            var factorNo = testScore.getFactor().getFactorNo();
            if (factorNo == 1) {
                ne_explain = exp;
            }
            //各个维度得分及结果解释
            if (factorNo != 4) {
                str2 +=String.format("<tr><td class=\"text-left text-nowrap\">%s</td><td class=\"text-left\">%.2f</td><td class=\"text-left\">%s</td></tr>",
                        testScore.getFactor().getFactorName(),
                        score,
                        exp
                );
            }
        }
        str1 += ne_explain;
        responseText.append(String.format("<table class=\"table table-striped\"><thead><tr><th class=\"text-left\">维度</th><th class=\"text-left\">得分</th><th class=\"text-left text-nowrap\">结果及建议</th></tr></thead><tbody>%s</tbody></table>", str2));
        var e_score = listOriginalScores.get(1);
        var n_score = listOriginalScores.get(2);
        if (e_score.compareTo(BigDecimal.valueOf(7)) < 0 && n_score.compareTo(BigDecimal.valueOf(4)) < 0) {
            str3 += "<b>你的气质类型为：</b>安静稳定型，倾向内向性格，情绪稳定。<br/><br/>" +
                         "<b>安静稳定型的神经特点：</b>感受性低；耐受性高；不随意反应低；外部表现少；情绪具有稳定性；反应速度慢但灵活。<br/><br/>"+
                         "<b>安静稳定型的心理特点：</b>稳重，考虑问题全面；安静，沉默，善于克制自己；善于忍耐。情绪不易外露；注意力稳定而不容易转移，外部动作少而缓慢。<br/><br/>" +
                         "<b>安静稳定型的典型表现：</b>这种人又称为安静型，在生活中是一个坚持而稳健的辛勤工作者。由于这些人具有与兴奋过程向均衡的强的抑制，所以行动缓慢而沉着，严格恪守既定的生活秩序和工作制度，" +
                         "不为无所谓的动因而分心。安静稳定型的人态度持重，交际适度，不作空泛的清谈，情感上不易激动，不易发脾气，也不易流露情感，能自治，也不常常显露自己的才能。这种人长时间坚持不懈，有条不紊地从事自己的工作。"+
                         "其不足是有些事情不够灵活，不善于转移自己的注意力。惰性使他因循守旧，表现出固定性有余，而灵活性不足。从容不迫和严肃认真的品德，以及性格的一贯性和确定性。";
        }
        if (e_score.compareTo(BigDecimal.valueOf(7)) >=0 && n_score.compareTo(BigDecimal.valueOf(4)) > 0) {
            str3 += "<b>你的气质类型为：</b>冲动暴躁型，倾向外向性格，情绪不稳定。<br/><br/> " +
                         "<b>冲动暴躁型的神经特点：</b>感受性低;耐受性高;不随意反应强;外倾性明显;情绪兴奋性高;抑制能力差;反应快但不灵活。<br/><br/>"+
                         "<b>冲动暴躁型的心理特点：</b>坦率热情；精力旺盛，容易冲动；脾气暴躁；思维敏捷；但准确性差；情感外露，但持续时间不长。<br/><br/>"+
                         "<b>冲动暴躁型的典型表现：</b>冲动暴躁型又称不可遏止型或战斗型。冲动暴躁型的气质特征是外向性、行动性和直觉性。"+
                         "具有强烈的兴奋过程和比较弱的抑郁过程，情绪易激动，反应迅速，行动敏捷，暴躁而有力；在语言上，表情上，姿态上都有一种强烈而迅速的情感表现。"+
                         "这种人的工作特点带有明显的周期性，埋头于事业，也准备去克服通向目标的重重困难和障碍，冲动暴躁型人一旦就业，往往对本职工作不那么专注，喜欢跳槽，经常更换工作单位，渴望成为自由职业者。"+
                         "这种气质反映在音乐风格中，多有慷慨激昂的激情，有崇高的英雄主义情绪，有突发的强音迸发出强烈的感情。这类型的人的不足是缺乏自制性、粗暴和急躁、易生气、易激动，因此在耐心、沉着和自制力等方面的心理修养不够。";
        }
        if (e_score.compareTo(BigDecimal.valueOf(7)) >= 0  && n_score.compareTo(BigDecimal.valueOf(4)) < 0) {
            str3 += "<b>你的气质类型为：</b>活泼愉悦型，倾向外向性格，情绪稳定。<br/><br/>" +
                         "<b>活泼愉悦型的神经特点：</b>感受性低；耐受性高；不随意反应性强；可塑性高；情绪兴奋性高；反应速度快而灵活。<br/><br/>"+
                         "<b>活泼愉悦型的心理特点：</b>活泼好动，善于交际；思维敏捷；容易接受新鲜事物；情绪情感容易产生也容易变化和消失，容易外露；情绪变化多样；体验不深刻等。<br/><br/>"+
                         "<b>活泼愉悦型的典型表现：</b>活泼愉悦型又称活泼型，敏捷好动，善于交际，在新的环境里不感到拘束。在工作学习上富有精力而效率高，表现出机敏的工作能力，善于适应环境变化。"+
                         "在集体中精神愉快，朝气蓬勃，愿意从事合乎实际的事业，能对事业心向神往，能迅速地把握新事物，在有充分自制能力和纪律性的情况下，会表现出巨大的积极性。兴趣广泛，但情感易变，如果事业上不顺利，热情可能消失，其速度与投身事业一样迅速。从事多样化的工作往往成绩卓越。";
        }
        if (e_score.compareTo(BigDecimal.valueOf(7)) < 0 && n_score.compareTo(BigDecimal.valueOf(4)) > 0) {
            str3 += "<b>你的气质类型为：</b>优柔敏感型，倾向内向性格，情绪不稳定。<br/><br/>"+
                    "<b>优柔敏感型的神经特点：</b>感受性高，不随意反应低，对情感的体验深刻、有力、持久，而且具有高度的情绪易感性。<br/><br/>" +
                    "<b>优柔敏感型的心理特点：</b>优柔敏感型的人一般表现为行为孤僻、不太合群、观察细致、非常敏感、表情忸怩、多愁善感、行动迟缓、优柔寡断，具有明显的内倾性。<br/><br/>"+
                    "<b>优柔敏感型的典型表现：</b>对于以人际交往为主的职业，如外交官、政治家、商人等外向性职业，优柔敏感型人都没有适应性和兴趣。"+
                    "而在只需要一个人刻苦奋斗的学术、教育、研究、技术开发和医学等内在要求慎重、细致、周密思考的职业领域，优柔敏感型人就感到适合。但是，也不能说优柔敏感型人对所有的职业都不能适应。"+
                    "优柔敏感型人中的许多人，不是单凭聪明去处理事情，而是把自己所掌握的工作内容在头脑中组合、计算，确定方针，然后在这个范围内一个一个地去实行，把问题处理好。";
        }
        return str1 + "|" + responseText + "|" + str3;
    }
}

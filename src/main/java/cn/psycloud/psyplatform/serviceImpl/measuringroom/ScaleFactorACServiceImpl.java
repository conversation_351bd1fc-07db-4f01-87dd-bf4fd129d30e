package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorAbnormalConditionDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleFactorACService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ScaleFactorACServiceImpl implements ScaleFactorACService {
    @Autowired
    private ScaleFactorAbnormalConditionDao scaleFactorAbnormalConditionDao;

    /**
     *  根据因子查询异常条件集合：返回bootstrap datatables格式
     * @param factorId 因子id
     * @return 异常条件集合
     */
    public BSDatatableRes<ScaleFactorAbnormalConditionEntity> getListByFactorId(Integer factorId) {
        var dtRes = new BSDatatableRes<ScaleFactorAbnormalConditionEntity>();
        var listAbnormalConditions = scaleFactorAbnormalConditionDao.getListByFactorId(factorId);
        dtRes.setData(listAbnormalConditions);
        dtRes.setRecordsTotal(listAbnormalConditions.size());
        dtRes.setRecordsFiltered(listAbnormalConditions.size());
        return dtRes;
    }

    /**
     *  添加异常条件
     * @param entity 异常条件实体对象
     * @return 影响行数
     */
    public int add(ScaleFactorAbnormalConditionEntity entity) {
        return scaleFactorAbnormalConditionDao.insert(entity);
    }

    /**
     *  删除异常条件
     * @param id 异常条件id
     * @return 影响行数
     */
    public int deleteById(Integer id) {
        return scaleFactorAbnormalConditionDao.delete(id);
    }
}

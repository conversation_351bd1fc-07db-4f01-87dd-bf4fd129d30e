package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleTypeDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleTypeDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleTypeEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleTypeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ScaleTypeServiceImpl implements ScaleTypeService {
    @Autowired
    private ScaleTypeDao scaleTypeDao;
    /**
     *  查询量表类型集合：分页
     * @param dto 量表类型实体对象
     * @return 量表类型集合
     */
    @Override
    public BSDatatableRes<ScaleTypeEntity> getListByPaged(ScaleTypeDto dto) {
        var dtRes = new BSDatatableRes<ScaleTypeEntity>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listScaleTypes = scaleTypeDao.getList(dto);
        var scaleTypeDto = new PageInfo<>(listScaleTypes);
        dtRes.setData(scaleTypeDto.getList());
        dtRes.setRecordsTotal((int) scaleTypeDto.getTotal());
        dtRes.setRecordsFiltered((int) scaleTypeDto.getTotal());
        return dtRes;
    }

    /**
     *  将数据格式转换成select2格式
     * @param listScaleTypes 课程分类集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<ScaleTypeEntity> listScaleTypes) {
        var lsNode = new ArrayList<>();
        for (ScaleTypeEntity entity : listScaleTypes) {
            var select2Data = new Select2Data();
            select2Data.setId(entity.getId());
            select2Data.setText(entity.getScaleTypeName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  查询量表类型集合：select2
     * @return 量表类型集合
     */
    @Override
    public List<Object> getListForSelect() {
        var listScaleTypes =scaleTypeDao.getList(new ScaleTypeDto());
        return getSelect2Data(listScaleTypes);
    }

    /**
     *  查询量表类型集合并获取分类下面的量表集合
     * @return 量表集合
     */
    @Override
    public List<ScaleTypeDto> getScalesByType() {
        return scaleTypeDao.getAllScalesByType(new ScaleTypeDto());
    }

    /**
     *  根据条件查询量表分类集合
     * @param dto  量表类型实体对象
     * @return   分类集合
     */
    public List<ScaleTypeEntity> getList(ScaleTypeDto dto) {
        return scaleTypeDao.getList(dto);
    }

    /**
     *  新增
     * @param entity 量表类型实体对象
     * @return 影响行数
     */
    @Override
    public int addScaleType(ScaleTypeEntity entity) {
        return scaleTypeDao.insert(entity);
    }

    /**
     *  更新
     * @param entity 量表类型实体对象
     * @return 影响行数
     */
    @Override
    public int updateScaleType(ScaleTypeEntity entity){
        return scaleTypeDao.update(entity);
    }

    /**
     *  删除
     * @param id 量表分类id
     * @return 影响行数
     */
    @Override
    public int delete(int id) {
        return scaleTypeDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }
}

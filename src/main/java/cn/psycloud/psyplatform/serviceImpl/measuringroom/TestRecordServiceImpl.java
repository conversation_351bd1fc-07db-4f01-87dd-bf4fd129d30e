package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.dao.anteroom.UserDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorDao;
import cn.psycloud.psyplatform.dao.measuringroom.ScaleQuestionDao;
import cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.*;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestScoreService;
import cn.psycloud.psyplatform.util.Base64Util;
import cn.psycloud.psyplatform.util.CommonHelper;
import cn.psycloud.psyplatform.util.POIWordHelper;
import cn.psycloud.psyplatform.util.PermissonHelper;
import com.deepoove.poi.data.PictureRenderData;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateUtil.ageOfNow;

@Slf4j
@Service
public class TestRecordServiceImpl implements TestRecordService {
    @Autowired
    private TestRecordDao testRecordDao;
    @Autowired
    private ScaleDao scaleDao;
    @Autowired
    private ScaleFactorDao scaleFactorDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private ScaleQuestionDao scaleQuestionDao;
    @Autowired
    private TestScoreService testScoreService;
    @Autowired
    private POIWordHelper poiWordHelper;
    @Value("${file.location}")
    String uploadPath;

    /**
     *  根据记录ID查询测评记录
     * @param recordId 记录id
     * @return 测评记录实体对象
     */
    @Override
    public TestRecordDto getById(Integer recordId) {
        var testRecord = testRecordDao.getById(recordId);
        var scale = scaleDao.getById(testRecord.getScaleId());
        List<ScaleQuestionEntity> listQuestions = scaleQuestionDao.getListByScaleIdForTestIng(scale.getId());
        scale.setListQuestions(listQuestions);
        var userDto = new UserDto();
        userDto.setUserId(testRecord.getUser().getUserId());
        var user = userDao.get(userDto);
        testRecord.setScale(scale);
        testRecord.setUser(user);
        return  testRecord;
    }

    /**
     *  查询测评记录集合：分页
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    @Override
    public BSDatatableRes<TestRecordDto> getListByPaged(TestRecordDto dto) {
        var dtRes = new BSDatatableRes<TestRecordDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<TestRecordDto> listRecords = testRecordDao.getList(dto);
        PageInfo<TestRecordDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return dtRes;
    }

    /**
     * 根据条件查询测评记录集合
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    @Override
    public List<TestRecordDto> getList(TestRecordDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testRecordDao.getList(dto);
    }

    /**
     *  获取我的测评记录
     * @param dto 查询条件
     * @return 测评记录集合
     */
    public BSDatatableRes<TestRecordDto> getMyRecords(TestRecordDto dto){
        var dtRes = new BSDatatableRes<TestRecordDto>();
        // 修复分页计算：pageIndex是偏移量，需要转换为页码
        int pageNum = dto.getPageIndex() / dto.getPageSize() + 1;
        PageHelper.startPage(pageNum, dto.getPageSize());
        List<TestRecordDto> listRecords = testRecordDao.getMyRecords(dto);
        PageInfo<TestRecordDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return dtRes;
    }

    /**
     *  保存测评记录
     * @param entity 测评记录实体对象
     * @return 测评记录id
     */
    @Override
    public int addRecord(TestRecordEntity entity) {
        entity.setId(0);
        testRecordDao.addTestRecord(entity);
        return  entity.getId();
    }

    /**
     *  删除
     * @param id 记录id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id) {
        return testRecordDao.deleteByRecordId(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  更新测评状态
     * @param recordId 记录id
     * @param state 测评状态：0-未完成 1-已完成 2-测谎未通过
     * @return 影响行数
     */
    @Override
    public int updateTestState(Integer recordId, Integer state) {
        var map = new HashMap<String, Integer>();
        map.put("recordId",recordId);
        map.put("state",state);
        return testRecordDao.updateTestState(map);
    }

    /**
     *  更新测评开始时间
     * @param recordId 记录id
     * @return 影响行数
     */
    @Override
    public int updateTestStartTime(Integer recordId) {
        var map = new HashMap<String, Object>();
        map.put("recordId",recordId);
        map.put("startTime",new Date());
        return testRecordDao.updateStartTime(map);
    }

    /**
     *  验证个人信息是否符合量表要求
     * @param dto 测评记录实体对象
     * @return 是否符合
     */
    @Override
    public boolean isUserInfoComplete(TestRecordDto dto) {
        boolean check = false;
        var limit = dto.getScale().getTestLimit();
        if (limit == null || "".equals(limit) ) {
            check = true;
        }
        else {
            var userDto = new UserDto();
            userDto.setUserId(dto.getUser().getUserId());
            var user = userDao.get(userDto);
            if (limit.contains("出生年月")) {
                if (user.getBirth() != null && user.getBirth() != "") {
                    String[] arrayAgeLimit = dto.getScale().getAgeLimit().split(",");
                    if ("0".equals(arrayAgeLimit[0]) && "0".equals(arrayAgeLimit[1]))
                        check = true;
                    else {
                        var age = ageOfNow(user.getBirth());
                        if (age >= Integer.parseInt(arrayAgeLimit[0]) && age <= Integer.parseInt(arrayAgeLimit[1])) {
                            check = true;
                        }
                    }
                }
            }
            if (limit.contains("性别")) check = user.getSex() != null && !"".equals(user.getSex());
            if (limit.contains("姓名")) check = user.getRealName() != null && !"".equals(user.getRealName());
        }
        return check;
    }

    /**
     *  判断是否异常
     * @param recordId 记录id
     * @return 异常标识
     */
    @Override
    public int isAbnormal(Integer recordId) {
        return testRecordDao.isAbnormal(recordId);
    }

    /**
     *  查询九型人格测评记录集合：分页
     * @param dto 九型人格测评实体对象
     * @return 记录集合
     */
    @Override
    public BSDatatableRes<NineHouseStatDto> getNineHouseList(NineHouseStatDto dto) {
        var dtRes = new BSDatatableRes<NineHouseStatDto>();
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<NineHouseStatDto> listRecords = testRecordDao.getNineHouseList(dto);
        PageInfo<NineHouseStatDto> testRecordDto = new PageInfo<>(listRecords);
        dtRes.setData(testRecordDto.getList());
        dtRes.setRecordsTotal((int)testRecordDto.getTotal());
        dtRes.setRecordsFiltered((int)testRecordDto.getTotal());
        return  dtRes;
    }

    /**
     *  保存测评结果解释
     * @param entity 测评结果解释实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordExplain(TestRecordExplainEntity entity){
        return testRecordDao.saveTestRecordExplain(entity) > 0 ;
    }

    /**
     * 获取因子结果解释
     * @param recordId 测评记录id
     * @return 因子结果解释列表
     */
    @Override
    public List<TestRecordExplainEntity> getFactorExplains(Integer recordId) {
        return testRecordDao.getFactorExplains(recordId);
    }

    /**
     *  获取报告图表
     * @param recordId 测评记录id
     * @return 测评报告图表集合
     */
    public List<TestRecordChartsEntity> getReportCharts(Integer recordId) {
        return testRecordDao.getReportCharts(recordId);
    }

    /**
     *  保存测评报告里的图表
     * @param dto 测评报告图表实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordCharts(TestRecordChartsDto dto) {
        var isSuccess = false;
        testRecordDao.delTestRecordCharts(dto.getRecordId());
        if(dto.getChartsImg() != null) {
            int chartOrder = 0;
            for(String chartImg: dto.getChartsImg()){
                var fileName = CommonHelper.getGUID()+".png";
                Base64Util.generateImage(chartImg.replace("data:image/png;base64,",""),uploadPath+"/charts/"+fileName);
                var chartsEntity = new TestRecordChartsEntity();
                chartsEntity.setRecordId(dto.getRecordId());
                chartsEntity.setChartsImg(fileName);
                chartsEntity.setChartOrder(chartOrder++);
                chartsEntity.setFactorType(dto.getFactorType());
                chartsEntity.setChartType(dto.getChartType());
                chartsEntity.setChartIndex(dto.getChartIndex());
                isSuccess = testRecordDao.saveTestRecordCharts(chartsEntity) > 0;
            }
        }
        return isSuccess;
    }

    /**
     *  保存测评报告里的图表（新版本，支持详细图表信息）
     * @param requestDto 测评报告图表请求实体对象
     * @return 操作是否成功
     */
    @Override
    public boolean saveTestRecordChartsV2(TestRecordChartsRequestDto requestDto) {
        var isSuccess = false;
        testRecordDao.delTestRecordCharts(requestDto.getRecordId());
        if(requestDto.getChartsImg() != null && !requestDto.getChartsImg().isEmpty()) {
            int chartOrder = 0;
            for(ChartInfoDto chartInfo: requestDto.getChartsImg()){
                var fileName = CommonHelper.getGUID()+".png";
                String imageData = chartInfo.getImageData();
                if(imageData != null) {
                    imageData = imageData.replace("data:image/png;base64,","");
                    Base64Util.generateImage(imageData, uploadPath+"/charts/"+fileName);

                    var chartsEntity = new TestRecordChartsEntity();
                    chartsEntity.setRecordId(requestDto.getRecordId());
                    chartsEntity.setChartsImg(fileName);
                    chartsEntity.setChartOrder(chartOrder++);
                    chartsEntity.setFactorType(chartInfo.getFactorType());
                    chartsEntity.setChartType(chartInfo.getChartType());
                    chartsEntity.setChartIndex(chartInfo.getChartIndex());
                    isSuccess = testRecordDao.saveTestRecordCharts(chartsEntity) > 0;
                }
            }
        }
        return isSuccess;
    }

    /**
     *  导出测评记录
     * @param dto 查询条件
     * @return  测评记录集合
     */
    @Override
    public List<ExportTestRecordDto> getExportTestRecordList(TestRecordDto dto) {
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        return testRecordDao.getExportTestRecordList(dto);
    }

    /**
     *  测评报告导出word
     * @param recordId 测评记录Id
     * @return 文件名
     */
    @Override
    public String exportTestReportToWord(Integer recordId, String folderName){
        String filePath = "";
        try {
            var reportDto = getReport(recordId);
            if(reportDto.getListExplains() != null && reportDto.getListExplains().size() > 0) {
                Map<String, Object> reportMap = new HashMap<>();

                // 构建基本信息
                buildBasicInfo(reportDto, reportMap);

                // 构建因子章节数据
                List<WordReportSection> factorSections = buildFactorSections(reportDto.getListExplains());
                reportMap.put("factorSections", factorSections);

                // 提供简单的因子列表
                List<Map<String, String>> simpleFactors = new ArrayList<>();
                for (WordReportSection section : factorSections) {
                    if (section.getFactors() != null) {
                        for (WordReportFactor factor : section.getFactors()) {
                            Map<String, String> factorMap = new HashMap<>();
                            factorMap.put("factorName", factor.getFactorName());
                            factorMap.put("interpretation", factor.getInterpretation());
                            simpleFactors.add(factorMap);
                        }
                    }
                }
                reportMap.put("factors", simpleFactors);

                // 提供格式化的因子内容（美观有层次感的HTML格式，包含真实图表）
                StringBuilder factorContent = new StringBuilder();

                // 获取图表数据
                List<TestRecordChartsEntity> chartsList = reportDto.getListCharts();

                // 调试：打印图表数据
                log.info("=== 图表数据调试信息 ===");
                if (chartsList != null && !chartsList.isEmpty()) {
                    log.info("找到 {} 个图表", chartsList.size());
                    for (TestRecordChartsEntity chart : chartsList) {
                        log.info("图表信息: factorType={}, chartType={}, chartIndex={}, fileName={}",
                            chart.getFactorType(), chart.getChartType(), chart.getChartIndex(), chart.getChartsImg());

                        // 检查文件是否存在
                        String chartPath = uploadPath + "/charts/" + chart.getChartsImg();
                        File chartFile = new File(chartPath);
                        log.info("图表文件路径: {}, 文件存在: {}", chartPath, chartFile.exists());
                    }
                } else {
                    log.warn("没有找到图表数据");
                }

                // 添加整体容器和样式
                factorContent.append("<div class='factor-report-container'>");
                factorContent.append("<style>");
                factorContent.append(".factor-report-container { font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif; line-height: 1.6; color: #333; max-width: 100%; }");
                factorContent.append(".factor-section { margin-bottom: 32px; background: #fff; border-radius: 16px; overflow: hidden; box-shadow: 0 8px 32px rgba(0,0,0,0.1); border: 1px solid #e8ecf4; transition: all 0.3s ease; }");
                factorContent.append(".factor-section:hover { transform: translateY(-2px); box-shadow: 0 12px 40px rgba(0,0,0,0.15); }");
                factorContent.append(".factor-header { background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%); padding: 24px 28px; border-bottom: 1px solid #e8ecf4; position: relative; }");
                factorContent.append(".factor-header::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 5px; background: linear-gradient(135deg, #727cf5 0%, #6c5ce7 100%); border-radius: 0 3px 3px 0; }");
                factorContent.append(".factor-header::after { content: ''; position: absolute; right: 24px; top: 50%; transform: translateY(-50%); width: 40px; height: 40px; background: radial-gradient(circle, rgba(114,124,245,0.1) 0%, transparent 70%); border-radius: 50%; }");
                factorContent.append(".factor-title { font-size: 20px; font-weight: 700; color: #2c3e50; margin: 0; display: flex; align-items: center; letter-spacing: 0.5px; }");
                factorContent.append(".factor-title i { margin-right: 12px; color: #727cf5; font-size: 18px; padding: 8px; background: rgba(114,124,245,0.1); border-radius: 8px; }");
                factorContent.append(".factor-content { padding: 28px; background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%); }");
                factorContent.append(".factor-table { width: 100%; border-collapse: collapse; margin-bottom: 24px; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 16px rgba(0,0,0,0.05); }");
                factorContent.append(".factor-table th { background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%); color: #2c3e50; font-weight: 700; padding: 20px 18px; text-align: left; border-bottom: 2px solid #e8ecf4; font-size: 15px; position: relative; }");
                factorContent.append(".factor-table th::after { content: ''; position: absolute; bottom: 0; left: 18px; right: 18px; height: 2px; background: linear-gradient(90deg, #727cf5 0%, #6c5ce7 100%); }");
                factorContent.append(".factor-table th i { margin-right: 8px; color: #727cf5; font-size: 14px; }");
                factorContent.append(".factor-table td { padding: 20px 18px; border-bottom: 1px solid #f1f3ff; vertical-align: top; transition: all 0.2s ease; }");
                factorContent.append(".factor-table tr:hover { background: linear-gradient(135deg, rgba(114,124,245,0.03) 0%, rgba(108,92,231,0.03) 100%); }");
                factorContent.append(".factor-table tr:nth-child(even) { background: rgba(248,249,255,0.5); }");
                factorContent.append(".factor-table tr:nth-child(even):hover { background: linear-gradient(135deg, rgba(114,124,245,0.05) 0%, rgba(108,92,231,0.05) 100%); }");
                factorContent.append(".factor-name { font-weight: 700; color: #2c3e50; font-size: 15px; position: relative; padding-left: 16px; }");
                factorContent.append(".factor-name::before { content: '●'; position: absolute; left: 0; color: #727cf5; font-size: 12px; top: 2px; }");
                factorContent.append(".factor-interpretation { color: #5a6c7d; line-height: 1.8; font-size: 14px; text-align: justify; }");
                factorContent.append(".chart-section { margin-top: 28px; padding: 28px; background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%); border-radius: 16px; border: 1px solid #e8ecf4; position: relative; }");
                factorContent.append(".chart-section::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #727cf5 0%, #6c5ce7 100%); border-radius: 16px 16px 0 0; }");
                factorContent.append(".chart-title { color: #2c3e50; font-weight: 700; margin-bottom: 20px; font-size: 18px; display: flex; align-items: center; letter-spacing: 0.5px; }");
                factorContent.append(".chart-title i { margin-right: 12px; color: #727cf5; font-size: 16px; padding: 8px; background: rgba(114,124,245,0.1); border-radius: 8px; }");
                factorContent.append(".chart-item { margin-bottom: 20px; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 16px rgba(0,0,0,0.08); transition: all 0.3s ease; }");
                factorContent.append(".chart-item:hover { transform: translateY(-2px); box-shadow: 0 8px 24px rgba(0,0,0,0.12); }");
                factorContent.append(".chart-item canvas { border-radius: 12px; }");
                factorContent.append("@media (max-width: 768px) { .factor-section { margin-bottom: 24px; border-radius: 12px; } .factor-header { padding: 20px 24px; } .factor-title { font-size: 18px; } .factor-content { padding: 24px 20px; } .factor-table th, .factor-table td { padding: 16px 14px; } .chart-section { padding: 20px; } }");
                factorContent.append("</style>");

                int sectionIndex = 0;
                for (WordReportSection section : factorSections) {
                    if (section.getFactors() != null && !section.getFactors().isEmpty()) {
                        sectionIndex++;

                        // 开始章节容器
                        factorContent.append("<div class='factor-section'>");

                        // 章节标题
                        String iconClass = getSectionIcon(section.getSectionTitle(), sectionIndex);
                        factorContent.append("<div class='factor-header'>");
                        factorContent.append("<h3 class='factor-title'>");
                        factorContent.append("<i class='fa ").append(iconClass).append("'></i>");
                        factorContent.append(section.getSectionTitle());
                        factorContent.append("</h3>");
                        factorContent.append("</div>");

                        // 章节内容
                        factorContent.append("<div class='factor-content'>");

                        // 因子表格
                        factorContent.append("<table class='factor-table'>");
                        factorContent.append("<thead>");
                        factorContent.append("<tr>");
                        factorContent.append("<th style='width: 25%;'><i class='fa fa-tag'></i>因子名称</th>");
                        factorContent.append("<th><i class='fa fa-file-text-o'></i>结果解释</th>");
                        factorContent.append("</tr>");
                        factorContent.append("</thead>");
                        factorContent.append("<tbody>");

                        for (WordReportFactor factor : section.getFactors()) {
                            factorContent.append("<tr>");
                            factorContent.append("<td class='factor-name'>").append(factor.getFactorName()).append("</td>");
                            factorContent.append("<td class='factor-interpretation'>").append(factor.getInterpretation()).append("</td>");
                            factorContent.append("</tr>");
                        }

                        factorContent.append("</tbody>");
                        factorContent.append("</table>");

                        // 图表区域（插入到每组结果解释后面，与前端逻辑保持一致）
                        String chartPrefix = getChartPrefix(section.getSectionTitle(), sectionIndex);
                        log.info("章节: {}, 图表前缀: {}", section.getSectionTitle(), chartPrefix);

                        factorContent.append("<div class='chart-section'>");
                        factorContent.append("<h4 class='chart-title'><i class='fa fa-bar-chart'></i>图表分析</h4>");

                        // 生成与前端一致的图表容器，包含真实图表
                        factorContent.append(generateChartContainersForWord(section.getFactors(), chartPrefix, chartsList));

                        factorContent.append("</div>");

                        factorContent.append("</div>"); // 结束 factor-content
                        factorContent.append("</div>"); // 结束 factor-section
                    }
                }

                factorContent.append("</div>"); // 结束整体容器
                reportMap.put("factorContent", factorContent.toString());

                // 图表已直接嵌入到factorContent中，无需额外处理

                String templatePath = CommonHelper.getResourcesFilePath("static/template/report_new.docx");
                String fileName = reportDto.getTestRecord().getId() + "_" + reportDto.getTestRecord().getUser().getRealName() + "_" + reportDto.getTestRecord().getScale().getScaleName() + ".docx";
                filePath = String.format("report/%s/%s",folderName, fileName);
                String fileAbPath=String.format(uploadPath +"report/%s/%s", folderName, fileName);

                poiWordHelper.downloadWord(templatePath, fileAbPath, reportMap);
            }
        } catch (Exception e) {
            throw new RuntimeException("导出测评报告失败", e);
        }
        return  filePath;
    }

    /**
     *  批量导出测评报告
     * @param dto 查询条件
     * @return 文件路径
     */
    @Override
    public String batchExportReport(TestRecordDto dto) {
        String result = "";
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        var recordIds = testRecordDao.getRecordIdsForBatchExport(dto);
        if(!recordIds.isEmpty()) {
            result = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
            for(Map<String,Integer> map: recordIds){
                exportTestReportToWord(map.get("id"), result);
            }
        }
        return  result;
    }

    /**
     * 获取因子结果解释（按父子关系组织）
     * @param recordId 测评记录id
     * @return 按父子关系组织的因子解释
     */
    @Override
    public List<FactorExplainHierarchyDto> getFactorExplainsWithHierarchy(Integer recordId) {
        // 获取因子解释
        List<TestRecordExplainEntity> explains = testRecordDao.getFactorExplains(recordId);

        // 获取测评记录以获取量表ID
        TestRecordDto testRecord = getById(recordId);
        Integer scaleId = testRecord.getScale().getId();

        // 获取该量表的所有因子信息（包含factorIds）
        List<ScaleFactorDto> factors = scaleFactorDao.getFactorsByScaleId(scaleId);

        // 创建因子ID到解释的映射
        Map<Integer, TestRecordExplainEntity> explainMap = explains.stream()
                .collect(Collectors.toMap(TestRecordExplainEntity::getFactorId, e -> e));

        // 创建因子ID到因子信息的映射
        Map<Integer, ScaleFactorDto> factorMap = factors.stream()
                .collect(Collectors.toMap(ScaleFactorDto::getId, f -> f));

        // 找出所有父因子（有factorIds的因子）
        List<ScaleFactorDto> parentFactors = factors.stream()
                .filter(f -> f.getFactorIds() != null && !f.getFactorIds().trim().isEmpty())
                .sorted(Comparator.comparing(ScaleFactorDto::getFactorType).reversed()) // 按类型倒序，高级因子在前
                .collect(Collectors.toList());

        // 构建层级结构
        List<FactorExplainHierarchyDto> result = new ArrayList<>();
        Set<Integer> usedFactorIds = new HashSet<>();

        for (ScaleFactorDto parentFactor : parentFactors) {
            if (explainMap.containsKey(parentFactor.getId()) && !usedFactorIds.contains(parentFactor.getId())) {
                FactorExplainHierarchyDto parentNode = buildFactorHierarchy(
                        parentFactor, explainMap, factorMap, usedFactorIds);
                if (parentNode != null) {
                    result.add(parentNode);
                }
            }
        }

        // 添加没有父因子的独立因子
        for (TestRecordExplainEntity explain : explains) {
            if (!usedFactorIds.contains(explain.getFactorId())) {
                FactorExplainHierarchyDto node = new FactorExplainHierarchyDto();
                node.setFactorId(explain.getFactorId());
                node.setFactorName(explain.getFactorName());
                node.setInterpretation(explain.getInterpretation());
                node.setOriginalScore(explain.getOriginalScore());
                node.setScore(explain.getScore());
                node.setChildren(new ArrayList<>());
                result.add(node);
            }
        }

        return result;
    }

    private FactorExplainHierarchyDto buildFactorHierarchy(
            ScaleFactorDto factor,
            Map<Integer, TestRecordExplainEntity> explainMap,
            Map<Integer, ScaleFactorDto> factorMap,
            Set<Integer> usedFactorIds) {

        if (!explainMap.containsKey(factor.getId())) {
            return null;
        }
        
        TestRecordExplainEntity explain = explainMap.get(factor.getId());
        FactorExplainHierarchyDto node = new FactorExplainHierarchyDto();
        
        // 设置因子基本信息
        node.setFactorId(explain.getFactorId());
        node.setFactorName(explain.getFactorName());
        node.setInterpretation(explain.getInterpretation());
        node.setFactorType(factor.getFactorType());
        node.setChildren(new ArrayList<>());
        node.setOriginalScore(explain.getOriginalScore());
        node.setScore(explain.getScore());
        
        usedFactorIds.add(factor.getId());
        
        // 处理子因子
        if (factor.getFactorIds() != null && !factor.getFactorIds().trim().isEmpty()) {
            String[] childIds = factor.getFactorIds().split(",");
            for (String childIdStr : childIds) {
                try {
                    Integer childId = Integer.parseInt(childIdStr.trim());
                    if (factorMap.containsKey(childId) && explainMap.containsKey(childId)) {
                        ScaleFactorDto childFactor = factorMap.get(childId);

                        // 递归构建子因子
                        FactorExplainHierarchyDto childNode = buildFactorHierarchy(
                                childFactor, explainMap, factorMap, usedFactorIds);
                        if (childNode != null) {
                            node.getChildren().add(childNode);
                        }
                    }
                } catch (NumberFormatException e) {
                    // 忽略无效的ID
                }
            }
        }
        return node;
    }

    /**
     * 构建Word报告基本信息
     * @param reportDto 报告数据
     * @param reportMap 报告Map
     */
    private void buildBasicInfo(ReportDto reportDto, Map<String, Object> reportMap) {
        if (reportDto == null || reportDto.getTestRecord() == null) {
            throw new RuntimeException("报告数据或测评记录为空");
        }

        TestRecordDto testRecord = reportDto.getTestRecord();
        UserDto user = testRecord.getUser();
        ScaleDto scale = testRecord.getScale();

        if (user == null) {
            throw new RuntimeException("用户信息为空");
        }
        if (scale == null) {
            throw new RuntimeException("量表信息为空");
        }

        // 基本信息 - 增加空值检查
        String userName = "";
        if (user.getRealName() != null && !user.getRealName().trim().isEmpty()) {
            userName = user.getRealName().trim();
        } else if (user.getLoginName() != null && !user.getLoginName().trim().isEmpty()) {
            userName = user.getLoginName().trim();
        } else {
            userName = "未知用户";
        }
        reportMap.put("userName", userName);

        String orgName = user.getStructName() != null ? user.getStructName().trim() : "";
        reportMap.put("orgName", orgName);

        String scaleName = scale.getScaleName() != null ? scale.getScaleName().trim() : "未知量表";
        reportMap.put("scaleName", scaleName);

        // 格式化测评日期
        if (testRecord.getStartTime() != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
            reportMap.put("testDate", dateFormat.format(testRecord.getStartTime()));
        } else {
            reportMap.put("testDate", "");
        }

        // 格式化耗时
        if (testRecord.getTimeInterval() != null) {
            int totalSeconds = testRecord.getTimeInterval();
            int minutes = totalSeconds / 60;
            int seconds = totalSeconds % 60;
            reportMap.put("costTime", String.format("%d分%d秒", minutes, seconds));
        } else {
            reportMap.put("costTime", "");
        }

        // 其他信息
        reportMap.put("testId", testRecord.getId() != null ? testRecord.getId().toString() : "");
    }

    /**
     * 构建因子章节数据（完全按照前端逻辑处理）
     * @param factorHierarchy 因子层次结构
     * @return Word报告章节列表
     */
    private List<WordReportSection> buildFactorSections(List<FactorExplainHierarchyDto> factorHierarchy) {
        List<WordReportSection> sections = new ArrayList<>();

        if (factorHierarchy == null || factorHierarchy.isEmpty()) {
            return sections;
        }

        // 完全按照前端processFactorHierarchy的逻辑
        List<FactorExplainHierarchyDto> topLevelFactors = new ArrayList<>();
        List<ParentGroup> parentGroups = new ArrayList<>();
        List<FactorExplainHierarchyDto> independentFactors = new ArrayList<>();
        Set<Integer> processedFactorIds = new HashSet<>();

        // 第一步：处理顶层因子和直接子因子（对应前端846-858行）
        for (FactorExplainHierarchyDto factor : factorHierarchy) {
            if (factor.getChildren() != null && !factor.getChildren().isEmpty()) {
                topLevelFactors.add(factor);
                parentGroups.add(new ParentGroup(factor.getFactorId(), factor.getFactorName(), factor.getChildren()));
            } else if (!processedFactorIds.contains(factor.getFactorId())) {
                independentFactors.add(factor);
                processedFactorIds.add(factor.getFactorId());
            }
        }

        // 第二步：递归收集因子（对应前端collectFactors函数）
        collectFactors(factorHierarchy, null, parentGroups, independentFactors, processedFactorIds);

        // 第三步：按前端显示顺序生成章节
        // 1. generateTopLevelContent - 显示顶层因子（三级因子结构中的最顶层）
        if (!topLevelFactors.isEmpty()) {
            WordReportSection topLevelSection = new WordReportSection();
            topLevelSection.setSectionTitle("结果分析");
            topLevelSection.setFactors(convertToWordFactors(topLevelFactors));
            sections.add(topLevelSection);
        }

        // 2. generateParentGroupContent - 为每个parentGroup生成章节
        for (ParentGroup parentGroup : parentGroups) {
            WordReportSection section = new WordReportSection();
            section.setSectionTitle(parentGroup.parentName);
            section.setFactors(convertToWordFactors(parentGroup.children));
            sections.add(section);
        }

        // 3. generateIndependentFactorsContent - 独立因子
        if (!independentFactors.isEmpty()) {
            WordReportSection independentSection = new WordReportSection();
            // 前端使用的标题是"结果解释及建议"，但在移动端是"结果分析"
            independentSection.setSectionTitle("结果解释及建议");
            independentSection.setFactors(convertToWordFactors(independentFactors));
            sections.add(independentSection);
        }

        return sections;
    }

    /**
     * 递归收集因子（对应前端collectFactors函数）
     */
    private void collectFactors(List<FactorExplainHierarchyDto> factors, ParentInfo parentInfo,
                               List<ParentGroup> parentGroups, List<FactorExplainHierarchyDto> independentFactors,
                               Set<Integer> processedFactorIds) {
        for (FactorExplainHierarchyDto factorData : factors) {
            if (processedFactorIds.contains(factorData.getFactorId())) {
                continue;
            }
            processedFactorIds.add(factorData.getFactorId());

            if (factorData.getChildren() != null && !factorData.getChildren().isEmpty()) {
                collectFactors(factorData.getChildren(),
                    new ParentInfo(factorData.getFactorId(), factorData.getFactorName()),
                    parentGroups, independentFactors, processedFactorIds);
            } else {
                if (parentInfo != null && factorData.getFactorType() != null && factorData.getFactorType() == 1) {
                    // 找到对应的parentGroup并添加子因子
                    ParentGroup existingGroup = parentGroups.stream()
                        .filter(group -> group.parentId.equals(parentInfo.parentId))
                        .findFirst()
                        .orElse(null);

                    if (existingGroup != null) {
                        boolean exists = existingGroup.children.stream()
                            .anyMatch(child -> child.getFactorId().equals(factorData.getFactorId()));
                        if (!exists) {
                            existingGroup.children.add(factorData);
                        }
                    } else {
                        List<FactorExplainHierarchyDto> children = new ArrayList<>();
                        children.add(factorData);
                        parentGroups.add(new ParentGroup(parentInfo.parentId, parentInfo.parentName, children));
                    }
                } else {
                    boolean exists = independentFactors.stream()
                        .anyMatch(factor -> factor.getFactorId().equals(factorData.getFactorId()));
                    if (!exists) {
                        independentFactors.add(factorData);
                    }
                }
            }
        }
    }

    // 辅助类
    private static class ParentGroup {
        Integer parentId;
        String parentName;
        List<FactorExplainHierarchyDto> children;

        ParentGroup(Integer parentId, String parentName, List<FactorExplainHierarchyDto> children) {
            this.parentId = parentId;
            this.parentName = parentName;
            this.children = new ArrayList<>(children);
        }
    }

    private static class ParentInfo {
        Integer parentId;
        String parentName;

        ParentInfo(Integer parentId, String parentName) {
            this.parentId = parentId;
            this.parentName = parentName;
        }
    }



    /**
     * 将FactorExplainHierarchyDto转换为WordReportFactor
     * @param factor 因子数据
     * @return Word报告因子
     */
    private WordReportFactor convertToWordFactor(FactorExplainHierarchyDto factor) {
        if (factor == null) {
            throw new RuntimeException("因子数据为空");
        }

        WordReportFactor wordFactor = new WordReportFactor();

        // 因子名称 - 必须有值
        String factorName = factor.getFactorName();
        if (factorName == null || factorName.trim().isEmpty()) {
            factorName = "未知因子";
        }
        wordFactor.setFactorName(factorName.trim());

        // 解释内容
        String interpretation = factor.getInterpretation();
        if (interpretation == null || interpretation.trim().isEmpty()) {
            interpretation = "暂无解释内容";
        }
        wordFactor.setInterpretation(interpretation.trim());

        return wordFactor;
    }

    /**
     * 将因子列表转换为Word报告因子列表
     * @param factors 因子列表
     * @return Word报告因子列表
     */
    private List<WordReportFactor> convertToWordFactors(List<FactorExplainHierarchyDto> factors) {
        List<WordReportFactor> wordFactors = new ArrayList<>();
        for (FactorExplainHierarchyDto factor : factors) {
            wordFactors.add(convertToWordFactor(factor));
        }
        return wordFactors;
    }

    /**
     * 根据章节标题获取合适的图标
     * @param sectionTitle 章节标题
     * @param sectionIndex 章节索引
     * @return 图标CSS类名
     */
    private String getSectionIcon(String sectionTitle, int sectionIndex) {
        if (sectionTitle == null) {
            return "fa-list";
        }

        String title = sectionTitle.toLowerCase();

        // 根据标题内容匹配图标
        if (title.contains("结果分析") || title.contains("分析")) {
            return "fa-line-chart";
        } else if (title.contains("解释") || title.contains("建议")) {
            return "fa-lightbulb-o";
        } else if (title.contains("人格") || title.contains("性格")) {
            return "fa-user";
        } else if (title.contains("情绪") || title.contains("心理")) {
            return "fa-heart";
        } else if (title.contains("能力") || title.contains("智力")) {
            return "fa-graduation-cap";
        } else if (title.contains("行为") || title.contains("表现")) {
            return "fa-eye";
        } else if (title.contains("社交") || title.contains("人际")) {
            return "fa-users";
        } else if (title.contains("压力") || title.contains("焦虑")) {
            return "fa-exclamation-triangle";
        } else if (title.contains("发展") || title.contains("成长")) {
            return "fa-arrow-up";
        } else {
            // 根据章节索引使用不同图标
            String[] defaultIcons = {
                "fa-line-chart", "fa-pie-chart", "fa-bar-chart",
                "fa-area-chart", "fa-signal", "fa-list-alt"
            };
            return defaultIcons[(sectionIndex - 1) % defaultIcons.length];
        }
    }

    /**
     * 根据章节标题和索引生成图表前缀
     * @param sectionTitle 章节标题
     * @param sectionIndex 章节索引
     * @return 图表前缀
     */
    private String getChartPrefix(String sectionTitle, int sectionIndex) {
        if (sectionTitle == null) {
            return "section_" + sectionIndex;
        }

        String title = sectionTitle.toLowerCase();

        // 根据标题内容生成前缀，与前端逻辑保持一致
        if (title.contains("结果分析") || title.contains("分析")) {
            return "topLevel";
        } else if (title.contains("解释") || title.contains("建议")) {
            return "independent";
        } else {
            // 对于父组，使用parent_前缀
            return "parent_" + sectionIndex;
        }
    }

    /**
     * 为Word文档生成图表容器HTML，包含真实图表图片
     * @param factors 因子列表
     * @param chartPrefix 图表前缀
     * @param chartsList 图表数据列表
     * @return 图表容器HTML
     */
    private String generateChartContainersForWord(List<WordReportFactor> factors, String chartPrefix, List<TestRecordChartsEntity> chartsList) {
        StringBuilder chartHtml = new StringBuilder();

        // 查找匹配的图表数据
        List<TestRecordChartsEntity> matchingCharts = findMatchingCharts(chartsList, chartPrefix);

        if (matchingCharts != null && !matchingCharts.isEmpty()) {
            // 有真实图表数据，插入图表图片
            for (TestRecordChartsEntity chart : matchingCharts) {
                chartHtml.append("<div class='chart-item' style='margin-bottom: 20px; text-align: center;'>");
                chartHtml.append("<div style='padding: 16px; background: #fff; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);'>");

                // 尝试读取图片文件并转换为base64
                String chartImagePath = uploadPath + "/charts/" + chart.getChartsImg();
                File chartFile = new File(chartImagePath);

                if (chartFile.exists()) {
                    try {
                        // 读取图片文件并转换为base64
                        byte[] imageBytes = java.nio.file.Files.readAllBytes(chartFile.toPath());
                        String base64Image = java.util.Base64.getEncoder().encodeToString(imageBytes);

                        // 直接在HTML中嵌入base64图片
                        chartHtml.append("<img src='data:image/png;base64,").append(base64Image).append("' ");
                        chartHtml.append("style='width: 100%; max-width: 500px; height: auto; border-radius: 8px; border: 1px solid #e8ecf4;' ");
                        chartHtml.append("alt='图表' />");

                        log.info("成功嵌入图表图片: {}", chart.getChartsImg());
                    } catch (Exception e) {
                        log.error("读取图表文件失败: {}, 错误: {}", chartImagePath, e.getMessage());
                        // 显示错误占位符
                        chartHtml.append("<div style='padding: 20px; border: 2px dashed #ff6b6b; border-radius: 8px; color: #ff6b6b;'>");
                        chartHtml.append("<i class='fa fa-exclamation-triangle'></i> 图表加载失败");
                        chartHtml.append("</div>");
                    }
                } else {
                    log.warn("图表文件不存在: {}", chartImagePath);
                    // 显示文件不存在的占位符
                    chartHtml.append("<div style='padding: 20px; border: 2px dashed #ffa500; border-radius: 8px; color: #ffa500;'>");
                    chartHtml.append("<i class='fa fa-file-o'></i> 图表文件不存在");
                    chartHtml.append("</div>");
                }

                // 添加图表说明
                if (chart.getChartType() != null && !chart.getChartType().isEmpty()) {
                    chartHtml.append("<p style='margin-top: 12px; color: #5a6c7d; font-size: 13px;'>");
                    chartHtml.append(getChartTypeDescription(chart.getChartType()));
                    chartHtml.append("</p>");
                }

                chartHtml.append("</div>");
                chartHtml.append("</div>");
            }
        } else {
            // 没有图表数据，显示占位符
            chartHtml.append("<div class='chart-item' style='margin-bottom: 20px;'>");
            chartHtml.append("<div class='chart-placeholder' style='text-align: center; padding: 40px 20px; color: #8492a6; font-size: 14px; background: #fff; border-radius: 12px; border: 2px dashed #e8ecf4;'>");
            chartHtml.append("<i class='fa fa-line-chart' style='font-size: 48px; margin-bottom: 16px; display: block; color: #727cf5;'></i>");
            chartHtml.append("<div>暂无图表数据</div>");
            chartHtml.append("</div>");
            chartHtml.append("</div>");
        }

        return chartHtml.toString();
    }

    /**
     * 查找匹配的图表数据
     * @param chartsList 所有图表数据
     * @param chartPrefix 图表前缀
     * @return 匹配的图表列表
     */
    private List<TestRecordChartsEntity> findMatchingCharts(List<TestRecordChartsEntity> chartsList, String chartPrefix) {
        log.info("查找图表匹配: chartPrefix={}", chartPrefix);

        if (chartsList == null || chartsList.isEmpty()) {
            log.warn("图表列表为空");
            return new ArrayList<>();
        }

        List<TestRecordChartsEntity> matchingCharts = new ArrayList<>();

        for (TestRecordChartsEntity chart : chartsList) {
            log.info("检查图表: factorType={}, chartPrefix={}", chart.getFactorType(), chartPrefix);

            // 尝试多种匹配方式
            boolean isMatch = false;
            if (chart.getFactorType() != null) {
                // 精确匹配
                if (chart.getFactorType().equals(chartPrefix)) {
                    isMatch = true;
                }
                // 如果没有精确匹配，尝试包含匹配
                else if (chartPrefix.contains("topLevel") && chart.getFactorType().contains("topLevel")) {
                    isMatch = true;
                }
                else if (chartPrefix.contains("independent") && chart.getFactorType().contains("independent")) {
                    isMatch = true;
                }
                else if (chartPrefix.contains("parent") && chart.getFactorType().contains("parent")) {
                    isMatch = true;
                }
            }

            if (isMatch) {
                log.info("找到匹配的图表: {}", chart.getChartsImg());
                matchingCharts.add(chart);
            }
        }

        log.info("匹配到 {} 个图表", matchingCharts.size());

        // 按chartIndex排序
        matchingCharts.sort((a, b) -> {
            Integer indexA = a.getChartIndex() != null ? a.getChartIndex() : 0;
            Integer indexB = b.getChartIndex() != null ? b.getChartIndex() : 0;
            return indexA.compareTo(indexB);
        });

        return matchingCharts;
    }

    /**
     * 获取图表类型描述
     * @param chartType 图表类型
     * @return 图表类型描述
     */
    private String getChartTypeDescription(String chartType) {
        if (chartType == null) {
            return "数据图表";
        }

        switch (chartType.toLowerCase()) {
            case "line":
                return "趋势分析图 - 显示因子得分变化趋势";
            case "column":
            case "bar":
                return "对比分析图 - 显示各因子得分对比";
            case "pie":
                return "占比分析图 - 显示因子得分占比分布";
            case "area":
                return "区域分析图 - 显示因子得分区域分布";
            case "radar":
                return "雷达分析图 - 显示多维度因子分析";
            default:
                return "数据分析图表";
        }
    }

    /**
     * 将图表图片数据添加到reportMap中
     * @param reportMap 报告数据Map
     * @param chartsList 图表数据列表
     */
    private void addChartImagesToReportMap(Map<String, Object> reportMap, List<TestRecordChartsEntity> chartsList) {
        if (chartsList == null || chartsList.isEmpty()) {
            return;
        }

        try {
            for (TestRecordChartsEntity chart : chartsList) {
                if (chart.getChartsImg() != null && !chart.getChartsImg().isEmpty()) {
                    String chartImagePath = uploadPath + "/charts/" + chart.getChartsImg();
                    File chartFile = new File(chartImagePath);

                    if (chartFile.exists()) {
                        // 构建图片标识符
                        String chartKey = "chartImage_" + chart.getFactorType() + "_" + chart.getChartIndex();

                        // 使用poi-tl的PictureRenderData
                        PictureRenderData pictureRenderData = new PictureRenderData(
                            500, 300, // 宽度和高度
                            chartImagePath
                        );

                        reportMap.put(chartKey, pictureRenderData);
                    }
                }
            }
        } catch (Exception e) {
            log.error("添加图表图片到报告失败: {}", e.getMessage());
        }
    }

    /**
     * 测试图表数据读取
     * @param recordId 测评记录ID
     */
    public void testChartData(Integer recordId) {
        log.info("=== 测试图表数据读取 ===");
        List<TestRecordChartsEntity> charts = getReportCharts(recordId);

        if (charts != null && !charts.isEmpty()) {
            log.info("找到 {} 个图表", charts.size());
            for (TestRecordChartsEntity chart : charts) {
                log.info("图表: factorType={}, chartType={}, fileName={}",
                    chart.getFactorType(), chart.getChartType(), chart.getChartsImg());

                String chartPath = uploadPath + "/charts/" + chart.getChartsImg();
                File chartFile = new File(chartPath);
                log.info("文件路径: {}, 存在: {}, 大小: {} bytes",
                    chartPath, chartFile.exists(), chartFile.exists() ? chartFile.length() : 0);
            }
        } else {
            log.warn("没有找到图表数据");
        }
    }

    /**
     *  获取测评报告
     * @param recordId 测评记录id
     * @return 测评报告实体对象
     */
    @Override
    public ReportDto getReport(Integer recordId) {
        var reportDto = new ReportDto();
        reportDto.setTestRecord(getById(recordId));
        reportDto.setListExplains(getFactorExplainsWithHierarchy(recordId));
        reportDto.setListCharts(getReportCharts(recordId));

        // 测试图表数据
        testChartData(recordId);

        return reportDto;
    }
}

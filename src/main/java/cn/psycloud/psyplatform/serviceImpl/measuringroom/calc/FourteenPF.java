package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.measuringroom.CalcDao;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 *  卡特尔14PF量表
 */
@Service
public class FourteenPF extends BaseCalc{
    @Autowired
    private CalcDao calcDao;

    @Override
    public void compute()
    {
        var scoreList = new int[17];
        var listFactors = getFactorList();
        int k = 0;
        for(ScaleFactorDto dto: listFactors) {
            if (k > 14) break;
            //计算因子的问题答案积分和(原始分)
            scoreList[k] = testScoreDao.computeFactorScore(recordId, dto.getQIds().split(",")).intValue();
            k++;
        }
        double[] sc = null;
        if ("男".equals(sex) && age < 12){ //第一种情况：8-11岁男性儿童
            sc = getScore(scoreList, "811m");
        }
        else if ("女" .equals(sex)&& age < 12) { //第二种情况：8-11岁女性儿童
            sc = getScore(scoreList, "811w");
        }
        else if ("男".equals(sex) && age >= 12) { //第三种情况：12-14岁男性儿童
            sc = getScore(scoreList, "124m");
        }
        else if ("女".equals(sex) && age >= 12) {//第四种情况：12-14岁女性儿童
            sc = getScore(scoreList, "124w");
        }
        /*
         '次级人格因素
        'A B C D E F G H I J N  O  Q3 Q4
        '0 1 2 3 4 5 6 7 8 9 10 11 12 13
        Dim cj(2)
        '适应与焦虑：0.2×(d + o + q4 - q3) - 0.1×(c + h) + 4.4
        cj(0) = 0.2 * (sc(3) + sc(11) + sc(13) - sc(12)) - 0.1 * (sc(2) + sc(7)) + 4.4
        '性格内外向：0.33×(a + f + h) + 0.06
        cj(1) = 0.33 * (sc(0) + sc(5) + sc(7)) + 0.06
        '神经敏感性：0.13×(i + o + q4 - c - e - f - h) + 0.07×(d + j) + 5.45
        cj(2) = 0.13 * (sc(8) + sc(11) + sc(13) - sc(2) - sc(4) - sc(5) - sc(7)) + 0.07 * (sc(3) + sc(9)) + 5.45
         */
        if(sc!=null) {
            sc[14] = 0.2 * (sc[3] + sc[11] + sc[13] - sc[12]) - 0.1 * (sc[2] + sc[7]) + 4.4;
            sc[15] = 0.33 * (sc[0] + sc[5] + sc[7]) + 0.06;
            sc[16] = 0.13 * (sc[8] + sc[11] + sc[13] - sc[2] - sc[4] - sc[5] - sc[7]) + 0.07 * (sc[3] + sc[9]) + 5.45;
            int j = 0;
            for(ScaleFactorDto dto :listFactors)
            {
                var testScoreEntity = new TestScoreEntity();
                testScoreEntity.setRecordId(recordId);
                testScoreEntity.setFactorId(dto.getId());
                testScoreEntity.setOriginalScore(BigDecimal.valueOf(sc[j]));
                testScoreEntity.setScore(BigDecimal.valueOf(sc[j]));
                testScoreEntity.setIsAbnormal(isAbnormal ? 1:0);
                testScoreEntity.setAbnormalValue(BigDecimal.valueOf(0));
                saveScore(testScoreEntity);
                j++;
            }
            //结果解释
            var exId = new int[17];
            for (int i = 0; i < 17; i++)
            {
                if (sc[i] < 3) exId[i] = i * 3 + 1;
                else if (sc[i] >= 3 && sc[i] <= 8) exId[i] = i * 3 + 2;
                else exId[i] = i * 3 + 3;
            }
            String ex = "";
            for (int id : exId) {
                ex += id + ",";
            }
            String ids = StrUtil.removeSuffix(ex,",");
            var explainMap = get14pfExplain(ids);
            var responseText = new StringBuilder();
            int m=0;
            for(Map<String,Object> map: explainMap) {
                if(m < exId.length) {
                    var _type = map.get("type");
                    String substring = _type.toString().substring(0, _type.toString().length() - 1).toUpperCase();
                    responseText.append(String.format("<dt>%s（%s）</dt><dd>%s</dd>",
                            map.get("type_name"),
                            substring,
                            map.get("interpretation")
                            ));
                    var testRecordExplainEntity = new TestRecordExplainEntity();
                    testRecordExplainEntity.setRecordId(recordId);
                    testRecordExplainEntity.setFactorId(Integer.parseInt(map.get("factor_id").toString()));
                    testRecordExplainEntity.setInterpretation(String.format("[%s{%s}]%s",
                            map.get("type_name"),
                            substring,
                            map.get("interpretation")
                    ));
                    testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
                }
                m++;
            }
            interpretation = responseText.toString();
        }
    }

    private double[] getScore(int[] score, String lx)
    {
        var sc = new double[score.length];
        var norm = new int[14][11];
        var normMap = get14pfNorm(lx);
        int i = 0;
        for(LinkedHashMap<String, Object> map: normMap) {
            int j =0;
            for(Map.Entry<String,Object> entry: map.entrySet()){
                if(j <= 10 && entry.getKey().equals(String.valueOf(j))) {
                    norm[i][j] = Integer.parseInt(entry.getValue().toString());
                }
                j++;
            }
            i++;
        }

        //计算出标准分
        for (int j = 0; j < 14; j++)
        {
            i = score[j] == 0 ? 1 : score[j];
            sc[j] = norm[j][i];
        }
        return sc;
    }

    /**
     *  获取常模
     * @param lx 类型
     * @return 常模
     */
    private List<LinkedHashMap<String, Object>> get14pfNorm(String lx) {return  calcDao.get14pfNorm(lx);}

    /**
     *  获取结果解释
     * @param ids 集合
     * @return 集合
     */
    private List<Map<String, Object>> get14pfExplain(String ids) {return calcDao.get14pfExplain(ids);}
}

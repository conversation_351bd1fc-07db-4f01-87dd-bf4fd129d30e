package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dto.measuringroom.ScaleIDDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InitCalc {
    @Autowired
    private GeneralCalc generalCalc;
    @Autowired
    private SCL90 scl90;
    @Autowired
    private FourteenPF fourteenPF;
    @Autowired
    private SixteenPF sixteenPF;
    @Autowired
    private APMAndNineHouse apmAndNineHouse;
    @Autowired
    private BKYYS bkyys;
    @Autowired
    private BRL brl;
    @Autowired
    private CW70 cw70;
    @Autowired
    private DISC disc;
    @Autowired
    private EMBU embu;
    @Autowired
    private EPQC epqc;
    @Autowired
    private HLD hld;
    @Autowired
    private HXJN hxjn;
    @Autowired
    private MBTI mbti;
    @Autowired
    private MBTIS mbtis;
    @Autowired
    private MBTIB mbtib;
    @Autowired
    private MMPI mmpi;
    @Autowired
    private MSSD mssd;
    @Autowired
    private NineHouse nineHouse;
    @Autowired
    private PCM pcm;
    @Autowired
    private PSQI psqi;
    @Autowired
    private RCCP rccp;
    @Autowired
    private SAS sas;
    @Autowired
    private SCSQ scsq;
    @Autowired
    private SDS sds;
    @Autowired
    private SPM spm;
    @Autowired
    private STI sti;
    @Autowired
    private UPI upi;
    @Autowired
    private XXJL xxjl;
    @Autowired
    private ZYLX zylx;
    @Autowired
    private AQY aqy;
    @Autowired
    private JKSY jksy;

    public void calc(Integer recordId, Integer scaleId) {
        BaseCalc baseCalc;
        switch (scaleId) {
            case ScaleIDDto.SCL90:  //SCL-90症状自评量表
                baseCalc = scl90;
                break;
            case ScaleIDDto._14PF: //卡特尔十四种人格因素问卷
                baseCalc = fourteenPF;
                break;
            case ScaleIDDto._16PF: //卡特尔十六种人格因素问卷
                baseCalc = sixteenPF;
                break;
            case ScaleIDDto.APMAndNineHouse: //公安心理关爱测评
                baseCalc = apmAndNineHouse;
                break;
            case ScaleIDDto.BKYYS: //贝康育婴师问卷
                baseCalc = bkyys;
                break;
            case ScaleIDDto.BRL: //包容力测试
                baseCalc = brl;
                break;
            case ScaleIDDto.CW70: //智力测验
                baseCalc = cw70;
                break;
            case ScaleIDDto.DISC: //Disc性格测试
                baseCalc = disc;
                break;
            case ScaleIDDto.EMBU: //父母养育方式问卷(EMBU)
                baseCalc = embu;
                break;
            case ScaleIDDto.EPQC:  //艾森克人格问卷（少年式）
                baseCalc = epqc;
                break;
            case ScaleIDDto.HLD: //霍兰德职业
                baseCalc = hld;
                break;
            case ScaleIDDto.HXJN: //核心技能自我评价
                baseCalc = hxjn;
                break;
            case ScaleIDDto.MBTI: //MBTI职业性格测试
                baseCalc = mbti;
                break;
            case ScaleIDDto.MBTIS: //MBTI标准版
                baseCalc = mbtis;
                break;
            case ScaleIDDto.MBTIB: //MBTI简版
                baseCalc = mbtib;
                break;
            case ScaleIDDto.MMPI: //明尼苏达人格
                baseCalc = mmpi;
                break;
            case ScaleIDDto.MSSD: //精神症状自我诊断
                baseCalc = mssd;
                break;
            case ScaleIDDto.NineHouse: //九型人格测试
                baseCalc = nineHouse;
                break;
            case ScaleIDDto.PCM: //压力应对方式量表
                baseCalc = pcm;
                break;
            case ScaleIDDto.PSQI:  //匹兹堡睡眠指数
                baseCalc = psqi;
                break;
            case ScaleIDDto.RCCP: //RCCP通用职业匹配测试量表
                baseCalc = rccp;
                break;
            case ScaleIDDto.SAS: //焦虑自评量表（SAS）
                baseCalc = sas;
                break;
            case ScaleIDDto.SCSQ: //简易应对方式
                baseCalc = scsq;
                break;
            case ScaleIDDto.SDS: //抑郁自评量表(SDS)
                baseCalc = sds;
                break;
            case ScaleIDDto.SPM: //瑞文标准推理
                baseCalc = spm;
                break;
            case ScaleIDDto.STI: //斯特劳里气质类型
                baseCalc = sti;
                break;
            case ScaleIDDto.UPI: //大学生人格问卷
                baseCalc = upi;
                break;
            case ScaleIDDto.XXJL: //学习焦虑量表
                baseCalc = xxjl;
                break;
            case ScaleIDDto.ZYLX: //职业人格测验
                baseCalc = zylx;
                break;
            case ScaleIDDto.YDL_aqy: //安全员人格、心理健康风险、消极应对
                baseCalc = aqy;
                break;
            case ScaleIDDto.JKSY: //心理健康素养
                baseCalc = jksy;
                break;
            default:
                baseCalc = generalCalc;
                break;
        }
        baseCalc.compute(recordId);
    }
}

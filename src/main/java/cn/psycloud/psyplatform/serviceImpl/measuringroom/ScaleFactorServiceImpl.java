package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.dao.measuringroom.ScaleFactorDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorEntity;
import cn.psycloud.psyplatform.service.measuringroom.ScaleFactorService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class ScaleFactorServiceImpl implements ScaleFactorService {
    @Autowired
    private ScaleFactorDao scaleFactorDao;

    /**
     *  根据ID查询量表所有的因子集合：返回Bootstrap Datatables所需格式
     * @param scaleId 量表id
     * @return 因子集合
     */
    @Override
    public BSDatatableRes<ScaleFactorDto> getFactorsByScaleId(Integer scaleId) {
        var  dtRes = new BSDatatableRes<ScaleFactorDto>();
        var factors = scaleFactorDao.getFactorsByScaleId(scaleId);
        for(var factor: factors){
            if(factor.getFactorType() == 2 || factor.getFactorType() == 3) {
                StringBuilder factorNames = new StringBuilder();
                var factorIds = factor.getFactorIds();
                if(factorIds != null && !factorIds.isEmpty()) {
                   var arrayFactor = factorIds.split(",");
                   for(var arrayId: arrayFactor) {
                       var factorName = scaleFactorDao.getFactorNameById(Integer.parseInt(arrayId));
                       factorNames.append(factorName).append(",");
                   }
                }
                factor.setComputeOfName(factorNames.substring(0, factorNames.length() - 1));
            }
        }
        dtRes.setData(factors);
        dtRes.setRecordsFiltered(factors.size());
        dtRes.setRecordsTotal(factors.size());
        return dtRes;
    }

    /**
     *  查询因子集合集合：select
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public List<Object> getListForSelect(ScaleFactorDto dto) {
        var listFactors = scaleFactorDao.getListForSelect(dto);
        return getSelect2Data(listFactors);
    }

    private List<Object> getSelect2Data(List<ScaleFactorEntity> listFactors) {
        var lsNode = new ArrayList<>();
        for (ScaleFactorEntity entity : listFactors) {
            var select2Data = new Select2Data();
            select2Data.setId(entity.getId());
            select2Data.setText(entity.getFactorName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  新增因子
     * @param entity 因子实体对象
     * @return 影响行数
     */
    @Override
    public int addFactor(ScaleFactorEntity entity) {
        var factorCount = scaleFactorDao.getFactorCount(entity.getScaleId());
        entity.setFactorNo(factorCount + 1);
        return scaleFactorDao.insert(entity);
    }

    /**
     *  修改因子
     * @param entity 因子实体对象
     * @return 影响行数
     */
    @Override
    public int updateFactor(ScaleFactorEntity entity) {
        return scaleFactorDao.update(entity);
    }

    /**
     *  删除
     * @param factorId 因子id
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteById(int factorId) {
        boolean isSuccess = false;
        try{
            var map = scaleFactorDao.getById(factorId);
            scaleFactorDao.delete(factorId);
            scaleFactorDao.updateFactorNo(map);
            isSuccess = true;
        }
        catch (Exception e) {
            log.error("删除因子发生错误："+e.getMessage());
        }
        return isSuccess;
    }

    /**
     *  因子排序
     * @param factorId 因子id
     * @param flag 排序标识
     * @return 影响行数
     */
    @Override
    public boolean sort(Integer factorId,String flag) {
        boolean isSuccess = false;
        try{
            var map= scaleFactorDao.getById(factorId);
            var scaleId = map.get("scale_id");
            var factorNo = map.get("factor_no");
            var sortMap = new HashMap<String, Integer>();
            sortMap.put("scaleId",scaleId);
            sortMap.put("factorNo",factorNo);
            sortMap.put("factorId",factorId);
            //上移
            if ("up".equals(flag)) {
                if(factorNo == 1) return  false;
                scaleFactorDao.moveUpOther(sortMap); //移动其它
                scaleFactorDao.moveUpSelf(sortMap);
                isSuccess = true;
            }
            //下移
            if ("down".equals(flag)) {
                var maxSort = scaleFactorDao.getFactorCount(scaleId);
                if(maxSort == 0) return false;
                if(maxSort == factorNo) return  false;
                scaleFactorDao.moveDownOther(sortMap);
                scaleFactorDao.moveDownSelf(sortMap);
                isSuccess = true;
            }
        }
        catch (Exception e) {
            log.error("因子排序出现错误：" + e.getMessage());
        }
        return isSuccess;
    }

    /**
     * 获取因子总数
     * @param scaleId 量表id
     * @return 因子数
     */
    @Override
    public int getFactorCount(Integer scaleId) {
        return scaleFactorDao.getFactorCount(scaleId);
    }

    /**
     *  根据因子id查询因子名称
     * @param factorId 因子id
     * @return 因子名称
     */
    @Override
    public String getFactorNameById(Integer factorId) {
        return scaleFactorDao.getFactorNameById(factorId);
    }
}

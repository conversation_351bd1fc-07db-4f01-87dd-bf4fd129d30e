package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class UPI extends BaseCalc{
    @Autowired
    private TestResultDao testResultDao;
    @Override
    public void compute(){
        computeCommonFactor(true);
        //计算总分
        var totalScore = computeTotalScore();
        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(0);
        //region 测谎
        BigDecimal lieScore = listFactorScores.get(0);
        var responseText = new StringBuilder();
        boolean lie = (lieScore.compareTo(BigDecimal.valueOf(3)) >= 0 && totalScore.compareTo(BigDecimal.valueOf(25)) >= 0) || (lieScore.compareTo(BigDecimal.valueOf(1)) <= 0&& totalScore.compareTo(BigDecimal.valueOf(5)) <= 0);
        if (lie) {
            state = 2;
            responseText.append(String.format("总分得分：%.2f，同时测谎得分：%.2f。掩饰性高，结果不可信。", totalScore, lieScore));
            interpretation = responseText.toString();
            testRecordExplainEntity.setInterpretation(interpretation);
            testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
            return;
        }
        //endregion
        var listResults = testResultDao.getList(recordId);
        /*
        A类筛选标准(满足一个即可)：
        (1) UPI总分在25分(包括25分)以上者；
        (2) 第25题做肯定选择者；
        (3) 辅助题中同时至少有两题做肯定选择者；
        (4) 明确提出咨询要求者(由于此条选择人数较多，有时不用)；
        */
        if (totalScore.compareTo(BigDecimal.valueOf(25)) >= 0 || Integer.parseInt(listResults.get(24).getANos()) == 1 || (listResults.get(60).getAScore().add(listResults.get(61).getAScore()).add(listResults.get(62).getAScore()).add(listResults.get(63).getAScore()).add(listResults.get(64).getAScore()).add( listResults.get(65).getAScore()).add(listResults.get(66).getAScore())).compareTo(BigDecimal.valueOf(2)) >= 0) {
            isAbnormal = true;
            responseText.append(String.format("总分：%s(>=25)；第25题选<是>；辅助题中做肯定选择的项目数大于2。<br/>因此被试者属于A类情况。表现为心理异常，如精神病（如精神分裂症等）和各类神经症(如恐怖症、强迫症、焦虑症、抑郁症等)，明显影响正常生活者。处理建议：建议进一步预约心理咨询，并可选择相应专业心理测试作为诊断依据。对于进一步确诊为A类的学生，一般处理意见为转介至当地心理门诊或精神科。", totalScore));
        }
        /*
        B类筛选标准(满足一个即可)：
        (1) UPI总分在20分至25分(包括20分,不包括25分)之间者；
        (2) 第8，16，26题中有一题做肯定选择者；
        (3) 辅助题中只有一题作肯定选择者；
        */
        else if (totalScore.compareTo(BigDecimal.valueOf(20)) >= 0 || totalScore.compareTo(BigDecimal.valueOf(25)) < 0 || (listResults.get(7).getAScore().add(listResults.get(15).getAScore()).add(listResults.get(25).getAScore())).compareTo(BigDecimal.valueOf(1)) >= 0 && (listResults.get(60).getAScore().add(listResults.get(61).getAScore()).add(listResults.get(62).getAScore()).add(listResults.get(63).getAScore()).add(listResults.get(64).getAScore()).add(listResults.get(65).getAScore()).add(listResults.get(66).getAScore())).compareTo(BigDecimal.valueOf(1)) == 0) {
            int aNo = 0;
            if (listResults.get(7).getAScore().compareTo(BigDecimal.valueOf(1)) == 0)
                aNo = 8;
            if (listResults.get(15).getAScore().compareTo(BigDecimal.valueOf(1)) == 0)
                aNo = 16;
            if (listResults.get(25).getAScore().compareTo(BigDecimal.valueOf(1)) == 0)
                aNo = 26;
            isAbnormal = true;
            responseText.append(String.format("总分：%s(>=20并且<=25)；第{aNo}题选“是”；辅助题中做肯定选择的项目数只有1项。<br/>因此被试者属于B类情况。表现为心理正常，但存在一定程度的心理问题（严重或一般心理问题），如人际关系不协调，新环境不适应等，有一定时间的病程，但心理问题没有充分泛化，仍能够维持正常学习和生活。处理建议：建议进一步预约心理咨询，并可选择相应专业心理测试作为诊断依据，排除A类可能性。对于确诊为B类的学生，可建议其到专业心理咨询机构进行心理咨询，直到症状明显好转或消失。",totalScore));
        }
        /*
         不属于A类和B类者应归为C类。
         */
        else
        {
            isAbnormal = false;
            responseText.append(String.format("总分：%s；此被试者属于C类情况。表现为心理正常，存在一般心理问题，但症状不明显或已经解决。处理建议：建议通过面谈，排除A、B类可能性；告知心理咨询的原则，目的和意义，提醒该类学生在出现心理困扰时及时向心理中心或专业心理服务机构求助。",totalScore));
        }
        interpretation = responseText.toString();
        testRecordExplainEntity.setInterpretation(interpretation.replace("<br/>","\r"));
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

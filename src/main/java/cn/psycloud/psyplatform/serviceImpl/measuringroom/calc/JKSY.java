package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.dao.measuringroom.TestResultDao;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  心理健康素养
 */
@Service
public class JKSY  extends BaseCalc{
    @Autowired
    private TestResultDao testResultDao;

    @Override
    public void compute(){
        computeCommonFactor(true);
        var listResults = testResultDao.getList(recordId);
        listResults = listResults.stream().sorted(Comparator.comparing(TestResultDto::getQNo)).collect(Collectors.toList());
        List<Integer> toAnswers  = new ArrayList<>();
        int[] answer = new int[listResults.size()];
        int k = 0;
        for(TestResultDto testResult: listResults) {
            answer[k] = testResult.getAScore().intValue();
            if(k>=30 && k <60){
                if(answer[k]== 1 || answer[k] ==2 || answer[k]==3) {
                    toAnswers.add(0);
                }
                if(answer[k]==4 || answer[k]==5) {
                    toAnswers.add(1);
                }
            }
            k++;
        }
        var totalFactorScore =(listFactorScores.get(listFactorIds.size()-2)).intValue();
        for (Integer toAnwser: toAnswers) {
            totalFactorScore += toAnwser;
        }
        var testScoreDto = new TestScoreDto();
        testScoreDto.setRecordId(recordId);
        testScoreDto.setFactorId(listFactorIds.get(listFactorScores.size() - 2));
        testScoreDto.setOriginalScore(BigDecimal.valueOf(totalFactorScore));
        testScoreDto.setScore(BigDecimal.valueOf(totalFactorScore));
        testScoreDao.updateXlsyTotalScorer(testScoreDto);
        //结果解释
        interpretation = getFactorExplain();
    }
}

package cn.psycloud.psyplatform.serviceImpl.measuringroom.calc;

import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestScoreEntity;
import lombok.var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *  智力测验
 */
@Service
public class CW70 extends BaseCalc{
    @Override
    public void compute() {
        BigDecimal score = computeTotalScore(); //计算原始分数
        BigDecimal sc = new BigDecimal(0); //计算因子分
        if (score.compareTo(BigDecimal.valueOf(2)) >= 0 && score.compareTo(BigDecimal.valueOf(5)) <= 0) {
            sc = (score.subtract(BigDecimal.valueOf(2))).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(94));
        }
        else if (score.compareTo(BigDecimal.valueOf(6)) == 0) {
            sc = BigDecimal.valueOf(103);
        }
        else if (score.compareTo(BigDecimal.valueOf(7)) >= 0 && score.compareTo(BigDecimal.valueOf(14)) <= 0) {
            sc = (score.subtract(BigDecimal.valueOf(7))).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(105));
        }
        else if (score.compareTo(BigDecimal.valueOf(15)) == 0) {
            sc = BigDecimal.valueOf(122);
        }
        else if (score.compareTo(BigDecimal.valueOf(16)) >= 0 && score.compareTo(BigDecimal.valueOf(24)) <= 0) {
            sc = (score.subtract(BigDecimal.valueOf(16))).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(123));
        }
        else if (score.compareTo(BigDecimal.valueOf(25)) == 0) {
            sc = BigDecimal.valueOf(143);
        }
        else if (score.compareTo(BigDecimal.valueOf(26)) >= 0 && score.compareTo(BigDecimal.valueOf(33)) <= 0) {
            sc = (score.subtract(BigDecimal.valueOf(26))).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(145));
        }
        else if (score.compareTo(BigDecimal.valueOf(34)) == 0) {
            sc = BigDecimal.valueOf(162);
        }
        else if (score.compareTo(BigDecimal.valueOf(35)) >= 0 && score.compareTo(BigDecimal.valueOf(42)) <= 0) {
            sc = (score.subtract(BigDecimal.valueOf(35))).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(164));
        }
        else if (score.compareTo(BigDecimal.valueOf(43)) == 0) {
            sc = BigDecimal.valueOf(181);
        }
        else if (score.compareTo(BigDecimal.valueOf(44)) >= 0 && score.compareTo(BigDecimal.valueOf(52)) <= 0) {
            sc = (score.subtract(BigDecimal.valueOf(44))).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(183));
        }
        else if (score.compareTo(BigDecimal.valueOf(53)) == 0) {
            sc = BigDecimal.valueOf(202);
        }
        else if (score.compareTo(BigDecimal.valueOf(54)) >= 0 && score.compareTo(BigDecimal.valueOf(55)) <= 0) {
            sc = (score.subtract(BigDecimal.valueOf(54))).multiply(BigDecimal.valueOf(2)).add(BigDecimal.valueOf(204));
        }
        else if (score.compareTo(BigDecimal.valueOf(56)) >= 0 && score.compareTo(BigDecimal.valueOf(62)) <= 0) {
            sc =  (score.subtract(BigDecimal.valueOf(56))).multiply(BigDecimal.valueOf(3)).add(BigDecimal.valueOf(209));
        }
        else if (score.compareTo(BigDecimal.valueOf(63)) == 0) {
            sc = BigDecimal.valueOf(231);
        }
        else if (score.compareTo(BigDecimal.valueOf(64)) >= 0 && score.compareTo(BigDecimal.valueOf(68)) <= 0) {
            sc =  (score.subtract(BigDecimal.valueOf(64))).multiply(BigDecimal.valueOf(3)).add(BigDecimal.valueOf(235));
        }
        else if (score.compareTo(BigDecimal.valueOf(69)) == 0) {
            sc = BigDecimal.valueOf(251);
        }
        else if (score.compareTo(BigDecimal.valueOf(70)) == 0) {
            sc = BigDecimal.valueOf(254);
        }

        int mm, IQ, grade = 4;
        mm = age * 12;
        if (mm == 0) mm = 1;
        if (mm > 182) mm = 182;

        IQ = sc.multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(mm), 2,RoundingMode.HALF_UP).intValue();

        if (IQ < 60) grade = 1;
        else if (IQ < 70) grade = 2;
        else if (IQ < 130) grade = 3;

        isAbnormal = grade < 3;
        var factorId = getTotalScoreFactorId();
        var testScoreEntity = new TestScoreEntity();
        testScoreEntity.setRecordId(recordId);
        testScoreEntity.setFactorId(factorId);
        testScoreEntity.setOriginalScore(score);
        testScoreEntity.setScore(score);
        testScoreEntity.setIsAbnormal(isAbnormal?1:0);
        testScoreEntity.setAbnormalValue(BigDecimal.valueOf(130));
        saveScore(testScoreEntity);

        var factorExplain = "";
        if (grade == 1){
            factorExplain =
                    "从该智力测量结果表明你的智力发展状况和同年龄的其他正常儿童相比较，你的智力发展可能存在一定的缺陷。" +
                    "你的大脑神经可能遭受到过如铅中毒、缺氧等损害，也有可能你长期营养不良或先天发育不良。建议你在老师或" +
                    "父母的陪同下到医院进行相应的测试并及早进行诊治。（切记：一次性智力测验并不能说明问题，本测量结果仅作参考！）<br>" +
                    "【建议】<br>" +
                    "1）在老师或父母的陪同下去医院做详细的检查和诊断，及早进行治疗；<br>" +
                    "2）如果医生诊断为智力发展迟缓，和医生一起商量教育方案；<br>"+
                    "3）可与特殊儿童学校联系，并向那里的老师咨询，可能应该接受特殊教育。";
        }
        if (grade == 2) {
            factorExplain =
                    "测量结果表明，你的智力发展水平和同年龄的儿童相比相对偏低。造成这种状态有可能是因为你没有认真去对待"+
                    "这次测试，也有可能是因为你长期缺乏必要的教育环境，也有可能是你的智力确实存在某些缺陷，但你不必灰心"+
                    "丧气，人的成功是多方面的因素，只要你好好学习、天天向上，今后你的成就说不定比别人都高。（切记：一次"+
                    "性智力测验并不能说明问题！）<br>【建议】<br>1）不要灰心丧气，智力只是众多能力要素中的一个方面，而且"+
                    "智力有一个发展的过程，此时的结果并不代表以后你的智力发展水平；<br>2）“勤能补拙”，摆正自己的位置，"+
                    "努力学习，积极发挥自己的其他优势，相信自己能够成功。";
        }
        if (grade == 3) {
            factorExplain =
                    "该智力量表测量结果表明你的智力发展状况和同年龄的其他儿童相比相对正常，没有智力方面的障碍。但请记住，"+
                    "一个人的成功是多方面的，只要你好好学习、天天向上，今后你一定大有作为的。（切记：一次性智力测验并不能说明问题！）<br>"+
                    "【建议】<br>1）全面地认识自己，智力只是众多能力要素中的一个方面，而要取得成功也是多方面因素共同作用的结果，"+
                    "因此了解自己其他方面的情况,扬长避短是很重要的；<br>2）注重方式方法，很多人智力水平都差不多，但是学习成绩有好有差，"+
                    "取得的成就有大有小，关键是找到适合自己的学习方法，才能事半功倍；<br>3）努力学习，一份耕耘一份收获。";
        }
        if (grade == 4) {
            factorExplain =
                    "恭喜你，从测量的结果来看，你的智力发展水平和同年龄的其他儿童相比较非常高，也就是说你很聪明。如果你"+
                    "能够将你的聪明才智用到学习上，并能持之以恒，开拓创新，你今后一定会很棒的！（切记：一次性智力测验并不能说明问题！）<br>"+
                    "【建议】<br>1）全面地认识自己，智力只是众多能力要素中的一个方面，而要取得成功也是多方面因素共同作用的结果，"+
                    "因此了解自己其他方面的情况是很重要的；<br>2）切忌骄傲自满，要踏实，注意细节，聪明的人往往在小事上犯粗心大意的毛病；<br>"+
                    "3）努力学习，也许你不必用尽全力就可以取得好的学习成绩，但是如果再加上勤奋刻苦的话，会百尺竿头更进一步；<br>"+
                    "4）虚心接受别人的意见；<br>5）多与别人交流，平等真诚待人，不要因为自己智力比较高就看不起别人，朋友是人生中宝贵的财富。";
        }
        interpretation = String.format("原始得分：%.2f，相对得分：%.2f，IQ得分：%d<br />%s",score, sc, IQ, factorExplain);
        var testRecordExplainEntity = new TestRecordExplainEntity();
        testRecordExplainEntity.setRecordId(recordId);
        testRecordExplainEntity.setFactorId(factorId);
        testRecordExplainEntity.setInterpretation(interpretation);
        testRecordDao.saveTestRecordExplain(testRecordExplainEntity);
    }
}

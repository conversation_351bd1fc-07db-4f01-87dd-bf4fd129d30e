package cn.psycloud.psyplatform.serviceImpl.counselingroom;

import cn.psycloud.psyplatform.dao.counselingroom.CounselingQuestionRespondDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionRespondDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionRespondEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingQuestionRespondService;
import cn.psycloud.psyplatform.util.SessionUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class CounselingQuestionRespondServiceImpl implements CounselingQuestionRespondService {
    @Autowired
    private CounselingQuestionRespondDao counselingQuestionRespondDao;
    /**
     *  发表回复
     * @param entity 回复实体对象
     * @return 影响行数
     */
    @Override
    public int post(CounselingQuestionRespondEntity entity){
        var user = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setResponder(user.getUserId());
        entity.setAddDate(new Date());
        return counselingQuestionRespondDao.insert(entity);
    }

    /**
     *  删除回复
     * @param id 回复id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return counselingQuestionRespondDao.delete(id);
    }

    /**
     *  查询答疑所有回复
     * @param questionId 提问Id
     * @return 回复集合
     */
    @Override
    public List<CounselingQuestionRespondDto> getListById(Integer questionId){
        return counselingQuestionRespondDao.getListByQuestionId(questionId);
    }
}

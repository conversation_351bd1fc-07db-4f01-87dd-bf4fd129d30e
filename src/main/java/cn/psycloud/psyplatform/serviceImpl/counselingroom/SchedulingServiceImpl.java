package cn.psycloud.psyplatform.serviceImpl.counselingroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.dao.counselingroom.SchedulingDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.FullCalendarData;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto;
import cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto;
import cn.psycloud.psyplatform.entity.counselingroom.SchedulingEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingOrderService;
import cn.psycloud.psyplatform.service.counselingroom.SchedulingService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.SessionUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class SchedulingServiceImpl implements SchedulingService {
    @Autowired
    private SchedulingDao schedulingDao;
    @Autowired
    private CounselingOrderService counselingOrderService;
    /**
     *  组装查询条件
     * @param dto 查询条件
     * @return 查询条件
     */
    private SchedulingDto getFilter(SchedulingDto dto){
        var childStructsIds = PermissonHelper.getChildStructIds(0);
        dto.setChildStructs(childStructsIds);
        return dto;
    }

    /**
     *  获取排班信息集合
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public List<Object> getList(SchedulingDto dto){
        var listSchdulings =  schedulingDao.getList(getFilter(dto));
        return getListForFullCalendar(listSchdulings);
    }

    /**
     *  把数据形式转换成fullCalendar的json数据格式
     * @param listSchedulings 排班集合
     * @return 排班集合
     */
    private List<Object> getListForFullCalendar(List<SchedulingDto> listSchedulings) {
        var lsNode = new ArrayList<>();
        for(SchedulingDto scheduling: listSchedulings){
            var counselingOrderDto = new CounselingOrderDto();
            counselingOrderDto.setSchedulingId(scheduling.getId());
            counselingOrderDto = counselingOrderService.getBySchedulingId(counselingOrderDto.getSchedulingId());
            String eventColor;
            if (DateUtil.compare(scheduling.getEndTime(),new Date()) < 0)
                eventColor = "#98a6ad";
            else if (counselingOrderDto==null || (counselingOrderDto!=null&&(counselingOrderDto.getId() == 0 || counselingOrderDto.getState() == 2)))
                eventColor = "#0acf97";
            else
                eventColor = "#727cf5";
            var fullCalendarData = new FullCalendarData();
            fullCalendarData.setId(scheduling.getId());
            fullCalendarData.setTitle(scheduling.getCounselorName());
            fullCalendarData.setStart(DateUtil.date(scheduling.getStartTime()));
            fullCalendarData.setEnd(DateUtil.date(scheduling.getEndTime()));
            fullCalendarData.setColor(eventColor);
            lsNode.add(fullCalendarData);
        }
        return lsNode;
    }

    /**
     *  获取预约的排班信息集合
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public List<Object> getOrderSchedulingList(SchedulingDto dto){
        dto.setEndTime(new Date());
        var listSchedulings = schedulingDao.getOrderSchedulingList(getFilter(dto));
        return getListForFullCalendar(listSchedulings);
    }

    /**
     *  排班
     * @param entity 排班实体对象
     * @return 影响行数
     */
    @Override
    public int arrange(SchedulingEntity entity){
        var user = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setOperator(user.getUserId());
        entity.setSchedueDate(new Date());
        return schedulingDao.insert(entity);
    }

    /**
     *  修改排班
     * @param entity 排班实体对象
     * @return 影响行数
     */
    @Override
    public int updateScheduling(SchedulingEntity entity){
        return schedulingDao.update(entity);
    }

    /**
     *  删除排班
     * @param id 排班id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return schedulingDao.delete(id);
    }

    /**
     *  验证咨询师的排班是否重复
     * @param dto  排班实体对象
     * @return 是否重复
     */
    @Override
    public int isSchedulingExists(SchedulingDto dto){
        return schedulingDao.isSchedulingExist(dto);
    }
}

package cn.psycloud.psyplatform.serviceImpl.counselingroom;

import cn.psycloud.psyplatform.dao.counselingroom.CounselingOrderDao;
import cn.psycloud.psyplatform.dao.counselingroom.CounselingRecordDao;
import cn.psycloud.psyplatform.dao.counselingroom.SchedulingDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingOrderEntity;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingRecordEntity;
import cn.psycloud.psyplatform.entity.counselingroom.SchedulingEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingRecordService;
import cn.psycloud.psyplatform.util.SessionUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CounselingRecordServiceImpl implements CounselingRecordService {
    @Autowired
    private CounselingRecordDao counselingRecordDao;
    @Autowired
    private SchedulingDao schedulingDao;
    @Autowired
    private CounselingOrderDao counselingOrderDao;
    /**
     *  保存咨询记录
     * @param entity 咨询记录实体对象
     * @return 影响行数
     */
    @Override
    public int addContent(CounselingRecordEntity entity){
        return counselingRecordDao.insert(entity);
    }

    /**
     *  根据预约id查询咨询记录
     * @param id 预约id
     * @return 咨询记录集合
     */
    @Override
    public List<CounselingRecordEntity> getList(Integer id){
        return counselingRecordDao.getList(id);
    }

    /**
     *  手动添加咨询记录
     * @param dto 咨询预约实体类
     * @return 执行是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addCounselingRecord(CounselingOrderDto dto) {
        boolean isSuccess = false;
        try{
            //region 添加排班信息
            var schedulingEntity = new SchedulingEntity();
            schedulingEntity.setCounselorId(dto.getCounselorId());
            schedulingEntity.setStartTime(dto.getStartTime());
            schedulingEntity.setEndTime(dto.getEndTime());
            schedulingEntity.setSchedueDate(new Date());
            var currentUser = (UserDto) SessionUtil.getSession().getAttribute("user");
            schedulingEntity.setOperator(currentUser.getUserId());
            schedulingEntity.setId(0);
            schedulingDao.insert(schedulingEntity);
            //endregion
            Integer schedulingId = schedulingEntity.getId();
            //region 添加咨询预约信息
            var counselingOrderEntity = new CounselingOrderEntity();
            counselingOrderEntity.setVisitorId(dto.getVisitorId());
            counselingOrderEntity.setStartTime(dto.getStartTime());
            counselingOrderEntity.setEndTime(dto.getEndTime());
            counselingOrderEntity.setCounselingTypeId(dto.getCounselingTypeId());
            counselingOrderEntity.setCounselingWay(dto.getCounselingWay());
            counselingOrderEntity.setIsPoint(dto.getIsPoint());
            counselingOrderEntity.setSelfComment(dto.getSelfComment());
            counselingOrderEntity.setDescription(dto.getDescription());
            counselingOrderEntity.setSchedulingId(schedulingId);
            counselingOrderEntity.setCounselingItemId(dto.getCounselingItemId());
            counselingOrderEntity.setState(3);
            counselingOrderEntity.setAddDate(new Date());
            counselingOrderEntity.setOperator(currentUser.getUserId());
            counselingOrderEntity.setId(0);
            counselingOrderDao.insert(counselingOrderEntity);
            //endregion
            Integer orderId = counselingOrderEntity.getId();
            //region 添加咨询内容
            var counselingRecordEntity = new CounselingRecordEntity();
            counselingRecordEntity.setOrderId(orderId);
            counselingRecordEntity.setCounselingContent(dto.getCounselingContent().get(0).getCounselingContent());
            counselingRecordEntity.setSendTime(new Date());
            counselingRecordEntity.setRealName(currentUser.getRealName());
            counselingRecordDao.insert(counselingRecordEntity);
            //endregion
            isSuccess = true;
        }
        catch (Exception e){
            log.error("手动添加咨询记录时发生错误：{}",e.getMessage());
            throw new RuntimeException();
        }
        return isSuccess;
    }
}

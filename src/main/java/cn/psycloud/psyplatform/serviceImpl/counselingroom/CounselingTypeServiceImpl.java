package cn.psycloud.psyplatform.serviceImpl.counselingroom;

import cn.psycloud.psyplatform.dao.counselingroom.CounselingTypeDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingTypeDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingTypeEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingTypeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CounselingTypeServiceImpl implements CounselingTypeService {
    @Autowired
    private CounselingTypeDao counselingTypeDao;
    /**
     *  查询集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<CounselingTypeEntity> getListByPaged(CounselingTypeDto dto){
        var dtRes = new BSDatatableRes<CounselingTypeEntity>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listCounselingTypes = counselingTypeDao.getList(dto);
        var counselingTypeDto = new PageInfo<>(listCounselingTypes);
        dtRes.setData(listCounselingTypes);
        dtRes.setRecordsTotal((int) counselingTypeDto.getTotal());
        dtRes.setRecordsFiltered((int) counselingTypeDto.getTotal());
        return dtRes;
    }

    /**
     *  查询集合
     * @param dto 查询条件
     * @return 集合
     */
    public List<CounselingTypeEntity> getList(CounselingTypeDto dto){
        return counselingTypeDao.getList(dto);
    }

    /**
     *  将数据格式转换成select2格式
     * @param listCounselingTypes 问题分类集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<CounselingTypeEntity> listCounselingTypes) {
        var lsNode = new ArrayList<>();
        for (CounselingTypeEntity entity : listCounselingTypes) {
            var select2Data = new Select2Data();
            select2Data.setId(entity.getId());
            select2Data.setText(entity.getCounselingType());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  查询集合：转换成select所需格式
     * @return 集合
     */
    @Override
    public List<Object> getListForSelect(CounselingTypeDto dto) {
        var listCounselingTypes =counselingTypeDao.getList(dto);
        return getSelect2Data(listCounselingTypes);
    }

    /**
     *  新增
     * @param counselingTypeEntity 实体对象
     * @return 影响行数
     */
    @Override
    public int add(CounselingTypeEntity counselingTypeEntity){
        return counselingTypeDao.insert(counselingTypeEntity);
    }

    /**
     *  更新
     * @param counselingTypeEntity 实体对象
     * @return 影响行数
     */
    @Override
    public int update(CounselingTypeEntity counselingTypeEntity){
        return counselingTypeDao.update(counselingTypeEntity);
    }

    /**
     *  删除
     * @param id 类型id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return counselingTypeDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }
}

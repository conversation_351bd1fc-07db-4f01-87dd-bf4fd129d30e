package cn.psycloud.psyplatform.serviceImpl.counselingroom;

import cn.psycloud.psyplatform.dao.counselingroom.CounselingQuestionDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingQuestionService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class CounselingQuestionServiceImpl implements CounselingQuestionService {
    @Autowired
    private CounselingQuestionDao counselingQuestionDao;
    /**
     *  根据ID查询
     * @param questionId 问题id
     * @return 问题实体对象
     */
    @Override
    public CounselingQuestionDto getById(Integer questionId){
        return counselingQuestionDao.getById(questionId);
    }

    /**
     *  提问
     * @param entity 心理答疑实体对象
     * @return 提问id
     */
    @Override
    public int post(CounselingQuestionEntity entity){
        entity.setAddDate(new Date());
        var user = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setQuestioner(user.getUserId());
        entity.setId(0);
        counselingQuestionDao.insert(entity);
        return entity.getId();
    }

    /**
     *  删除心理答疑
     * @param id 提问id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return counselingQuestionDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  修改
     * @param entity 心理答疑实体对象
     * @return 影响行数
     */
    @Override
    public int update(CounselingQuestionEntity entity){
        return counselingQuestionDao.update(entity);
    }

    /**
     *  获取心理答疑集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<CounselingQuestionDto> getListByPaged(CounselingQuestionDto dto){
        var dtRes = new BSDatatableRes<CounselingQuestionDto>();
        var user = (UserDto)SessionUtil.getSession().getAttribute("user");
        if(dto.getIsMyResponds() != null && dto.getIsMyResponds() == 1){
            dto.setResponder(user.getUserId());
        }
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listQuestions = counselingQuestionDao.getList(dto);
        var counselingQuestionDto = new PageInfo<>(listQuestions);
        dtRes.setData(counselingQuestionDto.getList());
        dtRes.setRecordsTotal((int)counselingQuestionDto.getTotal());
        dtRes.setRecordsFiltered((int)counselingQuestionDto.getTotal());
        return dtRes;
    }
}

package cn.psycloud.psyplatform.serviceImpl.counselingroom;

import cn.psycloud.psyplatform.dao.counselingroom.CounselingItemDao;
import cn.psycloud.psyplatform.dao.counselingroom.SchedulingDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingItemDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingItemEntity;
import cn.psycloud.psyplatform.service.counselingroom.CounselingItemService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CounselingItemServiceImpl implements CounselingItemService {
    @Autowired
    private CounselingItemDao counselingItemDao;
    @Autowired
    private SchedulingDao schedulingDao;
    /**
     *  根据ID查询咨询类型
     * @param id 咨询类型id
     * @return 咨询类型实体对象
     */
    @Override
    public CounselingItemDto getById(Integer id){
        return counselingItemDao.getById(id);
    }

    /**
     *  查询集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<CounselingItemDto> getListByPaged(CounselingItemDto dto){
        var dtRes = new BSDatatableRes<CounselingItemDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listCounselingItems = counselingItemDao.getList(dto);
        var counselingItemDto = new PageInfo<>(listCounselingItems);
        dtRes.setData(counselingItemDto.getList());
        dtRes.setRecordsTotal((int)counselingItemDto.getTotal());
        dtRes.setRecordsFiltered((int)counselingItemDto.getTotal());
        return dtRes;
    }

    /**
     *  将数据格式转换成select2格式
     * @param listCounselingItems 问题分类集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<CounselingItemDto> listCounselingItems) {
        var lsNode = new ArrayList<>();
        for (CounselingItemDto dto : listCounselingItems) {
            var select2Data = new Select2Data();
            select2Data.setId(dto.getId());
            select2Data.setText(dto.getCounselingItemName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  根据咨询师查询集合：转换成select所需格式
     * @param counselorId 咨询师id
     * @return 集合
     */
    @Override
    public List<Object> getListForSelectByCounselor(Integer counselorId){
        var dto = new CounselingItemDto();
        dto.setUserId(counselorId);
        var listCounselingItems =counselingItemDao.getList(dto);
        return getSelect2Data(listCounselingItems);
    }

    /**
     *  根据排班查询集合：转换成select所需格式
     * @param schedulingId 排班id
     * @return 集合
     */
    public List<Object> getListForSelect(Integer schedulingId){
        var schedulingDto = schedulingDao.getById(schedulingId);
        return getListForSelectByCounselor(schedulingDto.getCounselorId());
    }

    /**
     *  新增
     * @param entity  咨询类型实体对象
     * @return 影响行数
     */
    @Override
    public int addCounselingItem(CounselingItemEntity entity){
        return counselingItemDao.insert(entity);
    }

    /**
     *  更新
     * @param entity 咨询类型实体对象
     * @return 影响行数
     */
    @Override
    public int updateCounselingItem(CounselingItemEntity entity){
        return counselingItemDao.update(entity);
    }

    /**
     *  删除
     * @param id 咨询类型id
     * @return 影响行数
     */
    @Override
    public int deleteById(Integer id){
        return counselingItemDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = deleteById(id) > 0 ;
        }
        return isSuccess;
    }
}

package cn.psycloud.psyplatform.serviceImpl.counselingroom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dao.anteroom.MailDao;
import cn.psycloud.psyplatform.dao.counselingroom.CounselingOrderDao;
import cn.psycloud.psyplatform.dao.counselingroom.SchedulingDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto;
import cn.psycloud.psyplatform.dto.platform.SendSmsDto;
import cn.psycloud.psyplatform.entity.anteroom.MailEntity;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingOrderEntity;
import cn.psycloud.psyplatform.service.anteroom.UserService;
import cn.psycloud.psyplatform.service.counselingroom.CounselingOrderService;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.sms.aliyun.SendSmsService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CounselingOrderServiceImpl implements CounselingOrderService {
    @Autowired
    private CounselingOrderDao counselingOrderDao;
    @Autowired
    private SchedulingDao schedulingDao;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private MailDao mailDao;
    @Autowired
    private UserService userService;
    @Autowired
    private SendSmsService sendSmsService;
    /**
     *  根据排班id查询预约信息
     * @param schedulingId 排班id
     * @return 预约信息
     */
    @Override
    public CounselingOrderDto getBySchedulingId(Integer schedulingId){
        var counselingOrderDto = new CounselingOrderDto();
        counselingOrderDto.setSchedulingId(schedulingId);
        return counselingOrderDao.get(counselingOrderDto);
    }

    /**
     *  分页查询预约咨询记录
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<CounselingOrderDto> getListByPaged(CounselingOrderDto dto){
        var dtRes = new BSDatatableRes<CounselingOrderDto>();
        if(dto.getVisitorId() == null || dto.getVisitorId() ==0){
            dto.setChildStructs(PermissonHelper.getChildStructIds(0));
        }
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listOrders = counselingOrderDao.getList(dto);
        var counselingOrderDto = new PageInfo<>(listOrders);
        dtRes.setData(counselingOrderDto.getList());
        dtRes.setRecordsTotal((int)counselingOrderDto.getTotal());
        dtRes.setRecordsFiltered((int)counselingOrderDto.getTotal());
        return dtRes;
    }

    /**
     *  根据id查询预约信息
     * @param id 预约id
     * @return 预约信息
     */
    @Override
    public CounselingOrderDto getById(Integer id) {
        var counselingOrderDto = new CounselingOrderDto();
        counselingOrderDto.setId(id);
        return counselingOrderDao.get(counselingOrderDto);
    }

    /**
     *  删除预约
     * @param id 预约id
     * @return 影响行数
     */
    @Override
    public int deleteOrder(Integer id){
        return counselingOrderDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids 预约id集合
     * @return 执行是否成功
     */
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = deleteOrder(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  添加预约
     * @param entity 预约信息实体对象
     * @return 影响行数
     */
    @Override
    public int addOrder(HttpServletRequest request, CounselingOrderEntity entity){
        if (counselingOrderDao.insert(entity) > 0) {
            var sysConfig = sysConfigService.get();
            var schedulingDto = schedulingDao.getById(entity.getSchedulingId());
            //region 发送预约消息（站内信）
            if (sysConfig.getIsCounselingNotify() == 1) {
                //给来访者发送站内消息
                var mailEntity = new MailEntity();
                mailEntity.setMsgTitle("咨询预约成功");
                String redirectUrl = "";
                var currentUrl = request.getRequestURL().toString().toLowerCase();
                if (currentUrl.contains("app"))
                    redirectUrl = "/app/counseling/order_record";
                else
                    redirectUrl = "/counselingroom/counseling/order_record";
                mailEntity.setMsgContent(String.format("您的咨询已经预约成功！<a class=\"text-primary\" href=\"%s\"'>查看预约信息</a>",redirectUrl));
                mailEntity.setToUser(entity.getVisitorId());
                mailEntity.setFromUser(1);
                mailEntity.setSendDate(new Date());
                mailDao.sendMsg(mailEntity);
                //给咨询师发送站内消息
                mailEntity = new MailEntity();
                mailEntity.setMsgTitle("咨询预约成功");
                mailEntity.setMsgContent(String.format("您的咨询已经预约成功！<a class=\"text-primary\" href=\"%s\"'>查看预约信息</a>",redirectUrl));
                mailEntity.setToUser(schedulingDto.getCounselorId());
                mailEntity.setFromUser(1);
                mailEntity.setSendDate(new Date());
                mailDao.sendMsg(mailEntity);
            }
            //endregion

            //region 发送预约成功短信（SMS短信）
            if(sysConfig.getIsSmsEnabled() == 1 && sysConfig.getSmsConfig().getIsCounselingOrder() == 1){
                var visitorDto = userService.getById(entity.getVisitorId());
                if(!StrUtil.isEmpty(visitorDto.getMobile())){
                    SendSmsDto sendSmsDto = new SendSmsDto();
                    sendSmsDto.setPhoneNumber(visitorDto.getMobile());
                    sendSmsDto.setSignName(sysConfig.getSmsConfig().getSignName());
                    sendSmsDto.setTemplateCode(sysConfig.getSmsConfig().getVisitorCounselingSuccessTemplate());
                    sendSmsDto.setSmsType(2);
                    sendSmsService.sendNotify(sendSmsDto);
                }
                var counselorDto = userService.getById(schedulingDto.getCounselorId());
                if(!StrUtil.isEmpty(counselorDto.getMobile())){
                    SendSmsDto sendSmsDto = new SendSmsDto();
                    sendSmsDto.setPhoneNumber(counselorDto.getMobile());
                    sendSmsDto.setSignName(sysConfig.getSmsConfig().getSignName());
                    sendSmsDto.setTemplateCode(sysConfig.getSmsConfig().getCounselorCounselingSuccessTemplate());
                    sendSmsDto.setSmsType(2);
                    sendSmsService.sendNotify(sendSmsDto);
                }
            }
            //endregion
            return 1;
        }
        else
            return 0;
    }

    /**
     *  更新预约
     * @param entity 预约信息实体对象
     * @return 影响行数
     */
    @Override
    public int updateOrder(CounselingOrderEntity entity){
        if (counselingOrderDao.update(entity) > 0){
            var sysConfig = sysConfigService.get();
            var schedulingDto = schedulingDao.getById(entity.getSchedulingId());
            //发送消息（站内信）
            if (sysConfig.getIsCounselingNotify() == 1) {
                sendOrderChangeMsg(entity.getVisitorId());
            }
            //region 发送消息（SMS短信）
            if(sysConfig.getIsSmsEnabled() == 1 && sysConfig.getSmsConfig().getIsCounselingOrder() == 1){
                var visitorDto = userService.getById(entity.getVisitorId());
                if(!StrUtil.isEmpty(visitorDto.getMobile())){
                    SendSmsDto sendSmsDto = new SendSmsDto();
                    sendSmsDto.setPhoneNumber(visitorDto.getMobile());
                    sendSmsDto.setSignName(sysConfig.getSmsConfig().getSignName());
                    sendSmsDto.setTemplateCode(sysConfig.getSmsConfig().getVisitorCounselingChangeTemplate());
                    sendSmsDto.setSmsType(2);
                    sendSmsService.sendNotify(sendSmsDto);
                }
                var counselorDto = userService.getById(schedulingDto.getCounselorId());
                if(!StrUtil.isEmpty(counselorDto.getMobile())){
                    SendSmsDto sendSmsDto = new SendSmsDto();
                    sendSmsDto.setPhoneNumber(counselorDto.getMobile());
                    sendSmsDto.setSignName(sysConfig.getSmsConfig().getSignName());
                    sendSmsDto.setTemplateCode(sysConfig.getSmsConfig().getCounselorCounselingChangeTemplate());
                    sendSmsDto.setSmsType(2);
                    sendSmsService.sendNotify(sendSmsDto);
                }
            }
            //endregion
            return 1;
        }
        else
            return 0;
    }

    /**
     *  预约状态变更消息(站内信)
     * @param visitorId 预约对象Id
     */
    private void sendOrderChangeMsg(Integer visitorId) {
        var mailEntity = new MailEntity();
        mailEntity.setMsgTitle("咨询预约状态变更消息");
        mailEntity.setMsgContent("您的咨询预约状态有变更，！<a class=\"text-primary\" href=\"/counselingroom/counseling/order_record\">前往查看</a>");
        mailEntity.setToUser(visitorId);
        mailEntity.setFromUser(1);
        mailEntity.setSendDate(new Date());
        mailDao.sendMsg(mailEntity);
    }

    /**
     *  更新预约状态
     * @param id 预约id
     * @param state 状态
     * @return 影响行数
     */
    @Override
    public int updateState(Integer id, Integer state){
        var map = new HashMap<String, Integer>();
        map.put("id",id);
        map.put("state",state);
        return counselingOrderDao.updateState(map);
    }

    /**
     *  查询进行中或者未开始的预约集合
     * @param dto 条件
     * @return 集合
     */
    @Override
    public List<CounselingOrderDto> getOrderUnDoneList(CounselingOrderDto dto){
        if(dto.getVisitorId() == null || dto.getVisitorId() == 0){
            dto.setChildStructs(PermissonHelper.getChildStructIds(0));
        }
        var listOrders = counselingOrderDao.getList(dto);
        return listOrders
                .stream()
                .filter(a-> DateUtil.compare(a.getStartTime(),new Date()) > 0 || DateUtil.compare(a.getEndTime(),new Date()) > 0)
                .collect(Collectors.toList());
    }

    /**
     *  查询已经结束的预约集合
     * @param dto 条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<CounselingOrderDto> getOrderDoneList(CounselingOrderDto dto){
        var dtRes = new BSDatatableRes<CounselingOrderDto>();
        dto.setChildStructs(PermissonHelper.getChildStructIds(0));
        dto.setCurrentTime(new Date());
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listOrders = counselingOrderDao.getOrderDoneList(dto);
        var counselingOrderDto= new PageInfo<>(listOrders);
        dtRes.setData(counselingOrderDto.getList());
        dtRes.setRecordsTotal((int)counselingOrderDto.getTotal());
        dtRes.setRecordsFiltered((int)counselingOrderDto.getTotal());
        return dtRes;
    }
}

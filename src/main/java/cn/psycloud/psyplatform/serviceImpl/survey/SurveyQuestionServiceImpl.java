package cn.psycloud.psyplatform.serviceImpl.survey;

import cn.psycloud.psyplatform.dao.survey.SurveyItemDao;
import cn.psycloud.psyplatform.dao.survey.SurveyQuestionDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.survey.ImportSurveyQDto;
import cn.psycloud.psyplatform.dto.survey.SurveyQuestionDto;
import cn.psycloud.psyplatform.entity.survey.SurveyItemEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyQuestionEntity;
import cn.psycloud.psyplatform.service.survey.SurveyQuestionService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class SurveyQuestionServiceImpl implements SurveyQuestionService {
    @Autowired
    private SurveyQuestionDao surveyQuestionDao;
    @Autowired
    private SurveyItemDao surveyItemDao;

    /**
     *  查询题目集合：分页
     * @param dto  查询条件
     * @return 题目集合
     */
    @Override
    public BSDatatableRes<SurveyQuestionDto> getListBySurveyId(SurveyQuestionDto dto){
        var dtRes = new BSDatatableRes<SurveyQuestionDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listQuestions = surveyQuestionDao.getListBySurveyId(dto.getSurveyId());
        var surveyQuestionDto = new PageInfo<>(listQuestions);
        dtRes.setData(surveyQuestionDto.getList());
        dtRes.setRecordsTotal((int) surveyQuestionDto.getTotal());
        dtRes.setRecordsFiltered((int) surveyQuestionDto.getTotal());
        return dtRes;
    }

    @Override
    public List<SurveyQuestionDto> getListBySurveyId(Integer surveyId){
        return surveyQuestionDao.getListBySurveyId(surveyId);
    }

    private void deleteByQid(Integer qId) {
        //根据题目id查询量表id和题目序号
        var map = surveyQuestionDao.getById(qId);
        //根据题目id删除题目
        surveyQuestionDao.delete(qId);
        //根据题目id删除答案
        surveyItemDao.deleteByQid(qId);
        //更新删除题目后的题目序号
        surveyQuestionDao.updateQno(map);
    }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean batchDel(String ids) {
        boolean isSuccess = false;
        try {
            var arrayIds = ids.split(",");
            for (String arrayId : arrayIds) {
                var id = Integer.parseInt(arrayId);
                deleteByQid(id) ;
            }
            isSuccess = true;
        }
        catch (Exception e) {
            log.error("删除条目发生错误："+e.getMessage());
        }
        return isSuccess;
    }

    /**
     *  添加题目
     * @param entity 题目实体对象
     * @return 题目id
     */
    @Override
    public int add(SurveyQuestionEntity entity){
        entity.setId(0);
        var questionCount = surveyQuestionDao.getQuestionCount(entity.getSurveyId());
        entity.setQNumber(questionCount+1);
        return surveyQuestionDao.add(entity);
    }

    /**
     *  修改题目
     * @param entity 题目实体对象
     * @return 影响行数
     */
    @Override
    public int update(SurveyQuestionEntity entity){
        return surveyQuestionDao.update(entity);
    }

    /**
     *  excel导入题目和选项
     * @param list excel表格数据
     * @param surveyId 问卷id
     */
    @Override
    public void importQuestion(List<ImportSurveyQDto> list, Integer surveyId) {
        for(ImportSurveyQDto dto: list) {
            var question = new SurveyQuestionEntity();
            question.setId(0);
            question.setQNumber(Integer.parseInt(dto.getQNumber()));
            question.setQContent(dto.getQContent());
            var qType = 1;
            switch (dto.getQType()){
                case "单选":
                    qType = 1;
                    break;
                case "多选":
                    qType = 2;
                    break;
                case "填空":
                    qType = 3;
                    break;
                case "排序":
                    qType = 4;
                    break;
            }
            question.setQType(qType);
            question.setSurveyId(surveyId);
            surveyQuestionDao.add(question);
            var questionId = question.getId();
            //导入选项
            var items = dto.getItems().split("\\|");
            var len = items.length;
            for (int i=0; i < len; i++) {
                var item = new SurveyItemEntity();
                item.setItemContent(items[i].contains("_other") ? items[i].substring(0,items[i].length()-6)  : items[i]);
                item.setQId(questionId);
                item.setItemNo(i + 1);
                item.setIsOther(items[i].contains("_other") ? 1 : 0);
                surveyItemDao.insert(item);
            }
        }
    }
}

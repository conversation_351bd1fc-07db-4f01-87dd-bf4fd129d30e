package cn.psycloud.psyplatform.serviceImpl.survey;

import cn.psycloud.psyplatform.dao.survey.SurveyDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.DuallistData;
import cn.psycloud.psyplatform.dto.core.Select2Data;
import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import cn.psycloud.psyplatform.dto.survey.SurveyNameDto;
import cn.psycloud.psyplatform.entity.survey.SurveyEntity;
import cn.psycloud.psyplatform.service.survey.SurveyService;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
public class SurveyServiceImpl implements SurveyService {
    @Autowired
    private SurveyDao surveyDao;
    /**
     *  根据id查询问卷信息
     * @param surveyId 问卷id
     * @return 问卷实体类对象
     */
    @Override
    public SurveyDto getById(Integer surveyId){ return surveyDao.getById(surveyId);}

    /**
     *  获取问卷集合
     * @param dto 查询条件
     * @return 问卷集合
     */
    @Override
    public List<SurveyDto> getList(SurveyDto dto){ return surveyDao.getList(dto); }

    /**
     *  查询问卷集合：分页
     * @param dto 问卷实体对象
     * @return 问卷集合
     */
    @Override
    public BSDatatableRes<SurveyDto> getListByPaged(SurveyDto dto){
        var dtRes = new BSDatatableRes<SurveyDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listSurveys = surveyDao.getList(dto);
        var surveyDto = new PageInfo<>(listSurveys);
        dtRes.setData(surveyDto.getList());
        dtRes.setRecordsTotal((int) surveyDto.getTotal());
        dtRes.setRecordsFiltered((int) surveyDto.getTotal());
        return dtRes;
    }

    /**
     *  查询问卷集合：select格式
     * @return 集合
     */
    @Override
    public List<Object> getListForSelect(){
        var listSurveys = surveyDao.getListForSelect();
        return getSelect2Data(listSurveys);
    }

    /**
     *  查询问卷集合：DualListBox格式
     * @return 集合
     */
    @Override
    public List<Object> getListForDualList() {
        var dto = new SurveyDto();
        dto.setIsDone(1);
        var listSurveys = surveyDao.getList(dto);
        var lsNode = new ArrayList<>();
        for (SurveyDto survey : listSurveys)
        {
            var duallistData = new DuallistData();
            duallistData.setId(survey.getId());
            duallistData.setName(survey.getSurveyName());
            lsNode.add(duallistData);
        }
        return lsNode;
    }

    /**
     *  根据测评任务查询问卷集合：select
     * @param taskId 任务id
     * @return 集合
     */
    @Override
    public List<Object> getListByTaskId(Integer taskId){
        var listSurveys = surveyDao.getListByTaskId(taskId);
        return getSelect2Data(listSurveys);
    }

    @Override
    public List<Object> getListByMeasuringTaskId(Integer taskId){
        var listSurveys = surveyDao.getListByMeasuringTaskId(taskId);
        return getSelect2Data(listSurveys);
    }

    /**
     *  将数据格式转换成select2格式
     * @param listSurveys 量表集合
     * @return 对象集合
     */
    private List<Object> getSelect2Data(List<SurveyEntity> listSurveys) {
        var lsNode = new ArrayList<>();
        for (SurveyEntity entity : listSurveys) {
            var select2Data = new Select2Data();
            select2Data.setId(entity.getId());
            select2Data.setText(entity.getSurveyName());
            lsNode.add(select2Data);
        }
        return lsNode;
    }

    /**
     *  添加问卷
     * @param entity 问卷实体类对象
     * @return 影响行数
     */
    @Override
    public int addSurvey(SurveyEntity entity){
        entity.setId(0);
        entity.setCreateDate(new Date());
        var userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setOperator(Integer.parseInt(userDto.getUserId().toString()));
        surveyDao.insert(entity);
        return entity.getId() ;
    }

    /**
     *  修改问卷
     * @param entity 问卷实体类对象
     * @return 影响行数
     */
    @Override
    public int updateSurvey(SurveyEntity entity){ return surveyDao.update(entity); }

    /**
     *  删除问卷
     * @param id 问卷id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){ return surveyDao.delete(id); }

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for (String arrayId : arrayIds) {
            var id = Integer.parseInt(arrayId);
            isSuccess = delete(id) > 0 ;
        }
        return isSuccess;
    }

    /**
     *  修改问卷完成状态
     * @param surveyId 问卷id
     * @param state 状态标识
     * @return 影响行数
     */
    @Override
    public int updateDone(Integer surveyId, Integer state){
        var map = new HashMap<String,Integer>();
        map.put("surveyId",surveyId);
        map.put("isDone", state);
        return surveyDao.updateDone(map);
    }

    /**
     *  判断问卷名称是否存在
     * @param dto 问卷名称
     * @return 数量
     */
    @Override
    public boolean isSurveyNameExists(SurveyNameDto dto){
        if (dto.getOriginalName().equals(dto.getNewName())) return false;
        return surveyDao.isSurveyNameExists(dto.getNewName()) > 0;
    }
}

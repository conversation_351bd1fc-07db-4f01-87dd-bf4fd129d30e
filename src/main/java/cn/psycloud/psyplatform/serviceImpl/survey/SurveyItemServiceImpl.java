package cn.psycloud.psyplatform.serviceImpl.survey;

import cn.psycloud.psyplatform.dao.survey.SurveyItemDao;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.survey.SurveyItemEntity;
import cn.psycloud.psyplatform.service.survey.SurveyItemService;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SurveyItemServiceImpl implements SurveyItemService {
    @Autowired
    private SurveyItemDao surveyItemDao;

    /**
     *  查询条目的选项集合：返回Bootstrap Datatables格式
     * @param qId 题目id
     * @return 选项集合
     */
    @Override
    public BSDatatableRes<SurveyItemEntity> getItemsByQId(Integer qId){
        var dtRes = new BSDatatableRes<SurveyItemEntity>();
        var items = surveyItemDao.getListByQId(qId);
        dtRes.setData(items);
        dtRes.setRecordsTotal(items.size());
        dtRes.setRecordsFiltered(items.size());
        return dtRes;
    }

    /**
     *  添加选项（单个题目）
     * @param entity 选项实体对象
     * @return 影响行数
     */
    @Override
    public int add(SurveyItemEntity entity){
        //如果存在则删除
        surveyItemDao.deleteById(entity.getId());
        //计算答案序号
        var count = surveyItemDao.getItemCount(entity.getQId());
        entity.setItemNo(count + 1);
        return surveyItemDao.insert(entity);
    }

    /**
     *  删除选项
     * @param itemId 选项id
     * @return 影响行数
     */
    @Override
    public int delete(Integer itemId){
        return surveyItemDao.deleteById(itemId);
    }
}

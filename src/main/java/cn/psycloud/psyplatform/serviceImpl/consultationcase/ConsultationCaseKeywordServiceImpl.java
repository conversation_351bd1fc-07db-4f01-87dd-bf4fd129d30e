package cn.psycloud.psyplatform.serviceImpl.consultationcase;

import cn.psycloud.psyplatform.dao.consultationcase.ConsultationCaseKeywordDao;
import cn.psycloud.psyplatform.dao.consultationcase.ConsultationKeywordDao;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseKeywordEntity;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationKeywordEntity;
import cn.psycloud.psyplatform.service.consultationcase.ConsultationCaseKeywordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ConsultationCaseKeywordServiceImpl implements ConsultationCaseKeywordService {
    @Autowired
    private ConsultationCaseKeywordDao consultationCaseKeywordDao;
    @Autowired
    private ConsultationKeywordDao consultationKeywordDao;

    /**
     * 保存个案关键词关联（支持批量）
     * @param caseId 个案ID
     * @param keywords 关键词列表（逗号分隔）
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCaseKeywords(Integer caseId, String keywords) {
        try {
            // 先删除已存在的关键词关联记录
            deleteByCaseId(caseId);
            
            // 如果关键词为空，直接返回成功
            if (StringUtils.isEmpty(keywords)) {
                return true;
            }
            
            // 解析关键词列表
            List<String> keywordList = Arrays.stream(keywords.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .distinct()
                    .collect(Collectors.toList());
            
            // 批量创建关联实体
            for (String keyword : keywordList) {
                // 确保关键词存在于关键词库中
                ConsultationKeywordEntity keywordEntity = consultationKeywordDao.getByKeyword(keyword);
                if (keywordEntity == null) {
                    // 如果关键词不存在，自动创建
                    keywordEntity = new ConsultationKeywordEntity();
                    keywordEntity.setKeyword(keyword);
                    consultationKeywordDao.add(keywordEntity);
                }
                
                // 创建关联记录
                ConsultationCaseKeywordEntity entity = new ConsultationCaseKeywordEntity();
                entity.setCaseId(caseId);
                entity.setKeyword(keyword);
                consultationCaseKeywordDao.add(entity);
            }
            return true;
        } catch (Exception e) {
            log.error("保存个案关键词关联记录发生错误：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据个案ID查询关键词列表
     * @param caseId 个案ID
     * @return 关键词列表
     */
    @Override
    public List<String> getKeywordsByCaseId(Integer caseId) {
        return consultationCaseKeywordDao.getKeywordsByCaseId(caseId);
    }

    /**
     * 根据个案ID删除关键词关联记录
     * @param caseId 个案ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCaseId(Integer caseId) {
        try {
            int result = consultationCaseKeywordDao.deleteByCaseId(caseId);
            return result >= 0; // 删除0条记录也算成功
        } catch (Exception e) {
            log.error("删除个案关键词关联记录发生错误：{}", e.getMessage());
            return false;
        }
    }
} 
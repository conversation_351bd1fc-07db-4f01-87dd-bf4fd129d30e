package cn.psycloud.psyplatform.serviceImpl.consultationcase;

import cn.psycloud.psyplatform.dao.consultationcase.ConsultationKeywordDao;
import cn.psycloud.psyplatform.dto.consultationcase.ConsultationKeywordDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationKeywordEntity;
import cn.psycloud.psyplatform.service.consultationcase.ConsultationKeywordService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 咨询关键词Service实现类
 */
@Service
public class ConsultationKeywordServiceImpl implements ConsultationKeywordService {
    @Autowired
    private ConsultationKeywordDao consultationKeywordDao;

    /**
     *  查询关键词列表：分页
     * @param dto 查询条件
     * @return 关键词列表
     */
    @Override
    public BSDatatableRes<ConsultationKeywordDto> getListByPaged(ConsultationKeywordDto dto){
        BSDatatableRes<ConsultationKeywordDto> dtRes = new BSDatatableRes<>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        List<ConsultationKeywordDto> listKeywords = consultationKeywordDao.getList(dto);
        PageInfo<ConsultationKeywordDto> keywordDto = new PageInfo<>(listKeywords);
        dtRes.setData(keywordDto.getList());
        dtRes.setRecordsTotal((int)keywordDto.getTotal());
        dtRes.setRecordsFiltered((int)keywordDto.getTotal());
        return dtRes;
    }

    /*
     *  新增关键词
     * @param dto 关键词实体对象
     * @return 是否成功
     */
    @Override
    public boolean add(ConsultationKeywordEntity entity) {
        return consultationKeywordDao.add(entity) > 0;
    }

    /**
     *  更新关键词
     * @param entity 关键词实体对象
     * @return 是否成功
     */
    @Override
    public boolean update(ConsultationKeywordEntity entity) {
        return consultationKeywordDao.update(entity) > 0;
    }

    /**
     *  删除关键词
     * @param id 关键词ID
     * @return 是否成功
     */
    @Override
    public boolean delete(Integer id) {
        return consultationKeywordDao.delete(id) > 0;
    }

    /**
     *  批量删除关键词
     * @param ids 关键词ID集合
     * @return 是否成功
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        String[] arrayIds = ids.split(",");
        for(String id : arrayIds){
            isSuccess = consultationKeywordDao.delete(Integer.parseInt(id)) > 0;
        }
        return isSuccess;
    }

    /**
     *  获取所有有效关键词列表
     * @return 关键词列表
     */
    @Override
    public List<String> getAllKeywords() {
        return consultationKeywordDao.getAllKeywords();
    }

    /**
     *  检查关键词是否存在（排除指定ID）
     * @param keyword 关键词内容
     * @param excludeId 要排除的记录ID（新增时为null）
     * @return 是否存在
     */
    @Override
    public boolean checkKeywordExistsExcludeId(String keyword, Long excludeId) {
        return consultationKeywordDao.isKeywordExistsExcludeId(keyword, excludeId) > 0;
    }
} 
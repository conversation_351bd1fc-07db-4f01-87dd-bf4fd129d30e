package cn.psycloud.psyplatform.serviceImpl.consultationcase;

import cn.psycloud.psyplatform.dao.consultationcase.ConsultationCaseDao;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.service.consultationcase.ConsultationCaseKeywordService;
import cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto;
import cn.psycloud.psyplatform.dto.consultationcase.ExportConsultationCaseDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseEntity;
import cn.psycloud.psyplatform.service.consultationcase.ConsultationCaseService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import cn.psycloud.psyplatform.util.SessionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
public class ConsultationCaseServiceImpl implements ConsultationCaseService {
    @Autowired
    private ConsultationCaseDao consultationCaseDao;
    
    @Autowired
    private ConsultationCaseKeywordService consultationCaseKeywordService;

    /**
     * 新增个案
     * @param entity 个案实体
     * @return 影响行数
     */
    @Override
    public int add(ConsultationCaseEntity entity){
        var userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        entity.setCounselor(userDto.getUserId());
        entity.setCreateTime(new Date());
        
        int result = consultationCaseDao.add(entity);
        
        // 保存关键词关联
        if (result > 0 && entity.getId() != null) {
            consultationCaseKeywordService.saveCaseKeywords(entity.getId(), entity.getConsultationKeywords());
        }
        
        return result;
    }

    /**
     * 更新个案
     * @param entity 个案实体
     * @return 影响行数
     */
    @Override
    public int update(ConsultationCaseEntity entity){
        int result = consultationCaseDao.update(entity);
        
        // 更新关键词关联
        if (result > 0 && entity.getId() != null) {
            consultationCaseKeywordService.saveCaseKeywords(entity.getId(), entity.getConsultationKeywords());
        }
        
        return result;
    }

    /**
     * 删除个案
     * @param id 个案ID
     * @return 影响行数
     */
    @Override
    public int delete(Integer id){
        return consultationCaseDao.delete(id);
    }

    /**
     * 批量删除个案
     * @param ids 个案ID集合
     * @return 影响行数
     */
    @Override
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var arrayIds = ids.split(",");
        for(var id : arrayIds){
            isSuccess = consultationCaseDao.delete(Integer.parseInt(id)) > 0;
        }
        return isSuccess;
    }

    /**
     * 根据条件查询个案列表
     * @param dto 个案查询条件
     * @return 个案集合
     */
    @Override
    public BSDatatableRes<ConsultationCaseDto> getListByPaged(ConsultationCaseDto dto){
        var childStructsIds = PermissonHelper.getChildStructIds(dto.getStructId());
        dto.setChildStructs(childStructsIds);
        var dtRes = new BSDatatableRes<ConsultationCaseDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listConsultationCase = consultationCaseDao.getList(dto);
        var consultationCaseDto = new PageInfo<>(listConsultationCase);
        dtRes.setData(listConsultationCase);
        dtRes.setRecordsFiltered((int)consultationCaseDto.getTotal());
        dtRes.setRecordsTotal((int)consultationCaseDto.getTotal());
        return dtRes;
    }

    /**
     * 获取我的个案集合
     * @param dto 个案查询条件
     * @return 个案集合
     */
    @Override
    public BSDatatableRes<ConsultationCaseDto> getMyCases(ConsultationCaseDto dto) {
        var user = (UserDto)SessionUtil.getSession().getAttribute("user");
        dto.setUserId(user.getUserId());
        var dtRes = new BSDatatableRes<ConsultationCaseDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listConsultationCase = consultationCaseDao.getMyCases(dto);
        var consultationCaseDto = new PageInfo<>(listConsultationCase);
        dtRes.setData(listConsultationCase);
        dtRes.setRecordsFiltered((int)consultationCaseDto.getTotal());
        dtRes.setRecordsTotal((int)consultationCaseDto.getTotal());
        return dtRes;
    }

    /**
     * 导出咨询个案
     * @param consultationCaseDto 个案查询条件
     * @return 个案集合
     */
    public List<ExportConsultationCaseDto> getExportList(ConsultationCaseDto consultationCaseDto){
        var childStructsIds = PermissonHelper.getChildStructIds(consultationCaseDto.getStructId());
        consultationCaseDto.setChildStructs(childStructsIds);
        return consultationCaseDao.getExportList(consultationCaseDto);
    }

    /**
     * 根据ID查询个案
     * @param id 个案ID
     * @return 个案实体
     */
    @Override
    public ConsultationCaseDto getById(Integer id){
        var map = new HashMap<String, Integer>();
        map.put("id", id);
        UserDto userDto = (UserDto) SessionUtil.getSession().getAttribute("user");
        var roleId = userDto.getRole().getRoleId();
        if(roleId == 3){
            map.put("counselor",-1);
        }
        return consultationCaseDao.getById(map);
    }

    /**
     * 导出咨询个案Word文档
     * @param id 个案ID
     * @param response HTTP响应
     */
    @Override
    public void exportWordDocument(Integer id, HttpServletResponse response) {
        try {
            // 获取个案详情
            ConsultationCaseDto caseDto = getById(id);
            if (caseDto == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 生成文件名：机构名_姓名_咨询日期
            String fileName = generateFileName(caseDto);
            
            // 设置响应头，处理中文文件名编码
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            
            // 处理中文文件名编码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".docx");

            // 直接生成Word文档到响应流
            generateWordDocument(response, caseDto);
            
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 生成文件名
     * @param caseDto 个案数据
     * @return 文件名
     */
    private String generateFileName(ConsultationCaseDto caseDto) {
        StringBuilder fileName = new StringBuilder();
        
        // 机构名
        if (caseDto.getStructName() != null && !caseDto.getStructName().trim().isEmpty()) {
            fileName.append(caseDto.getStructName().trim());
        } else {
            fileName.append("未知机构");
        }
        fileName.append("_");
        
        // 姓名
        if (caseDto.getRealName() != null && !caseDto.getRealName().trim().isEmpty()) {
            fileName.append(caseDto.getRealName().trim());
        } else {
            fileName.append("未知姓名");
        }
        fileName.append("_");
        
        // 咨询日期
        if (caseDto.getConsultationDate() != null) {
            fileName.append(new SimpleDateFormat("yyyyMMdd").format(caseDto.getConsultationDate()));
        } else {
            fileName.append("未知日期");
        }
        
        return fileName.toString();
    }

    /**
     * 直接生成Word文档
     * @param response HTTP响应
     * @param caseDto 个案数据
     */
    private void generateWordDocument(HttpServletResponse response, ConsultationCaseDto caseDto) throws Exception {
        XWPFDocument document = new XWPFDocument();

        // 标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        titleParagraph.setSpacingAfter(300);
        titleParagraph.setSpacingLineRule(LineSpacingRule.AUTO);
        titleParagraph.setSpacingBetween(1.5);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText("心理咨询个案报告");
        titleRun.setBold(true);
        titleRun.setFontSize(20);
        titleRun.setFontFamily("宋体");
        titleRun.setColor("000000");
        titleRun.setTextPosition(10);

        // 基本信息表格
        addSectionTitle(document, "一、基本信息");
        XWPFTable baseTable = document.createTable(4, 4);
        setTableNoBorder(baseTable);
        setTableRow(baseTable, 0, "个案编号", caseDto.getId().toString(), "所属机构", nvl(caseDto.getStructName()));
        setTableRow(baseTable, 1, "咨询日期", nvl(formatDate(caseDto.getConsultationDate())), "咨询形式", getConsultationFormText(caseDto.getConsultationForm()));
        setTableRow(baseTable, 2, "咨询时长", nvl(caseDto.getConsultationDuration())+"分钟", "咨询类型", getConsultationTypeText(caseDto.getConsultationType()));
        setTableRow(baseTable, 3, "咨询师", nvl(caseDto.getCounselorName()), "", "");
        document.createParagraph(); // 表格后插入空段落

        // 来访者信息表格
        addSectionTitle(document, "二、来访者信息");
        XWPFTable visitorTable = document.createTable(3, 4);
        setTableNoBorder(visitorTable);
        setTableRow(visitorTable, 0, "来访年龄", getVisitorAgeText(caseDto.getVisitorAge()), "来访性别", nvl(caseDto.getVisitorGender()));
        setTableRow(visitorTable, 1, "婚姻状态", getMaritalStatusText(caseDto.getMaritalStatus()), "有无子女", getHasChildrenText(caseDto.getHasChildren()));
        setTableRow(visitorTable, 2, "咨询领域", getConsultationFieldText(caseDto.getConsultationField()), "", "");
        document.createParagraph(); // 表格后插入空段落

        // 问题描述
        addSectionTitle(document, "三、问题描述");
        addHighlightRow(document, "问题概述", nvl(caseDto.getProblemSummary()));
        addInfoRow(document, "咨询关键词", nvl(caseDto.getConsultationKeywords()));

        // 特殊情况
        addSectionTitle(document, "四、特殊情况");
        addInfoRow(document, "是否职场问题", getIsWorkplaceIssueText(caseDto.getIsWorkplaceIssue()));
        if (caseDto.getIsWorkplaceIssue() != null && caseDto.getIsWorkplaceIssue() == 1) {
            addInfoRow(document, "职场问题描述", nvl(caseDto.getWorkplaceDescription()));
        }
        addInfoRow(document, "是否有心理风险", getHasPsychologicalRiskText(caseDto.getHasPsychologicalRisk()));
        if (caseDto.getHasPsychologicalRisk() != null && caseDto.getHasPsychologicalRisk() == 1) {
            addInfoRow(document, "风险程度及简要描述",  " 【 " + getRiskLevelText(caseDto.getRiskLevel()) + "】" +
                    nvl(caseDto.getRiskDescription()));
        }

        // 后续建议
        addSectionTitle(document, "五、后续建议");
        addInfoRow(document, "后续建议", getFollowUpSuggestionText(caseDto.getFollowUpSuggestion()));
        if (caseDto.getFollowUpSuggestion() != null && caseDto.getFollowUpSuggestion() == 4) {
            addInfoRow(document, "其他建议", nvl(caseDto.getOtherSuggestion()));
        }

        // 时间信息
        addSectionTitle(document, "六、时间信息");
        addInfoRow(document, "添加时间", nvl(formatDateTime(caseDto.getCreateTime())));

        document.write(response.getOutputStream());
        document.close();
    }

    // 美化章节标题
    private void addSectionTitle(XWPFDocument document, String title) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setSpacingBefore(200);
        paragraph.setSpacingAfter(100);
        paragraph.setSpacingLineRule(LineSpacingRule.AUTO);
        paragraph.setSpacingBetween(1.5);
        XWPFRun run = paragraph.createRun();
        run.setText(title);
        run.setBold(true);
        run.setFontSize(12);
        run.setFontFamily("宋体");
        run.setColor("000000");
        run.setUnderline(UnderlinePatterns.SINGLE);
        run.setTextPosition(4);
    }

    // 表格无边框
    private void setTableNoBorder(XWPFTable table) {
        table.removeBorders();
        table.setWidth("100%");
    }

    // 设置表格一行内容
    private void setTableRow(XWPFTable table, int rowIdx, String label1, String value1, String label2, String value2) {
        XWPFTableRow row = table.getRow(rowIdx);
        setCell(row.getCell(0), label1, true);
        setCell(row.getCell(1), value1, false);
        setCell(row.getCell(2), label2, true);
        setCell(row.getCell(3), value2, false);
    }
    private void setCell(XWPFTableCell cell, String text, boolean isLabel) {
        cell.removeParagraph(0);
        XWPFParagraph p = cell.addParagraph();
        p.setSpacingLineRule(LineSpacingRule.AUTO);
        p.setSpacingBetween(1.5);
        XWPFRun r = p.createRun();
        r.setText(text);
        r.setFontFamily("宋体");
        r.setFontSize(14);
        if (isLabel) {
            r.setBold(true);
            r.setColor("000000");
        } else {
            r.setColor("000000");
        }
        p.setSpacingAfter(0);
        p.setSpacingBefore(0);
    }

    // 高亮内容行
    private void addHighlightRow(XWPFDocument document, String label, String value) {
        XWPFParagraph p = document.createParagraph();
        p.setSpacingBefore(100);
        p.setSpacingAfter(100);
        p.setSpacingLineRule(LineSpacingRule.AUTO);
        p.setSpacingBetween(1.5);
        XWPFRun labelRun = p.createRun();
        labelRun.setText(label + "：");
        labelRun.setBold(true);
        labelRun.setFontSize(14);
        labelRun.setFontFamily("宋体");
        labelRun.setColor("000000");
        XWPFRun valueRun = p.createRun();
        valueRun.setText(value);
        valueRun.setFontSize(14);
        valueRun.setFontFamily("宋体");
        valueRun.setColor("000000");
        valueRun.setTextPosition(2);
        // 设置底色（浅灰）
        valueRun.getCTR().addNewRPr().addNewShd().setFill("E7E6E6");
    }

    // 普通内容行
    private void addInfoRow(XWPFDocument document, String label, String value) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setSpacingBefore(60);
        paragraph.setSpacingAfter(60);
        paragraph.setIndentationFirstLine(400);
        paragraph.setSpacingLineRule(LineSpacingRule.AUTO);
        paragraph.setSpacingBetween(1.5);
        XWPFRun labelRun = paragraph.createRun();
        labelRun.setText(label + "：");
        labelRun.setBold(true);
        labelRun.setFontSize(14);
        labelRun.setFontFamily("宋体");
        labelRun.setColor("000000");
        XWPFRun valueRun = paragraph.createRun();
        valueRun.setText(value);
        valueRun.setFontSize(14);
        valueRun.setFontFamily("宋体");
        valueRun.setColor("000000");
    }

    /**
     * 获取咨询形式文本
     */
    private String getConsultationFormText(Integer form) {
        if (form == null) return "";
        switch (form) {
            case 1: return "驻场咨询";
            case 2: return "线上咨询";
            case 3: return "门店咨询";
            default: return "";
        }
    }

    /**
     * 获取咨询类型文本
     */
    private String getConsultationTypeText(Integer type) {
        if (type == null) return "";
        switch (type) {
            case 1: return "首次咨询";
            case 2: return "第二次咨询";
            case 3: return "第三次咨询";
            case 4: return "第四次咨询";
            case 5: return "第五次咨询";
            case 6: return "第六次及以上咨询";
            default: return "";
        }
    }

    /**
     * 获取来访年龄文本
     */
    private String getVisitorAgeText(Integer age) {
        if (age == null) return "";
        switch (age) {
            case 1: return "20岁及以下";
            case 2: return "21-25岁";
            case 3: return "26-30岁";
            case 4: return "31-35岁";
            case 5: return "36-40岁";
            case 6: return "41-45岁";
            case 7: return "46-50岁";
            case 8: return "50岁以上";
            default: return "";
        }
    }

    /**
     * 获取婚姻状态文本
     */
    private String getMaritalStatusText(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 1: return "未婚";
            case 2: return "已婚";
            default: return "";
        }
    }

    /**
     * 获取有无子女文本
     */
    private String getHasChildrenText(Integer hasChildren) {
        if (hasChildren == null) return "";
        switch (hasChildren) {
            case 1: return "有";
            case 2: return "无";
            default: return "";
        }
    }

    /**
     * 获取咨询领域文本
     */
    private String getConsultationFieldText(Integer field) {
        if (field == null) return "";
        switch (field) {
            case 1: return "心理健康";
            case 2: return "情绪压力";
            case 3: return "人际关系";
            case 4: return "恋爱情感";
            case 5: return "家庭关系";
            case 6: return "亲子教育";
            case 7: return "职场发展";
            case 8: return "个人成长";
            default: return "";
        }
    }

    /**
     * 获取是否职场问题文本
     */
    private String getIsWorkplaceIssueText(Integer isWorkplace) {
        if (isWorkplace == null) return "";
        switch (isWorkplace) {
            case 0: return "否";
            case 1: return "是";
            default: return "";
        }
    }

    /**
     * 获取是否有心理风险文本
     */
    private String getHasPsychologicalRiskText(Integer hasRisk) {
        if (hasRisk == null) return "";
        switch (hasRisk) {
            case 0: return "无";
            case 1: return "有";
            default: return "";
        }
    }

    /**
     * 获取风险等级文本
     */
    private String getRiskLevelText(Integer riskLevel) {
        if (riskLevel == null) return "";
        switch (riskLevel) {
            case 0: return "无风险";
            case 1: return "低风险";
            case 2: return "中风险";
            case 3: return "高风险";
            default: return "";
        }
    }

    /**
     * 获取后续建议文本
     */
    private String getFollowUpSuggestionText(Integer suggestion) {
        if (suggestion == null) return "";
        switch (suggestion) {
            case 1: return "无需跟进";
            case 2: return "定期咨询";
            case 3: return "转介就医";
            case 4: return "其他";
            default: return "";
        }
    }

    /**
     * 获取咨询数据看板统计数据
     * @param consultationCaseDto 查询条件
     * @return 统计数据
     */
    @Override
    public HashMap<String, Object> getDashboardData(ConsultationCaseDto consultationCaseDto) {
        HashMap<String, Object> result = new HashMap<>();

        // 基础统计数据
        result.put("totalCases", consultationCaseDao.getTotalCases(consultationCaseDto));
        result.put("todayCases", consultationCaseDao.getTodayCases(consultationCaseDto));
        result.put("weekCases", consultationCaseDao.getWeekCases(consultationCaseDto));
        result.put("avgCases", consultationCaseDao.getAvgCases(consultationCaseDto));

        // 按机构统计
        result.put("structStats", consultationCaseDao.getStatByStruct(consultationCaseDto));

        // 按咨询形式统计
        result.put("consultationFormStats", consultationCaseDao.getStatByConsultationForm(consultationCaseDto));

        // 按咨询类型统计
        result.put("consultationTypeStats", consultationCaseDao.getStatByConsultationType(consultationCaseDto));

        // 按性别统计
        result.put("genderStats", consultationCaseDao.getStatByGender(consultationCaseDto));

        // 按年龄统计
        result.put("ageStats", consultationCaseDao.getStatByAge(consultationCaseDto));

        // 按婚姻状态统计
        result.put("maritalStatusStats", consultationCaseDao.getStatByMaritalStatus(consultationCaseDto));

        // 按有无子女统计
        result.put("hasChildrenStats", consultationCaseDao.getStatByHasChildren(consultationCaseDto));

        // 按咨询领域统计
        result.put("consultationFieldStats", consultationCaseDao.getStatByConsultationField(consultationCaseDto));

        // 按咨询关键词统计
        result.put("keywordsStats", consultationCaseDao.getStatByKeywords(consultationCaseDto));

        // 职场类问题统计
        result.put("workplaceIssueStats", consultationCaseDao.getStatByWorkplaceIssue(consultationCaseDto));

        // 心理风险统计
        result.put("psychologicalRiskStats", consultationCaseDao.getStatByPsychologicalRisk(consultationCaseDto));

        // 心理风险等级分布统计
        result.put("riskLevelStats", consultationCaseDao.getStatByRiskLevel(consultationCaseDto));

        return result;
    }

    // 工具方法
    private String nvl(Object obj) { return obj == null ? "" : obj.toString(); }
    private String formatDate(Date date) { return date == null ? "" : new SimpleDateFormat("yyyy年MM月dd日").format(date); }
    private String formatDateTime(Date date) { return date == null ? "" : new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss").format(date); }
}

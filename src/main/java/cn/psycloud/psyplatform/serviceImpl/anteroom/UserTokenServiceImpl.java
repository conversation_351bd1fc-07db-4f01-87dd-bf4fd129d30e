package cn.psycloud.psyplatform.serviceImpl.anteroom;

import cn.psycloud.psyplatform.dao.anteroom.UserTokenDao;
import cn.psycloud.psyplatform.dto.anteroom.UserTokenDto;
import cn.psycloud.psyplatform.entity.anteroom.UserTokenEntity;
import cn.psycloud.psyplatform.service.anteroom.UserTokenService;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserTokenServiceImpl implements UserTokenService {
    @Autowired
    private UserTokenDao userTokenDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertToken(UserTokenEntity userToken) {
        return userTokenDao.insert(userToken);
    }

    /**
     * 清除token
     * @param token 用户Token值
     */
    @Override
    public void deleteByToken(String token) {
        userTokenDao.deleteByToken(token);
    }

    /**
     *  根据用户名删除token
     * @param loginName 用户名
     */
    @Override
    public void deleteByLoginName(@Param("loginName") String loginName){
        userTokenDao.deleteByLoginName(loginName);
    }
    /**
     * 获取用户登录token
     * @param token 用户token
     * @return 用户token实体对象
     */
    @Override
    public UserTokenDto getByToken(String token) {
        return userTokenDao.getByToken(token);
    }
}
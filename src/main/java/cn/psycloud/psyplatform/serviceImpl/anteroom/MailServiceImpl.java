package cn.psycloud.psyplatform.serviceImpl.anteroom;

import cn.psycloud.psyplatform.dao.anteroom.MailDao;
import cn.psycloud.psyplatform.dto.anteroom.MailDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.anteroom.MailEntity;
import cn.psycloud.psyplatform.service.anteroom.MailService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MailServiceImpl implements MailService {
    @Autowired
    private MailDao mailDao;
    /**
     *  根据条件查询消息集合
     * @param dto 消息实体对象
     * @return 消息实体对象
     */
    @Override
    public List<MailDto> getList(MailDto dto){
        return  mailDao.getList(dto);
    }

    /**
     * 查询消息集合：分页查询
     * @param dto 查询条件
     * @return 集合
     */
    @Override
    public BSDatatableRes<MailDto> getListByPaged(MailDto dto){
        var dtRes = new BSDatatableRes<MailDto>();
        PageHelper.startPage(dto.getPageIndex()/dto.getPageSize()+1,dto.getPageSize());
        var listMails = mailDao.getList(dto);
        var mailDto = new PageInfo<>(listMails);
        dtRes.setData(mailDto.getList());
        dtRes.setRecordsTotal((int)mailDto.getTotal());
        dtRes.setRecordsFiltered((int)mailDto.getTotal());
        return dtRes;
    }

    /**
     * 根据ID查询消息信息
     * @param id 消息id
     * @return 消息实体对象
     */
    @Override
    public MailDto getById(Integer id) {
        return mailDao.getById(id);
    }

    /**
     *  更改消息已读状态
     * @param ids ID集合
     * @return 执行是否成功
     */
    @Override
    public boolean updateReadState(String ids) {
        var isSuccess = false;
        var array_ids = ids.split(",");
        for (String arrayId : array_ids) {
            var id =Integer.parseInt(arrayId);
            isSuccess = mailDao.updateReadState(id) > 0;
        }
        return isSuccess;
    }

    /**
     *  删除消息
     * @param id 消息id
     * @return 影响行数
     */
    @Override
    public int delete(Integer id) {
        return mailDao.delete(id);
    }

    /**
     *  批量删除
     * @param ids ID集合
     * @return 执行是否成功
     */
    public boolean batchDel(String ids){
        boolean isSuccess = false;
        var array_Ids = ids.split(",");
        for (String arrayId : array_Ids) {
            Integer id = Integer.parseInt(arrayId);
            isSuccess = mailDao.delete(id) > 0;
        }
        return isSuccess;
    }

    /**
     *  发送站内消息
     * @param entity 消息实体对象
     * @return 影响行数
     */
    @Override
    public int sendMsg(MailEntity entity) {
        return mailDao.sendMsg(entity);
    }
}


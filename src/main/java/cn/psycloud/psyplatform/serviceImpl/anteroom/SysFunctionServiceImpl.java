package cn.psycloud.psyplatform.serviceImpl.anteroom;

import cn.psycloud.psyplatform.dao.anteroom.SysFunctionDao;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.ZtreeData;
import cn.psycloud.psyplatform.entity.anteroom.SysFunctionEntity;
import cn.psycloud.psyplatform.service.anteroom.SysFunctionService;
import cn.psycloud.psyplatform.util.redis.JedisUtil;
import com.alibaba.fastjson.JSON;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SysFunctionServiceImpl implements SysFunctionService {
    @Autowired
    private SysFunctionDao sysFunctionDao;
    //是否启用redis缓存
    @Value("${spring.redis.isEnabled}")
    private boolean isRedisEnabled;

    /**
     *  判断当前节点是否还有子节点
     * @param parentCode 父功能编码
     * @return 是否是父功能
     */
    public Boolean isParent(String parentCode){
        int childCount = sysFunctionDao.isParent(parentCode);
        return childCount > 0;
    }

    /**
     *  把list集合转换成zTree所需的json格式
     * @param functionList 功能集合
     * @return Object对象集合
     */
    public List<Object> createZTreeJson(List<SysFunctionEntity> functionList){
        var ztreeNode = new ArrayList<>();
        boolean isParent;
        for (SysFunctionEntity function: functionList) {
            isParent = isParent(function.getFunctionCode());
            var zTreeData = new ZtreeData();
            zTreeData.setId(function.getFunctionCode());
            zTreeData.setPId(function.getParentCode());
            zTreeData.setName(function.getFunctionName());
            zTreeData.setIsParent(isParent);
            ztreeNode.add(zTreeData);
        }
        return ztreeNode;
    }

    /**
     *  把数据转换成zTree所需的json格式
     * @return 功能集合
     */
    public List<ZtreeData> getListForZTree() {
        return sysFunctionDao.getListForZTree();
    }

    /**
     *  根据用户名获取用户权限
     * @param loginName 用户名
     * @return 功能集合
     */
    public List<SysFunctionDto> getSysFunctionByName(String loginName){
        return sysFunctionDao.getSysFunctionByName(loginName);
    }

    /**
     *  根据角色获取功能权限
     * @param roleId 角色id
     * @return 功能集合
     */
    public List<SysFunctionDto> getGrantByRole(Integer roleId){
        List<SysFunctionDto> sysFunctions = new ArrayList<>();
        //启用Redis缓存情况下，优先从缓存里取值
        if(isRedisEnabled && JedisUtil.exists("sysFunctions")&& roleId != 0){
            sysFunctions= JSON.parseArray(JedisUtil.get("sysFunctions"), SysFunctionDto.class);
            sysFunctions = sysFunctions.stream().filter(a-> Objects.equals(a.getRoleId(), roleId)).collect(Collectors.toList());
        }
        else{
            sysFunctions = sysFunctionDao.getGrantByRole(roleId);
        }
        return sysFunctions;
    }
}
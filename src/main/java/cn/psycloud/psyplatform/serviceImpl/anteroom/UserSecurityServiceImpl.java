package cn.psycloud.psyplatform.serviceImpl.anteroom;

import cn.psycloud.psyplatform.service.anteroom.UserSecurityService;
import cn.psycloud.psyplatform.util.redis.JedisUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class UserSecurityServiceImpl implements UserSecurityService {
    @Value("${platform.login-attempt.max-failed-attempts}")
    private Integer maxFailedAttempts;
    @Value("${platform.login-attempt.lock-time-minutes}")
    private Integer lockTimeMinutes;

    /**
     *  检验账户是否锁定
     * @param loginName 用户名
     * @return 是否锁定
     */
    @Override
    public boolean isAccountLocked(String loginName){
        var key = "lockedAccount_"+ loginName;
        return JedisUtil.get(key) != null;
    }

    /**
     *  增加登录失败次数
     * @param loginName 用户名
     */
    public int incrementLoginAttempts(String loginName){
        var key = "loginAttempts_"+ loginName;
        int attempts = JedisUtil.get(key) != null ? Integer.parseInt(JedisUtil.get(key)) : 0 ;
        attempts++;
        if(attempts >=maxFailedAttempts){
            lockAccount(loginName);
            clearLoginAttempts(loginName);
            return 0;
        }
        else {
            JedisUtil.set(key,String.valueOf(attempts),lockTimeMinutes);
            return  maxFailedAttempts - attempts;
        }
    }

    /**
     *  锁定账号
     * @param loginName 用户名
     */
    private void lockAccount(String loginName){
        var key = "lockedAccount_"+ loginName;
        JedisUtil.set(key,"true",lockTimeMinutes);
    }

    /**
     *  清除登录失败次数
     * @param loginName 用户名
     */
    public void clearLoginAttempts(String loginName){
        var key = "loginAttempts_"+ loginName;
        JedisUtil.del(key);
    }
}

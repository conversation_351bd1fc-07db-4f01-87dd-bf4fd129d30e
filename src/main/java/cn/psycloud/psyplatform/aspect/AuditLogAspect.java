package cn.psycloud.psyplatform.aspect;

import cn.hutool.core.util.StrUtil;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.entity.platform.OptLogEntity;
import cn.psycloud.psyplatform.service.platform.OptLogService;
import lombok.var;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpSession;
import java.util.Date;

@Aspect
@Component
public class AuditLogAspect {
    @Autowired
    private OptLogService auditLogService;
    //是否启用审计日志
    @Value("${platform.auditlog}")
    private boolean isAuditLogEnabled;
    //是否启用redis缓存
    @Value("${spring.redis.isEnabled}")
    private boolean isRedisEnabled;

    @Before("execution(* cn.psycloud.psyplatform.controller..*.*(..))")
    public void beforeMethodExecution(JoinPoint joinPoint) {
        if(isAuditLogEnabled){
            var optLogEntity = new OptLogEntity();
            UserDto userDto = null;
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null){
                HttpSession session = attributes.getRequest().getSession(false);
                if (session != null){
                    userDto = (UserDto) session.getAttribute("user");
                    if(userDto != null){
                        optLogEntity.setUserId(userDto.getUserId());
                        optLogEntity.setOptDate(new Date());
                        optLogEntity.setOptUrl(attributes.getRequest().getRequestURI());
                        optLogEntity.setOptClassname(joinPoint.getTarget().getClass().getSimpleName());
                        optLogEntity.setOptMethod( joinPoint.getSignature().getName());
                        Object[] args = joinPoint.getArgs();
                        StringBuilder parameters= new StringBuilder();
                        for (Object arg : args) {
                            parameters.append(arg).append(",");
                        }
                        optLogEntity.setOptArgs(StrUtil.removeSuffix(parameters.toString(),","));
                    /*
                    if(isRedisEnabled){
                        List<OptLogEntity> listAuditLogs = new ArrayList<>();
                        if(JedisUtil.exists("auditLog")){
                            listAuditLogs = JSON.parseArray(JedisUtil.get("auditLog"), OptLogEntity.class);
                            listAuditLogs.add(optLogEntity);
                        }
                        else {
                            listAuditLogs.add(optLogEntity);
                        }
                        JedisUtil.set("auditLog", JSONUtil.toJsonStr(listAuditLogs),0);
                    }*/
                        auditLogService.add(optLogEntity);
                    }
                }
            }
        }
    }
}

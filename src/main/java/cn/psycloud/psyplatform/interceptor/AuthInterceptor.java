package cn.psycloud.psyplatform.interceptor;

import cn.hutool.core.util.URLUtil;
import cn.psycloud.psyplatform.dto.anteroom.RoleDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.service.anteroom.SysFunctionService;
import cn.psycloud.psyplatform.service.anteroom.UserTokenService;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import cn.psycloud.psyplatform.util.CookieHelper;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 登录拦截器
 */
@Component
public class AuthInterceptor implements HandlerInterceptor {
    @Autowired
    private UserTokenService userTokenService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private SysFunctionService sysFunctionService;
    @Value("${platform.always-use-https}")
    private boolean alwaysUseHttps;

    private boolean checkLoginState(HttpServletResponse response, HttpServletRequest request) {
        if(request.getSession().getAttribute("user") != null){ //登录状态未失效
            return true;
        }
        else { //session失效，验证cookie
            var tokenCookie = CookieHelper.getCookie(request, "token");
            if(tokenCookie != null){
                var userToken = userTokenService.getByToken(tokenCookie);
                if (userToken != null &&userToken.getId() != 0) {
                    var userDto = new UserDto();
                    userDto.setUserId(userToken.getUser().getUserId());
                    userDto.setLoginName(userToken.getLoginName());
                    userDto.setRealName(userToken.getUser().getRealName());
                    userDto.setHeadPic(userToken.getUser().getHeadPic());
                    userDto.setStructId(userToken.getUser().getStructId());
                    userDto.setMobile(userToken.getUser().getMobile());
                    userDto.setIsMobileBind(userToken.getUser().getIsMobileBind());
                    var roleDto = new RoleDto();
                    roleDto.setRoleId(userToken.getUser().getRoleId());
                    var listSysFunctions = sysFunctionService.getGrantByRole(userToken.getUser().getRoleId());
                    roleDto.setListFunctions(listSysFunctions);
                    userDto.setRole(roleDto);
                    request.getSession().setAttribute("user",userDto);
                    return true;
                }
                else {
                    return false;
                }
            }
            else{
                return false;
            }
        }
    }

    private StringBuilder getCompleteUrl(HttpServletRequest request) {
        // 获取协议，例如 http 或 https
        String protocol = "http".equals(request.getScheme()) ? request.getScheme().replace("http","https")  : request.getScheme();
        // 获取主机名，例如 www.example.com
        String serverName = request.getServerName();
        // 获取端口号，如果是 80 或 443 则可能不会显示
        int serverPort = request.getServerPort();
        // 获取请求的上下文路径，如果是根路径则可能是空字符串
        String contextPath = request.getContextPath();
        // 获取请求的 servlet 路径，如果没有指定则可能是空字符串
        String servletPath = request.getServletPath();
        // 获取请求的路径信息，即 URL 中的额外路径部分
        String pathInfo = request.getPathInfo();

        StringBuilder completeUrl = new StringBuilder(protocol)
                .append("://")
                .append(serverName);

        completeUrl.append(contextPath).append(servletPath);

        // 如果有路径信息，也添加进去
        if (pathInfo != null) {
            completeUrl.append(pathInfo);
        }

        return completeUrl;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws IOException {
        if(!checkLoginState(response, request)){
            var currentRequestUrl = getCompleteUrl(request);
            String queryString = request.getQueryString();
            if (queryString != null) {
                currentRequestUrl.append("?").append(queryString);
            }
            var backUrl = currentRequestUrl.toString();
            var protocol = alwaysUseHttps ? "https://" : "http://";
            if(backUrl.contains("app")){ //H5手机端
                //response.sendRedirect(protocol+request.getServerName()+"/app/account/login?returnUrl="+ URLUtil.encodeAll(backUrl));
                response.sendRedirect(protocol+request.getServerName()+"/app/account/wx_login?returnUrl="+ URLUtil.encodeAll(backUrl));
                return  false;
            }
            else if(backUrl.contains("/mp")){//H5手机端
                var sysConfigDto = sysConfigService.get();
                if(sysConfigDto.getIsMobileLoginEnabled() == 1) {
                    response.sendRedirect("/mp/account/mobile_login?returnUrl="+ URLUtil.encodeAll(backUrl));
                    return  false;
                }
                else {
                    response.sendRedirect("/mp/account/login?returnUrl="+ URLUtil.encodeAll(backUrl));
                    return  false;
                }
            }
            else { //PC端
                response.sendRedirect(protocol+request.getServerName()+"/account/login?returnUrl="+ URLUtil.encodeAll(backUrl));
                return  false;
            }
        }
        return  true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView)  {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e)  {

    }
}

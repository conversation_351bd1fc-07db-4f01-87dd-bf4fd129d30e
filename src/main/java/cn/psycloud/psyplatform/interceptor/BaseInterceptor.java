package cn.psycloud.psyplatform.interceptor;

import cn.psycloud.psyplatform.dto.anteroom.MailDto;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.platform.PageData;
import cn.psycloud.psyplatform.service.anteroom.MailService;
import cn.psycloud.psyplatform.service.platform.SysConfigService;
import cn.psycloud.psyplatform.util.PermissonHelper;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公共数据资源：在请求处理之前执行
 */
@Component
public class BaseInterceptor implements HandlerInterceptor {
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private MailService mailService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws IOException {
        var sysConfigDto= sysConfigService.get();
        var pageData = new PageData();
        pageData.setSysConfigDto(sysConfigDto);
        request.setAttribute("pageData", pageData);
        //region 登录状态有效，获取页面数据
        if(request.getSession().getAttribute("user") != null) {
            var userDto = (UserDto)request.getSession().getAttribute("user");
            request.setAttribute("user",userDto);
            request.getSession().setAttribute("listFunctions",userDto.getRole().getListFunctions());
            List<SysFunctionDto> privilegeList = new ArrayList<>();
            if(userDto.getRole().getListFunctions() != null && userDto.getRole().getListFunctions().size() > 0) {
                privilegeList = PermissonHelper.createJson(userDto
                        .getRole()
                        .getListFunctions()
                        .stream()
                        .filter(sysFunction -> "menu".equals(sysFunction.getFunctionType()))
                        .collect(Collectors.toList()));
            }
            request.setAttribute("privilegeList", privilegeList);

            //站内消息
            var mailDto = new MailDto();
            mailDto.setToUser(userDto.getUserId());
            mailDto.setIsRead(0);
            var listMails = mailService.getList(mailDto);
            request.setAttribute("unReadMail", listMails);
        }
        //endregion
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView)  {
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e)  {

    }
}

package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.ScaleTypeDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleTypeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ScaleTypeDao {
    /**
     *  删除
     * @param id 量表分类id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  新增
     * @param entity 量表分类实体对象
     * @return 影响行数
     */
    int insert(ScaleTypeEntity entity);

    /**
     *  修改
     * @param entity 量表分类实体对象
     * @return 影响行数
     */
    int update(ScaleTypeEntity entity);

    /**
     *  根据条件查询量表分类集合
     * @param dto 量表分类实体对象
     * @return 量表分类集合
     */
    List<ScaleTypeEntity> getList(ScaleTypeDto dto);

    /**
     *  获取分类下的所有量表
     * @param dto 查询条件实体对象
     * @return 量表分类集合
     */
    List<ScaleTypeDto> getAllScalesByType(ScaleTypeDto dto);
}

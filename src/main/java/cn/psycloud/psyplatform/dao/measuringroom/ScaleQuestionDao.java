package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface ScaleQuestionDao {

    /**
     *  根据量表查询题目
     * @param scaleId 量表id
     * @return 题目集合
     */
    List<ScaleQuestionEntity> getListByScaleId(@Param("scaleId") Integer scaleId);

    /**
     *  根据量表查询题目包括答案
     * @param scaleId 量表id
     * @return 题目集合
     */
    List<ScaleQuestionEntity> getListByScaleIdForTestIng(@Param("scaleId") Integer scaleId);

    /**
     *  查询量表条目集合：DualListBox形式
     * @param scaleId 量表id
     * @return 题目集合
     */
    List<ScaleQuestionEntity> getListByScaleIdForDuallist(@Param("scaleId") Integer scaleId);

    /**
     *  导出测评选项数据时查询选项题目
     * @param scaleId  量表id
     * @return 题目序号集合
     */
    List<LinkedHashMap<String,Object>> getListForExport(@Param("scaleId") Integer scaleId);

    /**
     *  删除题目
     * @param qId 题目id
     * @return 影响行数
     */
    int delete(@Param("qId") Integer qId);

    /**
     *  根据题目id查询题目信息
     * @param qId 题目id
     * @return map集合
     */
    Map<String, Integer> getById(@Param("qId") Integer qId);

    /**
     *  删除题目后更新题目序号
     * @param map 参数
     * @return 影响行数
     */
    int updateQno(Map<String, Integer> map);

    /**
     *  添加题目
     * @param entity 题目实体对象
     * @return 题目id
     */
    int add(ScaleQuestionEntity entity);

    /**
     *  修改题目
     * @param entity 题目实体对象
     * @return 题目id
     */
    int update(ScaleQuestionEntity entity);

    /**
     *  获取题目总数
     * @param scaleId 量表id
     * @return 题目数
     */
    int getQuestionCount(@Param("scaleId") Integer scaleId);

    /**
     *  上移其它
     * @param map map参数
     * @return 影响行数
     */
    int moveUpOther(Map<String,Integer> map);

    /**
     *  上移自己
     * @param map map参数
     * @return 影响行数
     */
    int moveUpSelf(Map<String,Integer> map);

    /**
     *  下移其它
     * @param map map 参数
     * @return 影响行数
     */
    int moveDownOther(Map<String,Integer> map);

    /**
     *  下移自己
     * @param map map 参数
     * @return 影响行数
     */
    int moveDownSelf(Map<String,Integer> map);
}

package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface ScaleFactorDao {

    /**
     *  根据ID查询量表所有的因子集合
     * @param scaleId 量表id
     * @return 因子集合
     */
    List<ScaleFactorDto> getFactorsByScaleId(@Param("scaleId") Integer scaleId);

    /**
     *  获取因子集合：select
     * @return 因子集合
     */
    List<ScaleFactorEntity> getListForSelect(ScaleFactorDto dto);

    /**
     *  导出测评数据时获取因子集合
     * @param dto 条件
     * @return 因子名称map
     */
    List<LinkedHashMap<String,Object>> getListForExport(ScaleFactorDto dto);

    /**
     *  添加因子
     * @param entity 因子实体对象
     * @return 影响行数
     */
    int insert(ScaleFactorEntity entity);

    /**
     *  修改因子
     * @param entity  因子实体对象
     * @return 影响行数
     */
    int update(ScaleFactorEntity entity);

    /**
     *  获取因子总数
     * @param scaleId 量表Id
     * @return 因子数
     */
    int getFactorCount(@Param("scaleId") Integer scaleId);

    /**
     *  删除因子
     * @param id 因子id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  根据因子id查询因子序号和所属量表id
     * @param factorId 因子id
     * @return map结果集
     */
    HashMap<String, Integer> getById(@Param("factorId") Integer factorId);

    /**
     *  删除因子后更新因子序号
     * @param map 参数
     * @return 影响行数
     */
    int updateFactorNo(Map<String,Integer> map);

    /**
     *  上移其它
     * @param map map参数
     * @return 影响行数
     */
    int moveUpOther(Map<String,Integer> map);

    /**
     *  上移自己
     * @param map map参数
     * @return 影响行数
     */
    int moveUpSelf(Map<String,Integer> map);

    /**
     *  下移其它
     * @param map map 参数
     * @return 影响行数
     */
    int moveDownOther(Map<String,Integer> map);

    /**
     *  下移自己
     * @param map map 参数
     * @return 影响行数
     */
    int moveDownSelf(Map<String,Integer> map);

    /**
     *  根据因子id查询因子名称
     * @param factorId 因子id
     * @return 因子名称
     */
    String getFactorNameById(@Param("factorId") Integer factorId);
}

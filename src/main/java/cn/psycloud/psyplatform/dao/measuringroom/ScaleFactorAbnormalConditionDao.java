package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ScaleFactorAbnormalConditionDao {
    /**
     *  删除
     * @param id 因子异常条件id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  添加
     * @param entity 异常条件实体对象
     * @return 影响行数
     */
    int insert(ScaleFactorAbnormalConditionEntity entity);

    /**
     *  获取异常条件集合
     * @param factorId 因子id
     * @return 异常条件集合
     */
    List<ScaleFactorAbnormalConditionEntity> getListByFactorId(@Param("factorId") Integer factorId);
}

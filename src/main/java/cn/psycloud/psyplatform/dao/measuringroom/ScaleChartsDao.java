package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.ScaleChartsDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleChartsEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ScaleChartsDao {
    /**
     * 保存测评报告图表类型
     * @param scaleChartsEntity 测评报告图表类型实体对象
     * @return 影响行数
     */
    int insert(ScaleChartsEntity scaleChartsEntity);

    /**
     * 根据量表删除关联的图表类型
     * @param scaleId 量表Id
     * @return 影响行数
     */
    int deleteByScaleId(@Param("scaleId") Integer scaleId);

    /**
     * 根据量表和因子类型删除关联的图表类型
     * @param scaleId 量表Id
     * @param factorType 因子类型
     * @return 影响行数
     */
    int deleteByScaleIdAndFactorType(@Param("scaleId") Integer scaleId, @Param("factorType") Integer factorType);

    /**
     * 根据量表获取关联的测评报告图表类型
     * @param scaleId 量表Id
     * @return 图表类型列表
     */
    List<ScaleChartsDto> getListByScaleId(@Param("scaleId") Integer scaleId);


}

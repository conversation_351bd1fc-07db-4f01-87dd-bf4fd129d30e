package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.archiveroom.MeasuringRecordDto;
import cn.psycloud.psyplatform.dto.measuringroom.ExportTestRecordDto;
import cn.psycloud.psyplatform.dto.measuringroom.NineHouseStatDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface TestRecordDao {
    /**
     *  添加测评记录
     * @param entity 测评记录实体对象
     * @return 测评记录id
     */
    int addTestRecord(TestRecordEntity entity);

    /**
     *  更新测评记录
     * @param map 参数
     * @return 影响行数
     */
    int updateTestRecord(Map<String, Object> map);

    /**
     *  更新测评状态
     * @param map 参数
     * @return 影响行数
     */
    int updateTestState(Map<String, Integer> map);

    /**
     *  更新测评开始时间
     * @param map 参数
     * @return 影响行数
     */
    int updateStartTime(Map<String, Object> map);

    /**
     *  删除
     * @param recordId 记录id
     * @return 影响行数
     */
    int deleteByRecordId(@Param("recordId") Integer recordId);

    /**
     *  判断是否异常
     * @param recordId 记录id
     * @return 是否异常
     */
    int isAbnormal(@Param("recordId") Integer recordId);

    /**
     *  更新量表测试次数
     * @param scaleId 量表id
     * @return 影响行数
     */
    int updateTestCount(@Param("scaleId") Integer scaleId);

    /**
     *  保存九型人格测试结果
     * @param map 参数
     * @return 影响行数
     */
    int saveNineHouseRecord(Map<String, Integer> map);

    /**
     *  获取九型人格测试记录
     * @return 记录集合
     */
    List<NineHouseStatDto> getNineHouseList(NineHouseStatDto dto);

    /**
     *  保存测评结果解释
     * @param entity 测评结果解释实体对象
     * @return 影响行数
     */
    int saveTestRecordExplain(TestRecordExplainEntity entity);

    /**
     *  清除测评结果解释
     * @param recordId  记录id
     * @return 影响行数
     */
    int deleteTestRecordExplain(@Param("recordId") Integer recordId);

    /**
     *  获取测评结果解释
     * @param recordId 记录id
     * @return 测评结果解释实体对象
     */
    TestRecordExplainEntity getTestRecordExplainsByRecordId(@Param("recordId") Integer recordId);

    /**
     * 获取因子结果解释列表
     * @param recordId 记录id
     * @return 因子结果解释列表
     */
    List<TestRecordExplainEntity> getFactorExplains(@Param("recordId") Integer recordId);

    /**
     *  保存测评报告里的图表
     * @param entity  评报告里图表实体对象
     * @return 影响行数
     */
    int saveTestRecordCharts(TestRecordChartsEntity entity);

    /**
     *  清空测评报告图表
     * @param recordId 记录id
     * @return 影响行数
     */
    int delTestRecordCharts(@Param("recordId") Integer recordId);

    /**
     *  获取报告图表
     * @param recordId 记录id
     * @return 报告图表实体对象
     */
    List<TestRecordChartsEntity> getReportCharts(@Param("recordId") Integer recordId);

    /**
     *  根据记录ID查询记录
     * @param recordId 记录id
     * @return 测评记录
     */
    TestRecordDto getById(Integer recordId);

    /**
     * 根据条件查询测评记录集合
     * @param dto 查询条件实体对象
     * @return 测评记录集合
     */
    List<TestRecordDto> getList(TestRecordDto dto);

    /**
     *  获取我的测评记录
     * @param dto 查询条件
     * @return 测评记录集合
     */
    List<TestRecordDto> getMyRecords(TestRecordDto dto);
    /**
     *  根据记录id查询发送异常消息需要的一些值
     * @param recordId 记录id
     * @return map集合
     */
    Map<String,Object> getAbnormalNotifyInfo(@Param("recordId") Integer recordId);

    /**
     *  导出测评记录
     * @param dto 查询条件
     * @return  测评记录集合
     */
    List<ExportTestRecordDto> getExportTestRecordList(TestRecordDto dto);

    /**
     *  批量导出测试报告获取测评记录id集合
     * @param dto  查询条件
     * @return 测评记录id集合
     */
    List<Map<String,Integer>> getRecordIdsForBatchExport(TestRecordDto dto);

    /**
     *  查询用户的测评记录（生成档案）
     * @param userId 用户id
     * @return 测评记录集合
     */
    List<MeasuringRecordDto> getListForArchive(@Param("userId") Integer userId);


    /**
     *  根据任务id和用户id查询测评记录id
     * @param map 参数
     * @return 测评记录id
     */
    int getRecordIdByTaskIdAndUserId(HashMap<String, Integer> map);

    /**
     *  更新测评时间
     * @param map 参数
     * @return 影响行数
     */
    int updateTestTime(HashMap<String, Object> map);
}

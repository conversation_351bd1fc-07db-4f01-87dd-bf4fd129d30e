package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleUndoneDto;
import cn.psycloud.psyplatform.dto.measuringroom.TaskDto;
import cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyStatDto;
import cn.psycloud.psyplatform.entity.anteroom.UserEntity;
import cn.psycloud.psyplatform.entity.measuringroom.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface TaskDao {
    /**
     *  添加测评任务
     * @param entity 测评任务实体对象
     * @return 任务id
     */
    int addTask(TaskEntity entity);

    /**
     *  验证名称是否重复
     * @param map 传参
     * @return 是够重复标识
     */
    int isExist(HashMap<String,Object> map);

    /**
     *  添加测评对象：团体
     * @param entity 测评对象实体对象
     * @return 影响行数
     */
    int addTaskUser(TaskUserEntity entity);

    /**
     *  添加测评任务量表
     * @param entity  测评任务量表实体对象
     * @return 影响行数
     */
    int addTaskScale(TaskScaleEntity entity);

    /**
     * 添加问卷调查任务里的问卷
     * @param entity 任务和问卷关联实体
     * @return 影响行数
     */
    int addTaskSurvey(TaskSurveyEntity entity);

    /**
     *  添加测评任务测评记录（非限定的测试任务）
     * @param entity 测评记录实体对象
     * @return 影响行数
     */
    int addTaskRecord(TaskRecordEntity entity);

    /**
     *  获取测评任务对象集合
     * @param taskId 测评任务id
     * @return 对象集合
     */
    List<UserEntity> getTaskUsers(@Param("taskId") Integer taskId);

    /**
     *  获取测评任务量表
     * @param taskId 测评任务id
     * @return 量表集合
     */
    List<ScaleEntity> getTaskScales(@Param("taskId") Integer taskId);

    /**
     *  获取我的测评任务
     * @param dto 查询条件
     * @return 测评任务集合
     */
    List<TaskDto> getMyTasks(TaskDto dto);

    /**
     *  判断任务中的量表是否已经做过
     * @param map 桉树
     * @return 测评记录对象
     */
    TestRecordEntity isScaleDone(Map<String, Integer> map);

    /**
     *  根据任务Id获取记录Id
     * @param map 参数
     * @return 测评记录id
     */
    Integer getRecordIdByTaskId(Map<String,Integer> map);

    /**
     *  查询任务的测试数量
     * @param taskId 任务id
     * @return 记录数
     */
    int getTaskTestCount(@Param("taskId") Integer taskId);

    /**
     *  插入任务包含的组织
     * @param map 参数
     * @return 影响行数
     */
    int addTaskStruct(Map<String,Integer> map);

    /**
     *  插入任务信息
     * @param map 参数
     * @return 影响行数
     */
    int addTaskByTaskId(Map<String,Integer> map);

    /**
     *  修改测评任务基本信息
     * @param entity 测评任务实体对象
     * @return 影响行数
     */
    int updateTask(TaskEntity entity);

    /**
     *  根据测评记录ID查询结果查看规则
     * @param recordId 记录id
     * @return 查看规则
     */
    Integer getResultViewRuleByRecordId(@Param("recordId") Integer recordId);

    /**
     *  查询测评任务集合：select
     * @return 任务集合
     */
    List<TaskEntity> getListForSelect(TaskDto dto);

    /**
     *  获取测评任务集合
     * @param dto 查询条件
     * @return 测评任务集合
     */
    List<TaskDto> getList(TaskDto dto);

    /**
     *  删除测评任务
     * @param taskId 测评任务id
     * @return 影响行数
     */
    int deleteById(@Param("taskId") Integer taskId);

    /**
     *  根据测评任务id查询测评对象
     * @param taskId 测评任务id
     * @return 用户集合
     */
    List<UserDto> getUsersByTaskId(@Param("taskId") Integer taskId);

    /**
     *  根据测评任务id查询测评任务信息
     * @param taskId 任务id
     * @return 测评任务实体对象
     */
    TaskDto getById(@Param("taskId") Integer taskId);

    /**
     *  判断用户是不是属于当前测评任务
     * @param map 参数
     * @return 返回记录数
     */
    int isTaskValid(Map<String,Integer> map);

    /**
     *  判断任务里的调查问卷是否完成
     * @param map 传参
     * @return 数量
     */
    int isTaskSurveyDone(Map<String,Integer> map);

    /**
     * 判断问卷调查任务里的问卷是否完成
     * @param map 传参
     * @return 数量
     */
    TaskSurveyRecordEntity isSurveyOfTaskDone(Map<String,Integer> map);

    /**
     * 查询用户的测评任务里面未完成的量表
     * @param map 传参
     * @return 未完成量表集合
     */
    List<ScaleUndoneDto> getScaleListUndone(Map<String,Integer> map);

    /**
     * 调查问卷结果统计
     * @param entity 调查问卷任务和问卷关联实体类
     * @return 实体对象
     */
    TaskSurveyStatDto getTaskSurveyStat(TaskSurveyEntity entity);

    /**
     * 根据测评记录ID获取任务ID
     * @param recordId 测评记录ID
     * @return 任务ID
     */
    Integer getTaskIdByRecordId(@Param("recordId") Integer recordId);
}

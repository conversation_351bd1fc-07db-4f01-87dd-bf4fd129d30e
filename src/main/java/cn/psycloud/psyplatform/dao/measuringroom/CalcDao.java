package cn.psycloud.psyplatform.dao.measuringroom;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface CalcDao {
    /**
     *  获取《卡特尔14PF》常模
     * @param lx 类型
     * @return 常模
     */
    List<LinkedHashMap<String, Object>> get14pfNorm(@Param("lx") String lx);

    /**
     *  获取《卡特尔14PF》结果解释
     * @param ids 集合
     * @return 结果解释
     */
    List<Map<String, Object>> get14pfExplain(@Param("ids") String ids);

    /**
     *  获取《卡特尔16PF》常模
     * @param sex 性别
     * @return 常模
     */
    List<LinkedHashMap<String, Object>> get16pfNorm(@Param("sex") Integer sex);

    /**
     *   获取《卡特尔16PF》结果解释
     * @param ids 集合
     * @return 结果解释
     */
    List<Map<String, Object>> get16pfExplain(@Param("ids") String ids);

    /**
     *  获取《艾森克人格问卷少年式》结果解释
     * @param ids 集合
     * @return 结果解释
     */
    List<LinkedHashMap<String, Object>> getEPQCExplain(@Param("ids") String ids);

    /**
     *  获取MBTI结果解释
     * @param typeName 类型
     * @return 结果解释
     */
    String getMBTIExplain(@Param("typeName") String typeName);

    /**
     *  获取mbti因子得分
     * @param map 参数
     * @return 因子得分
     */
    String getQidsByRecordIdAndFactorName(Map<String,Object> map);
    List<LinkedHashMap<String,Object>> getTestResultForMBTI(Map<String,Object> map);

    /**
     *  获取《MMPI》分量表
     * @return 分量表
     */
    List<LinkedHashMap<String, Object>> getMmpiLB();

    /**
     *  获取《MMPI》常模
     * @return 常模
     */
    List<LinkedHashMap<String, Object>> getMmpiNorm(@Param("id") Integer id);

    /**
     *  获取《MMPI》结果解释
     * @return 结果解释
     */
    List<LinkedHashMap<String, Object>> getMmpiExplain();

    /**
     *  获取《RCCP通用职业匹配测试量表》结果解释
     * @return 结果解释
     */
    List<LinkedHashMap<String,Object>> getRccpExplain();

    /**
     *  获取《瑞文标准推理》常模
     * @param age 年龄
     * @return 常模
     */
    List<LinkedHashMap<String,Object>> getSpmNorm(@Param("age") Integer age);

    /**
     *  获取《斯特劳力气质类型》结果解释
     * @param id id
     * @return 结果解释
     */
    List<LinkedHashMap<String,Object>> getStiExplain(@Param("id") Integer id);

    /**
     *  获取学习焦虑量表常模
     * @param age 年龄
     * @return 模记录
     */
    List<LinkedHashMap<String,Object>> getXxjlNorm(@Param("age") Integer age);

    /**
     *  获取《职业人格》结果解释
     * @return 结果解释
     */
    List<LinkedHashMap<String,Object>> getZylxExplain();

    /**
     *  获取scl-90量表常模
     * @return 常模记录
     */
    List<LinkedHashMap<String,Object>> getSclNorm();
}

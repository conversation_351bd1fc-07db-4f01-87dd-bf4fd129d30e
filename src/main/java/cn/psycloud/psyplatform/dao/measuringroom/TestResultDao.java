package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.TestResultDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestResultEntity;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface TestResultDao {
    /**
     *  保存答案
     * @param entity 答案实体对象
     * @return 影响行数
     */
    int saveResult(TestResultEntity entity);

    /**
     *  删除答案
     * @param recordId 记录id
     */
    int deleteResultByRecordId(Integer recordId);

    /**
     *  根据条件查询答案记录：父母养育方式问卷(EMBU)
     * @param recordId 记录id
     * @return 答案集合
     */
    List<TestResultDto> getEmbuList(Integer recordId);

    /**
     *  根据条件查询答案记录：九型人格测试
     * @param recordId 记录id
     * @return 答案集合
     */
    List<TestResultDto> getNineHouseList(Integer recordId);

    /**
     *  根据条件查询答案记录：Disc性格测试
     * @param recordId 记录id
     * @return 答案集合
     */
    List<TestResultDto> getDiscList(Integer recordId);

    /**
     *  根据条件查询答案记录
     * @param recordId 记录id
     * @return 答案集合
     */
    List<TestResultDto> getList(Integer recordId);

    BigDecimal getAnswerScore(Map<String, Object> map);
}

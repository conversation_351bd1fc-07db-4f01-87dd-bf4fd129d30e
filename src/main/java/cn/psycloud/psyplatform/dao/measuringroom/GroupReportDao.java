package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.GroupReportRequestDto;
import cn.psycloud.psyplatform.dto.measuringroom.GroupReportResponseDto.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 团体测评报告数据访问接口
 */
@Repository
public interface GroupReportDao {
    
    /**
     * 获取量表信息
     * @param scaleId 量表ID
     * @return 量表信息
     */
    ScaleInfoDto getScaleInfo(@Param("scaleId") Integer scaleId);
    
    /**
     * 获取测评总体情况统计
     * @param request 查询条件
     * @return 总体情况统计
     */
    OverallSituationDto getOverallSituation(GroupReportRequestDto request);
    
    /**
     * 按部门统计完成情况
     * @param request 查询条件
     * @return 部门完成情况统计列表
     */
    List<CompletionStatDto> getDepartmentCompletionStats(GroupReportRequestDto request);
    
    /**
     * 按性别统计完成情况
     * @param request 查询条件
     * @return 性别完成情况统计列表
     */
    List<CompletionStatDto> getGenderCompletionStats(GroupReportRequestDto request);
    
    /**
     * 按年龄段统计完成情况
     * @param request 查询条件
     * @return 年龄段完成情况统计列表
     */
    List<CompletionStatDto> getAgeCompletionStats(GroupReportRequestDto request);
    
    /**
     * 按组织统计预警等级分布
     * @param request 查询条件
     * @return 组织预警等级统计列表
     */
    List<OrganizationWarningStatDto> getOrganizationWarningStats(GroupReportRequestDto request);
    
    /**
     * 按性别统计预警等级分布
     * @param request 查询条件
     * @return 性别预警等级统计列表
     */
    List<GenderWarningStatDto> getGenderWarningStats(GroupReportRequestDto request);
    
    /**
     * 按年龄段统计预警等级分布
     * @param request 查询条件
     * @return 年龄段预警等级统计列表
     */
    List<AgeWarningStatDto> getAgeWarningStats(GroupReportRequestDto request);
    
    /**
     * 按因子统计预警等级分布
     * @param request 查询条件
     * @return 因子预警等级统计列表
     */
    List<FactorWarningStatDto> getFactorWarningStats(GroupReportRequestDto request);
    
    /**
     * 获取各组织红码橙码统计
     * @param request 查询条件
     * @return 重点预警统计列表
     */
    List<CriticalWarningStatDto> getCriticalWarningStats(GroupReportRequestDto request);
} 
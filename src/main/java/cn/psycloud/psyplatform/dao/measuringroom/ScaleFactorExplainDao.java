package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ScaleFactorExplainDao {
    /**
     *  删除
     * @param id 因子解释id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  添加因子解释
     * @param entity 因子解释实体对象
     * @return 影响行数
     */
    int insert(ScaleFactorExplainEntity entity);

    /**
     *  修改因子解释
     * @param entity 因子解释实体对象
     * @return 影响行数
     */
    int update(ScaleFactorExplainEntity entity);

    /**
     *  根据因子id查询因子解释
     * @param factorId 因子id
     * @return 因子结果解释集合
     */
    List<ScaleFactorExplainEntity> getListById(@Param("factorId") Integer factorId);

    /**
     *  根据因子id集合查询因子结果解释集合
     * @param ids id集合
     * @return 结果解释集合
     */
    List<ScaleFactorExplainEntity> getListByIds(@Param("ids") String ids);

    /**
     *  根据量表id查询因子结果解释集合
     * @param scaleId 量表id
     * @return 因子结果解释集合
     */
    List<ScaleFactorExplainEntity> getListByScaleId(@Param("scaleId") Integer scaleId);
}

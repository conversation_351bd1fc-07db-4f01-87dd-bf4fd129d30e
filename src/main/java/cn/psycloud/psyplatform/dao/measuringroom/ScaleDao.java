package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.ScaleDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface ScaleDao {
    /**
     *  删除
     * @param id 量表id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  新增
     * @param entity 量表实体对象
     * @return 影响行数
     */
    int insert(ScaleEntity entity);

    /**
     *  修改
     * @param entity 量表实体对象
     * @return 影响行数
     */
    int update(ScaleEntity entity);

    /**
     *  更改量表状态
     * @param map map参数
     * @return 影响行数
     */
    int updateScaleDone(Map<String, Integer> map);

    /**
     *  设置量表排序
     * @param map map参数
     * @return 影响行数
     */
    int setSort(Map<String, Integer> map);

    /**
     *  根据ID查询量表信息
     * @param scaleId 量表id
     * @return 量表实体对象
     */
    ScaleDto getById(@Param("scaleId") Integer scaleId);

    /**
     *  根据条件查询单个量表信息
     * @param dto 量表实体对象
     * @return 量表实体对象
     */
    ScaleDto get(ScaleDto dto);

    /**
     *  根据条件查询量表集合
     * @param dto 量表实体对象
     * @return 量表集合
     */
    List<ScaleDto> getList(ScaleDto dto);

    /**
     *  根据测评任务查询量表集合
     * @param taskId 任务id
     * @return 量表集合
     */
    List<ScaleEntity> getListByTaskId(@Param("taskId") Integer taskId);

    /**
     *  查询量表集合：select2
     * @return
     */
    List<ScaleEntity> getListForSelect();

    /**
     *  手机端首页
     * @return 量表集合
     */
    List<ScaleDto> getListForIndex();

    /**
     *  根据名称查询数量
     * @param scaleName 量表名称
     * @return 数量
     */
    int getCountByName(@Param("scaleName") String scaleName);

    /**
     *  根据id查询量表信息：导出量表
     * @param scaleId 量表Id
     * @return 量表信息
     */
    ScaleDto getScaleForExport(@Param("scaleId") Integer scaleId);
}

package cn.psycloud.psyplatform.dao.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface ScaleAnswerDao {
    /**
     *  删除答案（根据答案id）
     * @param aId 答案id
     * @return 影响行数
     */
    int deleteByAid(@Param("aId") Long aId);

    /**
     *  根据答案id查询答案所属题目id和答案序号
     * @param aId
     * @return map集合
     */
    Map<String, Integer> getById(@Param("aId") Long aId);

    /**
     *  根据题目id查询所有答案
     * @param qId 题目id
     * @return 答案集合
     */
    List<ScaleAnswerEntity> getListByQId(@Param("qId") Integer qId);

    /**
     *  删除答案后更新答案序号
     * @param map 参数
     * @return 影响行数
     */
    int updateAno(Map<String, Integer> map);

    /**
     *  删除答案（根据题目id）
     * @param qId 题目id
     * @return 影响行数
     */
    int deleteByQid(@Param("qId") Integer qId);

    /**
     *  添加答案
     * @param entity 答案实体对象
     * @return 影响行数
     */
    int insert(ScaleAnswerEntity entity);

    /**
     *  修改答案
     * @param entity 答案实体对象
     * @return 影响行数
     */
    int update(ScaleAnswerEntity entity);

    /**
     *  获取答案总数
     * @param qId 题目id
     * @return 答案数
     */
    int getAnswerCount(@Param("qId") Integer qId);

    /**
     *  获取答案的最大数
     * @param scaleId 量表Id
     * @return 答案的最大数
     */
    int getMaxANo(@Param("scaleId") Integer scaleId);
}

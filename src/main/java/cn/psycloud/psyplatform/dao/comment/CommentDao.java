package cn.psycloud.psyplatform.dao.comment;

import cn.psycloud.psyplatform.dto.comment.CommentDto;
import cn.psycloud.psyplatform.entity.comment.CommentEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommentDao {
    /**
     *  发表评论
     * @param entity 评论实体类
     * @return 影响行数
     */
    int addComment(CommentEntity entity);

    /**
     *  删除评论
     * @param id 评论id
     * @return 影响行数
     */
    int deleteComment(@Param("id") Integer id);

    /**
     *  获取训练营评论集合
     * @param dto 查询条件
     * @return 集合
     */
    List<CommentDto> getComments(CommentDto dto);

    /**
     *  审核评论
     * @param id 评论id
     * @return 影响行数
     */
    int checkComment(@Param("id") Integer id);
}

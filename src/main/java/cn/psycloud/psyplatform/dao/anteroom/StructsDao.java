package cn.psycloud.psyplatform.dao.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.StructsDto;
import cn.psycloud.psyplatform.entity.anteroom.StructsEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public interface StructsDao {
    /**
     *  添加组织
     * @param entity 组织实体对象
     * @return 影响行数
     */
    int add(StructsEntity entity);

    /**
     *  修改组织
     * @param entity 组织实体对象
     * @return 影响行数
     */
    int update(StructsEntity entity);

    /**
     *  删除
     * @param id 组织id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  清空组织
     */
    void truncateStructs();

    /**
     *  查询组织集合
     * @param entity 组织实体对象
     * @return 组织集合
     */
    List<StructsDto> getList(StructsEntity entity);

    /**
     *  根据id查询所有子节点
     * @param parentId 父节点
     * @return 子节点集合
     */
    List<StructsDto> getChildList(@Param("parentId") Integer parentId);

    /**
     *  根据组织ID查询组织信息
     * @param id 组织id
     * @return 组织对象
     */
    StructsDto getById(@Param("id") Integer id);

    /**
     *  移动组织
     * @param targetNode 目标节点
     * @param node 源节点
     * @return 影响行数
     */
    int moveStruct(@Param("targetNode") Integer targetNode, @Param("node") Integer node);

    /**
     *  根据组织名称和父id获取组织id
     * @param map 参数
     * @return 组织ID
     */
    Integer getStructIdByStructName(Map<String, Object> map);

    /**
     *  根据组织名称获取组织id
     * @param structName 组织名称
     * @return 组织ID
     */
    Integer getIdByStructName(@Param( "structName") String structName);
}
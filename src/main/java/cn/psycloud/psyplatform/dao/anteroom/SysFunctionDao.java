package cn.psycloud.psyplatform.dao.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.ZtreeData;
import cn.psycloud.psyplatform.entity.anteroom.SysFunctionEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 *  平台功能
 */
@Repository
public interface SysFunctionDao {
    /**
     *  判断当前节点是否还有子节点
     * @param parentCode 父功能编码
     * @return 子功能数量
     */
    int isParent(@Param("parentCode") String parentCode);

    /**
     *  根据条件查询功能集合
     * @param entity 功能实体对象
     * @return 功能集合
     */
    List<SysFunctionEntity> getList(SysFunctionEntity entity);

    /**
     *  查询功能集合转换成Ztree对象
     * @return ZTree格式的功能集合
     */
    List<ZtreeData> getListForZTree();

    /**
     *  根据用户名获取用户权限
     * @param loginName 用户名
     * @return 功能集合
     */
    List<SysFunctionDto> getSysFunctionByName(@Param("loginName") String loginName);

    /**
     *  根据角色获取功能权限
     * @param roleId 角色id
     * @return 功能集合
     */
    List<SysFunctionDto> getGrantByRole(@Param("roleId") Integer roleId);

    /**
     *  根据角色ID获取角色权限返回zTree格式
     * @param roleId 角色id
     * @return 功能集合
     */
    List<ZtreeData> getGrantForZTree(@Param("roleId") Integer roleId);
}

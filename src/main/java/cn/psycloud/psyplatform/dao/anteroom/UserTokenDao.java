package cn.psycloud.psyplatform.dao.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.UserTokenDto;
import cn.psycloud.psyplatform.entity.anteroom.UserTokenEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserTokenDao  {
    /**
     * 写入token
     * @param userToken 用户token实体对象
     * @return 影响行数
     */
    int insert(UserTokenEntity userToken);

    /**
     * 清除指定token
     * @param token 用户Token值
     * @return 影响行数
     */
    int deleteByToken(@Param("token") String token);

    /**
     *  根据用户名删除token
     * @param loginName 用户名
     * @return 影响行数
     */
    int deleteByLoginName(@Param("loginName") String loginName);

    /**
     * 获取用户登录token
     * @param token 用户token
     * @return 用户Token实体对象
     */
    UserTokenDto getByToken(@Param("token") String token);
}
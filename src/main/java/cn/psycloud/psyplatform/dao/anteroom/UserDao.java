package cn.psycloud.psyplatform.dao.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.LoginDto;
import cn.psycloud.psyplatform.dto.anteroom.ModifyPwdDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.entity.anteroom.CounselorInfoEntity;
import cn.psycloud.psyplatform.entity.anteroom.UserEntity;
import cn.psycloud.psyplatform.entity.anteroom.WechatUserEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface UserDao {
    /**
     * 账号密码登录
     * @param login 登录实体对象
     * @return 用户实体对象
     */
    UserDto signIn(LoginDto login);

    /**
     *  短信登录
     * @param mobile 手机号码
     * @return 用户实体对象
     */
    UserDto smsLogin(@Param("mobile") String mobile);

    /**
     *  手机号码登录/注册
     * @param mobile 手机号码
     * @return 用户实体对象
     */
    UserDto mobileLogin(@Param("mobile") String mobile);

    /**
     *  查询用户集合
     * @param dto 用户实体对象
     * @return 用户实体集合
     */
    List<UserDto> getUserList(UserDto dto);

    /**
     *  获取推荐咨询师集合(下拉列表)
     * @return 用户集合
     */
    List<UserDto> getCounselorListForSelect(UserDto dto);

    /**
     *  获取单个用户信息
     * @param dto 用户实体对象
     * @return 用户实体
     */
    UserDto get(UserDto dto);

    /**
     *  根据用户名查询用户信息
     * @param loginName 用户名
     * @return 用户实体对象
     */
    UserDto getUserByName(@Param("loginName") String loginName);

    /**
     *  根据条件查询平台用户信息
     * @param dto 用户实体对象
     * @return 用户集合
     */
    List<UserDto> getAdminUsers(UserDto dto);

    /**
     *  根据条件查询咨询师集合
     * @param dto 用户实体对象
     * @return 咨询师集合
     */
    List<UserDto> getCounselorList(UserDto dto);

    /**
     *  根据条件查询来访者信息
     * @param dto 来访者实体对象
     * @return 来访者集合
     */
    List<UserDto> getVisitorList(UserDto dto);

    /**
     *  获取推荐咨询师集合
     * @return 推荐咨询师集合
     */
    List<UserDto> getRecommendCounselorList();

    /**
     *  根据id删除
     * @param userId 用户id
     * @return 影响行数
     */
    int deleteById(@Param("userId") Integer userId);

    /**
     *  新增用户
     * @param entity 用户实体
     * @return 用户id
     */
    Integer addUser(UserEntity entity);

    /**
     *  修改用户信息
     * @param dto 用户信息实体对象
     * @return 影响行数
     */
    int addUserInfo(UserDto dto);

    /**
     * 添加咨询师附加信息
     * @param entity
     * @return
     */
    int addCounselorInfo(CounselorInfoEntity entity);

    /**
     *  根据用户ID获取用户密码
     * @param userId 用户id
     * @return 密码
     */
    String getPwdById(@Param("userId") Integer userId);

    /**
     *  修改密码
     * @param dto 修改密码实体对象
     * @return 影响行数
     */
    int modifyPassword(ModifyPwdDto dto);

    /**
     *  修改密码时校验旧密码
     * @param dto 修改密码实体对象
     * @return 影响行数
     */
    int modifyPasswordByOriginalPwd(ModifyPwdDto dto);

    /**
     *  更新用户最后修改密码时间
     * @param userId 用户id
     * @return 影响行数
     */
    int updateLastChangePwdDate(@Param("userId") Integer userId);

    /**
     *  获取用户最后修改密码时间
     * @param userId 用户Id
     * @return 时间
     */
    Date getLastPasswordChangeDate(@Param("userId") Integer userId);

    /**
     * 更新登录时间和IP地址
     * @param map 参数
     * @return 影响行数
     */
    int updateLoginDateAndIp(Map<String,String> map);

    /**
     *  删除用户角色
     * @param userId 用户id
     * @return 影响行数
     */
    int deleteUserRole(@Param("userId") Integer userId);

    /**
     *  添加用户角色
     * @param map 参数
     * @return 影响行数
     */
    int addUserRole(Map<String,String> map);

    /**
     *  修改用户角色
     * @param map 参数
     * @return 影响行数
     */
    int updateUserRole(Map<String,String> map);

    /**
     *  修改用户信息
     * @param loginName 用户名
     * @param userId 用户id
     * @return 影响行数
     */
    int updateUser(@Param("loginName") String loginName, @Param("userId") Integer userId);

    /**
     *  修改用户详细信息
     * @param dto 用户实体
     * @return 影响行数
     */
    int updateUserInfo(UserDto dto);

    /**
     *  简易修改个人信息
     * @param map 参数
     * @return 影响行数
     */
    int updateForScale(Map<String,Object> map);

    /**
     * 验证用户名是否存在
     * @param loginName 用户名
     * @return 是否存在
     */
    int isLoginNameExist(@Param("loginName") String loginName);

    /**
     *  更换头像
     * @param map 参数
     * @return 影响行数
     */
    int updateAvatar(Map<String, String> map);

    /**
     *  注册审核
     * @param userId  用户Id
     * @return 影响行数
     */
    int checkUser(@Param("userId") Integer userId);

    /**
     *  更新咨询师附加信息
     * @param entity 咨询师附加信息实体对象
     * @return  影响行数
     */
    int updateCounselorInfo(CounselorInfoEntity entity);

    /**
     *  查询咨询师附加信息
     * @param userId 用户id
     * @return 咨询师附加信息实体对象
     */
    CounselorInfoEntity getCounselorInfoByUserId(@Param("userId") Integer userId);

    /**
     *  绑定手机号码
     * @return 影响行数
     */
    int bindMobile(Map<String,String> map);

    /**
     *  验证手机号码是否被绑定
     * @param mobile 手机号码
     * @return 影响行数
     */
    int checkMobile(@Param("mobile") String mobile);

    /**
     *  更改用户所属角色
     * @param userId 用户id
     * @param roleId 角色id
     * @return 影响行数
     */
    int updateUserRole(@Param("userId") Integer userId,@Param("roleId") Integer roleId);

    /**
     *  预约咨询验证来访者信息
     * @param loginName 来访者账号
     * @return 用户id
     */
    String checkUserInfo(@Param("loginName") String loginName);

    /**
     *  生成时返回的用户集合
     * @param dto 条件
     * @return 用户集合
     */
    List<UserDto>getUserListForArchive(UserDto dto);

    /**
     *  根据用户名查询用户id
     * @param loginName 用户名
     * @return 用户id
     */
    Integer getUserIdByLoginName(@Param("loginName") String loginName);

    /**
     *  根据用户ID查询用户手机号码
     */
    String getMobileByUserId(@Param("userId") Integer userId);

    /**
     * 添加微信用户信息
     * @param entity 微信用户信息实体
     * @return 影响行数
     */
    int addWechatUser(WechatUserEntity entity);

    /**
     * 根据unionid查询用户信息
     * @param unionid 微信unionid
     * @return 微信用户信息实体
     */
    UserDto getUserInfoByWechatUnionId(@Param("unionid") String unionid);

    /**
     * 根据用户名和真实姓名查询用户id
     * @param map 用户名和真实姓名
     * @return 用户id
     */
    UserDto getUserInfoByLoginNameAndRealName(HashMap<String, String> map);
}
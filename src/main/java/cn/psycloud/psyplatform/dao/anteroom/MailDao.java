package cn.psycloud.psyplatform.dao.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.MailDto;
import cn.psycloud.psyplatform.entity.anteroom.MailEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 *  站内消息
 */
@Repository
public interface MailDao {
    /**
     *  根据条件查询消息集合
     * @param dto 消息实体对象
     * @return 消息实体对象
     */
    List<MailDto> getList(MailDto dto);

    /**
     *  根据ID查询消息
     * @param id 消息id
     * @return 消息实体对象
     */
    MailDto getById(@Param("id") Integer id);

    /**
     *  发送站内消息
     * @param entity 消息实体对象
     * @return 影响行数
     */
    int sendMsg(MailEntity entity);

    /**
     *  删除
     * @param id 消息id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  更改已读状态
     * @param id 消息id
     * @return 影响行数
     */
    int updateReadState (@Param("id") Integer id);
}
package cn.psycloud.psyplatform.dao.consultationcase;

import cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto;
import cn.psycloud.psyplatform.dto.consultationcase.ExportConsultationCaseDto;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.List;

@Repository
public interface ConsultationCaseDao {
    /**
     * 新增个案
     * @param entity 个案实体
     * @return 影响行数
     */
    int add(ConsultationCaseEntity entity);

    /**
     * 更新个案
     * @param entity 个案实体
     * @return 影响行数
     */
    int update(ConsultationCaseEntity entity);

    /**
     * 删除个案
     * @param id 个案ID
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     * 根据条件查询个案列表
     * @param consultationCaseDto 个案查询条件
     * @return 个案集合
     */
    List<ConsultationCaseDto> getList(ConsultationCaseDto consultationCaseDto);

    /**
     * 获取我的个案集合
     * @param consultationCaseDto 个案查询条件
     * @return 个案集合
     */
    List<ConsultationCaseDto> getMyCases(ConsultationCaseDto consultationCaseDto);

    /**
     * 根据ID查询个案
     * @param map 查询参数集合
     * @return 个案内容
     */
    ConsultationCaseDto getById(HashMap<String, Integer> map);

    /**
     * 导出咨询个案
     *
     * @param consultationCaseDto 个案查询条件
     * @return 个案集合
     */
    List<ExportConsultationCaseDto> getExportList(ConsultationCaseDto consultationCaseDto);

    /**
     * 按机构统计咨询个案数量
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByStruct(ConsultationCaseDto consultationCaseDto);

    /**
     * 按咨询形式统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByConsultationForm(ConsultationCaseDto consultationCaseDto);

    /**
     * 按咨询类型统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByConsultationType(ConsultationCaseDto consultationCaseDto);

    /**
     * 按性别统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByGender(ConsultationCaseDto consultationCaseDto);

    /**
     * 按年龄统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByAge(ConsultationCaseDto consultationCaseDto);

    /**
     * 按婚姻状态统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByMaritalStatus(ConsultationCaseDto consultationCaseDto);

    /**
     * 按有无子女统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByHasChildren(ConsultationCaseDto consultationCaseDto);

    /**
     * 按咨询领域统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByConsultationField(ConsultationCaseDto consultationCaseDto);

    /**
     * 按咨询关键词统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByKeywords(ConsultationCaseDto consultationCaseDto);

    /**
     * 职场类问题统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByWorkplaceIssue(ConsultationCaseDto consultationCaseDto);

    /**
     * 心理风险统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByPsychologicalRisk(ConsultationCaseDto consultationCaseDto);

    /**
     * 风险等级统计
     * @param consultationCaseDto 查询条件
     * @return 统计结果
     */
    List<HashMap<String, Object>> getStatByRiskLevel(ConsultationCaseDto consultationCaseDto);

    /**
     * 获取总个案数
     * @param consultationCaseDto 查询条件
     * @return 总个案数
     */
    Integer getTotalCases(ConsultationCaseDto consultationCaseDto);

    /**
     * 获取今日新增个案数
     * @param consultationCaseDto 查询条件
     * @return 今日新增个案数
     */
    Integer getTodayCases(ConsultationCaseDto consultationCaseDto);

    /**
     * 获取本周新增个案数
     * @param consultationCaseDto 查询条件
     * @return 本周新增个案数
     */
    Integer getWeekCases(ConsultationCaseDto consultationCaseDto);

    /**
     * 获取日均个案数
     * @param consultationCaseDto 查询条件
     * @return 日均个案数
     */
    Integer getAvgCases(ConsultationCaseDto consultationCaseDto);
}

package cn.psycloud.psyplatform.dao.consultationcase;

import cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseKeywordEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ConsultationCaseKeywordDao {
    /**
     * 新增个案关键词关联
     * @param entity 关联实体
     * @return 影响行数
     */
    int add(ConsultationCaseKeywordEntity entity);

    /**
     * 根据个案ID查询关键词列表
     * @param caseId 个案ID
     * @return 关键词列表
     */
    List<String> getKeywordsByCaseId(@Param("caseId") Integer caseId);

    /**
     * 根据个案ID删除关键词关联记录
     * @param caseId 个案ID
     * @return 影响行数
     */
    int deleteByCaseId(@Param("caseId") Integer caseId);
} 
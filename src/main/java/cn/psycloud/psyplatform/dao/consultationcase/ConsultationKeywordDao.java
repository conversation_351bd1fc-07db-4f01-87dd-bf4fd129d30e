package cn.psycloud.psyplatform.dao.consultationcase;

import cn.psycloud.psyplatform.dto.consultationcase.ConsultationKeywordDto;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationKeywordEntity;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 咨询关键词DAO接口
 */
public interface ConsultationKeywordDao {
    /**
     * 添加关键词
     */
    int add(ConsultationKeywordEntity entity);
    
    /**
     * 更新关键词
     */
    int update(ConsultationKeywordEntity entity);

    /**
     * 删除关键词
     */
    int delete(@Param("id") Integer id);

    /**
     * 根据关键词查询
     */
    ConsultationKeywordEntity getByKeyword(@Param("keyword") String keyword);

    /**
     * 查询关键词列表
     */
    List<ConsultationKeywordDto> getList(ConsultationKeywordDto dto);

    /**
     * 查询所有关键词
     */
    List<String> getAllKeywords();

    /**
     * 检查关键词是否存在（排除指定ID）
     * @param keyword 关键词内容
     * @param excludeId 要排除的记录ID（新增时为null）
     * @return 存在数量
     */
    int isKeywordExistsExcludeId(@Param("keyword") String keyword, @Param("excludeId") Long excludeId);
} 
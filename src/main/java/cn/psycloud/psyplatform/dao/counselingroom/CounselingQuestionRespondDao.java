package cn.psycloud.psyplatform.dao.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionRespondDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionRespondEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CounselingQuestionRespondDao {
    /**
     *  根据提问id获取回复集合
     * @param questionId 提问Id
     * @return 回复集合
     */
    List<CounselingQuestionRespondDto> getListByQuestionId(@Param("questionId") Integer questionId);

    /**
     *  发表回复
     * @param entity 回复实体对象
     * @return 影响行数
     */
    int insert(CounselingQuestionRespondEntity entity);

    /**
     *  删除回复
     * @param id 回复id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);
}

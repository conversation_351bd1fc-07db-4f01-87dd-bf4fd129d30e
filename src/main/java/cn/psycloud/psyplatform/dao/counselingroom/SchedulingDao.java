package cn.psycloud.psyplatform.dao.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto;
import cn.psycloud.psyplatform.entity.counselingroom.SchedulingEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SchedulingDao {
    /**
     *  可预约的排班集合
     * @param dto 查询条件
     * @return 排班集合
     */
    List<SchedulingDto> getOrderSchedulingList(SchedulingDto dto);

    /**
     *  查询排班集合
     * @param dto 查新条件
     * @return 集合
     */
    List<SchedulingDto> getList(SchedulingDto dto);

    /**
     *  根据id查询排班信息
     * @param id 排班id
     * @return 排班信息
     */
    SchedulingDto getById(@Param("id") Integer id);

    /**
     *  删除
     * @param id 排班id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  新增
     * @param entity 排班实体对象
     * @return 影响行数
     */
    int insert(SchedulingEntity entity);

    /**
     *  修改
     * @param entity 排班实体对象
     * @return 影响行数
     */
    int update(SchedulingEntity entity);

    /**
     *  验证咨询师排班时间是否重复
     * @param dto 排班信息
     * @return 记录数
     */
    int isSchedulingExist(SchedulingDto dto);
}

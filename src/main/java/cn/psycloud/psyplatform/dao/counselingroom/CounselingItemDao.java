package cn.psycloud.psyplatform.dao.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.CounselingItemDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingItemEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CounselingItemDao {
    /**
     *  删除
     * @param id 咨询类型id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  新增咨询师咨询类型
     * @param entity 咨询类型实体对象
     * @return 影响行数
     */
    int insert(CounselingItemEntity entity);

    /**
     *  修改咨询师咨询类型
     * @param entity  咨询类型实体对象
     * @return 影响行数
     */
    int update(CounselingItemEntity entity);

    /**
     *  查询集合
     * @param dto 条件
     * @return 集合
     */
    List<CounselingItemDto> getList(CounselingItemDto dto);

    /**
     *  根据ID查询咨询类型
     * @param id  咨询类型id
     * @return 咨询类型实体对象
     */
    CounselingItemDto getById(@Param("id") Integer id);
}

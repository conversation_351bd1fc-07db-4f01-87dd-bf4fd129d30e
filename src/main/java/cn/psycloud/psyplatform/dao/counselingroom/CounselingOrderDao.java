package cn.psycloud.psyplatform.dao.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingOrderEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface CounselingOrderDao {
    /**
     *  删除预约信息
     * @param id 预约id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);
    /**
     *  添加预约
     * @param entity 预约信息实体对象
     * @return 影响行数
     */
    int insert(CounselingOrderEntity entity);

    /**
     *  更新预约
     * @param entity 预约信息实体对象
     * @return 影响行数
     */
    int update(CounselingOrderEntity entity);

    /**
     *  更新预约状态
     * @param map 参数
     * @return 影响行数
     */
    int updateState(Map<String, Integer> map);

    /**
     *  根据条件查询预约信息
     * @param dto 查询条件
     * @return 预约信息
     */
    CounselingOrderDto get(CounselingOrderDto dto);

    /**
     *  根据条件查询集合
     * @param dto 查询条件
     * @return  集合
     */
    List<CounselingOrderDto> getList(CounselingOrderDto dto);

    /**
     *  查询已经结束的预约集合
     * @param dto 查询条件
     * @return 集合
     */
    List<CounselingOrderDto> getOrderDoneList(CounselingOrderDto dto);

}

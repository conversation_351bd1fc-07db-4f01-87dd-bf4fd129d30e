package cn.psycloud.psyplatform.dao.counselingroom;

import cn.psycloud.psyplatform.entity.counselingroom.CounselingRecordEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CounselingRecordDao {
    /**
     *  添加咨询记录
     * @param entity 咨询记录实体对象
     * @return 影响行数
     */
    int insert(CounselingRecordEntity entity);

    /**
     *  根据预约id查询咨询记录
     * @param id 预约id
     * @return 咨询记录集合
     */
    List<CounselingRecordEntity>getList(@Param("id") Integer id);
}

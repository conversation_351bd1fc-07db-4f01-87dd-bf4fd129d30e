package cn.psycloud.psyplatform.dao.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionCapacityDto;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionDto;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingTypeCapacityDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CounselingQuestionDao {
    /**
     *  删除在线心理答疑
     * @param id 提问Id
     * @return 影响行数
     */
   int delete(@Param("id") Integer id);

    /**
     *  新增在线心理答疑
     * @param entity 心理答疑实体对象
     * @return 提问id
     */
   int insert(CounselingQuestionEntity entity);

    /**
     *  修改在线心理答疑
     * @param entity 心理答疑实体对象
     * @return 影响行数
     */
   int update(CounselingQuestionEntity entity);

    /**
     *  根据提问id获取提问详细
     * @param questionId 提问id
     * @return 提问实体对象
     */
   CounselingQuestionDto getById(@Param("questionId") Integer questionId);

    /**
     *  根据条件查询在线心理答疑集合
     * @param dto 查询条件
     * @return 集合
     */
   List<CounselingQuestionDto> getList(CounselingQuestionDto dto);

    /**
     *  数据看板：最新答疑
     * @return 集合
     */
    List<CounselingQuestionDto> getForDashboard();

    /**
     * 查询最近某段时间内的每天在线答疑量
     * @return 集合
     */
    List<CounselingQuestionCapacityDto> getCounselingQuestionCapacitytList();

    /**
     *  查询最近某段时间内的问题类型的每天咨询量
     * @return 集合
     */
    List<CounselingTypeCapacityDto> getCounselingTypeCapacityList();
}

package cn.psycloud.psyplatform.dao.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.CounselingTypeDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingTypeEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CounselingTypeDao {
    /**
     *  删除
     * @param id 类型id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  新增
     * @param entity 咨询问题类型实体对象
     * @return 影响行数
     */
    int insert(CounselingTypeEntity entity);

    /**
     *  修改
     * @param entity 咨询问题类型实体对象
     * @return 影响行数
     */
    int update(CounselingTypeEntity entity);

    /**
     *  根据条件查询咨询问题类型集合
     * @param dto 条件对象
     * @return 类型集合
     */
    List<CounselingTypeEntity> getList(CounselingTypeDto dto);
}

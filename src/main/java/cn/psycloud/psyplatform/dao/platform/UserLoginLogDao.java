package cn.psycloud.psyplatform.dao.platform;

import cn.psycloud.psyplatform.dto.platform.UserLoginLogDto;
import cn.psycloud.psyplatform.entity.platform.UserLoginLogEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserLoginLogDao {
    /**
     *  记录登录信息
     * @param entity 登录信息实体类对象
     * @return 影响行数
     */
    int add(UserLoginLogEntity entity);

    /**
     *  获取登录信息集合
     * @param dto 查询条件
     * @return 集合
     */
    List<UserLoginLogEntity> getList(UserLoginLogDto dto);
}

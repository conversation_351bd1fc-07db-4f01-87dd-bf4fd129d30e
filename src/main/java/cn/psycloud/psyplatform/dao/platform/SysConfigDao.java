package cn.psycloud.psyplatform.dao.platform;

import cn.psycloud.psyplatform.dto.platform.SysConfigDto;
import cn.psycloud.psyplatform.entity.platform.SysConfigEntity;
import org.springframework.stereotype.Repository;

@Repository
public interface SysConfigDao {
    /**
     *  获取平台参数信息
     * @return 平台参数信息
     */
    SysConfigDto get();

    /**
     * 更新平台参数信息
     * @param  sysConfig 平台参数实体对象
     * @return 影响行数
     */
    int update(SysConfigEntity sysConfig);
}

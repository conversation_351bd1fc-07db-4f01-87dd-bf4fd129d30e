package cn.psycloud.psyplatform.dao.platform;

import cn.psycloud.psyplatform.dto.platform.DashboardDto;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public interface DashboardDao {
    /**
     *  获取系统数据看板数据
     * @return 系统数据看板数据
     */
    DashboardDto getDashboardData(DashboardDto dashboardDto);
    
    /**
     * 获取咨询形式分布
     * @param dashboardDto 查询参数
     * @return 咨询形式分布数据
     */
    List<Map<String, Object>> getConsultationsByForm(DashboardDto dashboardDto);
    
    /**
     * 获取咨询领域分布
     * @param dashboardDto 查询参数
     * @return 咨询领域分布数据
     */
    List<Map<String, Object>> getConsultationsByField(DashboardDto dashboardDto);
    
    /**
     * 获取心理风险分布
     * @param dashboardDto 查询参数
     * @return 心理风险分布数据
     */
    List<Map<String, Object>> getConsultationsByRisk(DashboardDto dashboardDto);
    
    /**
     * 获取咨询量趋势(按月)
     * @param dashboardDto 查询参数
     * @return 咨询量趋势数据
     */
    List<Map<String, Object>> getConsultationsTrend(DashboardDto dashboardDto);
    
    /**
     * 获取活动类型分布
     * @param dashboardDto 查询参数
     * @return 活动类型分布数据
     */
    List<Map<String, Object>> getActivitiesByType(DashboardDto dashboardDto);
    
    /**
     * 获取活动量趋势(按月)
     * @param dashboardDto 查询参数
     * @return 活动量趋势数据
     */
    List<Map<String, Object>> getActivitiesTrend(DashboardDto dashboardDto);
    
    /**
     * 获取测评预警等级分布
     * @param dashboardDto 查询参数
     * @return 测评预警等级分布数据
     */
    List<Map<String, Object>> getTestsByWarningLevel(DashboardDto dashboardDto);
    
    /**
     * 获取测评量趋势(按月)
     * @param dashboardDto 查询参数
     * @return 测评量趋势数据
     */
    List<Map<String, Object>> getTestsTrend(DashboardDto dashboardDto);
}

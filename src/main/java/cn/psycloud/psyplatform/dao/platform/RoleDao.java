package cn.psycloud.psyplatform.dao.platform;

import cn.psycloud.psyplatform.dto.anteroom.RoleDto;
import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.entity.anteroom.RoleEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 平台角色
 */
@Repository
public interface RoleDao {
    /**
     * 根据id删除角色
     * @param id 角色id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  新增角色
     * @param role 实体对象
     * @return 影响行数
     */
    int insert(RoleEntity role);

    /**
     *  修改角色
     * @param role 实体对象
     * @return 影响行数
     */
    int update(RoleEntity role);

    /**
     *  查询平台角色集合
     * @param dto 实体对象
     * @return 角色集合
     */
    List<RoleDto> getList(RoleDto dto);

    /**
     *  根据角色ID获取角色权限
     * @param roleId 角色id
     * @return 功能集合
     */
    List<SysFunctionDto> getGrant(@Param("roleId") Integer roleId);

    /**
     *  清空角色权限
     * @param roleId  角色id
     * @return 影响行数
     */
    int clearGrant(@Param("roleId") Integer roleId);

    /**
     *  角色授权
     * @param map 参数
     * @return 影响行数
     */
    int addGrant(Map<String,String> map);
}
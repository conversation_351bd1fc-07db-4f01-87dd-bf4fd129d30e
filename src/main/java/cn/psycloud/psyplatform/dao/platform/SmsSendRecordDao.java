package cn.psycloud.psyplatform.dao.platform;

import cn.psycloud.psyplatform.dto.platform.SmsSendRecordDto;
import cn.psycloud.psyplatform.entity.platform.SmsSendRecordEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SmsSendRecordDao {
    /**
     *  添加发送记录
     * @param entity 实体类对象
     * @return 影响函数
     */
    int addRecord(SmsSendRecordEntity entity);

    /**
     *  查询短信发送记录集合
     * @param dto 实体类对象
     * @return 集合
     */
    List<SmsSendRecordDto> getList(SmsSendRecordDto dto);
}

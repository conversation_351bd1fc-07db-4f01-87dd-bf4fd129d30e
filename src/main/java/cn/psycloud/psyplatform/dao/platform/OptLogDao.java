package cn.psycloud.psyplatform.dao.platform;

import cn.psycloud.psyplatform.dto.platform.ExportOptLogDto;
import cn.psycloud.psyplatform.dto.platform.OptLogDto;
import cn.psycloud.psyplatform.entity.platform.OptLogEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OptLogDao {
    /**
     *  记录操作日志
     * @param entity 日志实体类对象
     * @return 影响行数
     */
    int add(OptLogEntity entity);

    /**
     *  获取操作日志记录集合
     * @param dto 条件
     * @return 集合
     */
    List<OptLogEntity> getList(OptLogDto dto);

    /**
     *  导出操作日志记录
     * @param dto 条件
     * @return 集合
     */
    List<ExportOptLogDto> getExportList(OptLogDto dto);
}

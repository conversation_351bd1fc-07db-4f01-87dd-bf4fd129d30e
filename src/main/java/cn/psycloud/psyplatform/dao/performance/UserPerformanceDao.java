package cn.psycloud.psyplatform.dao.performance;

import cn.psycloud.psyplatform.dto.performance.UserPerformanceDto;
import cn.psycloud.psyplatform.entity.performance.UserPerformanceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.List;

/**
 * 用户绩效Dao
 */
@Repository
public interface UserPerformanceDao {
    /**
     * 新增用户绩效信息
     * @param userPerformanceEntity 用户绩效信息实体
     * @return 新增结果
     */
    int insertUserPerformance(UserPerformanceEntity userPerformanceEntity);

    /**
     * 根据主键ID删除用户绩效信息
     * @param id 主键ID
     * @return 删除结果
     */
    int deleteById(@Param("id") Integer id);

    /**
     * 根据用户ID和年度删除用户绩效信息
     * @param userId 用户ID
     * @param yearly 年度
     * @return 删除结果
     */
    int deleteByUserIdAndYearly(@Param("userId") Integer userId, @Param("yearly") String yearly);

    /**
     *  查询用户绩效信息列表
     * @param userPerformanceDto 查询条件
     * @return 用户绩效信息列表
     */
    List<UserPerformanceDto> getList(UserPerformanceDto userPerformanceDto);

    /**
     * 查询用户绩效数据是否存在
     * @param map 查询条件
     * @return 查询结果
     */
    int isExist(HashMap<String,Object> map);

    /**
     * 根据用户ID查询用户绩效信息列表
     * @param userId 用户ID
     * @return 用户绩效信息列表
     */
    List<UserPerformanceDto> getByUserId(@Param("userId") Integer userId);
}

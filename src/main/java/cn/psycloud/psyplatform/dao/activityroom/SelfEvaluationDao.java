package cn.psycloud.psyplatform.dao.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.SelfEvaluationDto;
import cn.psycloud.psyplatform.entity.activityroom.SelfEvaluationEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public interface SelfEvaluationDao {
    /**
     * 插入个人点评
     * @param entity 个人点评实体
     * @return 插入行数
     */
    int insert(SelfEvaluationEntity entity);

    /**
     * 删除个人点评
     * @param map 删除条件
     * @return 删除行数
     */
    int delete(HashMap<String, Object> map);

    /**
     * 根据活动ID获取个人点评列表
     * @param activityId 活动ID
     * @return 个人点评列表
     */
    List<SelfEvaluationDto> getSelfEvaluationList(@Param("activityId") Integer activityId);

    /**
     * 根据活动ID和用户ID获取个人点评
     * @param map 查询条件
     * @return 个人点评
     */
    String getMySelfEvaluationList(HashMap<String, Object> map);

    /**
     * 根据用户ID获取个人点评列表
     * @param userId 用户ID
     * @return 个人点评列表
     */
    List<SelfEvaluationDto> getMySelfEvaluationListByUerId(@Param("userId") Integer userId);
}

package cn.psycloud.psyplatform.dao.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.*;
import cn.psycloud.psyplatform.dto.survey.SurveyResultDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivityEntity;
import cn.psycloud.psyplatform.entity.activityroom.OverallEvaluationEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 心理活动
 */
@Repository
public interface ActivityDao {
    /**
     * 添加心理活动
     * @param activityEntity 心理活动实体
     * @return 影响行数
     */
    int add(ActivityEntity activityEntity);

    /**
     * 修改心理活动
     * @param activityEntity 心理活动实体
     * @return 影响行数
     */
    int update(ActivityEntity activityEntity);

    /**
     * 删除心理活动
     * @param id 心理活动id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     * 根据条件查询心理活动列表
     * @param activityDto 心理活动查询条件
     * @return 心理活动集合
     */
    List<ActivityDto> getList(ActivityDto activityDto);

    /**
     * 根据id查询心理活动
     * @param id 心理活动id
     * @return 心理活动实体
     */
    ActivityDto getById(@Param("id") Integer id);

    /**
     * 判断参与活动的用户是否已做调查问卷
     * @param map 参数集合
     * @return 是否已做调查问卷
     */
    int isSurveyDone(HashMap<String,Integer> map);

    /**
     * 获取我的活动
     * @param userId 用户id
     * @return 我的活动集合
     */
    List<ActivityDto> getMyActivities(@Param("userId") Integer userId);

    /**
     * 更新活动总评
     * @param overallEvaluationEntity 总评实体
     * @return  影响行数
     */
    int updateOverallEvaluation(OverallEvaluationEntity overallEvaluationEntity);

    /**
     * 获取活动总评
     * @param activityId 活动id
     * @return 活动总评
     */
    OverallEvaluationDto getOverallEvaluation(@Param("activityId") Integer activityId);

    /**
     *  问卷调查结果导出
     * @param dto 条件
     * @return 集合
     */
    List<LinkedHashMap<String, Object>> getExportSurveyTestResult(ExportActivitySurveyRecordDto dto);

    /**
     *  问卷调查结果导出（按选项序号）
     * @param dto 条件
     * @return 集合
     */
    List<LinkedHashMap<String, Object>> getExportSurveyTestResultByText(ExportActivitySurveyRecordDto dto);

    /**
     * 获取活动报告
     * @param activityId 活动id
     * @return 活动报告实体类对象
     */
    ActivityReportDto getActivityReport(@Param("activityId") Integer activityId);

    /**
     * 获取活动参与人员清单
     * @param activityId 活动id
     * @return 参与人员清单
     */
    List<ParticipantDto> getActivityParticipants(@Param("activityId") Integer activityId);

    /**
     * 获取咨询师的活动集合：select
     * @param userId 咨询师id
     * @return 活动集合
     */
    List<ActivityForSelectDto> getCounselorActivitiesForSelect(@Param("userId") Integer userId);

    /**
     * 获取活动问卷结果数据
     * @param activityId 活动ID
     * @param surveyId 问卷ID
     * @return 问卷结果集合
     */
    List<SurveyResultDto> getActivitySurveyResults(@Param("activityId") Integer activityId, @Param("surveyId") Integer surveyId);

    /**
     * 获取活动数据看板基础统计数据
     * @param dto 查询条件
     * @return 基础统计数据
     */
    ActivityDashboardDto getActivityDashboardBasicStats(ActivityDto dto);

    /**
     * 获取活动类型统计数据
     * @param dto 查询条件
     * @return 活动类型统计数据
     */
    List<ActivityTypeStatDto> getActivityTypeStats(ActivityDto dto);

    /**
     * 获取咨询师统计数据
     * @param dto 查询条件
     * @return 咨询师统计数据
     */
    List<CounselorStatDto> getCounselorStats(ActivityDto dto);
}

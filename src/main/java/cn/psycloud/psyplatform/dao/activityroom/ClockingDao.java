package cn.psycloud.psyplatform.dao.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.ClockingDto;
import cn.psycloud.psyplatform.entity.activityroom.ClockingEntity;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.List;

/**
 * 签到签退DAO
 */
@Repository
public interface ClockingDao {
    /**
     * 插入签到签退记录
     * @param entity 签到签退实体
     * @return 影响行数
     */
    int insert(ClockingEntity entity);

    /**
     * 查询签到签退记录
     * @param map 参数集合
     * @return 签到签退实体
     */
    int isExists(HashMap<String,Integer> map);

    /**
     * 查询签到签退记录列表
     * @param clockingDto 签到签退DTO
     * @return 签到签退实体列表
     */
    List<ClockingDto> getList(ClockingDto clockingDto);

    /**
     * 查询当前用户的活动状态
     * @param map 参数集合
     * @return 活动状态
     */
    Integer getUserActivityStatus(HashMap<String,Integer> map);
}

package cn.psycloud.psyplatform.dao.activityroom;

import cn.psycloud.psyplatform.entity.activityroom.ActivityPicEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivityPicDao {

    /**
     * 上传活动图片
     * @param entity 活动图片实体
     * @return 是否成功
     */
    int uploadPic(ActivityPicEntity entity);

    /**
     * 删除活动图片
     * @param id 活动图片id
     * @return 是否成功
     */
    int delPic(@Param("id") Integer id);

    /**
     * 获取活动图片列表
     * @param activityId 活动id
     * @return 活动图片列表
     */
    List<ActivityPicEntity> getList(@Param("activityId") Integer activityId);
}

package cn.psycloud.psyplatform.dao.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.ActivityClockingPicDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivityClockingPicEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ActivityClockingPicDao {
    /**
     * 上传签到图片
     * @param entity 签到图片实体类
     * @return 操作是否成功
     */
    int uploadPic(ActivityClockingPicEntity entity);

    /**
     * 删除图片
     * @param id 图片id
     * @return 操作是否成功
     */
    int delPic(@Param("id") Integer id);

    /**
     * 获取签到图片列表
     * @return 签到图片列表
     */
    List<ActivityClockingPicEntity> getList(ActivityClockingPicDto activityClockingPicDto);

    /**
     * 获取随机图片
     * @return 图片名称
     */
    String getRandomPic(@Param("structId") Integer structId);
}

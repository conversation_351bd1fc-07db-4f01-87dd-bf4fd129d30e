package cn.psycloud.psyplatform.dao.archiveroom;

import cn.psycloud.psyplatform.dto.archiveroom.ArchiveAdviceDto;
import cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ArchiveAdviceDao {
    /**
     * 删除评语
     * @param id 评语id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  添加评语
     * @param entity 评语实体对象
     * @return 影响行数
     */
    int insert(AdviceEntity entity);

    /**
     *  修改评语
     * @param entity 评语实体对象
     * @return 影响行数
     */
    int update(AdviceEntity entity);

    /**
     *  查询评语集合
     * @param dto 查询条件
     * @return 集合
     */
    List<AdviceEntity> getList(ArchiveAdviceDto dto);

    /**
     *  根据id查询评语
     * @param id 评语id
     * @return 评语实体对象
     */
    AdviceEntity getById(@Param("id") Integer id);
}

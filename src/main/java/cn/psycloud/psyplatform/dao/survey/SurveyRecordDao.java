package cn.psycloud.psyplatform.dao.survey;

import cn.psycloud.psyplatform.dto.activityroom.ActivitySurveyRecordDto;
import cn.psycloud.psyplatform.dto.activityroom.SurveyAnswerDetailDto;
import cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto;
import cn.psycloud.psyplatform.dto.survey.ExportSurveyRecordDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivitySurveyEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyResultEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface SurveyRecordDao {
    /**
     *  根据id删除调查问卷记录
     * @param id 记录id
     * @return 影响行数
     */
    int delRecord(@Param("id") Integer id);

    /**
     *  删除测评任务下某个用户的所有问卷作答记录
     * @param map 传参
     * @return 影响行数
     */
    int delRecordByTaskId(Map<String,Integer> map);

    /**
     * 删除问卷调查任务下某个用户的所有问卷作答记录
     * @param map 传参
     * @return 影响行数
     */
    int delRecordOfTaskSurveyByTaskId(Map<String,Integer> map);

    /**
     *  添加问卷作答记录：测评任务
     * @param entity 实体对象
     * @return 记录id
     */
    int addRecord(TaskSurveyRecordEntity entity);

    /**
     *  添加问卷作答记录：活动
     * @param entity 实体对象
     * @return 记录id
     */
    int addRecordForActivity(ActivitySurveyEntity entity);

    /**
     *  添加问卷作答选项结果
     * @param entity 结果实体对象
     * @return 影响行数
     */
    int addResult(SurveyResultEntity entity);

    /**
     *  删除作答记录下的所有选项结果
     * @param recordId 记录id
     * @return 影响行数
     */
    int deleteResultByRecordId(@Param("recordId") Integer recordId);

    /**
     * 更新作答记录状态
     * @param recordId 记录id
     * @return 影响行数
     */
    int updateSurveyRecordState(@Param("recordId") Integer recordId);

    /**
     * 更新作答时间
     * @param map 传参
     * @return 影响行数
     */
    int updateSurveyRecordDate(Map<String,Object> map);

    /**
     *  添加测评任务与调查问卷关联记录
     * @param map 传参
     * @return 影响行数
     */
    int addTaskSurvey(Map<String,Integer> map);

    /**
     *  添加活动与调查问卷关联记录
     * @param map 传参
     * @return 影响行数
     */
    int addActivitySurvey(HashMap<String, Integer> map);

    /**
     *  删除活动下所有的作答记录
     * @param map 传参
     * @return 影响行数
     */
    int delRecordByActivityId(HashMap<String, Integer> map);

    /**
     *  根据条件查询问卷调查记录集合：测评任务
     * @param dto 查询条件
     * @return 集合
     */
    List<TaskSurveyDto> getList(TaskSurveyDto dto);

    /**
     * 根据条件查询问卷调查记录集合：问卷调查任务
     * @param dto 查询条件
     * @return 集合
     */
    List<TaskSurveyDto> getListForTaskSurvey(TaskSurveyDto dto);

    /**
     *  获取我的问卷记录：问卷调查任务
     * @param dto 查询条件
     * @return 问卷记录集合
     */
    List<TaskSurveyDto> getMyRecords(TaskSurveyDto dto);

    /**
     *  导出问卷作答记录：测评任务
     * @param dto 查询条件
     * @return 作答记录集合
     */
    List<ExportSurveyRecordDto> getExportRecordList(TaskSurveyDto dto);

    /**
     *  导出问卷作答记录：问卷调查任务
     * @param dto 查询条件
     * @return 作答记录集合
     */
    List<ExportSurveyRecordDto> getExportTaskSurveyRecord(TaskSurveyDto dto);

    /**
     *  导出调查问卷任务的结果
     * @param dto 条件
     * @return 作答结果集合
     */
    List<LinkedHashMap<String, Object>> getExportTaskSurveyResult(TaskSurveyDto dto);


    /**
     *  获取活动的问卷作答记录
     * @param dto 查询条件
     * @return 作答记录集合
     */
    List<ActivitySurveyRecordDto> getActivitySurveyRecordList(ActivitySurveyRecordDto dto);

    /**
     * 获取问卷作答记录id
     * @param map 传参
     * @return 记录id
     */
    int getSurveyRecordId(HashMap<String, Integer> map);

    /**
     * 获取问卷作答记录的详细信息（题目和选择结果）
     * @param recordId 记录ID
     * @return 作答详细信息集合
     */
    List<SurveyAnswerDetailDto> getSurveyAnswerDetails(@Param("recordId") Integer recordId);
}

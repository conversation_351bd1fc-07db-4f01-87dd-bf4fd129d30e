package cn.psycloud.psyplatform.dao.survey;

import cn.psycloud.psyplatform.dto.survey.SurveyQuestionDto;
import cn.psycloud.psyplatform.entity.survey.SurveyQuestionEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface SurveyQuestionDao {
    /**
     *  根据问卷Id查询问题集合
     * @param surveyId 问卷id
     * @return 问题集合
     */
    List<SurveyQuestionDto> getListBySurveyId(@Param("surveyId") Integer surveyId);

    /**
     *  获取题目总数
     * @param surveyId 问卷id
     * @return 题目数
     */
    int getQuestionCount(@Param("surveyId") Integer surveyId);

    /**
     *  根据题目id查询题目信息
     * @param qId 题目id
     * @return map集合
     */
    Map<String, Integer> getById(@Param("qId") Integer qId);

    /**
     *  删除题目
     * @param qId 题目id
     * @return 影响行数
     */
    int delete(@Param("qId") Integer qId);

    /**
     *  添加题目
     * @param entity 题目实体对象
     * @return 题目id
     */
    int add(SurveyQuestionEntity entity);

    /**
     *  修改题目
     * @param entity 题目实体对象
     * @return 影响行数
     */
    int update(SurveyQuestionEntity entity);

    /**
     *  删除题目后更新题目序号
     * @param map 参数
     * @return 影响行数
     */
    int updateQno(Map<String, Integer> map);

    /**
     *  导出问卷选项结果数据时查询选项题目
     * @param surveyId  问卷id
     * @return 题目序号集合
     */
    List<LinkedHashMap<String,Object>> getListForExport(@Param("surveyId") Integer surveyId);
}

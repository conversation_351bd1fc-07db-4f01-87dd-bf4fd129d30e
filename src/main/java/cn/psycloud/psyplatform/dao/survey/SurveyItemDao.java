package cn.psycloud.psyplatform.dao.survey;

import cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyItemEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyItemDao {
    /**
     *  删除选项（根据选项id）
     * @param id 选项id
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id);

    /**
     *  添加选项
     * @param entity 选项实体对象
     * @return 影响行数
     */
    int insert(SurveyItemEntity entity);

    /**
     *  删除选项（根据题目id）
     * @param qId 题目id
     * @return 影响行数
     */
    int deleteByQid(@Param("qId") Integer qId);

    /**
     *  根据题目id查询所有选项
     * @param qId 题目id
     * @return 选项集合
     */
    List<SurveyItemEntity> getListByQId(@Param("qId") Integer qId);

    /**
     *  获取选项总数
     * @param qId 题目id
     * @return 选项数
     */
    int getItemCount(@Param("qId") Integer qId);

    /**
     *  获取选项的最大数
     * @param surveyId 问卷Id
     * @return 选项的最大数
     */
    int getMaxItemNo(@Param("surveyId") Integer surveyId);
}


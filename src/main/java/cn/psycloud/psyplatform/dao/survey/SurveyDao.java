package cn.psycloud.psyplatform.dao.survey;

import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import cn.psycloud.psyplatform.entity.survey.SurveyEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SurveyDao {
    /**
     *  删除
     * @param id 问卷id
     * @return 影响行数
     */
    int delete(@Param("id") Integer id);

    /**
     *  新增
     * @param entity 调查问卷实体类对象
     * @return 影响行数
     */
    int insert(SurveyEntity entity);

    /**
     *  修改
     * @param entity 调查问卷实体类对象
     * @return 影响行数
     */
    int update(SurveyEntity entity);

    /**
     *  更新完成状态
     * @param map 传参
     * @return 影响行数
     */
    int updateDone(Map<String, Integer> map);

    /**
     *  根据id查询问卷信息
     * @param surveyId 问卷id
     * @return 问卷实体对象
     */
    SurveyDto getById(@Param("surveyId") Integer surveyId);

    /**
     *  获取问卷集合
     * @param dto 条件
     * @return 问卷集合
     */
    List<SurveyDto> getList(SurveyDto dto);

    /**
     *  获取问卷集合：select2
     * @return 问卷集合
     */
    List<SurveyEntity> getListForSelect();

    /**
     *  根据测评任务查询问卷集合
     * @param taskId 任务id
     * @return 集合
     */
    List<SurveyEntity> getListByTaskId(@Param("taskId") Integer taskId);

    List<SurveyEntity> getListByMeasuringTaskId(@Param("taskId") Integer taskId);

    /**
     *  判断问卷名称是否重复
     * @param surveyName 问卷名称
     * @return 问卷数量
     */
    int isSurveyNameExists(@Param("surveyName") String surveyName);
}

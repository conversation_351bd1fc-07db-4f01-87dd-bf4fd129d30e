package cn.psycloud.psyplatform.entity.platform;

import lombok.Data;

/**
 * 短信配置
 */
@Data
public class SmsConfigEntity {
    private static final long serialVersionUID = 1L;
    //服务商
    private String serviceName;
    //短信签名
    private String signName;
    //是否启用短信登录
    private Integer isLogin;
    //短信登录：短信模板
    private String loginTemplate;
    //是否启用注册短信验证
    private Integer isRegister;
    //短信注册：短信模板
    private String registerTemplate;
    //是否启用短信手机绑定
    private Integer isMobileBind;
    //手机绑定：短信模板
    private String mobileBindTemplate;
    //是否启用修改密码短信验证
    private Integer isModifyPwd;
    //修改密码：短信模板
    private String modifyPwdTemplate;
    //是否启用测评异常短信
    private Integer isMeasuringNotify;
    //测评异常提醒：短信模板
    private String measuringNotifyTemplate;
    //是否启用咨询预约提醒
    private Integer isCounselingOrder;
    //预约成功（咨询师）：短信模板
    private String counselorCounselingSuccessTemplate;
    //预约成功（来访者）：短信模板
    private String visitorCounselingSuccessTemplate;
    //预约状态变化（咨询师）：短信模板
    private String counselorCounselingChangeTemplate;
    //预约状态变化（来访者）：短信模板
    private String visitorCounselingChangeTemplate;
    //是否启用心理训练营开营提醒
    private Integer isTrainingCampStart;
    //心理训练营开营提醒：短信模版代码
    private String trainingCampStartTemplate;
}

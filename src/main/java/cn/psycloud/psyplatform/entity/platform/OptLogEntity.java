package cn.psycloud.psyplatform.entity.platform;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.Date;

/**
 *  操作日志
 */
@Data
public class OptLogEntity {
    private static final long serialVersionUID = 1L;
    //id
    public Integer id;
    //用户id
    public Integer userId;
    //操作时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date optDate;
    //操作页面
    public String optUrl;
    //操作对象
    public String optClassname;
    //操作方法
    public String optMethod;
    //操作参数
    public String optArgs;
}

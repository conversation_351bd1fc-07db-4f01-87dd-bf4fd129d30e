package cn.psycloud.psyplatform.entity.platform;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.Date;

/**
 *  用户登录日志
 */
@Data
public class UserLoginLogEntity {
    private static final long serialVersionUID = 1L;
    //id
    public Integer id;
    //用户id
    public String loginName;
    //登录日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date loginDate;
    //IP地址
    public String ipAddress;
    //设备信息
    public String deviceInfo;
    //登录状态
    public Integer state;
    //登录方式
    public Integer loginWay;
}

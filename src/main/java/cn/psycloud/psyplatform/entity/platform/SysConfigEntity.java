package cn.psycloud.psyplatform.entity.platform;

import lombok.Data;
import java.util.Date;

/**
 * 平台参数
 */
@Data
public class SysConfigEntity {
    private static final long serialVersionUID = 1L;
    //平台名称
    public String platformName ;
    //机构名称
    public String orgName ;
    //是否开放注册：0-否 1-开放
    public Integer isOpenReg ;
    //注册是否需要审核： 0- 否 1-是
    public Integer isRegChecked ;
    //测评异常是否通知： 0- 否 1-是
    public Integer isAbnormalNotify ;
    //咨询预约是否通知： 0- 否 1-是
    public Integer isCounselingNotify ;
    //是否启用手机短信
    public Integer isSmsEnabled ;
    //是否启用积分
    public Integer isPointsEnabled ;
    //是否启用微信登录接口
    public Integer isWeChatEnabled ;
    //软件安装日期
    public Date installDate ;
    //咨询倾诉热线
    public String counselingHotline ;
}

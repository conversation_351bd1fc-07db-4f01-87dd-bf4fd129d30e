package cn.psycloud.psyplatform.entity.activityroom;

import lombok.Data;
import java.util.Date;

/**
 * 个人点评
 */
@Data
public class SelfEvaluationEntity {
    private static final long serialVersionUID = 1L;
    // 主键ID
    private Integer id;
    // 活动ID
    private Integer activityId;
    // 操作人ID（点评人）
    private Integer operator;
    // 被点评用户ID
    private Integer userId;
    // 点评内容
    private String evaluationContent;
    // 点评标签（逗号分隔）
    private String tags;
    // 操作时间
    private Date operateDate;
    // 是否有效
    private Boolean isValid;
}

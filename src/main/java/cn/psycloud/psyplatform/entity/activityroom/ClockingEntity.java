package cn.psycloud.psyplatform.entity.activityroom;

import lombok.Data;
import java.util.Date;

/**
 * 签到签退实体
 */
@Data
public class ClockingEntity {
    private static final long serialVersionUID = 1L;
    //主键ID
    private Integer id;
    //用户ID
    private Integer userId;
    //活动ID
    private Integer activityId;
    //操作时间
    private Date clockingTime;
    //操作类型：1-签到，2-签退
    private Integer actionType;
}

package cn.psycloud.psyplatform.entity.activityroom;

import lombok.Data;
import java.util.Date;

/**
 * 心理活动实体
 */
@Data
public class ActivityEntity {
    private static final long serialVersionUID = 1L;
    //活动id
    private Integer id;
    //活动主题
    private String activityName;
    //活动介绍
    private String activityIntro;
    //活动类型
    private Integer activityType;
    //活动开始时间
    private Date startTime;
    //活动结束时间
    private Date endTime;
    //负责咨询师
    private Integer counselor;
    //创建日期
    private Date addDate;
    //是否有效
    private Integer isValid;
    //活动封面
    private String activityCover;
    //调查问卷id
    private Integer surveyId;
    //操作人
    private Integer operator;
    //活动总评
    private String overallEvaluation;
    // 活动所属组织
    private Integer structId;
}

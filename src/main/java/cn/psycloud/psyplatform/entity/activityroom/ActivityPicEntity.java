package cn.psycloud.psyplatform.entity.activityroom;

import lombok.Data;
import java.util.Date;

/**
 * 活动图片
 */
@Data
public class ActivityPicEntity {
    private static final long serialVersionUID = 1L;
    //主键
    private Integer id;
    //活动ID
    private Integer activityId;
    //图片名称
    private String fileName;
    //上传时间
    private Date uploadTime;
    //上传人ID
    private Integer operator;
    //是否有效
    private Boolean isValid;
} 
package cn.psycloud.psyplatform.entity.activityroom;

import lombok.Data;
import java.util.Date;

/**
 * 签到图片
 */
@Data
public class ActivityClockingPicEntity {
    private static final long serialVersionUID = 1L;
    // 主键
    private Integer id;
    // 机构ID
    private Integer structId;
    //文件名称
    private String fileName;
    //上传时间
    private Date uploadTime;
    //上传人
    private Integer operator;
    //是否有效
    private Integer isValid;
}

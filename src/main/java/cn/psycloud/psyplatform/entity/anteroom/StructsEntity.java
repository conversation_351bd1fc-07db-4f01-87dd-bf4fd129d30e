package cn.psycloud.psyplatform.entity.anteroom;

import lombok.Data;

/**
 * 组织实体类
 */
@Data
public class StructsEntity {
    private static final long serialVersionUID = 1L;
    //组织ID
    private Integer id;
    //组织名称
    private String structName;
    //父ID
    private Integer parentId;
    //组织编码
    private String orgCode;
    //组织父编码
    private String orgParentCode;
    //是否有效
    private Integer IsValid;
}
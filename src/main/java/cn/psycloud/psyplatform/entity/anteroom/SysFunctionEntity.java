package cn.psycloud.psyplatform.entity.anteroom;

import lombok.Data;

/**
 * 平台功能实体类
 */
@Data
public class SysFunctionEntity {
    private static final long serialVersionUID = 1L;
    //自增ID
    private Integer id;
    //功能编码
    private String functionCode;
    //父功能编码
    private String parentCode;
    //功能名称
    private String functionName;
    //功能类型
    private String functionType;
    //Url
    private String url;
    //Icon
    private String icon;
    //排序
    private Integer sort;
    //层级
    private Integer level;
    //是否有效
    private Integer isValid;
}
package cn.psycloud.psyplatform.entity.anteroom;

import lombok.Data;

import java.util.Date;

/**
 * 积分明细
 */
@Data
public class PointsDetailEntity {
    private static final long serialVersionUID = 1L;
    //自增ID
    private Integer id;
    //用户ID
    private Integer userId;
    //积分明细
    private Integer point;
    //积分操作类型： 0 - 消费 1- 充值
    private Integer chargeType;
    //积分来源
    private Integer sourceId;
    //操作人
    private Integer operator;
    //操作日期
    private Date operateDate;
}

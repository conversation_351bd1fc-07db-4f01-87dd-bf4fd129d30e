package cn.psycloud.psyplatform.entity.anteroom;

import lombok.Data;

import java.util.Date;

/**
 * 用户详细信息实体类
 */
@Data
public class UserInfoEntity {
    private static final long serialVersionUID = 1L;
    //自增id
    private  Integer id;
    //用户ID
    private Integer userId;
    //所属机构ID
    private Integer structId;
    //真实姓名
    private String realName;
    //性别
    private String sex;
    //出生日期
    private String birth;
    //身份证号
    private String iDCardNo;
    //民族
    private String nation;
    //手机号码
    private String mobile;
    //手机号码是否绑定
    private Integer isMobileBind;
    //紧急联系人
    private String emergencyContactPerson;
    //紧急联系电话
    private String emergencyContactMobile;
    //邮箱
    private String email;
    //地址/省
    private String addressProvince;
    //地址/市
    private String addressCity;
    //地址/区
    private String addressDist;
    //详细地址
    private String addressDetail;
    //头像
    private String headImg;
    //籍贯
    private String nativePlace;
    //文化程度
    private String education;
    //职业
    private String job;
    //宗教信仰
    private String religion;
    //婚姻状态
    private String marriage;
    //个人简介
    private String description;
    //是否审核
    private Integer isChecked;
    //最后登录IP地址
    private String lastLoginIp;
    //最后登录时间
    private Date lastLoginDateTime;
    //注册日期
    private Date regDate;
    //积分
    private Integer points;
    //操作者
    private Integer operator;
    //是否有效
    private Integer isValid;
}

package cn.psycloud.psyplatform.entity.anteroom;

import lombok.Data;

import java.util.Date;

/**
 * 平台角色实体类
 */
@Data
public class RoleEntity {
    private static final long serialVersionUID = 1L;
    //角色ID
    private Integer id;
    //角色名称
    private String roleName;
    //添加日期
    private Date addDate;
    //添加人
    private Integer operator;
    //标识： p-平台角色 j-机构角色
    private String flag;
    //是否有效
    private Integer isValid;
}

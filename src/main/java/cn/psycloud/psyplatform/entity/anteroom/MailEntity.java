package cn.psycloud.psyplatform.entity.anteroom;

import lombok.Data;

import java.util.Date;

/**
 * 站内消息
 */
@Data
public class MailEntity {
    private static final long serialVersionUID = 1L;
    //消息ID
    private Integer id;
    //消息标题
    private String msgTitle;
    //消息内容
    private String msgContent;
    //收件人
    private Integer toUser;
    //发送人
    private Integer fromUser;
    //是否已读
    private Integer isRead;
    //发送日期
    private Date sendDate;
    //是否有效
    private Integer isValid;
}

package cn.psycloud.psyplatform.entity.measuringroom;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 测评记录实体类
 */
@Data
public class TestRecordEntity {
    private static final long serialVersionUID = 1L;
    //测评记录ID
    private Integer id;
    //测评对象ID
    private Integer userId;
    //测评量表ID
    private Integer scaleId;
    //测评状态
    private Integer state;
    //开始测评时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //结束测评时间
    private Date endTime;
    //结果解释
    private String interpretation;
    //是否删除
    private Integer isValid;
}
package cn.psycloud.psyplatform.entity.measuringroom;

import lombok.Data;
import java.util.Date;

/**
 * 量表实体类
 */
@Data
public class ScaleEntity {
    private static final long serialVersionUID = 1L;
    //量表ID
    private Integer id;
    //量表名称
    private String scaleName;
    //量表类型ID
    private Integer scaleTypeId;
    //量表别名
    private String scaleAlias;
    //量表介绍
    private String scaleIntro;
    //指导语
    private String scaleGuide;
    //答题所需时间（分钟）
    private Integer needTime;
    //限制条件
    private String testLimit;
    //年龄限制
    private String ageLimit;
    //是否完成（0-否 1-是）
    private Integer isDone;
    //创建时间
    private Date createDate;
    //是否推荐（0-否 1-是）
    private Integer isRecommend;
    //是否有效（0-否 1-是）
    private Integer isValid;
    //缩略图图片
    private String thumbnail;
    //测试次数
    private Integer testCount;
    //排序
    private Integer sort;
}
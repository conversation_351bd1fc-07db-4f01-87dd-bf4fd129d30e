package cn.psycloud.psyplatform.entity.measuringroom;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TestScoreEntity {
    private static final long serialVersionUID = 1L;
    //测评记录ID
    private Integer recordId;
    // 因子ID
    private Integer factorId;
    //原始分
    private BigDecimal originalScore;
    //因子分
    private BigDecimal score;
    //是否异常
    private Integer isAbnormal;
    //异常值
    private BigDecimal abnormalValue;
    //预警级别
    private Integer warningLevel;
}

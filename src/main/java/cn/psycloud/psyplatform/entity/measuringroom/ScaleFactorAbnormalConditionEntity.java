package cn.psycloud.psyplatform.entity.measuringroom;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 因子异常条件
 */
@Data
public class ScaleFactorAbnormalConditionEntity {
    private static final long serialVersionUID = 1L;
    //自增ID
    private Integer id;
    //因子ID
    private Integer factorId;
    //年龄异常条件
    private Integer ageCondition;
    //年龄异常值
    private Integer ageValue;
    //性别异常值
    private String sexCondition;
    //得分异常条件
    private Integer scoreCondition;
    //得分异常条件值
    private BigDecimal scoreValue;
}


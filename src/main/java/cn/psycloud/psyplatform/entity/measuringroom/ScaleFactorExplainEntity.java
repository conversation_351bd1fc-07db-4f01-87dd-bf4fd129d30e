package cn.psycloud.psyplatform.entity.measuringroom;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 因子结果解释
 */
@Data
public class ScaleFactorExplainEntity {
    private static final long serialVersionUID = 1L;
    //自增ID
    private Integer id;
    //因子ID
    private Integer factorId;
    //因子名称
    private String factorName;
    //开始值
    private BigDecimal startValue;
    //结束值
    private BigDecimal endValue;
    //解释内容
    private String interpretation;
    //预警级别
    private Integer warningLevel;
}
package cn.psycloud.psyplatform.entity.measuringroom;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 结果解释
 */
@Data
public class TestRecordExplainEntity {
    private static final long serialVersionUID = 1L;
    //测评记录ID
    private Integer recordId;
    //因子ID
    private Integer factorId;
    // 因子名称
    private String factorName;
    //原始分
    private BigDecimal originalScore;
    //因子分
    private BigDecimal score;
    //结果解释
    private String interpretation;
}
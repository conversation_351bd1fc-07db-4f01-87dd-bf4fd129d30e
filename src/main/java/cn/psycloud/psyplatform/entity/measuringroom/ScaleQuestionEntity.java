package cn.psycloud.psyplatform.entity.measuringroom;

import lombok.Data;

/**
 * 量表的条目实体类
 */
@Data
public class ScaleQuestionEntity {
    private static final long serialVersionUID = 1L;
    //条目ID
    private Integer id;
    //条目内容
    private String qContent;
    //条目类型：1-单选 2-多选 3-填空
    private Integer qType;
    //条目序号
    private Integer qNumber;
    //量表ID
    private Integer scaleId;
    //是否有效
    private Integer isValid;
}
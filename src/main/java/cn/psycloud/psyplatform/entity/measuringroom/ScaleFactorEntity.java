package cn.psycloud.psyplatform.entity.measuringroom;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 量表因子实体类
 */
@Data
public class ScaleFactorEntity {
    private static final long serialVersionUID = 1L;
    //因子ID
    private Integer id;
    //因子序号
    private Integer factorNo;
    //因子类型：1-普通因子 2-复合因子
    private Integer factorType;
    //量表
    private Integer scaleId;
    //因子名称
    private String factorName;
    //量表英文名
    private String factorEn;
    //量表简称
    private String factorShortName;
    //计分方式
    private Integer formulaId;
    //条目集合
    private String qIds;
    //因子id集合
    private String factorIds;
    //复合因子计分公式
    private String compute;
    //最小分
    private BigDecimal minScore;
    //最大分
    private BigDecimal maxScore;
    //平均分
    private BigDecimal avgScore;
    //标准分
    private BigDecimal standardScore;
    //是否效度量表
    private Integer isLie;
    //是否有效
    private Integer isValid;
}
package cn.psycloud.psyplatform.entity.measuringroom;

import cn.psycloud.psyplatform.entity.survey.SurveyResultEntity;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 *  任务里的调查问卷
 */
@Data
public class TaskSurveyRecordEntity {
    private static final long serialVersionUID = 1L;
    //调查问卷记录Id
    private Integer id;
    //问卷答题人
    private Integer userId;
    //问卷id
    private Integer surveyId;
    //作答时间
    private Date recordDate;
    //是否完成
    private Integer isDone;
    //是否有效
    private Integer isValid;
    //测评任务id
    private Integer taskId;
    //作答选项结果集合
    private List<SurveyResultEntity> listAnswers;
}

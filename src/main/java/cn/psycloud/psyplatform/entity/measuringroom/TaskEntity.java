package cn.psycloud.psyplatform.entity.measuringroom;

import lombok.Data;

import java.util.Date;

/**
 * 测评任务
 */
@Data
public class TaskEntity {
    private static final long serialVersionUID = 1L;
    //任务ID
    private Integer id;
    //任务名称
    private String taskName;
    //开始时间
    private Date startTime;
    //结束时间
    private Date endTime;
    //测评负责人
    private Integer personInCharge;
    //任务类型：1-限定测试 2-非限定 3-自由测试
    private Integer taskType;
    //是否限制测试数量
    private Integer isLimitTestCount;
    //测试次数
    private Integer limitTestCount;
    //结果查看规则
    private Integer resultViewRule;
    //是否有效
    private Integer isValid;
    //是否启用调查问卷
    private Integer isSurvey;
    //问卷id
    private Integer surveyId;
    //任务使用场景：1- 量表测评 2- 调查问卷
    private Integer taskKind;
    //是否显示背景图片
    private Integer showBackground;
    //背景图片文件名
    private String backgroundUrl;
}

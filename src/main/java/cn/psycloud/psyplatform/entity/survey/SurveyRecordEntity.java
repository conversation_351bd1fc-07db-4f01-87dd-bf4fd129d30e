package cn.psycloud.psyplatform.entity.survey;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class SurveyRecordEntity {
    //调查问卷记录Id
    private Integer id;
    //问卷答题人
    private Integer userId;
    //问卷id
    private Integer surveyId;
    //作答时间
    private Date recordDate;
    //是否完成
    private Integer isDone;
    //是否有效
    private Integer isValid;
    //作答选项结果集合
    private List<SurveyResultEntity> listAnswers;
}

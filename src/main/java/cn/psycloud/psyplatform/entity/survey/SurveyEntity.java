package cn.psycloud.psyplatform.entity.survey;
import lombok.Data;
import java.util.Date;

/**
 *  调查问卷
 */
@Data
public class SurveyEntity {
    private static final long serialVersionUID = 1L;
    //问卷ID
    private Integer id;
    //问卷名称
    private String surveyName;
    //是否启用
    private Integer isEnabled;
    //添加人
    private Integer operator;
    //添加日期
    private Date createDate;
    //是否完成
    private Integer isDone;
    //是否有效
    private Integer isValid;
}

package cn.psycloud.psyplatform.entity.counselingroom;

import lombok.Data;

import java.util.Date;

/**
 * 在线心理答疑回复
 */
@Data
public class CounselingQuestionRespondEntity {
    private static final long serialVersionUID = 1L;
    //回复ID
    private Integer id;
    //问题ID
    private Integer questionId;
    //回复用户ID
    private Integer responder;
    //回复内容
    private String content;
    //回复日期
    private Date addDate;
    //是否有效
    private Integer isValid;
}

package cn.psycloud.psyplatform.entity.counselingroom;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.Date;

/**
 * 咨询记录实体类
 */
@Data
public class CounselingRecordEntity {
    private static final long serialVersionUID = 1L;
    //自增id
    private  Integer id;
    //预约ID
    private Integer orderId;
    //发送人
    private String realName;
    //咨询内容
    private String counselingContent;
    //发送时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;
}

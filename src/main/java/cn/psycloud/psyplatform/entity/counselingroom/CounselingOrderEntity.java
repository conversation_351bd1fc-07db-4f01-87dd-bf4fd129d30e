package cn.psycloud.psyplatform.entity.counselingroom;

import lombok.Data;
import java.util.Date;

/**
 * 预约咨询实体类
 */
@Data
public class CounselingOrderEntity {
    private static final long serialVersionUID = 1L;
    //预约ID
    private Integer id;
    //来访者ID
    private Integer visitorId;
    //预约开始时间
    private Date startTime;
    //预约结束时间
    private Date endTime;
    //咨询问题类型
    private Integer counselingTypeId;
    //咨询方式
    private Integer counselingWay;
    //是否重点个案
    private Integer isPoint;
    //自我分析
    private String selfComment;
    //问题描述
    private String description;
    //排班ID
    private Integer schedulingId;
    //处理预约意见
    private String handleInfo;
    //咨询项目ID
    private  Integer counselingItemId;
    //预约状态：0-未处理  1- 已接受  2- 取消  3-已结束
    private Integer state;
    //添加日期
    private Date addDate;
    //添加人
    private Integer operator;
    //是否有效
    private Integer isValid;
}
package cn.psycloud.psyplatform.entity.counselingroom;

import lombok.Data;
import java.util.Date;

/**
 * 咨询师排班实体类
 */
@Data
public class SchedulingEntity {
    private static final long serialVersionUID = 1L;
    //排班ID
    private Integer id;
    //咨询师ID
    private Integer counselorId;
    //排班开始时间
    private Date startTime;
    //排班结束时间
    private Date endTime;
    //排班日期
    private Date schedueDate;
    //排班人
    private Integer operator;
}
package cn.psycloud.psyplatform.entity.counselingroom;

import lombok.Data;

import java.util.Date;

@Data
public class CounselingQuestionEntity {
    private static final long serialVersionUID = 1L;
    //提问ID
    private Integer id;
    //提问用户ID
    private Integer questioner;
    //问题类型ID
    private Integer counselingTypeId;
    //问题标题
    private String title;
    //问题描述
    private String content;
    //是否匿名
    private Integer isAnonymous;
    //图片
    private String img;
    //提问时间
    private Date addDate;
    //是否有效
    private Integer isValid;

}

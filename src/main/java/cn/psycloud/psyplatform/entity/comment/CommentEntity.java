package cn.psycloud.psyplatform.entity.comment;

import lombok.Data;
import java.util.Date;

@Data
public class CommentEntity {
    private static final long serialVersionUID = 1L;
    //评论Id
    private Integer id;
    //功能模块：1- 心理训练营 2-训练营课程 3- 心理微课
    private Integer functionType;
    //产品id
    private Integer productId;
    //评论用户ID
    private Integer userId;
    //评论内容
    private String commentContent;
    //评论日期
    private Date commentDate;
    //是否审核
    private Integer isChecked;
    //是否有效
    private Integer isValid;
}

package cn.psycloud.psyplatform.dto.consultationcase;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

/**
 * 心理咨询个案
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConsultationCaseDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //主键ID
    private Integer id;
    //咨询日期
    @JSONField(format = "yyyy-MM-dd")
    private Date consultationDate;
    //咨询形式(1:驻场咨询 2:线上咨询 3:门店咨询)
    private Integer consultationForm;
    //咨询时长(分钟)
    private Integer consultationDuration;
    //咨询类型(1:首次咨询 2:第二次咨询 3:第三次咨询 4:第四次咨询 5:第五次咨询 6:第六次及以上咨询)
    private Integer consultationType;
    //来访性别(男/女)
    private String visitorGender;
    //来访年龄(1:20岁及以下 2:21-25 3:26-30 4:31-35 5:36-40 6:41-45 7:46-50 8:50岁以上)
    private Integer visitorAge;
    //婚姻状态(1:未婚 2:已婚)
    private Integer maritalStatus;
    //来访有无子女(1:有 2:无)
    private Integer hasChildren;
    //咨询领域(1:心理健康 2:情绪压力 3:人际关系 4:恋爱情感 5:家庭关系 6:亲子教育 7:职场发展 8:个人成长)
    private Integer consultationField;
    //问题概述
    private String problemSummary;
    //咨询关键词
    private String consultationKeywords;
    //是否属于职场类相关问题(1:是 0:否)
    private Integer isWorkplaceIssue;
    //职场问题描述
    private String workplaceDescription;
    //来访是否有心理风险(1:有 0:无)
    private Integer hasPsychologicalRisk;
    // 风险等级(3-高风险/2-中风险/1-低风险/0-无风险)
    private Integer riskLevel;
    //风险程度+简要描述
    private String riskDescription;
    //后续建议(1:无需跟进 2:定期咨询 3:转介就医 4:其他)
    private Integer followUpSuggestion;
    //其他建议
    private String otherSuggestion;
    //咨询师ID
    private Integer counselor;
    //咨询师姓名
    private String counselorName;
    //添加时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    //是否有效(1:有效 0:无效)
    private Integer isValid;
    //来访者ID
    private Integer userId;
    //来访者用户名
    private String loginName;
    //来访者姓名
    private String realName;
    // 所属组织的下级组织集合
    private List<Integer> childStructs;
    // 所属机构
    private Integer structId;
    // 所属机构名称
    private String structName;
    // 查询开始时间
    @JSONField(format = "yyyy-MM-dd")
    private Date startDate;
    // 查询结束时间
    @JSONField(format = "yyyy-MM-dd")
    private Date endDate;
}

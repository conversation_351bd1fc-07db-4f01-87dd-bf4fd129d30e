package cn.psycloud.psyplatform.dto.consultationcase;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 咨询关键词DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConsultationKeywordDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    
    //主键ID
    private Integer id;
    
    //关键词内容
    private String keyword;
    
    //是否有效(1:有效 0:无效)
    private Integer isValid;
    
    //使用次数统计
    private Integer usageCount;
} 
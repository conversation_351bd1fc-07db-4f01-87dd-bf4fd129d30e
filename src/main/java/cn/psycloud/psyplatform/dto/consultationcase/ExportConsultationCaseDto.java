package cn.psycloud.psyplatform.dto.consultationcase;

import cn.psycloud.psyplatform.util.converter.CustomConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 导出咨询个案实体类
 */
@Data
public class ExportConsultationCaseDto {
    private static final long serialVersionUID = 1L;
    // 所属机构名称
    @ExcelProperty(value ="机构名称", converter = CustomConverter.class)
    private String structName;
    //咨询师姓名
    @ExcelProperty(value ="咨询师姓名", converter = CustomConverter.class)
    private String counselorName;
    //来访者用户名
    @ExcelProperty(value ="工号", converter = CustomConverter.class)
    private String loginName;
    //来访者姓名
    @ExcelProperty(value ="姓名", converter = CustomConverter.class)
    private String realName;
    //咨询日期
    @ExcelProperty(value ="咨询日期", converter = CustomConverter.class)
    private String consultationDate;
    //咨询形式(1:驻场咨询 2:线上咨询 3:门店咨询)
    @ExcelProperty(value ="咨询形式", converter = CustomConverter.class)
    private String consultationForm;
    //咨询时长(分钟)
    @ExcelProperty(value ="咨询时长(分钟)", converter = CustomConverter.class)
    private Integer consultationDuration;
    //咨询类型(1:首次咨询 2:第二次咨询 3:第三次咨询 4:第四次咨询 5:第五次咨询 6:第六次及以上咨询)
    @ExcelProperty(value ="咨询类型", converter = CustomConverter.class)
    private String consultationType;
    //来访性别(男/女)
    @ExcelProperty(value ="来访性别", converter = CustomConverter.class)
    private String visitorGender;
    //来访年龄(1:20岁及以下 2:21-25 3:26-30 4:31-35 5:36-40 6:41-45 7:46-50 8:50岁以上)
    @ExcelProperty(value ="来访年龄", converter = CustomConverter.class)
    private String visitorAge;
    //婚姻状态(1:未婚 2:已婚)
    @ExcelProperty(value ="婚姻状态", converter = CustomConverter.class)
    private String maritalStatus;
    //来访有无子女(1:有 2:无)
    @ExcelProperty(value ="来访有无子女", converter = CustomConverter.class)
    private String hasChildren;
    //咨询领域(1:心理健康 2:情绪压力 3:人际关系 4:恋爱情感 5:家庭关系 6:亲子教育 7:职场发展 8:个人成长)
    @ExcelProperty(value ="咨询领域", converter = CustomConverter.class)
    private String consultationField;
    //问题概述
    @ExcelProperty(value ="问题概述", converter = CustomConverter.class)
    private String problemSummary;
    //咨询关键词
    @ExcelProperty(value ="咨询关键词", converter = CustomConverter.class)
    private String consultationKeywords;
    //是否属于职场类相关问题(1:是 0:否)
    @ExcelProperty(value ="是否属于职场类相关问题", converter = CustomConverter.class)
    private String isWorkplaceIssue;
    //职场问题描述
    @ExcelProperty(value ="职场问题描述", converter = CustomConverter.class)
    private String workplaceDescription;
    //来访是否有心理风险(1:有 0:无)
    @ExcelProperty(value ="来访是否有心理风险", converter = CustomConverter.class)
    private String hasPsychologicalRisk;
    //风险等级(3-高风险/2-中风险/1-低风险/0-无风险)
    @ExcelProperty(value ="风险等级", converter = CustomConverter.class)
    private String riskLevel;
    //风险程度+简要描述
    @ExcelProperty(value ="风险程度简要描述", converter = CustomConverter.class)
    private String riskDescription;
    //后续建议(1:无需跟进 2:定期咨询 3:转介就医 4:其他)
    @ExcelProperty(value ="后续建议", converter = CustomConverter.class)
    private String followUpSuggestion;
    //其他建议
    @ExcelProperty(value ="其他建议", converter = CustomConverter.class)
    private String otherSuggestion;
    //添加时间
    @ExcelProperty(value ="添加时间", converter = CustomConverter.class)
    private String createTime;
}

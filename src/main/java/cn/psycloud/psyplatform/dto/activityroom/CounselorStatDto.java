package cn.psycloud.psyplatform.dto.activityroom;

import lombok.Data;

/**
 * 咨询师统计DTO
 */
@Data
public class CounselorStatDto {
    private static final long serialVersionUID = 1L;
    
    //咨询师ID
    private Integer counselorId;
    
    //咨询师姓名
    private String counselorName;
    
    //活动次数
    private Integer activityCount;
    
    //总时长（分钟）
    private Long totalDuration;
    
    //总时长（小时，格式化显示用）
    private String totalDurationFormatted;
}

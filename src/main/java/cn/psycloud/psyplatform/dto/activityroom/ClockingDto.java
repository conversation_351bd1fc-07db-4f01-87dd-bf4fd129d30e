package cn.psycloud.psyplatform.dto.activityroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

@EqualsAndHashCode(callSuper=true)
@Data
public class ClockingDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //主键ID
    private Integer id;
    //用户ID
    private Integer userId;
    //用户名
    private String loginName;
    //用户名称
    private String realName;
    //用户所属组织
    private String structFullName;
    //活动ID
    private Integer activityId;
    //活动主题
    private String activityName;
    //操作时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date clockingTime;
    //操作类型：1-签到，2-签退
    private Integer actionType;
}

package cn.psycloud.psyplatform.dto.activityroom;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 活动数据看板DTO
 */
@Data
public class ActivityDashboardDto {
    private static final long serialVersionUID = 1L;
    
    //查询开始时间
    @JSONField(format = "yyyy-MM-dd")
    private Date startDate;
    
    //查询结束时间
    @JSONField(format = "yyyy-MM-dd")
    private Date endDate;
    
    //活动数量
    private Integer activityCount;
    
    //总签到人次
    private Integer totalClockInCount;
    
    //总签退人次
    private Integer totalClockOutCount;
    
    //完成问卷调查人次
    private Integer surveyCompletedCount;

    //按活动类型统计
    private List<ActivityTypeStatDto> activityTypeStats;
    
    //按咨询师统计
    private List<CounselorStatDto> counselorStats;
}

package cn.psycloud.psyplatform.dto.activityroom;

import lombok.Data;

/**
 * 问卷作答详细信息DTO
 */
@Data
public class SurveyAnswerDetailDto {
    private static final long serialVersionUID = 1L;
    
    //题目序号
    private Integer qNumber;
    //题目内容
    private String qContent;
    //题目类型 1:单选 2:多选 3:填空 4:排序
    private Integer qType;
    //选择的选项内容
    private String selectedContent;
    //原始选项ID（用于多选、排序等复杂类型）
    private String itemId;
    //其他选项的填空内容
    private String otherAnswer;
}

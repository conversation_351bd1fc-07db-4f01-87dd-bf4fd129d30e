package cn.psycloud.psyplatform.dto.activityroom;

import lombok.Data;

/**
 * 活动类型统计DTO
 */
@Data
public class ActivityTypeStatDto {
    private static final long serialVersionUID = 1L;
    
    //活动类型
    private Integer activityType;
    
    //活动类型名称
    private String activityTypeName;
    
    //总人次
    private Integer totalParticipants;
    
    //总时长（分钟）
    private Long totalDuration;
    
    //总时长（小时，格式化显示用）
    private String totalDurationFormatted;
}

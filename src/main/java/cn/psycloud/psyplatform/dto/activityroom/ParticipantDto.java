package cn.psycloud.psyplatform.dto.activityroom;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.Date;

/**
 * 活动参与人员DTO
 */
@Data
public class ParticipantDto {
    private static final long serialVersionUID = 1L;
    
    // 用户ID
    private Integer userId;
    
    // 用户名
    private String loginName;
    
    // 姓名
    private String realName;
    
    // 所属组织
    private String structName;
    
    // 签到时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date clockingInTime;
    
    // 签退时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date clockingOutTime;
    
    // 是否完成问卷调查
    private Boolean isSurveyCompleted;
}

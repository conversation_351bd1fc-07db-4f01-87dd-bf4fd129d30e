package cn.psycloud.psyplatform.dto.activityroom;

import lombok.Data;
import java.util.List;

@Data
public class ActivityReportDto {
    private static final long serialVersionUID = 1L;
    //活动id
    private Integer activityId;
    //活动主题
    private String activityName;
    //活动开始时间
    private String startTime;
    //活动结束时间
    private String endTime;
    //驻场咨询师
    private String counselor;
    //活动类型
    private String activityType;
    //签到人数
    private Integer clockingInCount;
    //签退人数
    private Integer clockingOutCount;
    //活动总评
    private String overallEvaluation;
    //活动自评
    private List<SelfEvaluationDto> selfEvaluations;
    //问卷名称
    private String surveyName;
    //问卷提交人数
    private Integer surveySubmitCount;
    //参与人员清单
    private List<ParticipantDto> participants;
}

package cn.psycloud.psyplatform.dto.activityroom;

import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import cn.psycloud.psyplatform.dto.survey.SurveyResultDto;
import cn.psycloud.psyplatform.dto.survey.SurveyResultStatDto;
import cn.psycloud.psyplatform.entity.survey.SurveyRecordEntity;
import lombok.Data;

import java.util.List;

/**
 * 活动问卷结果统计
 */
@Data
public class ActivitySurveyStatDto {
    private static final long serialVersionUID = 1L;
    //活动ID
    private Integer activityId;
    //活动名称
    private String activityName;
    //问卷id
    private Integer surveyId;
    //问卷名称
    private String surveyName;
    
    private SurveyDto survey;
    //活动里的问卷作答记录集合
    private List<SurveyRecordEntity> surveyRecords;
    //活动里的问卷选择结果项集合
    private List<SurveyResultDto> surveyResults;
    //总参与人数
    private Integer totalCount;
    //已完成数
    private Integer doneCount;
    //未完成数
    private Integer unDoneCount;
    //完成率
    private Double doneRate;
    //未完成率
    private Double unDoneRate;
    //结果选择频率统计
    private List<SurveyResultStatDto> surveyResultStat;
    //评分题总体平均分
    private Double ratingOverallAverage;
}

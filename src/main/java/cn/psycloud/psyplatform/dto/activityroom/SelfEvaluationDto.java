package cn.psycloud.psyplatform.dto.activityroom;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.Date;

@Data
public class SelfEvaluationDto {
    private static final long serialVersionUID = 1L;
    // 活动主题
    private String activityName;
    //活动开始时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //活动结束时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //参与者ID
    private Integer userId;
    //参与者姓名
    private String realName;
    //心理咨询师姓名
    private String counselorName;
    // 点评内容
    private String evaluationContent;
    // 点评标签（逗号分隔）
    private String tags;
}

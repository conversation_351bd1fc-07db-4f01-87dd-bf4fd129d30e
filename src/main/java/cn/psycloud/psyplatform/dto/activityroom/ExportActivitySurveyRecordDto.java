package cn.psycloud.psyplatform.dto.activityroom;

import lombok.Data;

@Data
public class ExportActivitySurveyRecordDto {
    private static final long serialVersionUID = 1L;
    //活动id
    private Integer activityId;
    //调查问卷id
    private Integer surveyId;
    //用户名
    private String loginName;
    //姓名
    private String realName;
    //作答时间
    private String recordDate;
    //组织id
    private Integer structId;
    //行转列拼接sql
    private String sql;
    //导出类型：1-按选项序号导出，2-按选项内容导出
    private Integer exportType;
    
    // 新增筛选条件字段
    //筛选用户名
    private String searchLoginName;
    //筛选姓名
    private String searchRealName;
    //筛选作答开始时间
    private String startRecordDate;
    //筛选作答结束时间
    private String endRecordDate;
    //筛选题目序号
    private String questionNumber;
    //筛选选择选项序号
    private String selectedOption;
}

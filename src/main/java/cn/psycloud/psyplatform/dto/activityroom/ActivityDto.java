package cn.psycloud.psyplatform.dto.activityroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

/**
 * 心理活动
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //活动id
    private Integer id;
    //活动主题
    private String activityName;
    //活动介绍
    private String activityIntro;
    //活动类型
    private Integer activityType;
    //活动封面
    private String activityCover;
    //活动开始时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //活动结束时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //咨询师id
    private Integer counselorId;
    //负责咨询师
    private String counselorName;
    //创建日期
    private Date addDate;
    //操作人id
    private Integer operator;
    //操作人姓名
    private String operatorName;
    //是否有效
    private Integer isValid;
    //调查问卷id
    private Integer surveyId;
    //调查问卷名称
    private String surveyName;
    //调查问卷
    private SurveyDto survey;
    //个人活动状态：0 -未签到 1 -已签到 2 -未签退 3 -已签退
    private Integer activityStateForUser;
    //活动总评
    private String overallEvaluation;
    //签到人数
    private Integer clockingInNum;
    //问卷是否完成
    private Integer isSurveyDone;
    //活动自评的个人标签
    private String tags;
    //查询开始时间
    @JSONField(format = "yyyy-MM-dd")
    private Date startDate;
    //查询结束时间
    @JSONField(format = "yyyy-MM-dd")
    private Date endDate;
    // 活动所属组织
    private Integer structId;
    // 活动所属组织名称
    private String structName;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
}

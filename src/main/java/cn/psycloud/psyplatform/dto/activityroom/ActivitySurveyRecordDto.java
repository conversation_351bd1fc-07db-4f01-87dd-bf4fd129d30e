package cn.psycloud.psyplatform.dto.activityroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.survey.SurveyResultEntity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper=true)
@Data
public class ActivitySurveyRecordDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //调查问卷记录Id
    private Integer id;
    //问卷答题人
    private Integer userId;
    //问卷答题人用户名
    private String loginName;
    //问卷答题人姓名
    private String realName;
    //问卷id
    private Integer surveyId;
    //问卷名称
    private String surveyName;
    //作答时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date recordDate;
    //是否完成
    private Integer isDone;
    //是否有效
    private Integer isValid;
    //活动id
    private Integer activityId;
    //活动主题
    private String activityName;
    //作答选项结果集合
    private List<SurveyResultEntity> listAnswers;
    //问卷作答详细信息（题目和选择结果）
    private List<SurveyAnswerDetailDto> answerDetails;

    // 筛选条件字段
    //筛选用户名
    private String searchLoginName;
    //筛选姓名
    private String searchRealName;
    //筛选作答开始时间
    private String startRecordDate;
    //筛选作答结束时间
    private String endRecordDate;
    //筛选题目序号
    private String questionNumber;
    //筛选选择选项序号
    private String selectedOption;
}

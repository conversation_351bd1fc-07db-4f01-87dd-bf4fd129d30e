package cn.psycloud.psyplatform.dto.activityroom;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 签到图片
 */
@Data
public class ActivityClockingPicDto {
    private static final long serialVersionUID = 1L;
    // 主键
    private Integer id;
    // 机构ID
    private Integer structId;
    //文件名称
    private String fileName;
    //上传时间
    private Date uploadTime;
    //上传人
    private Integer operator;
    //是否有效
    private Integer isValid;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
}

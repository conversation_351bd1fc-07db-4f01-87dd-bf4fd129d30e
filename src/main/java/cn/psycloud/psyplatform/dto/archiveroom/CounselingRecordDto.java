package cn.psycloud.psyplatform.dto.archiveroom;

import cn.psycloud.psyplatform.entity.counselingroom.CounselingRecordEntity;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 咨询记录
 */
@Data
public class CounselingRecordDto {
    private static final long serialVersionUID = 1L;
    //咨询日期
    private Date counselingDate;
    //咨询师姓名
    private String counselorName;
    //咨询内容集合
    private List<CounselingRecordEntity> listCounselingContent;
}


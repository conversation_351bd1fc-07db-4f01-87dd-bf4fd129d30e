package cn.psycloud.psyplatform.dto.archiveroom;

import cn.psycloud.psyplatform.dto.activityroom.SelfEvaluationDto;
import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto;
import cn.psycloud.psyplatform.dto.performance.UserPerformanceDto;
import cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity;
import lombok.Data;

import java.util.List;

@Data
public class ArchiveDto {
    private static final long serialVersionUID = 1L;
    private UserDto user;
    //测试记录集合
    private List<MeasuringRecordDto> measuringRecords ;
    //咨询记录集合
    private List<CounselingRecordDto> counselingRecord;
    //评语
    private AdviceEntity advice;
    //绩效信息集合
    private List<UserPerformanceDto> userPerformance;
    //心理咨询个案集合
    private List<ConsultationCaseDto> consultationCases;
    //个人点评集合
    private List<SelfEvaluationDto> selfEvaluations;
}
package cn.psycloud.psyplatform.dto.archiveroom;

import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class MeasuringRecordDto {
    private static final long serialVersionUID = 1L;
    //量表名称
    private String scaleName;
    //测评时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //测评耗时
    private Integer timeInterval;
    //结果解释
    private String interpretation;
}
package cn.psycloud.psyplatform.dto.performance;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper=true)
@Data
public class UserPerformanceDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //主键ID
    private Integer id;
    //用户名
    private String loginName;
    //用户姓名
    private String realName;
    //用户所属组织
    private String structFullName;
    //年度
    private String yearly;
    //一月
    private String month01;
    //二月
    private String month02;
    //三月
    private String month03;
    //四月
    private String month04;
    //五月
    private String month05;
    //六月
    private String month06;
    //七月
    private String month07;
    //八月
    private String month08;
    //九月
    private String month09;
    //十月
    private String month10;
    //十一月
    private String month11;
    //十二月
    private String month12;
    // 所属组织
    private Integer structId;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
}

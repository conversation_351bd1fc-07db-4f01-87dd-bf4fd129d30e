package cn.psycloud.psyplatform.dto.performance;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 导入人员绩效信息实体
 */
@Data
public class ImportUserPerformanceDto {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "工号", index = 0)
    private String loginName;
    @ExcelProperty(value = "年度", index = 1)
    private String yearly;
    @ExcelProperty(value = "一月", index = 2)
    private String month01;
    @ExcelProperty(value = "二月", index = 3)
    private String month02;
    @ExcelProperty(value = "三月", index = 4)
    private String month03;
    @ExcelProperty(value = "四月", index = 5)
    private String month04;
    @ExcelProperty(value = "五月", index = 6)
    private String month05;
    @ExcelProperty(value = "六月", index = 7)
    private String month06;
    @ExcelProperty(value = "七月", index = 8)
    private String month07;
    @ExcelProperty(value = "八月", index = 9)
    private String month08;
    @ExcelProperty(value = "九月", index = 10)
    private String month09;
    @ExcelProperty(value = "十月", index = 11)
    private String month10;
    @ExcelProperty(value = "十一月", index = 12)
    private String month11;
    @ExcelProperty(value = "十二月", index = 13)
    private String month12;
}

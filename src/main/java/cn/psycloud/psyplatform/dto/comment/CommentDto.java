package cn.psycloud.psyplatform.dto.comment;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class CommentDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //评论id
    private Integer id;
    //功能模块：1- 心理训练营 2-训练营课程 3-心理微课 4-咨询师
    private Integer functionType;
    //产品id
    private Integer productId;
    //产品名称
    private String productName;
    //评论用户ID
    private Integer userId;
    //评论用户名
    private String loginName;
    //评论用户姓名
    private String realName;
    //评论用户头像
    private String headImg;
    //评论内容
    private String commentContent;
    //评论日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date commentDate;
    //是否审核
    private Integer isChecked;
    //是否有效
    private Integer isValid;
}

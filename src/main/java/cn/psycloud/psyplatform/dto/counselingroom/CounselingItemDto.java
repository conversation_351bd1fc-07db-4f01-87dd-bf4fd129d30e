package cn.psycloud.psyplatform.dto.counselingroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CounselingItemDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //自增ID
    private Integer id;
    //咨询师ID
    private Integer userId;
    //咨询师用户名
    private String loginName;
    //咨询师姓名
    private String realName;
    //咨询项目名称
    private String counselingItemName;
    //积分
    private Integer points;
    //是否有效
    private Integer isValid;
}

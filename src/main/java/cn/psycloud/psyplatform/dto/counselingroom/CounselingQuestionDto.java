package cn.psycloud.psyplatform.dto.counselingroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionRespondEntity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 在线问答
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CounselingQuestionDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //提问ID
    private Integer id;
    //提问用户ID
    private Integer questioner;
    //提问用户用户名
    private String questionerLoginName;
    //提问用户头像
    private String headPic;
    //问题类型ID
    private Integer counselingTypeId;
    //问题类型名称
    private String counselingTypeName;
    //问题标题
    private String title;
    //问题描述
    private String content;
    //提问时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date addDate;
    //是否有效
    private Integer isValid;
    //是否匿名
    private Integer isAnonymous;
    //图片
    private String img;
    //回复数量
    private Integer respondCount;
    //回复者
    private Integer responder;
    //答疑的回复集合
    private List<CounselingQuestionRespondEntity> listResponds;
    //是否包含有我的回答
    private Integer isMyResponds;
}
package cn.psycloud.psyplatform.dto.counselingroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  咨询问题类型
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CounselingTypeDto  extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //问题类型ID
    private Integer id;
    //问题类型名称
    private String counselingType;
    //问题描述
    private String remark;
    //是否有效
    private Integer isValid;
}

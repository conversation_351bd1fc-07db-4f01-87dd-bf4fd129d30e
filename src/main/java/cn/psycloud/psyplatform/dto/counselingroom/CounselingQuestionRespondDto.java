package cn.psycloud.psyplatform.dto.counselingroom;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class CounselingQuestionRespondDto {
    private static final long serialVersionUID = 1L;
    //回复ID
    private Integer id;
    //问题ID
    private Integer questionId;
    //回复用户ID
    private Integer responder;
    //回复用户的用户名
    private String responderLoginName;
    //回复用户的头像
    private String headPic;
    //回复内容
    private String content;
    //回复日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date addDate;
    //是否有效
    private Integer isValid;
}
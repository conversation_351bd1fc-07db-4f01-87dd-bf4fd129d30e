package cn.psycloud.psyplatform.dto.counselingroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SchedulingDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //排班ID
    private Integer id;
    //咨询师ID
    private Integer counselorId;
    //咨询师
    private String counselorName;
    //排班开始时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //排班结束时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //排班日期
    private Date schedueDate;
    //排班人
    private Integer operator;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
}
package cn.psycloud.psyplatform.dto.counselingroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingRecordEntity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CounselingOrderDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //预约ID
    private Integer id;
    //来访者ID
    private Integer visitorId;
    //来访者姓名
    private String visitorName;
    //来访者头像
    private String visitorHeadPic;
    //所属机构
    private int structId;
    //咨询师ID
    private int counselorId;
    //咨询师姓名
    private String counselorName;
    //咨询师头像
    private String counselorHeadPic;
    //咨询问题类型
    private Integer counselingTypeId;
    //咨询问题类型名称
    private String counselingType;
    //预约开始时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //预约结束时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //咨询方式
    private Integer counselingWay;
    //是否重点个案
    private Integer isPoint;
    //自我分析
    private String selfComment;
    //问题描述
    private String description;
    //排班ID
    private Integer schedulingId;
    //处理预约意见
    private String handleInfo;
    //咨询项目ID
    private  Integer counselingItemId;
    //咨询类型名称
    private String counselingItemName;
    //预约状态：0-未处理  1- 已接受  2- 取消  3-已结束
    private Integer state;
    //添加日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date addDate;
    //添加人
    private Integer operator;
    //是否有效
    private Integer isValid;
    //咨询记录
    private List<CounselingRecordEntity> counselingContent;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
    //当前时间
    private Date currentTime;
}
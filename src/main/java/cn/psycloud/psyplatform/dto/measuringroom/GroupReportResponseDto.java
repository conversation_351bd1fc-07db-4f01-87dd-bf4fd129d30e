package cn.psycloud.psyplatform.dto.measuringroom;

import lombok.Data;
import java.util.List;

/**
 * 团体测评报告响应DTO
 */
@Data
public class GroupReportResponseDto {
    
    // 1. 测评工具介绍
    private ScaleInfoDto scaleInfo;
    
    // 2. 测评总体情况
    private OverallSituationDto overallSituation;
    
    // 3. 测评情况分析
    private AnalysisDto analysis;
    
    /**
     * 测评工具介绍
     */
    @Data
    public static class ScaleInfoDto {
        // 量表ID
        private Integer scaleId;
        // 量表名称
        private String scaleName;
        // 量表介绍
        private String scaleIntro;
        // 量表指导语
        private String scaleGuide;
        // 答题所需时间
        private Integer needTime;
    }
    
    /**
     * 测评总体情况
     */
    @Data
    public static class OverallSituationDto {
        // 总测评人数
        private Integer totalCount;
        // 完成人数
        private Integer completedCount;
        // 未完成人数
        private Integer uncompletedCount;
        
        // 按部门统计的完成情况
        private List<CompletionStatDto> departmentStats;
        
        // 按性别统计的完成情况
        private List<CompletionStatDto> genderStats;
        
        // 按年龄段统计的完成情况
        private List<CompletionStatDto> ageStats;
    }
    
    /**
     * 测评情况分析
     */
    @Data
    public static class AnalysisDto {
        // 按组织统计预警等级分布
        private List<OrganizationWarningStatDto> organizationWarningStats;
        
        // 按性别统计预警等级分布
        private List<GenderWarningStatDto> genderWarningStats;
        
        // 按年龄段统计预警等级分布
        private List<AgeWarningStatDto> ageWarningStats;
        
        // 按因子统计预警等级分布
        private List<FactorWarningStatDto> factorWarningStats;
        
        // 各组织红码橙码统计
        private List<CriticalWarningStatDto> criticalWarningStats;
    }
    
    /**
     * 完成情况统计基础DTO
     */
    @Data
    public static class CompletionStatDto {
        // 分类名称（部门名称、性别、年龄段）
        private String categoryName;
        // 总人数
        private Integer totalCount;
        // 完成人数
        private Integer completedCount;
        // 未完成人数
        private Integer uncompletedCount;
        // 完成率
        private Double completionRate;
    }
    
    /**
     * 组织预警等级统计DTO
     */
    @Data
    public static class OrganizationWarningStatDto {
        // 组织ID
        private Integer structId;
        // 组织名称
        private String structName;
        // 总人数
        private Integer totalCount;
        // 绿码人数（无预警）
        private Integer greenCount;
        // 黄码人数（轻度预警）
        private Integer yellowCount;
        // 橙码人数（中度预警）
        private Integer orangeCount;
        // 红码人数（重度预警）
        private Integer redCount;
        // 绿码占比
        private Double greenRate;
        // 黄码占比
        private Double yellowRate;
        // 橙码占比
        private Double orangeRate;
        // 红码占比
        private Double redRate;
    }
    
    /**
     * 性别预警等级统计DTO
     */
    @Data
    public static class GenderWarningStatDto {
        // 性别
        private String gender;
        // 总人数
        private Integer totalCount;
        // 绿码人数
        private Integer greenCount;
        // 黄码人数
        private Integer yellowCount;
        // 橙码人数
        private Integer orangeCount;
        // 红码人数
        private Integer redCount;
        // 绿码占比
        private Double greenRate;
        // 黄码占比
        private Double yellowRate;
        // 橙码占比
        private Double orangeRate;
        // 红码占比
        private Double redRate;
    }
    
    /**
     * 年龄段预警等级统计DTO
     */
    @Data
    public static class AgeWarningStatDto {
        // 年龄段
        private String ageGroup;
        // 总人数
        private Integer totalCount;
        // 绿码人数
        private Integer greenCount;
        // 黄码人数
        private Integer yellowCount;
        // 橙码人数
        private Integer orangeCount;
        // 红码人数
        private Integer redCount;
        // 绿码占比
        private Double greenRate;
        // 黄码占比
        private Double yellowRate;
        // 橙码占比
        private Double orangeRate;
        // 红码占比
        private Double redRate;
    }
    
    /**
     * 重点预警统计DTO（红码橙码）
     */
    @Data
    public static class CriticalWarningStatDto {
        // 组织ID
        private Integer structId;
        // 组织名称
        private String structName;
        // 总人数
        private Integer totalCount;
        // 红码人数
        private Integer redCount;
        // 橙码人数
        private Integer orangeCount;
        // 红码橙码总数
        private Integer criticalCount;
        // 红码占比
        private Double redRate;
        // 橙码占比
        private Double orangeRate;
        // 红码橙码占比
        private Double criticalRate;
    }

    /**
     * 因子预警等级统计DTO
     */
    @Data
    public static class FactorWarningStatDto {
        // 因子ID
        private Integer factorId;
        // 因子名称
        private String factorName;
        // 总人数
        private Integer totalCount;
        // 绿码人数（无预警）
        private Integer greenCount;
        // 蓝码人数（轻度预警）
        private Integer blueCount;
        // 黄码人数（中度预警）
        private Integer yellowCount;
        // 橙码人数（重度预警）
        private Integer orangeCount;
        // 红码人数（极重度预警）
        private Integer redCount;
        // 绿码占比
        private Double greenRate;
        // 蓝码占比
        private Double blueRate;
        // 黄码占比
        private Double yellowRate;
        // 橙码占比
        private Double orangeRate;
        // 红码占比
        private Double redRate;
    }
} 
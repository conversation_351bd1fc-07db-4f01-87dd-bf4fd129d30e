package cn.psycloud.psyplatform.dto.measuringroom;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 因子解释层级DTO
 */
@Data
public class FactorExplainHierarchyDto {
    private static final long serialVersionUID = 1L;
    
    // 因子ID
    private Integer factorId;
    
    // 因子名称
    private String factorName;
    
    // 因子类型：1-一级因子 2-二级因子 3-三级因子
    private Integer factorType;

    private BigDecimal originalScore;

    private BigDecimal score;
    
    // 结果解释
    private String interpretation;
    
    // 子因子列表
    private List<FactorExplainHierarchyDto> children;
    
    // 是否为顶层因子
    private Boolean isTopLevel;
    
    // 父因子ID（如果有的话）
    private Integer parentFactorId;
} 
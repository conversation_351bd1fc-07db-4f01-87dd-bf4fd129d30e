package cn.psycloud.psyplatform.dto.measuringroom;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ImportScaleQDto {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "序号", index = 0)
    private String qNo;

    @ExcelProperty(value = "题目", index = 1)
    private String qContent;

    @ExcelProperty(value = "题型", index = 2)
    private String qType;

    @ExcelProperty(value = "答案", index = 3)
    private String answers;

    @ExcelProperty(value = "计分规则", index = 4)
    private String scores;
}

package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CheckAbnormalDto {
    private static final long serialVersionUID = 1L;
    private Integer factorId;
    private Integer age;
    private String sex;
    private BigDecimal factorScore;
    private BigDecimal conditionValue;
    private List<ScaleFactorAbnormalConditionEntity> listAbnormalConditions;
}

package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity;
import cn.psycloud.psyplatform.entity.survey.SurveyEntity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TaskDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //任务ID
    private Integer id;
    //任务名称
    private String taskName;
    //开始时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //结束时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //测评负责人
    private Integer personInCharge;
    //任务类型：1-限定测试 2-非限定 3-自由测试
    private Integer taskType;
    //是否限制测试数量
    private Integer isLimitTestCount;
    //测试次数
    private Integer limitTestCount;
    //结果查看规则
    private Integer resultViewRule;
    //是否有效
    private Integer isValid;
    //负责人
    private UserDto userOfPersonInCharge;
    //测评对象集合
    private List<UserDto> users;
    //单个被试者的用户名
    private String loginName;
    //单个被试者的姓名
    private String realName;
    //来访者ID
    private Integer visitorId;
    //测评量表集合
    private List<ScaleEntity> scales;
    //测评对象所属组织集合
    private String structIds;
    //测评量表ID集合
    private String scaleIds;
    //测评对象角色
    private Integer roleId;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
    //是否启用调查问卷
    private Integer isSurvey;
    //问卷id
    private Integer surveyId;
    //问卷名称
    private String surveyName;
    //任务使用场景：1- 量表测评 2- 调查问卷
    private Integer taskKind;
    //任务里的调查问卷id集合
    private String surveyIds;
    //任务里的调查问卷集合
    private List<SurveyEntity> surveys;
    //任务里的问卷作答记录集合
    private List<TaskSurveyDto> surveyRecords;
    //是否显示背景图片
    private Integer showBackground;
    //背景图片文件名
    private String backgroundUrl;
}
package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.util.converter.CustomConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 *  导出测评记录实体类
 */
@Data
public class ExportTestRecordDto {
    private static final long serialVersionUID = 1L;
    //测评对象账号
    @ExcelProperty(value ="用户名", converter = CustomConverter.class)
    private String loginName;
    //测评对象姓名
    @ExcelProperty(value = "姓名", converter = CustomConverter.class)
    private String realName;
    //测评对象性别
    @ExcelProperty(value = "性别", converter = CustomConverter.class)
    private String sex;
    //组织全称
    @ExcelProperty(value = "所属组织", converter = CustomConverter.class)
    private String structFullName;
    //量表名称
    @ExcelProperty(value = "量表", converter = CustomConverter.class)
    private String scaleName;
    //测评状态
    @ExcelProperty(value = "状态", converter = CustomConverter.class)
    private String state;
    //开始测评时间
    @ExcelProperty(value = "开始时间", converter = CustomConverter.class)
    private String startTime;
    //结束测评时间
    @ExcelProperty(value = "结束时间", converter = CustomConverter.class)
    private String endTime;
}

package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.survey.SurveyResultEntity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TaskSurveyDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //调查问卷记录Id
    private Integer id;
    //问卷答题人
    private Integer userId;
    //问卷答题人用户名
    private String loginName;
    //问卷答题人姓名
    private String realName;
    //问卷id
    private Integer surveyId;
    //问卷名称
    private String surveyName;
    //作答时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date recordDate;
    //是否完成
    private Integer isDone;
    //是否有效
    private Integer isValid;
    //测评任务id
    private Integer taskId;
    //开始测评时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //结束测评时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //测评任务名称
    private String taskName;
    //测评对象所属组织
    private int structId;
    //组织全称
    private String structFullName;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
    //作答选项结果集合
    private List<SurveyResultEntity> listAnswers;
    //行转列拼接sql
    private String sql;
}

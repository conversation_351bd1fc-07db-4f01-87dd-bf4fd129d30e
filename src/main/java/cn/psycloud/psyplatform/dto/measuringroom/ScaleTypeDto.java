package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity;
import lombok.Data;
import java.util.List;

@Data
public class ScaleTypeDto {
    private static final long serialVersionUID = 1L;
    //类型ID
    private Integer id;
    //类型名称
    private String scaleTypeName;
    //排序
    private Integer sort;
    //是否有效
    private Integer isValid;
    //量表类别下的量表集合
    private List<ScaleEntity> scales;
    //页码
    private Integer pageIndex;
    //每页显示数据数目
    private Integer pageSize;
}

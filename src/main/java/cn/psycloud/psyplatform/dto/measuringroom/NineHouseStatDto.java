package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 九型人格测试结果统计
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NineHouseStatDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    private Integer recordId;
    private String realName;
    private String loginName;
    private String structFullName;
    private int factorId;
    private String factorName;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date testDate;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
    //测评对象所属组织
    private int structId;
}

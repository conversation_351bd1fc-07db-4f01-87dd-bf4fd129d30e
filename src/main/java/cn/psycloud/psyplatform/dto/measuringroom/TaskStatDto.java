package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import lombok.Data;

import java.util.List;

/**
 * 测评数据统计
 */
@Data
public class TaskStatDto {
    private static final long serialVersionUID = 1L;
    //量表名称
    private String scaleName;
    //量表介绍
    private String scaleIntro;
    //测试总人数
    private Integer totalCount;
    //已测人数
    private Integer doneCount;
    //未测人数
    private Integer unDoCount;
    //无效人数
    private Integer invalidCount;
    //异常人数
    private Integer abnormalCount;
    //性别数量分布
    private List<TaskStatCnt> listSexFreq;
    //婚姻状况数量分布
    private List<TaskStatCnt> listMarriageFreq;
    //文化程度数量分布
    private List<TaskStatCnt> listEducationFreq;
    //宗教信仰数量分布
    private List<TaskStatCnt> listReligionFreq;
    //条目总数
    private Integer qCount;
    //答案总数
    private Integer aMaxNo;
    //选择频度
    private int[][] aFreq;
    //测评结果分布
    private String statResultDistribution;
    //异常名单
    private List<UserDto> abnormalUsers;
}
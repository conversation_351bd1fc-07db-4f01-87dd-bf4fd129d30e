package cn.psycloud.psyplatform.dto.measuringroom;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 团体测评报告请求参数DTO
 */
@Data
public class GroupReportRequestDto {
    
    // 所属组织ID
    private Integer structId;
    
    // 所属组织的下级组织集合
    private List<Integer> childStructs;
    
    // 测评任务ID
    private Integer taskId;
    
    // 量表ID
    private Integer scaleId;
    
    // 测评开始时间范围
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeBegin;
    
    // 测评结束时间范围
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTimeEnd;
    
    // 选择的组织层级（用于报告中按层级展示）
    private Integer reportStructLevel;
} 
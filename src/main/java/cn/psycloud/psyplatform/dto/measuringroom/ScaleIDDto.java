package cn.psycloud.psyplatform.dto.measuringroom;

import lombok.Data;

@Data
public class ScaleIDDto {
    private static final long serialVersionUID = 1L;
    //scl-90 症状自评量表
    public static final int SCL90 = 10000003;
    //抑郁自评量表
    public static final int SDS = 10000006;
    //学习焦虑量表
    public static final int XXJL = 10000007;
    //家庭环境量表(FES)
    public static final int FES = 10000008;
    //大学生人格问卷
    public static final int UPI = 10000009;
    //智力测验
    public static final int CW70 = 10000010;
    //瑞文标准推理
    public static final int SPM = 10000017;
    //卡特尔十六种人格因素问卷
    public static final int _16PF = 10000019;
    //卡特尔十四种人格因素问卷
    public static final int _14PF = 10000032;
    //艾森克人格问卷（少年式）
    public static final int EPQC = 10000025;
    //艾森克人格问卷（成人式）
    public static final int EPQA = 10000061;
    //斯特劳里气质类型
    public static final int STI = 10000029;
    //焦虑自评量表
    public static final int SAS = 10000030;
    // 职业人格
    public static final int ZYLX = 10000033;
    //核心技能
    public static final int HXJN = 10000054;
    //RCCP通用职业匹配测试量表
    public static final int RCCP = 10000055;
    //贝克抑郁
    public static final int BDI = 10000063;
    //明尼苏达MMPI
    public static final int MMPI = 10000070;
    //霍兰德职业测试量表（HLD）
    public static final int HLD = 10000083;
    //匹兹堡睡眠质量指数
    public static final int PSQI = 10000117;
    //精神症状自我诊断
    public static final int MSSD = 10000118;
    //MBTI职业性格测试
    public static final int MBTI = 10000121;
    //压力应对方式量表
    public static final int PCM = 10000132;
    //包容力测试
    public static final int BRL = 10000139;
    //父母养育方式问卷(EMBU)
    public static final int EMBU = 10000148;
    //九型人格测试
    public static final int NineHouse = 10000151;
    //Disc性格测试
    public static final int DISC = 10000152;
    //简易应对方式
    public static final int SCSQ = 10000155;
    //MBTI标准版
    public static final int MBTIS = 10000157;
    //MBTI简版
    public static final int MBTIB = 10000158;
    //公安心理关爱测评
    public static final int APMAndNineHouse = 10000162;
    //安全员人格、心理健康风险、消极应对评估
    public static final int YDL_aqy = 10000163;
    //贝康育婴师性格情绪问卷
    public static final int BKYYS = 10000167;
    //心理健康素养
    public static final int JKSY = 10000179;
}
package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class FactorLevelGroupDto {
    private Integer factorType;
    private String factorTypeName;
    private List<TestRecordExplainEntity> explains;
    
    public FactorLevelGroupDto() {
        this.explains = new ArrayList<>();
    }
}
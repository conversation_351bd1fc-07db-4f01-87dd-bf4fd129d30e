package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import cn.psycloud.psyplatform.dto.survey.SurveyResultDto;
import cn.psycloud.psyplatform.dto.survey.SurveyResultStatDto;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity;
import lombok.Data;
import java.util.List;

/**
 *  问卷结果统计
 */
@Data
public class TaskSurveyStatDto {
    private static final long serialVersionUID = 1L;
    //任务ID
    private Integer id;
    //任务名称
    private String taskName;
    //问卷id
    private Integer surveyId;
    //问卷名称
    private String surveyName;
    private TaskSurveyEntity taskSurvey;

    private SurveyDto survey;
    //任务里的问卷作答记录集合
    private List<TaskSurveyRecordEntity> surveyRecords;
    //任务里的问卷选择结果项集合
    private List<SurveyResultDto> surveyResults;
    //总参与人数
    private Integer totalCount;
    //已完成数
    private Integer doneCount;
    //未完成数
    private Integer unDoneCount;
    //完成率
    private Double doneRate;
    //未完成率
    private Double unDoneRate;
    //结果选择频率统计
    private List<SurveyResultStatDto> surveyResultStat;
}

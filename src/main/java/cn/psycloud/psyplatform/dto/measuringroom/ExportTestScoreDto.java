package cn.psycloud.psyplatform.dto.measuringroom;

import lombok.Data;

import java.util.List;

/**
 *  测评数据导出实体
 */
@Data
public class ExportTestScoreDto {
    private static final long serialVersionUID = 1L;
    //测评任务ID
    private Integer taskId;
    //测评量表ID
    private Integer scaleId;
    private String scaleIds;
    //因子id
    private Integer factorId;
    private String factorIds;
    //调查问卷id
    private Integer surveyId;
    //用户名
    private String loginName;
    //姓名
    private String realName;
    //开始测评时间
    private String startTime;
    //结束测评时间
    private String endTime;
    //组织id
    private Integer structId;
    //预警级别
    private Integer warningLevel;
    //导出的数据类型
    private String dataType;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
    //行转列拼接sql
    private String sql;
}

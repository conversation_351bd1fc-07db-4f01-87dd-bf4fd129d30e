package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorEntity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TestScoreDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //测评记录ID
    private Integer recordId;
    //因子ID
    private Integer factorId;
    //原始分
    private BigDecimal originalScore;
    //因子分
    private BigDecimal score;
    //是否异常
    private Integer isAbnormal;
    //异常值
    private BigDecimal abnormalValue;
    //预警级别
    private Integer warningLevel;
    //测评任务ID
    private Integer taskId;
    //量表ID
    private Integer scaleId;

    private String scaleIds;
    //量表名称
    private String scaleName;

    private ScaleFactorEntity factor;

    private String factorIds;
    //因子
    private String factorName;
    //因子英文
    private String factorEn;
    //得分异常条件
    private String scoreAbnormalCondition;
    //测评对象用户名
    private String loginName;
    //测评对象姓名
    private String realName;
    //测评对象性别
    private String sex;
    //测评对象角色ID
    private Integer roleId;
    //测评对象角色名称
    private String roleName;
    //测评对象所属机构ID
    private Integer structId;
    //测评对象所属机构
    private String structFullName;
    //测评开始时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //测评结束时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //测评记录
    private TestRecordDto testRecord;
    //负责组织集合
    private String structs;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
}
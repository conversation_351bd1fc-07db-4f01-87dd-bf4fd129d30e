package cn.psycloud.psyplatform.dto.measuringroom;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TestResultDto {
    private static final long serialVersionUID = 1L;
    //测评记录ID
    private Integer recordId;
    //条目序号
    private Integer qNo;
    //条目类型
    private Integer qType;
    //所选答案序号
    private String aNos;
    //答案对应分数
    private BigDecimal aScore;
    //答案对应的因子：九型人格测试
    private Integer factorId;
}

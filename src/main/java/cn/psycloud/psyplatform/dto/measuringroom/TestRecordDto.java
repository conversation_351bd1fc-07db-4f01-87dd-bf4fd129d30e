package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestResultEntity;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class TestRecordDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //测评记录ID
    private Integer id;
    //测评对象ID
    private Integer userId;
    //测评对象账号
    private String loginName;
    //测评对象姓名
    private String realName;
    //测评对象所属角色ID
    private Integer roleId;
    //测评对象所属角色名称
    private String roleName;
    //测评对象性别
    private String sex;
    //文化程度
    private String education;
    //宗教信仰
    private String religion;
    //婚姻状态
    private String marriage;
    //测评对象所属组织
    private int structId;
    //组织全称
    private String structFullName;
    //测评量表ID
    private Integer scaleId;
    //量表名称
    private String scaleName;
    //量表介绍
    private String scaleIntro;
    //测评状态
    private Integer state;
    //开始测评时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    //结束测评时间
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    //测评耗时
    private Integer timeInterval;
    //结果解释
    private String interpretation;
    //是否删除
    private Integer isValid;
    //测评量表
    private ScaleDto scale;
    //量表题目总数
    private Integer qCount;
    //测评对象
    private UserDto user;
    //测评任务ID
    private Integer taskId;
    //选择答案集合
    private List<TestResultEntity> results;
    //测试结果解释
    private List<TestRecordExplainEntity> listExplains;
    //测评报告图表
    private TestRecordChartsEntity listCharts;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
}
package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.anteroom.ImportUserDto;
import lombok.Data;
import java.util.List;

@Data
public class ImportResultDto {
    private List<ImportUserDto> users;
    private int successCount;
    private int failCount;
    private List<ImportUserDto> failList;
    
    public ImportResultDto(List<ImportUserDto> users) {
        this.users = users;
        this.successCount = (int) users.stream().filter(u -> u.getState() == 1).count();
        this.failCount = (int) users.stream().filter(u -> u.getState() == 0).count();
        this.failList = users.stream().filter(u -> u.getState() == 0).collect(java.util.stream.Collectors.toList());
    }
} 
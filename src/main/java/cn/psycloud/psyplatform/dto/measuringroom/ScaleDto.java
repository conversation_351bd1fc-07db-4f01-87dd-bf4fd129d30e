package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.entity.measuringroom.ScaleAdviceEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 量表实体
 */
@Data
public class ScaleDto {
    private static final long serialVersionUID = 1L;
    //量表ID
    private Integer id;
    //量表名称
    private String scaleName;
    //量表类型ID
    private Integer scaleTypeId;
    //量表类型名称
    private String scaleTypeName;
    //量表别名
    private String scaleAlias;
    //量表介绍
    private String scaleIntro;
    //指导语
    private String scaleGuide;
    //答题所需时间（分钟）
    private Integer needTime;
    //限制条件
    private String testLimit;
    //年龄限制
    private String ageLimit;
    //是否完成（0-否 1-是）
    private Integer isDone;
    //创建时间
    private Date createDate;
    //是否推荐（0-否 1-是）
    private Integer isRecommend;
    //是否有效（0-否 1-是）
    private Integer isValid;
    //量表指导意见
    private ScaleAdviceEntity scaleAdvice;
    //条目集合
    private List<ScaleQuestionEntity> listQuestions;
    //测评报告图表类型
    private List<ScaleChartsDto> listCharts;
    //缩略图图片
    private String thumbnail;
    //测试次数
    private Integer testCount;
    //题数
    private Integer qCount;
    //排序
    private Integer sort;
    //页码
    private Integer pageIndex;
    //每页显示数据数目
    private Integer pageSize;
}

package cn.psycloud.psyplatform.dto.measuringroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ScaleQuestionDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //条目ID
    private Integer id;
    //条目内容
    private String qContent;
    //条目类型：1-单选 2-多选 3-填空
    private Integer qType;
    //条目序号
    private Integer qNumber;
    //量表ID
    private Integer scaleId;
    //是否有效
    private Integer isValid;
    //条目的答案集合
    private List<ScaleAnswerEntity> listAnswers;
    //答案集合字符串
    private String answers;
}
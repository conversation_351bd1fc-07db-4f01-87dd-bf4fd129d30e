package cn.psycloud.psyplatform.dto.platform;

import lombok.Data;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统数据看板DTO
 */
@Data
public class DashboardDto {
    private static final long serialVersionUID = 1L;
    
    // 查询条件
    private Integer structId;
    private String startDate;
    private String endDate;
    private List<Integer> childStructs;
    
    // 咨询量统计
    private Integer totalConsultations;                    // 总咨询量
    private List<Map<String, Object>> consultationsByForm; // 按咨询形式分布(驻场、线上、门店等)
    private List<Map<String, Object>> consultationsByField; // 按咨询领域分布
    private List<Map<String, Object>> consultationsByRisk;  // 按心理风险分布
    private List<Map<String, Object>> consultationsTrend;   // 咨询量趋势(按月)
    
    // 活动量统计
    private Integer totalActivities;                      // 总活动量
    private List<Map<String, Object>> activitiesByType;   // 按活动类型分布
    private List<Map<String, Object>> activitiesTrend;    // 活动量趋势(按月)
    
    // 测评量统计
    private Integer totalTests;                           // 总测评量
    private List<Map<String, Object>> testsByWarningLevel; // 按预警等级分布
    private List<Map<String, Object>> testsTrend;         // 测评量趋势(按月)
    
    // 其他统计
    private Double riskConsultationRate;                  // 高风险咨询占比
    private Integer completedTestsCount;                  // 已完成测评数
    private Double testCompletionRate;                    // 测评完成率
}

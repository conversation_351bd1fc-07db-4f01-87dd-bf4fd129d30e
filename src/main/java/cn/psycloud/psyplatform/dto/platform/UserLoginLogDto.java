package cn.psycloud.psyplatform.dto.platform;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserLoginLogDto  extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //id
    public Integer id;
    //用户名
    public String loginName;
    //登录日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date loginDate;
    //登录客户端IP地址
    public String ipAddress;
    //设备信息
    public String deviceInfo;
    //登录成功标识 0 -失败 1-成功
    public Integer state;
    //登录方式
    public Integer loginWay;
    public Date startTime;
    public Date endTime;
}

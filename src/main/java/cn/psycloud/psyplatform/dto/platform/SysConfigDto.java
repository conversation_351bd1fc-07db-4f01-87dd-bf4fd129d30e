package cn.psycloud.psyplatform.dto.platform;

import cn.psycloud.psyplatform.entity.platform.SmsConfigEntity;
import lombok.Data;

import java.util.Date;

/**
 * 平台参数
 */
@Data
public class SysConfigDto {
    private static final long serialVersionUID = 1L;
    //平台名称
    private String platformName ;
    //机构名称
    private String orgName ;
    //是否开放注册：0-否 1-开放
    private Integer isOpenReg ;
    //注册是否需要审核： 0- 否 1-是
    private Integer isRegChecked ;
    //测评异常是否通知： 0- 否 1-是
    private Integer isAbnormalNotify ;
    //咨询预约是否通知： 0- 否 1-是
    private Integer isCounselingNotify ;
    //是否启用手机短信
    private Integer isSmsEnabled ;
    //是否启用积分
    private Integer isPointsEnabled ;
    //是否启用微信登录接口
    private Integer isWeChatEnabled ;
    //软件安装日期
    private Date installDate ;
    //咨询倾诉热线
    private String counselingHotline ;
    //使用期限：默认一年
    private Integer limitedDays;
    //是否启用软件使用期限制：默认启用
    private Boolean isLimitedDayEnabled;
    //是否启用手机号码直接登录注册
    private Integer isMobileLoginEnabled;
    //短信配置
    private SmsConfigEntity smsConfig;
}
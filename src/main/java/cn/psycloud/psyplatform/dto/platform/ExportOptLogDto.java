package cn.psycloud.psyplatform.dto.platform;

import cn.psycloud.psyplatform.util.converter.CustomConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 *  导出操作记录
 */
@Data
public class ExportOptLogDto {
    private static final long serialVersionUID = 1L;
    //用户id
    @ExcelProperty(value ="用户", converter = CustomConverter.class)
    public Integer userId;
    //操作时间
    @ExcelProperty(value ="操作时间", converter = CustomConverter.class)
    public Date optDate;
    //操作页面
    @ExcelProperty(value ="操作页面", converter = CustomConverter.class)
    public String optUrl;
    //操作对象
    @ExcelProperty(value ="操作对象", converter = CustomConverter.class)
    public String optClassname;
    //操作方法
    @ExcelProperty(value ="操作方法", converter = CustomConverter.class)
    public String optMethod;
    //操作参数
    @ExcelProperty(value ="操作参数", converter = CustomConverter.class)
    public String optArgs;
}

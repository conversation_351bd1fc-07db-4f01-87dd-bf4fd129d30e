package cn.psycloud.psyplatform.dto.platform;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class SmsSendRecordDto {
    private static final long serialVersionUID = 1L;

    private Integer id;
    //手机号
    private String mobile;
    //发送日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date sendDate;
    //签名
    private String signName;
    //模版code
    private String templateCode;
    //页码
    private Integer pageIndex;
    //每页显示数据数目
    private Integer pageSize;
}

package cn.psycloud.psyplatform.dto.anteroom;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 导入人员信息实体
 */
@Data
public class ImportUserDto {
    private static final long serialVersionUID = 1L;
    @ExcelProperty(value = "用户名", index = 0)
    private String loginName;
    @ExcelProperty(value = "姓名", index = 1)
    private String realName;
    @ExcelProperty(value = "性别", index = 2)
    private String sex;
    @JSONField(format = "yyyy-MM-dd")
    @ExcelProperty(value = "出生年月", index = 3)
    private String birth;
    @ExcelProperty(value = "手机号码",index = 4)
    private String mobile;
    //导入状态
    private int state;
    //导入返回消息
    private String msg;
}
package cn.psycloud.psyplatform.dto.anteroom;

import cn.psycloud.psyplatform.entity.counselingroom.CounselingItemEntity;
import lombok.Data;
import java.util.List;

/**
 * 咨询师附加信息
 */
@Data
public class CounselorInfoDto {
    private static final long serialVersionUID = 1L;
    //用户ID
    private Integer userId;
    //是否推荐
    private Integer isRecommend;
    //咨询方向
    private String beGoodAt;
    //咨询师介绍
    private String counselorIntro;
    //咨询项目
    private List<CounselingItemEntity> counselingItemLists;
}
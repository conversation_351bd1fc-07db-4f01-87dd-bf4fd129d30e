package cn.psycloud.psyplatform.dto.anteroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PointsDetailDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //自增ID
    private Integer id;
    //用户ID集合
    private String userIds;
    //用户ID
    private Integer userId;
    //用户名
    private String loginName;
    //姓名
    private String realName;
    //所属组织
    private Integer structId;
    //所属组织全称
    private String structFullName;
    //积分明细
    private Integer point;
    //积分操作类型： 0 - 消费 1- 充值
    private Integer chargeType;
    //积分来源
    private Integer sourceId;
    //积分来源名称
    public String sourceName;
    //操作人
    private Integer operator;
    //操作日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
}
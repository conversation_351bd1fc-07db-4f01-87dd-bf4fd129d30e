package cn.psycloud.psyplatform.dto.anteroom;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class MailDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //消息ID
    private Integer id;
    //消息标题
    private String msgTitle;
    //消息内容
    private String msgContent;
    //收件人
    private Integer toUser;
    //发送人
    private Integer fromUser;
    //发送人姓名
    private String fromName;
    //发送人头像
    private String fromHeadPic;
    //是否已读
    private Integer isRead;
    //发送日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date sendDate;
    //是否有效
    private Integer isValid;
}
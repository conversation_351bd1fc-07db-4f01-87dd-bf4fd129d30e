package cn.psycloud.psyplatform.dto.anteroom;

import cn.psycloud.psyplatform.dto.comment.CommentDto;
import cn.psycloud.psyplatform.entity.anteroom.CounselorInfoEntity;
import cn.psycloud.psyplatform.entity.anteroom.WechatUserEntity;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class UserDto {
    private static final long serialVersionUID = 1L;
    //用户ID
    private Integer userId;
    //产品id（获取评论使用）
    private Integer productId;
    //用户名
    private String loginName;
    //密码
    private String pwd;
    //真实姓名
    private String realName;
    //角色ID
    private Integer roleId;
    //角色
    private RoleDto role;
    //所属机构ID
    private Integer structId;
    //是否是咨询师身份
    private int isCounselor;
    //所属机构名称
    private String structName;
    //所属机构全称
    private String structFullName;
    //性别
    private String sex;
    //出生日期
    private String birth;
    //年龄
    private Integer age;
    //手机号码
    private String mobile;
    //手机号码是否绑定
    private Integer isMobileBind;
    //紧急联系人
    private String emergencyContactPerson;
    //紧急联系电话
    private String emergencyContactMobile;
    //身份证号
    private String iDCardNo;
    //民族
    private String nation;
    //邮箱
    private String email;
    //地址/省
    private String addressProvince;
    //地址/市
    private String addressCity;
    //地址/区
    private String addressDist;
    //详细地址
    private String addressDetail;
    //头像
    private String headPic;
    //籍贯
    private String nativePlace;
    //文化程度
    private String education;
    //职业
    private String job;
    //宗教信仰
    private String religion;
    //婚姻状态
    private String marriage;
    //年龄范围
    private String ageRange;
    //工作性质
    private String jobNature;
    //所在单位
    private String orgType;
    //个人简介
    private String description;
    //最后登录IP地址
    private String lastLoginIp;
    //最后登录时间
    private Date lastLoginDateTime;
    //注册日期
    private Date regDate;
    //是否审核
    private Integer isChecked;
    //操作者
    private Integer operator;
    //是否有效
    private Integer isValid;
    //咨询师附加信息
    private CounselorInfoEntity counselorInfo;
    //积分
    private Integer points;
    //评论
    private List<CommentDto> comments;
    //用户角色标识
    private String flag;
    //页码
    private Integer pageIndex;
    //每页显示数据数目
    private Integer pageSize;
    //所属组织的下级组织集合
    private List<Integer> childStructs;
    //所属组织的所有父组织集合
    private  List<Integer> parentStrusts;
    //微信用户信息
    private WechatUserEntity wechatUserEntity;
}

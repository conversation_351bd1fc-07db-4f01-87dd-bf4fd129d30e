package cn.psycloud.psyplatform.dto.anteroom;

import cn.psycloud.psyplatform.entity.anteroom.UserEntity;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class RoleDto {
    private static final long serialVersionUID = 1L;
    //角色ID
    private Integer roleId;
    //角色名称
    private String roleName;
    //添加日期
    private Date addDate;
    //添加人
    private Integer operator;
    //添加人
    private UserEntity operatorUser;
    //标识： p-平台角色 j-机构角色
    private String flag;
    //是否有效
    private Integer isValid;
    //角色具有的权限
    private List<SysFunctionDto> listFunctions;
    //页码
    private Integer pageIndex;
    //每页显示数据数目
    private Integer pageSize;
}
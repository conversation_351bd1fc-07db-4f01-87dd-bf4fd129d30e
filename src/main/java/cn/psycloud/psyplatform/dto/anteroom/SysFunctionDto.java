package cn.psycloud.psyplatform.dto.anteroom;

import lombok.Data;

import java.util.List;

/**
 * 平台功能
 */
@Data
public class SysFunctionDto {
    private static final long serialVersionUID = 1L;
    //自增ID
    private Integer id;
    //功能编码
    private String functionCode;
    //父功能编码
    private String parentCode;
    //功能名称
    private String functionName;
    //功能类型
    private String functionType;
    //Url
    private String url;
    //Icon
    private String icon;
    //排序
    private Integer sort;
    //层级
    private Integer level;
    //是否有效
    private Integer isValid;
    //子功能
    private List<SysFunctionDto> children;
    //用户名
    private String loginName;
    //角色id
    private Integer roleId;
}

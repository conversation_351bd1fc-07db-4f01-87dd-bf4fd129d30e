package cn.psycloud.psyplatform.dto.survey;

import cn.psycloud.psyplatform.util.converter.CustomConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ExportSurveyRecordDto {
    private static final long serialVersionUID = 1L;
    //账号
    @ExcelProperty(value ="用户名", converter = CustomConverter.class)
    private String loginName;
    //姓名
    @ExcelProperty(value = "姓名", converter = CustomConverter.class)
    private String realName;
    //组织全称
    @ExcelProperty(value = "所属组织", converter = CustomConverter.class)
    private String structFullName;
    //测评任务
    @ExcelProperty(value = "任务名称", converter = CustomConverter.class)
    private String taskName;
    //状态
    @ExcelProperty(value = "状态", converter = CustomConverter.class)
    private String isDone;
    @ExcelProperty(value = "作答时间", converter = CustomConverter.class)
    private String recordDate;
}

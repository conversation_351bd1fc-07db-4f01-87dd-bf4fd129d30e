package cn.psycloud.psyplatform.dto.survey;

import cn.psycloud.psyplatform.dto.core.BasePageDto;
import cn.psycloud.psyplatform.entity.survey.SurveyItemEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 *  调查问卷题目
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SurveyQuestionDto extends BasePageDto {
    private static final long serialVersionUID = 1L;
    //问题id
    private Integer id;
    //问题内容
    private String qContent;
    //问题编号
    private Integer qNumber;
    //问题类型
    private Integer qType;
    //问卷id
    private Integer surveyId;
    //选项集合
    private List<SurveyItemEntity> listItems;
}

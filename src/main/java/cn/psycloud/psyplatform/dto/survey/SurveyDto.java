package cn.psycloud.psyplatform.dto.survey;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *  调查问卷
 */
@Data
public class SurveyDto {
    private static final long serialVersionUID = 1L;
    //问卷ID
    private Integer id;
    //问卷名称
    private String surveyName;
    //是否启用
    private Integer isEnabled;
    //添加人
    private Integer operator;
    //添加人姓名
    private String realName;
    //添加日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    //是否有效
    private Integer isValid;
    //是否完成
    private Integer isDone;
    //页码
    private Integer pageIndex;
    //每页显示数据数目
    private Integer pageSize;
    //问题集合
    private List<SurveyQuestionDto> listQuestions;
}

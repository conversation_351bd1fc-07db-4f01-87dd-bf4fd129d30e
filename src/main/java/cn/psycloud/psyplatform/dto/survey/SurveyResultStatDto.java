package cn.psycloud.psyplatform.dto.survey;

import lombok.Data;
import java.util.List;

@Data
public class SurveyResultStatDto {
    private static final long serialVersionUID = 1L;
    //题目序号
    private Integer qNumber;
    //题目内容
    private String qContent;
    //题目类型
    private Integer qType;
    //结果选择数量集合
    List<SurveyResultCountDto> listResultCounts;
    //评分题平均分
    private Double ratingAverage;
}

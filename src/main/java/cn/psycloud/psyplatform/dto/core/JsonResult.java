package cn.psycloud.psyplatform.dto.core;

import lombok.Data;

@Data
public class JsonResult<T>{
    //结果代码
    private Integer resultCode;
    //结果消息
    private String resultMsg;
    //数据
    private T data;

    /**
     *  无参构造函数。若没有数据返回，提示信息为：操作成功。
     */
    public JsonResult() {
        this.resultCode = ResultCodeAndMsg.SuccessCode;
        this.resultMsg = ResultCodeAndMsg.SuccessMsg;
    }

    /**
     *  有数据返回时，提示信息为：操作成功。
     * @param data 返回数据
     */
    public JsonResult(T data){
        this.data = data;
        this.resultCode = ResultCodeAndMsg.SuccessCode;
        this.resultMsg = ResultCodeAndMsg.SuccessMsg;
    }

    /**
     *  有数据返回时，自定义消息提示。
     * @param data 返回数据
     * @param msg 返回消息
     */
    public JsonResult(T data, String msg){
        this.data = data;
        this.resultCode = ResultCodeAndMsg.SuccessCode;
        this.resultMsg = msg;
    }
}
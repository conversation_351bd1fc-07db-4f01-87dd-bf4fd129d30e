package cn.psycloud.psyplatform.dto.core;

import lombok.Data;

@Data
public class ResultCodeAndMsg {
    // 用户名或者密码错误代码
    public static final Integer LoginPasswordOrUserNameErrorCode = 100;
    // 用户名或者密码错误消息
    public static final String LoginPasswordOrUserNameErrorMsg = "登录失败";
    //未登录或者登录状态失效
    public static final Integer UnLoginCode = 102;
    public static final String UnLoginMsg = "未登录或者登录状态失效！";
    //系统检测为默认密码
    public static final Integer PASSWORD_NEED_CHANGE_CODE = 101;
    public static final String PASSWORD_NEED_CHANGE_MSG= "系统检测到您使用的是默认密码，建议修改密码，防止个人信息泄露。";
    //密码过期
    public static final Integer PASSWORD_EXPIRE_CODE = 108;
    public static final String PASSWORD_EXPIRE_MSG = "您的密码已过期，必须更改。";
    //登录次数超限
    public static final Integer ACCOUNT_LOCKED_CODE = 109;
    public static final String ACCOUNT_LOCKED_MSG = "登录失败次数超限，账号已停用，请稍后再次尝试登录。";
    //登录失败次数提示
    public static final Integer LOGIN_ATTEMPT = 110;
    //通用成功代码
    public static final Integer SuccessCode = 200;
    //通用操作成功信息
    public static final String SuccessMsg = "操作成功";
    //通用失败代码
    public static final Integer FailureCode = 201;
    //通用失败消息
    public static final String FailureMsg = "操作失败";
    //修改密码成功
    public static final String ResetPwdSuccess = "已经成功修改密码，请妥善保存！";
    //更换头像成功
    public static final String UpdateAvatarSuccess = "头像更换成功！";
    //量表添加完成后消息
    public static final String ScaleAddSuccessMsg = "恭喜您！您已经完成整个量表的添加过程，前往量表库测试一下吧。";
    // 量表测试前验证基本信息是否符合量表要求：验证失败后的消息
    public static final String ScaleUserInfoValidFailedMsg = "您的基本信息不符合量表要求，请完善个人信息后再重试。";
    public static final Integer ScaleUserInfoValidFailedCode = 103;
    //测试任务测试数量额度用完
    public static final String TaskTestCountEmptyMsg = "该批次测试额度已经用完。";
    public static final Integer TaskTestCountEmptyCode = 104;

    //测评任务时间已结束
    public static final Integer TaskEnded = 105;
    //测评任务无效
    public static final Integer TaskInvalid = 106;

    //任务里的调查问卷
    public  static  final Integer TaskSurveyDoneCode = 107;
    public static final String TaskSurveyDoneMsg = "您已经完成该问卷！";

    //添加测评任务成功
    public static final String ScaleTaskCreateSuccess = "您已经成功添加测评任务！";
    //咨询师添加预约成功
    public static final String CounselingOrderCounselorAddSuccess = "预约成功，请留意咨询时间，按时与来访者进行咨询工作。";
    //来访者添加预约成功
    public static final String CounselingOrderVisitorAddSuccess = "预约成功，请耐心等待咨询师处理您的预约请求，稍后可以在【我的预约】里查看预约状态。";
    //验证预约时间段
    public static final String CounselingOrderExistFailed = "该咨询师在该时间段内已经有预约了！";
    //验证来访者信息是否存在失败
    public static final String CheckVisitorExistFailed = "查不到该来访者信息！";
    //手机号码已经被绑定
    public static final String CheckMobileExists = "该手机号码已经被占用，请更换其它手机号码！";
    //手机号码未被绑定
    public static final String CheckMobileNoExists = "该手机号码可以使用！";
    public static final String MobileUnBinded = "该手机号码尚未注册，请注册后再登录！";

    //手机号码没有完善
    public static final  Integer MOBILE_IS_NULL_CODE = 202;
    public  static final String MOBILE_IS_NULL_MSG = "手机号码没有绑定";
    //手机号码绑定成功
    public static final String BindMobileSuccess = "绑定成功！";
    //积分余额不够提示
    public static final String PoIntegersNotEnough = "您的积分余额不足，请先充值！";
    //发表评论成功
    public static final String CommentSuccess = "发表成功！";
    //短信发送成功
    public static final String SEND_SMS_SUCCESS ="短信已成功发送到您的手机，请注意查收。";
    //短信发送失败
    public static final String SEND_SMS_FAILED ="短信发送失败。";
    //短信验证码还在有效期内
    public static final String SEND_SMS_NOTEXPIRED = "发送短信频繁，请稍后再试";
    //心理训练营报名成功的提示语
    public static final String REG_TRAININGCAMP_SUCCESS = "恭喜你！报名成功，正式开启心理打卡训练营之旅。从现在起，你将踏上一段自我探索与成长的奇妙征程。在接下来的日子里，让我们一起用打卡的方式，解锁内心的力量，遇见更好的自己。";
    //心理训练营打卡成功
    public static final String TRAININGCAMP_CHECKIN_SUCCESS= "打卡成功！";
    //训练营调查问卷完成提示
    public static final Integer TRAININGCAMP_SURVEY_DONE_CODE = 111;
    public static final String TRAININGCAMP_SURVEY_DONE = "您已经完成该调查问卷！";
}

package cn.psycloud.psyplatform.globalException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    /**
     * 处理异常
     */
    @ExceptionHandler(Exception.class)
    public void handleExceptions(HttpServletRequest request, Exception ex) {
        ex.printStackTrace();
        log.error("系统内部错误: " + ex);
    }
}
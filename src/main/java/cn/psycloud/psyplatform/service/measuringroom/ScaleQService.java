package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.DuallistData;
import cn.psycloud.psyplatform.dto.measuringroom.ImportScaleQDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleQuestionDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleQuestionEntity;

import java.util.List;

public interface ScaleQService {
    /**
     *  查询量表条目集合：DualListBox形式
     * @param scaleId 量表Id
     * @return 题目集合
     */
    List<DuallistData> getQListForDualList(Integer scaleId);

    /**
     *  查询量表条目集合：分页
     * @param dto 查询条件
     * @return 题目集合
     */
    BSDatatableRes<ScaleQuestionEntity> getListByScaleId(ScaleQuestionDto dto);

    /**
     *  删除条目
     * @param qId 题目id
     * @return 是否成功
     */
    boolean delete(Integer qId);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  添加题目
     * @param entity 题目实体对象
     * @return 题目id
     */
    int addQuestion(ScaleQuestionEntity entity);

    /**
     *  修改题目
     * @param entity 题目实体对象
     * @return 影响行数
     */
    int updateQuestion(ScaleQuestionEntity entity);

    /**
     *  excel导入题目和答案
     * @param list excel表格数据
     * @param scaleId 量表id
     */
    void importQuestion(List<ImportScaleQDto> list, Integer scaleId);

    /**
     *  题目排序
     * @param qId 题目id
     * @param flag 排序标识
     * @return 影响行数
     */
    boolean sort(Integer qId,String flag);
}

package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorExplainEntity;

public interface ScaleFactorExplainService {
    /**
     *  删除因子解释
     * @param id 因子解释Id
     * @return 影响行数
     */
    int deleteById(int id);

    /**
     *  添加因子解释
     * @param entity 因子解释实体对象
     * @return 影响行数
     */
    int addExplain(ScaleFactorExplainEntity entity);

    /**
     *  修改因子解释
     * @param entity 因子解释实体对象
     * @return 影响行数
     */
    int updateExplain(ScaleFactorExplainEntity entity);

    /**
     *  根据id查询因子结果解释
     * @param factorId 因子id
     * @return 结果解释集合
     */
    BSDatatableRes<ScaleFactorExplainEntity> getListById(Integer factorId);
}

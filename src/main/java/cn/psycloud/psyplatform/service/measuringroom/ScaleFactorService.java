package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleFactorDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorEntity;

import java.util.List;

public interface ScaleFactorService {
    /**
     *  根据ID查询量表所有的因子集合：返回Bootstrap Datatables所需格式
     * @param scaleId 量表id
     * @return 因子集合
     */
     BSDatatableRes<ScaleFactorDto> getFactorsByScaleId(Integer scaleId);

    /**
     *  查询因子集合集合：select
      * @param dto 查询条件
     * @return 集合
     */
    List<Object> getListForSelect(ScaleFactorDto dto);

    /**
     *  新增因子
     * @param entity 因子实体对象
     * @return 影响行数
     */
    int addFactor(ScaleFactorEntity entity);

    /**
     *  修改因子
     * @param entity 因子实体对象
     * @return 影响行数
     */
    int updateFactor(ScaleFactorEntity entity);

    /**
     *  删除
     * @param factorId 因子id
     * @return 影响行数
     */
    boolean deleteById(int factorId);

    /**
     *  因子排序
     * @param factorId 因子id
     * @param flag 排序标识
     * @return 影响行数
     */
    boolean sort(Integer factorId,String flag);

    /**
     * 获取因子总数
     * @param scaleId 量表id
     * @return 因子数
     */
    int getFactorCount(Integer scaleId);

    /**
     *  根据因子id查询因子名称
     * @param factorId 因子id
     * @return 因子名称
     */
    String getFactorNameById(Integer factorId);
}

package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleDto;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleNameDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleEntity;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.util.List;

public interface ScaleService {
    /**
     * 根据量表ID查询量表详细信息
     * @param scaleId 量表id
     * @return 量表实体对象
     */
    ScaleDto getById(Integer scaleId);

    /**
     *  根据条件查询量表集合
     * @param dto 量表实体对象
     * @return 量表集合
     */
    List<ScaleDto> getList(ScaleDto dto);

    /**
     *  查询量表集合：分页
     * @param dto 量表实体对象
     * @return 量表集合
     */
    BSDatatableRes<ScaleDto> getListByPaged(ScaleDto dto);

    /**
     *  查询量表集合：DualListBox格式
     * @return 集合
     */
    List<Object> getListForDualList();

    /**
     *  查询量表集合：select格式
     * @return 集合
     */
    List<Object> getListForSelect();

    /**
     *  获取推荐量表集合
     * @return 量表集合
     */
    List<ScaleDto> getRecommendList(ScaleDto dto);

    /**
     *  新增
     * @param entity 量表实体对象
     * @return 影响行数
     */
    int addScale(ScaleEntity entity);

    /**
     *  更新
     * @param entity 量表实体对象
     * @return 影响行数
     */
    int updateScale(ScaleEntity entity);

    /**
     *  删除
     * @param id 量表id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  验证量表名称是否存在
     * @param dto 量表实体对象
     * @return 是否存在
     */
    boolean isScaleNameExists(ScaleNameDto dto);

    /**
     *  更改量表状态
     * @param scaleId 量表id
     * @param state 状态
     * @return 影响行数
     */
    int updateScaleDone(Integer scaleId, Integer state);

    /**
     *  获取量表类别下的量表集合
     * @param scaleTypeId 量表分类Id
     * @return 量表集合
     */
    List<ScaleDto> getListByType(Integer scaleTypeId);

    /**
     *  根据测评任务查询量表集合
     * @param taskId 任务id
     * @return 集合
     */
    List<Object> getListByTaskId(Integer taskId);

    /**
     * 手机端首页
     * @return 量表集合
     */
    List<ScaleDto> getListForIndex();

    /**
     * 设置量表排序
     * @param scaleId 量表id
     * @param sort  排序
     * @return 影响行数
     */
    int setSort(Integer scaleId, Integer sort);

    /**
     *  导出量表
     * @param scaleId 量表id
     */
    XWPFDocument exportScale(Integer scaleId);
}

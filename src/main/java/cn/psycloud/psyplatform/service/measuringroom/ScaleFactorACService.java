package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleFactorAbnormalConditionEntity;

public interface ScaleFactorACService {
    /**
     *  根据因子查询异常条件集合：返回bootstrap datatables格式
     * @param factorId 因子id
     * @return 异常条件集合
     */
    BSDatatableRes<ScaleFactorAbnormalConditionEntity> getListByFactorId(Integer factorId);

    /**
     *  添加异常条件
     * @param entity 异常条件实体对象
     * @return 影响行数
     */
    int add(ScaleFactorAbnormalConditionEntity entity);

    /**
     *  删除异常条件
     * @param id 异常条件id
     * @return 影响行数
     */
    int deleteById(Integer id);
}

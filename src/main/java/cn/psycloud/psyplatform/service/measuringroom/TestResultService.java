package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.TestResultDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestResultVO;

import java.util.List;

public interface TestResultService {
    /**
     *  保存答案
     * @return 执行是否成功
     */
    boolean saveResult(TestResultVO vo);

    /**
     *  保存答案：父母养育方式问卷(EMBU)
     * @return 执行是否成功
     */
    boolean saveResultEMBU(TestResultVO vo);

    /**
     *  根据条件查询答案记录
     * @param recordId 记录Id
     * @return 选项集合
     */
    List<TestResultDto> getList(Integer recordId);
}

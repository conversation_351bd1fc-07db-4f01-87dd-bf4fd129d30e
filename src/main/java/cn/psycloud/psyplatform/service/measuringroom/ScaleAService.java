package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleAnswerEntity;

public interface ScaleAService {

    /**
     *  查询条目的答案集合：返回Bootstrap Datatables格式
     * @param qId 题目id
     * @return 答案集合
     */
    BSDatatableRes<ScaleAnswerEntity> getAnswersByQId(Integer qId);

    /**
     *  添加答案（单个条目）
     * @param entity 答案实体对象
     * @return 影响行数
     */
    int addAnswer(ScaleAnswerEntity entity);

    /**
     *  添加答案（批量）
     * @param entity 答案实体对象
     * @param qIds 题目id集合
     * @return 是否成功
     */
    boolean batchAddAnswer(ScaleAnswerEntity entity, String qIds);

    /**
     *  删除答案
     * @param aId 答案id
     * @return 影响行数
     */
    int delete(Long aId);
}

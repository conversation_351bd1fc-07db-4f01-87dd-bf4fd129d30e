package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleTypeDto;
import cn.psycloud.psyplatform.entity.measuringroom.ScaleTypeEntity;

import java.util.List;

public interface ScaleTypeService {
    /**
     *  查询量表类型集合：分页
     * @param model 量表类型实体对象
     * @return 量表类型集合
     */
    BSDatatableRes<ScaleTypeEntity> getListByPaged(ScaleTypeDto model);

    /**
     *  查询量表类型集合：select2
     * @return 量表类型集合
     */
    List<Object> getListForSelect();

    /**
     *  查询量表类型集合并获取分类下面的量表集合
     * @return 量表集合
     */
    List<ScaleTypeDto> getScalesByType();

    /**
     *  根据条件查询量表分类集合
     * @param dto  量表类型实体对象
     * @return   分类集合
     */
    List<ScaleTypeEntity> getList(ScaleTypeDto dto);

    /**
     *  新增
     * @param entity 量表类型实体对象
     * @return 影响行数
     */
    int addScaleType(ScaleTypeEntity entity);

    /**
     *  更新
     * @param entity 量表类型实体对象
     * @return 影响行数
     */
    int updateScaleType(ScaleTypeEntity entity);

    /**
     *  删除
     * @param id 量表分类id
     * @return 影响行数
     */
    int delete(int id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);
}

package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ExportTestScoreDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestScoreDto;

import java.util.LinkedHashMap;
import java.util.List;

public interface TestScoreService {
    /**
     *  根据测评记录ID删除测评得分情况
     * @param recordId 测评记录id
     * @return 影响行数
     */
    int delTestScoreByRecordId(Integer recordId);

    /**
     *  计算量表得分情况
     * @param recordId 测评记录id
     * @param scaleId 量表id
     */
    void calc(Integer recordId, Integer scaleId);

    /**
     *  获取测评报告
     * @param recordId 测评记录id
     * @return 测评得分集合
     */
    List<TestScoreDto> getReport(Integer recordId);

    /**
     *  查询测评得分情况集合：分页
     * @param dto 测评得分实体对象
     * @return 测评得分集合
     */
    BSDatatableRes<TestScoreDto> getTestScoreListByPaged(TestScoreDto dto);

    /**
     *  查询测评得分情况集合
     * @param dto 测评得分实体对象
     * @return 测评得分集合
     */
    List<TestScoreDto> getTestScoreList(TestScoreDto dto);

    /**
     *  导出测评得分情况到excel
     * @param dto 条件
     */
    List<LinkedHashMap<String,Object>> exportTestScore(ExportTestScoreDto dto);

    /**
     *  导出测评选项
     * @param dto 条件
     * @return 集合
     */
    List<LinkedHashMap<String,Object>> exportTestResult(ExportTestScoreDto dto);

    /**
     *  导出测评选项得分
     * @param dto 条件
     * @return 集合
     */
    List<LinkedHashMap<String,Object>> exportTestResultScore(ExportTestScoreDto dto);

    /**
     *  导出问卷调查结果数据
     * @param dto 条件
     * @return 集合
     */
    List<LinkedHashMap<String,Object>> exportSurveyResutl(ExportTestScoreDto dto);

    /**
     *  测评统计获取测评得分情况
     * @param factorId 因子id
     * @param recordIds 测评记录id集合
     * @return 测评得分集合
     */
    List<TestScoreDto> getTestScoreListForStat(Integer factorId, String recordIds);

    /**
     *  异常结果导出报告
     * @param dto 测评得分实体对象
     * @return 测评记录集合
     */
    List<TestRecordDto> getRecordListByTestScore(TestScoreDto dto);
}

package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.anteroom.UserDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ScaleUndoneDto;
import cn.psycloud.psyplatform.dto.measuringroom.TaskDto;
import cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyStatDto;
import cn.psycloud.psyplatform.entity.measuringroom.TaskEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface TaskService {
    /**
     *  验证任务名称是否重复
     * @param taskName 任务名称
     * @param taskKind 任务类型
     * @return 是否存在
     */
    int isTaskNameExist(String taskName, Integer taskKind);

    /**
     *  添加测评任务
     * @param dto 测评任务实体对象
     * @return 是否成功
     */
    boolean addTask(TaskDto dto, HttpServletRequest request);

    /**
     *  修改测评任务基本信息
     * @param entity 测评任务实体对象
     * @return 影响行数
     */
    int updateTask(TaskEntity entity);

    /**
     *  获取测评任务集合：分页
     * @param dto 查询条件
     * @return 测评任务集合
     */
    BSDatatableRes<TaskDto> getListByPaged(TaskDto dto);

    /**
     *  删除测评任务
     * @param taskId 测评任务id
     * @return 影响行数
     */
    int delTask(Integer taskId);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  根据测评任务id查询测评对象
     * @param taskId 测评任务id
     * @return 用户集合
     */
    List<UserDto> getUsersByTaskId(Integer taskId);

    /**
     *  根据任务ID查询任务详情
     * @param taskId 测评任务id
     * @return 测评任务实体丢向
     */
    TaskDto getById(Integer taskId);

    /**
     *  获取我的测评任务
     * @param dto 查询条件
     * @return 测评任务集合
     */
    BSDatatableRes<TaskDto> getMyTasks(TaskDto dto);

    /**
     *  判断任务中的量表是否已经做过
     * @param taskId 测评任务id
     * @param scaleId 量表id
     * @return 测评记录实体对象
     */
    TestRecordEntity isScaleDone(Integer taskId, Integer scaleId);

    /**
     *  获取记录Id
     * @param taskId 测评任务id
     * @param scaleId 量表id
     * @return 测评记录id
     */
    Integer getRecordIdByTaskId(Integer taskId, Integer scaleId);

    /**
     *  根据测评记录ID查询结果查看规则
     * @param recordId 记录id
     * @return 查看规则
     */
    Integer getResultViewRuleByRecordId(Integer recordId);

    /**
     *  查询测评任务集合：转换成select所需格式
     * @return 集合
     */
    List<Object> getListForSelect(TaskDto taskDto);

    /**
     *  判断用户是不是属于当前测评任务
     * @param taskId 任务id
     * @return 记录数
     */
    int isTaskValid(Integer taskId);

    /**
    判断测评任务时间是否已结束
    */
    boolean isTaskEnded(Integer taskId);

    /**
     *  判断任务里的调查问卷是否完成
     * @param taskId 任务id
     * @return 是否完成
     */
    boolean isTaskSurveyDone(Integer userId,Integer taskId);

    /**
     *  判断问卷调查任务里的问卷是否完成
     * @param userId 用户id
     * @param taskId 任务id
     * @param surveyId 问卷id
     * @return 问卷作答记录实体对象
     */
    TaskSurveyRecordEntity isSurveyOfTaskDone(Integer userId, Integer taskId, Integer surveyId);

    /**
     *  查询用户的测评任务里面未完成的量表
     * @param userId 用户id
     * @param taskId 任务id
     * @return 未完成量表集合
     */
    List<ScaleUndoneDto> getScaleListUndone(Integer userId,Integer taskId);

    /**
     * 调查问卷结果统计
     * @param entity 调查问卷任务和问卷关联实体类
     * @return 实体对象
     */
    TaskSurveyStatDto getTaskSurveyStat(TaskSurveyEntity entity);

    /**
     * 调查问卷的结果统计
     * @param response
     * @param request
     * @param taskSurveyEntity
     * @return
     */
    String createSurveyStatReport(HttpServletResponse response, HttpServletRequest request, TaskSurveyEntity taskSurveyEntity);

    /**
     * 根据测评记录ID获取背景图片信息
     * @param recordId 测评记录ID
     * @return 背景图片信息
     */
    Map<String, Object> getBackgroundByRecordId(Integer recordId);
}

package cn.psycloud.psyplatform.service.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.ClockingDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.activityroom.ClockingEntity;

/**
 * 签到签退服务接口
 */
public interface ClockingService {
    /**
     * 添加签到签退记录
     * @param clockingEntity 签到签退实体
     * @return 影响行数
     */
    int addClockingRecord(ClockingEntity clockingEntity);

    /**
     * 查询签到签退记录是否存在
     * @param userId 用户ID
     * @param activityId 活动ID
     * @param actionType 操作类型：1-签到，2-签退
     * @return 是否签到签退
     */
    boolean isExists(Integer userId, Integer activityId, Integer actionType);

    /**
     * 查询签到签退记录列表
     * @param clockingDto 签到签退DTO
     * @return 签到签退实体列表
     */
    BSDatatableRes<ClockingDto> getListByPaged(ClockingDto clockingDto);

    /**
     * 查询当前用户的活动状态
     * @param userId 用户ID
     * @param activityId 活动ID
     * @return 活动状态
     */
    Integer getUserActivityStatus(Integer userId, Integer activityId);
}

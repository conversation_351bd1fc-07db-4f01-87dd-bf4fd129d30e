package cn.psycloud.psyplatform.service.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.SelfEvaluationDto;
import cn.psycloud.psyplatform.entity.activityroom.SelfEvaluationEntity;
import java.util.List;

public interface SelfEvaluationService {
    /**
     * 插入个人点评
     * @param SelfEvaluations 个人点评实体集合
     * @return 插入行数
     */
    boolean addSelfEvaluation(List<SelfEvaluationEntity> SelfEvaluations);

    /**
     * 根据活动ID获取个人点评列表
     * @param activityId 活动ID
     * @return 个人点评列表
     */
    List<SelfEvaluationDto> getSelfEvaluationList(Integer activityId);

    /**
     * 根据活动ID和用户ID获取个人点评
     * @param activityId 活动ID
     * @param userId 用户ID
     * @return 个人点评
     */
    String getMySelfEvaluationList(Integer activityId, Integer userId);
}

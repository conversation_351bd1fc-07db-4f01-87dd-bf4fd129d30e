package cn.psycloud.psyplatform.service.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.ActivityClockingPicDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivityClockingPicEntity;
import java.util.List;

public interface ActivityClockingPicService {
    /**
     * 插入签到图片上传记录
     * @param activityClockingPic 签到图片上传记录实体类
     * @return 影响行数
     */
    int insert(ActivityClockingPicEntity activityClockingPic);

    /**
     * 删除签到图片上传记录
     * @param id 签到图片上传记录ID
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     * 批量删除签到图片上传记录
     * @param ids 签到图片上传记录ID集合
     * @return 影响行数
     */
    boolean batchDel(String ids);

    /**
     * 获取签到图片上传记录列表
     * @return 签到图片上传记录列表
     */
    List<ActivityClockingPicEntity> getList(ActivityClockingPicDto activityClockingPicDto);

    /**
     * 获取随机图片
     * @return 图片名称
     */
    String getRandomPic();
}

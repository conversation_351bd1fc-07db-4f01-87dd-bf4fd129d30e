package cn.psycloud.psyplatform.service.activityroom;

import cn.psycloud.psyplatform.dto.activityroom.*;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.activityroom.ActivityEntity;
import cn.psycloud.psyplatform.entity.activityroom.OverallEvaluationEntity;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 心理活动服务接口
 */
public interface ActivityService {
    /**
     * 添加心理活动
     * @param activityEntity 心理活动实体
     * @return 活动ID
     */
    int add(ActivityEntity activityEntity);

    /**
     * 修改心理活动
     * @param activityEntity 心理活动实体
     * @return 是否成功
     */
    int update(ActivityEntity activityEntity);

    /**
     * 删除心理活动
     * @param id 心理活动id
     * @return 是否成功
     */
    int delete(Integer id);

    /**
     * 批量删除心理活动
     * @param ids 心理活动id集合
     * @return 是否成功
     */
    boolean batchDel(String ids);

    /**
     * 根据条件查询心理活动列表：分页
     * @param activityDto 心理活动查询条件
     * @return 心理活动列表
     */
    BSDatatableRes<ActivityDto> getListByPaged(ActivityDto activityDto);

    /**
     * 根据条件查询心理活动列表
     * @param activityDto 心理活动查询条件
     * @return 心理活动列表
     */
    List<ActivityDto> getList(ActivityDto activityDto);

    /**
     * 根据id查询心理活动
     * @param id 心理活动id
     * @return 心理活动详情
     */
    ActivityDto getById(Integer id);

    /**
     * 根据id查询心理活动详情
     * @param id 心理活动id
     * @return 心理活动详情
     */
    ActivityDto getForDetail(Integer id);

    /**
     * 判断调查问卷是否已做
     * @param userId 用户ID
     * @param activityId 活动ID
     * @param surveyId 调查问卷ID
     * @return 是否已做
     */
    boolean isSurveyDone(Integer userId, Integer activityId, Integer surveyId);

    /**
     * 导出活动问卷作答记录
     * @param dto 导出条件
     * @return 导出数据
     */
    List<LinkedHashMap<String,Object>> exportSurveyResult(ExportActivitySurveyRecordDto dto);

    /**
     * 获取活动问卷结果统计
     * @param activityId 活动ID
     * @param surveyId 问卷ID
     * @return 统计结果
     */
    ActivitySurveyStatDto getActivitySurveyStat(Integer activityId, Integer surveyId);

    /**
     * 获取我的活动
     * @param userId 用户ID
     * @return 我的活动列表
     */
    List<ActivityDto> getMyActivities(Integer userId);

    /**
     * 更新活动总评
     * @param overallEvaluationEntity 总评实体
     * @return 是否成功
     */
    int updateOverallEvaluation(OverallEvaluationEntity overallEvaluationEntity);

    /**
     * 获取活动总评
     * @param activityId 活动ID
     * @return 活动总评
     */
    OverallEvaluationDto getOverallEvaluation(Integer activityId);

    /**
     * 获取活动报告
     * @param activityId 活动ID
     * @return 活动报告
     */
    ActivityReportDto getActivityReport(Integer activityId);

    /**
     * 获取咨询师的活动集合：select
     * @param userId 咨询师id
     * @return 活动集合
     */
    List<Object> getCounselorActivitiesForSelect(Integer userId);

    /**
     * 获取活动数据看板数据
     * @param dto 查询条件
     * @return 活动数据看板数据
     */
    ActivityDashboardDto getActivityDashboard(ActivityDto dto);

    /**
     * 批量导出活动问卷作答记录为Word文档
     * @param dto 导出条件
     * @return 压缩包文件名
     */
    String exportSurveyToWord(ExportActivitySurveyRecordDto dto);
}

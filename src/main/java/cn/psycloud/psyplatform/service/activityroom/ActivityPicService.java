package cn.psycloud.psyplatform.service.activityroom;

import cn.psycloud.psyplatform.entity.activityroom.ActivityPicEntity;
import java.util.List;

public interface ActivityPicService {
    /**
     * 添加活动图片
     * @param activityPicEntity 活动图片实体
     * @return 是否成功
     */
    int uploadPic(ActivityPicEntity activityPicEntity);

    /**
     * 删除活动图片
     * @param id 活动图片id
     * @return 是否成功
     */
    int delPic(Integer id);

    /**
     * 批量删除活动图片
     * @param ids 活动图片id集合
     * @return 是否成功
     */
    boolean batchDel(String ids);

    /**
     * 获取活动图片列表
     * @param activityId 活动id
     * @return 活动图片列表
     */
    List<ActivityPicEntity> getList(Integer activityId);
}

package cn.psycloud.psyplatform.service.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.SchedulingDto;
import cn.psycloud.psyplatform.entity.counselingroom.SchedulingEntity;
import java.util.List;

public interface SchedulingService {
    /**
     *  获取排班信息集合
     * @param dto 查询条件
     * @return 集合
     */
    List<Object> getList(SchedulingDto dto);

    /**
     *  获取预约的排班信息集合
     * @param dto 查询条件
     * @return 集合
     */
    List<Object> getOrderSchedulingList(SchedulingDto dto);

    /**
     *  排班
     * @param entity 排班实体对象
     * @return 影响行数
     */
    int arrange(SchedulingEntity entity);

    /**
     *  修改排班
     * @param entity 排班实体对象
     * @return 影响行数
     */
    int updateScheduling(SchedulingEntity entity);

    /**
     *  删除排班
     * @param id 排班id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  验证咨询师的排班是否重复
     * @param dto  排班实体对象
     * @return 是否重复
     */
    int isSchedulingExists(SchedulingDto dto);
}

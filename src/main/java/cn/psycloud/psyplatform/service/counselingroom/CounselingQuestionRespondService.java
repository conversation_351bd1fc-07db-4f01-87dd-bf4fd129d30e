package cn.psycloud.psyplatform.service.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionRespondDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionRespondEntity;

import java.util.List;

public interface CounselingQuestionRespondService {
    /**
     *  发表回复
     * @param entity 回复实体对象
     * @return 影响行数
     */
    int post(CounselingQuestionRespondEntity entity);

    /**
     *  删除回复
     * @param id 回复id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  查询答疑所有回复
     * @param questionId 提问Id
     * @return 回复集合
     */
    List<CounselingQuestionRespondDto> getListById(Integer questionId);
}

package cn.psycloud.psyplatform.service.counselingroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingItemDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingItemEntity;

import java.util.List;

public interface CounselingItemService {
    /**
     *  根据ID查询咨询类型
     * @param id 咨询类型id
     * @return 咨询类型实体对象
     */
    CounselingItemDto getById(Integer id);

    /**
     *  查询集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    BSDatatableRes<CounselingItemDto> getListByPaged(CounselingItemDto dto);

    /**
     *  根据咨询师查询集合：转换成select所需格式
     * @param counselorId 咨询师id
     * @return 集合
     */
    List<Object> getListForSelectByCounselor(Integer counselorId);

    /**
     *  根据排班查询集合：转换成select所需格式
     * @param schedulingId 排班id
     * @return 集合
     */
    List<Object> getListForSelect(Integer schedulingId);

    /**
     *  新增
     * @param entity  咨询类型实体对象
     * @return 影响行数
     */
    int addCounselingItem(CounselingItemEntity entity);

    /**
     *  更新
     * @param entity 咨询类型实体对象
     * @return 影响行数
     */
    int updateCounselingItem(CounselingItemEntity entity);

    /**
     *  删除
     * @param id 咨询类型id
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);
}

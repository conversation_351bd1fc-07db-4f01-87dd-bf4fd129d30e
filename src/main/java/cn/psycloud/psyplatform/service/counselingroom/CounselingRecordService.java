package cn.psycloud.psyplatform.service.counselingroom;

import cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingRecordEntity;

import java.util.List;

public interface CounselingRecordService {
    /**
     *  保存咨询记录
     * @param entity 咨询记录实体对象
     * @return 影响行数
     */
    int addContent(CounselingRecordEntity entity);

    /**
     *  根据预约id查询咨询记录
     * @param id 预约id
     * @return 咨询记录集合
     */
    List<CounselingRecordEntity> getList(Integer id);

    /**
     *  手动添加咨询记录
     * @param dto 咨询预约实体类
     * @return 执行是否成功
     */
    boolean addCounselingRecord(CounselingOrderDto dto);
}

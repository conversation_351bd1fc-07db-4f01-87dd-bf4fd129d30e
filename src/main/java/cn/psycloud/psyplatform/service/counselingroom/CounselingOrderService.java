package cn.psycloud.psyplatform.service.counselingroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingOrderDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingOrderEntity;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface CounselingOrderService {
    /**
     *  根据排班id查询预约信息
     * @param schedulingId 排班id
     * @return 预约信息
     */
    CounselingOrderDto getBySchedulingId(Integer schedulingId);

    /**
     *  分页查询预约咨询记录
     * @param dto 查询条件
     * @return 集合
     */
    BSDatatableRes<CounselingOrderDto> getListByPaged(CounselingOrderDto dto);

    /**
     *  根据id查询预约信息
     * @param id 预约id
     * @return 预约信息
     */
    CounselingOrderDto getById(Integer id);

    /**
     *  删除预约
     * @param id 预约id
     * @return 影响行数
     */
    int deleteOrder(Integer id);

    /**
     *  批量删除
     * @param ids 预约id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  添加预约
     * @param entity 预约信息实体对象
     * @return 影响行数
     */
    int addOrder(HttpServletRequest request, CounselingOrderEntity entity);

    /**
     *  更新预约
     * @param entity 预约信息实体对象
     * @return 影响行数
     */
    int updateOrder(CounselingOrderEntity entity);

    /**
     *  更新预约状态
     * @param id 预约id
     * @param state 状态
     * @return 影响行数
     */
    int updateState(Integer id, Integer state);

    /**
     *  查询进行中或者未开始的预约集合
     * @param dto 条件
     * @return 集合
     */
    List<CounselingOrderDto> getOrderUnDoneList(CounselingOrderDto dto);

    /**
     *  查询已经结束的预约集合
     * @param dto 条件
     * @return 集合
     */
    BSDatatableRes<CounselingOrderDto> getOrderDoneList(CounselingOrderDto dto);
}

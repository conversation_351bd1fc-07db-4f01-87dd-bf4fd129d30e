package cn.psycloud.psyplatform.service.counselingroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingQuestionDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingQuestionEntity;

public interface CounselingQuestionService {
    /**
     *  根据ID查询
     * @param questionId 问题id
     * @return 问题实体对象
     */
    CounselingQuestionDto getById(Integer questionId);

    /**
     *  提问
     * @param entity 心理答疑实体对象
     * @return 提问id
     */
    int post(CounselingQuestionEntity entity);

    /**
     *  删除心理答疑
     * @param id 提问id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  修改
     * @param entity 心理答疑实体对象
     * @return 影响行数
     */
    int update(CounselingQuestionEntity entity);

    /**
     *  获取心理答疑集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    BSDatatableRes<CounselingQuestionDto> getListByPaged(CounselingQuestionDto dto);
}

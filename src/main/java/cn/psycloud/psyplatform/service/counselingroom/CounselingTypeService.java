package cn.psycloud.psyplatform.service.counselingroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.counselingroom.CounselingTypeDto;
import cn.psycloud.psyplatform.entity.counselingroom.CounselingTypeEntity;

import java.util.List;

public interface CounselingTypeService {
    /**
     *  查询集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    BSDatatableRes<CounselingTypeEntity> getListByPaged(CounselingTypeDto dto);

    /**
     *  查询集合
     * @param dto 查询条件
     * @return 集合
     */
    List<CounselingTypeEntity> getList(CounselingTypeDto dto);

    /**
     *  查询集合：转换成select所需格式
     * @return 集合
     */
    List<Object> getListForSelect(CounselingTypeDto dto);

    /**
     *  新增
     * @param counselingTypeEntity 实体对象
     * @return 影响行数
     */
    int add(CounselingTypeEntity counselingTypeEntity);

    /**
     *  更新
     * @param counselingTypeEntity 实体对象
     * @return 影响行数
     */
    int update(CounselingTypeEntity counselingTypeEntity);

    /**
     *  删除
     * @param id 类型id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);
}

package cn.psycloud.psyplatform.service.consultationcase;

import cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseKeywordDto;
import java.util.List;

public interface ConsultationCaseKeywordService {
    
    /**
     * 保存个案关键词关联（支持批量）
     * @param caseId 个案ID
     * @param keywords 关键词列表（逗号分隔）
     * @return 是否成功
     */
    boolean saveCaseKeywords(Integer caseId, String keywords);

    
    /**
     * 根据个案ID查询关键词列表
     * @param caseId 个案ID
     * @return 关键词列表
     */
    List<String> getKeywordsByCaseId(Integer caseId);

    /**
     * 根据个案ID删除关键词关联记录
     * @param caseId 个案ID
     * @return 是否成功
     */
    boolean deleteByCaseId(Integer caseId);
} 
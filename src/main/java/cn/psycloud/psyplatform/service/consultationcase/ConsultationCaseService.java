package cn.psycloud.psyplatform.service.consultationcase;

import cn.psycloud.psyplatform.dto.consultationcase.ConsultationCaseDto;
import cn.psycloud.psyplatform.dto.consultationcase.ExportConsultationCaseDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationCaseEntity;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ConsultationCaseService {
    /**
     * 新增个案
     * @param entity 个案实体
     * @return 影响行数
     */
    int add(ConsultationCaseEntity entity);

    /**
     * 更新个案
     * @param entity 个案实体
     * @return 影响行数
     */
    int update(ConsultationCaseEntity entity);

    /**
     * 删除个案
     * @param id 个案ID
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     * 批量删除个案
     * @param ids 个案ID集合
     * @return 影响行数
     */
    boolean batchDel(String ids);

    /**
     * 根据条件查询个案列表
     * @param consultationCaseDto 个案查询条件
     * @return 个案集合
     */
    BSDatatableRes<ConsultationCaseDto> getListByPaged(ConsultationCaseDto consultationCaseDto);

    /**
     * 获取我的个案集合
     * @param dto 个案查询条件
     * @return 个案集合
     */
    BSDatatableRes<ConsultationCaseDto> getMyCases(ConsultationCaseDto dto);

    /**
     * 根据ID查询个案详情
     * @param id 个案ID
     * @return 个案实体
     */
    ConsultationCaseDto getById(Integer id);

    /**
     * 导出咨询个案Word文档
     * @param id 个案ID
     * @param response HTTP响应
     */
    void exportWordDocument(Integer id, HttpServletResponse response);

    /**
     * 导出咨询个案
     * @param consultationCaseDto 个案查询条件
     * @return 个案集合
     */
    List<ExportConsultationCaseDto> getExportList(ConsultationCaseDto consultationCaseDto);

    /**
     * 获取咨询数据看板统计数据
     * @param consultationCaseDto 查询条件
     * @return 统计数据
     */
    java.util.HashMap<String, Object> getDashboardData(ConsultationCaseDto consultationCaseDto);
}

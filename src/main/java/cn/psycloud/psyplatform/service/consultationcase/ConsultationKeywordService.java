package cn.psycloud.psyplatform.service.consultationcase;

import cn.psycloud.psyplatform.dto.consultationcase.ConsultationKeywordDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.consultationcase.ConsultationKeywordEntity;
import java.util.List;

/**
 * 咨询关键词Service接口
 */
public interface ConsultationKeywordService {

    /**
     * 获取关键词列表
     * @param dto 查询条件
     * @return 分页结果
     */
    BSDatatableRes<ConsultationKeywordDto> getListByPaged(ConsultationKeywordDto dto);

    /**
     * 添加关键词
     */
    boolean add(ConsultationKeywordEntity entity);

    /**
     * 更新关键词
     */
    boolean update(ConsultationKeywordEntity entity);

    /**
     * 删除关键词
     */
    boolean delete(Integer id);

    /**
     * 批量删除关键词
     */
    boolean batchDel(String ids);

    /**
     * 获取所有关键词
     */
    List<String> getAllKeywords();

    /**
     * 检查关键词是否存在（排除指定ID）
     * @param keyword 关键词内容
     * @param excludeId 要排除的记录ID（新增时为null）
     * @return 是否存在
     */
    boolean checkKeywordExistsExcludeId(String keyword, Long excludeId);
} 
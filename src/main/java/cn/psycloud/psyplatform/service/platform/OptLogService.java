package cn.psycloud.psyplatform.service.platform;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.platform.ExportOptLogDto;
import cn.psycloud.psyplatform.dto.platform.OptLogDto;
import cn.psycloud.psyplatform.entity.platform.OptLogEntity;

import java.util.List;

public interface OptLogService {
    /**
     *  操作日志
     * @param entity 实体类对象
     * @return 影响行数
     */
    int add(OptLogEntity entity);

    /**
     *  获取操作日志集合：分页
     * @param dto 操作日志实体对象
     * @return 集合
     */
    BSDatatableRes<OptLogEntity> getListByPaged(OptLogDto dto);

    /**
     *  导出操作日志
     * @param dto 条件
     * @return 集合
     */
    List<ExportOptLogDto> getExportList(OptLogDto dto);
}

package cn.psycloud.psyplatform.service.platform;

import cn.psycloud.psyplatform.dto.platform.SysConfigDto;
import cn.psycloud.psyplatform.entity.platform.SysConfigEntity;

/**
 * 平台参数业务接口
 */
public interface SysConfigService {
    /**
     *  获取平台参数信息
     * @return 平台参数信息实体对象
     */
    SysConfigDto get();

    /**
     *  更新平台参数信息
     * @param entity 平台参数实体对象
     * @return 影响行数
     */
    int update(SysConfigEntity entity);
}
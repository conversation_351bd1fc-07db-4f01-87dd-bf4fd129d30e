package cn.psycloud.psyplatform.service.platform;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.platform.UserLoginLogDto;
import cn.psycloud.psyplatform.entity.platform.UserLoginLogEntity;

public interface UserLoginLogService {
    /**
     *  保存登录日志
     * @param entity 登录信息实体类对象
     * @return 影响行数
     */
    int add(UserLoginLogEntity entity);

    /**
     *  获取登录日志集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    BSDatatableRes<UserLoginLogEntity> getListByPaged(UserLoginLogDto dto);
}

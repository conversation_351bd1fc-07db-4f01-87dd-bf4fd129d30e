package cn.psycloud.psyplatform.service.platform;

import cn.psycloud.psyplatform.dto.anteroom.RoleDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.core.ZtreeData;
import cn.psycloud.psyplatform.entity.anteroom.RoleEntity;

import java.util.List;

/**
 *  平台角色业务接口
 */
public interface RoleService {
    /**
     *  新增
     * @param entity 平台角色实体对象
     * @return 影响行数
     */
    int addRole(RoleEntity entity);

    /**
     *  更新
     * @param entity 平台角色实体对象
     * @return 影响行数
     */
    int updateRole(RoleEntity entity);

    /**
     *  删除
     * @param id 角色id
     * @return 影响行数
     */
    int del(Integer id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  查询平台角色集合
     * @param dto 实体对象
     * @return 角色集合
     */
    List<RoleDto> getList(RoleDto dto);

    /**
     *  查询平台角色集合：分页查询
     * @param dto 实体对象
     * @return 角色集合
     */
    BSDatatableRes<RoleDto> getListByPaged(RoleDto dto);

    /**
     *  根据角色ID获取角色权限返回ztree格式
     * @param roleId 角色ID
     * @return 平台功能集合
     */
    List<ZtreeData> getRolePrivilegeForZTree(Integer roleId);

    /**
     *  角色授权
     * @param roleId 角色id
     * @param grants 权限集合
     * @return 执行是否成功
     */
    Boolean addGrant(Integer roleId, String[] grants);

    /**
     *  清空角色权限
     * @param roleId 角色id
     * @return 影响行数
     */
    int clearGrant(Integer roleId);

    /**
     *  查询平台角色集合：转换成select所需格式
     * @return 平台角色集合
     */
    List<Object> getListForSelectP();

    /**
     *  查询咨询师角色集合：转换成select所需格式
     * @param dto 角色实体对象
     * @return 咨询师角色集合
     */
    List<Object> getListForSelect(RoleDto dto);

    /**
     *  查询角色集合(除了咨询师=2和家属=4)：转换成select所需格式
     * @return 咨询师角色集合
     */
    List<Object> getListForSelectAll();
}
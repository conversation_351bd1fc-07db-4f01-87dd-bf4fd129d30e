package cn.psycloud.psyplatform.service.anteroom;

public interface UserSecurityService {
    /**
     *  检验账户是否锁定
     * @param loginName 用户名
     * @return 是否锁定
     */
    boolean isAccountLocked(String loginName);

    /**
     *  增加登录失败次数
     * @param loginName 用户名
     */
    int incrementLoginAttempts(String loginName);

    /**
     *  清除登录失败次数
     * @param loginName 用户名
     */
    void clearLoginAttempts(String loginName);
}

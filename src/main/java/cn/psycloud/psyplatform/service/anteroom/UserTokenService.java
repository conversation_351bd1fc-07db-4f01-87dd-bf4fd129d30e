package cn.psycloud.psyplatform.service.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.UserTokenDto;
import cn.psycloud.psyplatform.entity.anteroom.UserTokenEntity;
import org.apache.ibatis.annotations.Param;

public interface UserTokenService {
    /**
     * 写入用户登录token
     * @param userToken
     * @return
     */
    int insertToken(UserTokenEntity userToken);

    /**
     *  根据用户名删除token
     * @param loginName 用户名
     */
    void deleteByLoginName(@Param("loginName") String loginName);

    /**
     * 清除指定token
     * @param token 用户token
     */
    void deleteByToken(String token);

    /**
     * 获取用户登录token
     * @param token 用户token
     * @return 用户token对象
     */
    UserTokenDto getByToken(String token);
}
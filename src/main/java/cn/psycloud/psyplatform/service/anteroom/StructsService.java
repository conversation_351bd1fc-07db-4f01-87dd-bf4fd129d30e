package cn.psycloud.psyplatform.service.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.StructsDto;
import cn.psycloud.psyplatform.entity.anteroom.StructsEntity;
import java.util.List;

public interface StructsService {
    /**
     *  添加组织
     * @param entity 组织实体对象
     * @return 影响行数
     */
    int addStruct(StructsEntity entity);

    /**
     *  修改组织
     * @param entity 组织实体对象
     * @return 影响行数
     */
    int updateStruct(StructsEntity entity);

    /**
     *  删除组织
     * @param id 组织id
     * @return 影响行数
     */
    int delStruct(Integer id);

    /**
     *  清空组织
     */
    void truncateStructs();

    /**
     *  根据条件查询用户负责的组织集合返回ztree所需格式
     * @param userId 用户id
     * @param checkPriviledge  组织集合
     * @return 用户负责的组织集合
     */
    List<Object> getListForZtree(Integer userId, boolean checkPriviledge);

    /**
     *  获取组织集合(下拉列表)：select
     * @return 组织集合
     */
    List<Object> getListForSelect();

    /**
     *  根据组织ID查询组织信息
     * @param structId 组织id
     * @return 组织实体对象
     */
    StructsDto getById(Integer structId);

    /**
     *  获取子组织集合
     * @param structId 组织id
     * @return 子组织集合
     */
    List<StructsDto> getChild(int structId);

    /**
     *  将list的id拼接成字符串
     * @param list 集合
     * @return 拼接后的字符串
     */
    List<Integer> getChildIds(List<StructsDto> list);

    /**
     *  移动组织
     * @param targetNode 目标节点
     * @param node 源节点
     * @return 影响行数
     */
    int moveStruct(Integer targetNode, Integer node);

    /**
     *  获取最顶级组织id
     * @param structId 组织id
     * @return 最顶级组织id
     */
    Integer getTopStructId(Integer structId);

    /**
     *  根据组织名称和父id获取组织id
     * @param parentId 父id
     * @param structName 组织名称
     * @return 组织id
     */
    Integer getStructIdByStructName(Integer parentId, String structName);

    /**
     *  根据组织名称和父id获取组织id
     * @param structName 参数
     * @return 组织id
     */
    Integer getIdByStructName(String structName);
}
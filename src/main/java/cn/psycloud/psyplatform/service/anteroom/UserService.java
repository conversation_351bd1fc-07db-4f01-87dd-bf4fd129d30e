package cn.psycloud.psyplatform.service.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.*;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.anteroom.CounselorInfoEntity;
import cn.psycloud.psyplatform.entity.anteroom.WechatUserEntity;
import java.util.List;
import java.util.Map;

public interface UserService {
    /**
     *  账号密码登录
     * @param login 实体类对象
     * @return 用户实体对象
     */
    UserDto signIn(LoginDto login);

    /**
     *  短信登录
     * @param mobile 手机号码
     * @return 实体类对象
     */
    UserDto smsLogin(String mobile);

    /**
     *  手机号码登录/注册
     * @param mobile 手机号码
     * @return 用户实体对象
     */
    UserDto mobileLogin(String mobile);

    /**
     *  查询用户集合
     * @param dto 实体对象
     * @return 用户集合
     */
    List<UserDto> getList(UserDto dto, boolean checkPrivilege);

    /**
     *  根据条件查询用户集合
     * @param dto 查询条件对象
     * @param userType 查询用户类型
     * @param checkPrivilege 是否验证数据权限
     * @return 用户集合
     */
    BSDatatableRes<UserDto> getUserListByPaged(UserDto dto, String userType, boolean checkPrivilege);

    /**
     *  查询用户集合：转换成select所需格式
     * @return 对象集合
     */
    List<Object> getCounselorListForSelect();

    /**
     *  网站首页推荐咨询师列表
     * @return 咨询师集合
     */
    List<UserDto> getRecommendCounselorList();

    /**
     *  根据ID查询用户信息
     * @param userId 用户id
     * @return 用户实体对象
     */
    UserDto getById(Integer userId);

    /**
     *  根据用户名查询用户信息
     * @param loginName 用户名
     * @return 用户实体对象
     */
    UserDto getUserByName(String loginName);

    /**
     *  验证旧密码
     * @param dto 修改密码实体对象
     * @return 验证成功是否
     */
    boolean verifyPassword(ModifyPwdDto dto);

    /**
     *  修改密码
     * @param dto 修改密码实体对象
     * @return 影响行数
     */
    boolean modifyPassword(ModifyPwdDto dto);

    /**
     *  修改密码时校验旧密码
     * @param dto 修改密码实体对象
     * @return 影响行数
     */
    boolean modifyPasswordByOriginalPwd(ModifyPwdDto dto);

    /**
     *  判断用户的密码是否过期
     * @param userId 用户Id
     * @return 是否过期
     */
    boolean checkPasswordExpiry(Integer userId);

    /**
     *  更新用户登录时间和IP地址
     * @param params 传参
     */
    void updateLoginDateAndIp(Map<String, String> params);

    /**
     *  保存用户信息
     * @param dto 用户信息实体对象
     * @return 是否执行成功
     */
    boolean insertUserInfo(UserDto dto);

    /**
     *  添加用户
     * @param dto 用户实体对象
     * @return 影响行数
     */
    boolean addUser(UserDto dto);

    /**
     *  修改用户
     * @param dto 用户实体对象
     * @return 影响行数
     */
    boolean updateUser(UserDto dto);

    /**
     *  简易修改个人信息
     * @return 影响行数
     */
    int updateForScale(UserDto dto);

    /**
     *  验证用户名是否存在
     * @param loginName 用户名
     * @return 是否存在
     */
    int isLoginNameExist(String loginName);

    /**
     *  删除用户
     * @param userId 用户id
     * @return 影响行数
     */
    int delUser(Integer userId);

    /**
     *  批量删除
     * @param ids 用户id集合
     * @return 执行是否成功
     */
    boolean batchDelUser(String ids);

    /**
     *  注册审核
     * @param userId 用户id
     * @return 影响行数
     */
    int check(Integer userId);

    /**
     *  批量注册审核
     * @param ids 用户id集合
     * @return 是否成功
     */
    boolean batchCheck(String ids);

    /**
     *  更换头像
     * @param userId 用户Id
     * @param headPic 头像
     * @return 影响行数
     */
    int updateAvatar(Integer userId, String headPic);

    /**
     *  批量开通：预览
     * @param dto 用户实体对象
     * @return 用户集合
     */
    List<UserDto> getPreviewUserList(BatchAddDto dto);

    /**
     *  批量开通
     * @return 是否成功
     */
    boolean batchAdd(List<BatchAddDto> list);

    /**
     *  更新咨询师附加信息
     * @param entity 咨询师信息实体对象
     */
    void updateCounselorInfo(CounselorInfoEntity entity);

    /**
     *  绑定手机号码
     * @param mobile 手机号码
     * @return 影响行数
     */
    int bindMobile(String mobile);

    /**
     *  验证手机号码是否被绑定
     * @param mobile 手机号码
     * @return 影响行数
     */
    int checkMobile(String mobile);

    /**
     *  手机号码登录/注册
     * @param mobile 手机号码
     * @return 用户实体对象
     */
    UserDto MobileLogin(String mobile);

    /**
     *  批量更新用户所属角色
     * @param roleId 角色id
     * @param userIds 用户id集合
     * @return 是否执行成功
     */
    boolean batchUpdate(Integer roleId, String userIds);

    /**
     *  用户集合
     * @param users  用户集合
     * @return 导入结果集合
     */
    List<ImportUserDto> importVisitor(List<ImportUserDto> users, Integer structId);

    /**
     *  预约咨询验证来访者信息
     * @param loginName 来访者用户名
     * @return 用户id
     */
    String checkUserInfo(String loginName);

    /**
     *  根据用户ID查询用户手机号码
     */
    String getMobileByUserId();

    /**
     * 添加微信用户信息
     * @param entity 微信用户信息实体
     * @return 影响行数
     * */
    int addWechatUser(WechatUserEntity entity);

    /**
     * 根据unionId查询用户信息
     * @param unionId 微信unionId
     * @return 用户信息
     */
    UserDto getUserInfoByWechatUnionId(String unionId);

    /**
     * 绑定微信用户
     * @param loginName 登录名
     * @param realName 真实姓名
     * @return 影响行数
     */
    UserDto bindWechatUser(String loginName, String realName);
}
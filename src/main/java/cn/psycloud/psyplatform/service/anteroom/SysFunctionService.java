package cn.psycloud.psyplatform.service.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.SysFunctionDto;
import cn.psycloud.psyplatform.dto.core.ZtreeData;
import cn.psycloud.psyplatform.entity.anteroom.SysFunctionEntity;
import java.util.List;

public interface SysFunctionService {
    /**
     * 判断当前节点是否还有子节点
     * @param parentCode 父功能编码
     * @return 是否
     */
    Boolean isParent(String parentCode);

    /**
     *  把list集合转换成zTree所需的json格式
     * @param functionList 功能集合
     * @return Object对象集合
     */
    List<Object> createZTreeJson(List<SysFunctionEntity> functionList);

    /**
     *  把数据转换成zTree所需的json格式
     * @return 功能集合
     */
    List<ZtreeData> getListForZTree();

    /**
     *  根据用户名获取用户权限
     * @param loginName 用户名
     * @return 功能集合
     */
    List<SysFunctionDto> getSysFunctionByName(String loginName);

    /**
     *  根据角色获取功能权限
     * @param roleId 角色id
     * @return 功能集合
     */
    List<SysFunctionDto> getGrantByRole(Integer roleId);
}
package cn.psycloud.psyplatform.service.anteroom;

import cn.psycloud.psyplatform.dto.anteroom.MailDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.anteroom.MailEntity;

import java.util.List;

public interface MailService {
    /**
     *  根据条件查询消息集合
     * @param dto 消息实体对象
     * @return 消息实体对象
     */
    List<MailDto> getList(MailDto dto);

    /**
     * 查询消息集合：分页查询
     * @param dto 查询条件
     * @return 集合
     */
    BSDatatableRes<MailDto> getListByPaged(MailDto dto);

    /**
     * 根据ID查询消息信息
     * @param id 消息id
     * @return 消息实体对象
     */
    MailDto getById(Integer id);

    /**
     *  更改消息已读状态
     * @param ids ID集合
     * @return 执行是否成功
     */
    boolean updateReadState(String ids);

    /**
     *  删除消息
     * @param id 消息id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  批量删除
     * @param ids ID集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  发送站内消息
     * @param entity 消息实体对象
     * @return 影响行数
     */
    int sendMsg(MailEntity entity);
}

package cn.psycloud.psyplatform.service.archiveroom;

import cn.psycloud.psyplatform.dto.archiveroom.ArchiveAdviceDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.archiveroom.AdviceEntity;

public interface ArchiveAdviceService {
    /**
     *  添加评语
     * @param entity 评语实体对象
     * @return 影响行数
     */
    int insert(AdviceEntity entity);

    /**
     *  修改评语
     * @param entity 评语实体对象
     * @return 影响行数
     */
    int update(AdviceEntity entity);

    /**
     *  删除评语
     * @param id 评语Id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  批量删除
     * @param ids ID集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  根据条件查询评语集合
     * @param dto 查询条件
     * @return 集合
     */
    BSDatatableRes<AdviceEntity> getListByPaged(ArchiveAdviceDto dto);
}

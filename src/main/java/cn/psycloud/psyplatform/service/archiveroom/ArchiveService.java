package cn.psycloud.psyplatform.service.archiveroom;

import cn.psycloud.psyplatform.dto.archiveroom.ArchiveConditionDto;
import cn.psycloud.psyplatform.dto.archiveroom.ArchiveDto;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface ArchiveService {
    /**
     *  生成档案
     * @param dto 条件
     * @return 用户集合
     */
    Map<String,Object> createArchive(HttpServletResponse response, HttpServletRequest request, ArchiveConditionDto dto);

    /**
     *  获取档案用户
     * @param userId 用户id
     * @return 用户集合
     */
    ArchiveDto getArchiveContentByUserId(Integer userId);
}

package cn.psycloud.psyplatform.service.performance;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.ImportResultDto;
import cn.psycloud.psyplatform.dto.performance.ImportUserPerformanceDto;
import cn.psycloud.psyplatform.dto.performance.UserPerformanceDto;

import java.util.List;

public interface UserPerformanceService {

    /**
     * 批量新增用户绩效信息
     * @param list 用户绩效信息列表
     * @return 执行是否成功
     */
    ImportResultDto batchInsertUserPerformance(List<ImportUserPerformanceDto> list);

    /**
     * 删除用户绩效信息
     * @param id 主键ID
     * @return 影响行数  
     */
    int deleteById(Integer id);

    /**
     * 批量删除用户绩效信息
     * @param ids 主键ID列表
     * @return 影响行数  
     */
    boolean batchDel(String ids);

    /**
     * 查询用户绩效信息列表
     * @param userPerformanceDto 用户绩效信息
     * @return 用户绩效信息列表
     */
    BSDatatableRes<UserPerformanceDto> getListByPaged(UserPerformanceDto userPerformanceDto);
}

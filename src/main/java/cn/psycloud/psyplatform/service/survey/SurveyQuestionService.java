package cn.psycloud.psyplatform.service.survey;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.survey.ImportSurveyQDto;
import cn.psycloud.psyplatform.dto.survey.SurveyQuestionDto;
import cn.psycloud.psyplatform.entity.survey.SurveyQuestionEntity;

import java.util.List;

public interface SurveyQuestionService {
    /**
     *  查询题目集合：分页
     * @param dto  查询条件
     * @return 题目集合
     */
    BSDatatableRes<SurveyQuestionDto> getListBySurveyId(SurveyQuestionDto dto);

    /**
     *  根据问卷id查询题目集合
     * @param surveyId 问卷id
     * @return 题目集合
     */
    List<SurveyQuestionDto> getListBySurveyId(Integer surveyId);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  添加题目
     * @param entity 题目实体对象
     * @return 题目id
     */
    int add(SurveyQuestionEntity entity);

    /**
     *  修改题目
     * @param entity 题目实体对象
     * @return 影响行数
     */
    int update(SurveyQuestionEntity entity);

    /**
     *  excel导入题目和选项
     * @param list excel表格数据
     * @param surveyId 问卷id
     */
    void importQuestion(List<ImportSurveyQDto> list, Integer surveyId);
}

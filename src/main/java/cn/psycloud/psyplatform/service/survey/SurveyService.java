package cn.psycloud.psyplatform.service.survey;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.survey.SurveyDto;
import cn.psycloud.psyplatform.dto.survey.SurveyNameDto;
import cn.psycloud.psyplatform.entity.survey.SurveyEntity;

import java.util.List;

public interface SurveyService {
    /**
     *  根据id查询问卷信息
     * @param surveyId 问卷id
     * @return 问卷实体类对象
     */
    SurveyDto getById(Integer surveyId);

    /**
     *  获取问卷集合
     * @param dto 查询条件
     * @return 问卷集合
     */
    List<SurveyDto> getList(SurveyDto dto);

    /**
     *  查询问卷集合：分页
     * @param dto 问卷实体对象
     * @return 问卷集合
     */
    BSDatatableRes<SurveyDto> getListByPaged(SurveyDto dto);

    /**
     *  查询问卷集合：select格式
     * @return 集合
     */
    List<Object> getListForSelect();

    /**
     *  查询问卷集合：DualListBox格式
     * @return 集合
     */
    List<Object> getListForDualList();

    /**
     *  根据测评任务查询问卷集合：select
     * @param taskId 任务id
     * @return 集合
     */
    List<Object> getListByTaskId(Integer taskId);
    List<Object> getListByMeasuringTaskId(Integer taskId);
    /**
     *  添加问卷
     * @param entity 问卷实体类对象
     * @return 影响行数
     */
    int addSurvey(SurveyEntity entity);

    /**
     *  修改问卷
     * @param entity 问卷实体类对象
     * @return 影响行数
     */
    int updateSurvey(SurveyEntity entity);

    /**
     *  删除问卷
     * @param id 问卷id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  修改问卷完成状态
     * @param surveyId 问卷id
     * @param state 状态标识
     * @return 影响行数
     */
    int updateDone(Integer surveyId, Integer state);

    /**
     *  判断问卷名称是否存在
     * @param dto 问卷名称
     * @return 数量
     */
    boolean isSurveyNameExists(SurveyNameDto dto);
}

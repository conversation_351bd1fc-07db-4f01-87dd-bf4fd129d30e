package cn.psycloud.psyplatform.service.survey;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.survey.SurveyItemEntity;

public interface SurveyItemService {
    /**
     *  查询条目的选项集合：返回Bootstrap Datatables格式
     * @param qId 题目id
     * @return 选项集合
     */
    BSDatatableRes<SurveyItemEntity> getItemsByQId(Integer qId);

    /**
     *  添加选项（单个题目）
     * @param entity 选项实体对象
     * @return 影响行数
     */
    int add(SurveyItemEntity entity);

    /**
     *  删除选项
     * @param itemId 选项id
     * @return 影响行数
     */
    int delete(Integer itemId);
}

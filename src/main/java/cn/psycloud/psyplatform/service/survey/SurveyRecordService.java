package cn.psycloud.psyplatform.service.survey;

import cn.psycloud.psyplatform.dto.activityroom.ActivitySurveyRecordDto;
import cn.psycloud.psyplatform.dto.activityroom.SurveyAnswerDetailDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.TaskSurveyDto;
import cn.psycloud.psyplatform.dto.survey.ExportSurveyRecordDto;
import cn.psycloud.psyplatform.entity.activityroom.ActivitySurveyEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TaskSurveyRecordEntity;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

public interface SurveyRecordService {
    /**
     *  删除测评任务下某个用户的所有问卷作答记录
     * @param userId 用户Id
     * @param taskId 任务id
     * @return 影响行数
     */
    int delRecordByTaskId(Integer userId, Integer taskId);

    /**
     *  删除问卷调查任务下某个用户的所有问卷作答记录
     * @param userId 用户Id
     * @param taskId 任务id
     * @return 影响行数
     */
    int delRecordOfTaskSurveyByTaskId(Integer userId, Integer taskId);

    /**
     *  删除活动下某个用户的所有问卷作答记录
     * @param userId 用户Id
     * @param activityId 活动id
     * @param surveyId 问卷id
     * @return 影响行数
     */
    int delRecordByActivityId(Integer userId, Integer activityId, Integer surveyId);

    /**
     *  更新问卷作答记录的日期
     * @param recordId 记录id
     * @param recordDate 作答日期
     * @return 影响行数
     */
    int updateSurveyRecordDate(Integer recordId, Date recordDate);

    /**
     *  添加问卷作答记录：测评任务
     * @param entity 实体对象
     */
    boolean addRecord(TaskSurveyRecordEntity entity);

    /**
     *  添加问卷作答记录：问卷调查任务
     * @param entity 实体对象
     * @return 是否成功
     */
    boolean addRecordForTaskSurvey(TaskSurveyRecordEntity entity);

    /**
     *  添加问卷作答记录：活动
     * @param entity 实体对象
     * @return 是否成功
     */
    boolean addRecordForActivity(ActivitySurveyEntity entity);

    /**
     *  查询测评任务里调查问卷记录集合：分页
     * @param dto 调查问卷记录实体对象
     * @return 问卷作答记录集合
     */
    BSDatatableRes<TaskSurveyDto> getListByPaged(TaskSurveyDto dto);

    /**
     *  查询问卷调查任务里调查问卷记录集合：分页
     * @param dto 调查问卷记录实体对象
     * @return 问卷作答记录集合
     */
    BSDatatableRes<TaskSurveyDto> getListForTaskSurveyByPaged(TaskSurveyDto dto);

    /**
     *  删除
     * @param id 记录id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  导出测评记录
     * @param dto 查询条件
     * @return  测评记录集合
     */
    List<ExportSurveyRecordDto> getExportRecordList(TaskSurveyDto dto);

    /**
     *  导出问卷调查记录
     * @param dto 查询条件
     * @return 记录集合
     */
    List<ExportSurveyRecordDto> getExportTaskSurveyRecord(TaskSurveyDto dto);

    /**
     *  导出调查问卷结果数据
     * @param dto 条件
     * @return 集合
     */
    List<LinkedHashMap<String,Object>> exportTaskSurveyResult(TaskSurveyDto dto);

    /**
     *  获取活动的问卷作答记录：分页
     * @param dto 查询条件
     * @return 作答记录集合
     */
    BSDatatableRes<ActivitySurveyRecordDto> getActivitySurveyRecordListByPaged(ActivitySurveyRecordDto dto);

    /**
     *  获取我的问卷记录
     * @param dto 查询条件
     * @return 测评记录集合
     */
    BSDatatableRes<TaskSurveyDto> getMyRecords(TaskSurveyDto dto);

    /**
     * 获取调查问卷作答记录id
     * @param taskId 任务id
     * @param userId 用户id
     * @param surveyId 问卷id
     * @return 记录id
     */
    int getSurveyRecordId(Integer taskId, Integer userId, Integer surveyId);

    /**
     * 获取活动问卷作答记录的详细信息（包含题目和选择结果）
     * @param dto 查询条件
     * @return 包含详细信息的作答记录集合
     */
    BSDatatableRes<ActivitySurveyRecordDto> getActivitySurveyRecordListWithDetailsbyPaged(ActivitySurveyRecordDto dto);
}

package cn.psycloud.psyplatform.service.comment;

import cn.psycloud.psyplatform.dto.comment.CommentDto;
import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.entity.comment.CommentEntity;

public interface CommentService {
    /**
     *  发表评论
     * @param entity 评论实体对象
     * @return  影响行数
     */
    int addComment(CommentEntity entity);

    /**
     *  删除评论
     * @param id 评论id
     * @return 影响行数
     */
    int deleteComment(Integer id);

    /**
     *  批量删除评论
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDelComments(String ids);

    /**
     *  获取训练营评论集合：分页
     * @param dto 查询条件
     * @return 集合
     */
    BSDatatableRes<CommentDto> getCommentListByPaged(CommentDto dto);

    /**
     *  审核评论
     * @param ids 评论id集合
     * @return 影响行数
     */
    boolean checkComment(String ids);
}

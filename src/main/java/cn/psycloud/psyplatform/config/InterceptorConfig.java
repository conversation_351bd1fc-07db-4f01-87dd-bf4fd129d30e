package cn.psycloud.psyplatform.config;

import cn.psycloud.psyplatform.interceptor.AuthInterceptor;
import cn.psycloud.psyplatform.interceptor.BaseInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {
    @Bean
    BaseInterceptor getBaseInterceptor(){
        return new BaseInterceptor();
    }
    @Bean
    AuthInterceptor getAuthInterceptor(){
        return new AuthInterceptor();
    }

    @Value("${file.location}")
    String uploadPath;
    @Value("${file.accessPath}")
    String accessPath;
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getAuthInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/account/**")
                .excludePathPatterns("/app/account/**")
                .excludePathPatterns("/sms/**")
                .excludePathPatterns("/error/**")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/AZihmY67mW.txt")
                .excludePathPatterns("/MP_verify_nXwpefDbVUkQdEDR.txt");
        registry.addInterceptor(getBaseInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/static/**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
        registry.addResourceHandler(accessPath)
                .addResourceLocations("file:///" + uploadPath);
    }
}

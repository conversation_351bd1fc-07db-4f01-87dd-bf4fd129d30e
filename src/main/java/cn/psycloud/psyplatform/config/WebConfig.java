package cn.psycloud.psyplatform.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
 public class WebConfig implements WebMvcConfigurer {
    /**
     * 接口地址忽略大小写
     * @param pathMatchConfigurer pathMatchConfigurer
     */
    @Override
    public void configurePathMatch(PathMatchConfigurer pathMatchConfigurer) {
        AntPathMatcher matcher = new AntPathMatcher();
        matcher.setCaseSensitive(false);
        pathMatchConfigurer.setPathMatcher(matcher);
    }
}
